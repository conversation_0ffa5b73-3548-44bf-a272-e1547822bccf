**15**

**A**

**72. Write a menu driven program to implement following operations on the doubly linked list.**

- Insert a node at the front of the linked list.
- Delete a node from specified position.
- Insert a node at the end of the linked list. (Home Work)
- Display all nodes. (Home Work)

**B**

**73. WAP to delete alternate nodes of a doubly linked list.**

**C**

**74. Write a program to simulate music player application using suitable data structure. There is no estimation about number of music files to be managed by the music player. Your program should support all the basic music player operations to play and manage the playlist.**
