# **RBAC Matrix**

### Users

| **Action / Feature**  | **Super Admin** | **User** | **Faculty (Tenant)** | **Student (Tenant)** |
| --------------------- | --------------- | -------- | -------------------- | -------------------- |
| View All Users        | ✅              | ❌       | ❌                   | ❌                   |
| View Own Profile      | ✅              | ✅       | ✅                   | ✅                   |
| Edit Own Profile      | ✅              | ✅       | ✅                   | ✅                   |
| Manage Users (CRUD)   | ✅              | ❌       | ✅                   | ❌                   |
| Assign Roles to Users | ✅              | ❌       | ✅                   | ❌                   |

### Assignments

| **Action / Feature**             | **Super Admin** | **User** | **Faculty (Tenant)** | **Student (Tenant)** |
| -------------------------------- | --------------- | -------- | -------------------- | -------------------- |
| View All Assignments             | ✅              | ✅       | ✅                   | ✅                   |
| Create/Update/Delete Assignments | ✅              | ❌       | ✅                   | ❌                   |
| Assign Assignments to Students   | ✅              | ❌       | ✅                   | ❌                   |

### Subjects

| **Action / Feature**               | **Super Admin** | **User** | **Faculty (Tenant)** | **Student (Tenant)** |
| ---------------------------------- | --------------- | -------- | -------------------- | -------------------- |
| View All Subjects                  | ✅              | ✅       | ✅                   | ✅                   |
| Create/Update/Delete Subjects      | ✅              | ❌       | ✅                   | ❌                   |
| Assign Subjects to Faculty/Student | ✅              | ❌       | ✅                   | ❌                   |

### Submissions

| **Action / Feature**      | **Super Admin** | **User** | **Faculty (Tenant)** | **Student (Tenant)** |
| ------------------------- | --------------- | -------- | -------------------- | -------------------- |
| View All Submissions      | ✅              | ✅       | ✅                   | ✅                   |
| Create/Submit Submissions | ❌              | ❌       | ❌                   | ✅                   |
| Review/Grade Submissions  | ❌              | ❌       | ✅                   | ❌                   |
| Update/Delete Submissions | ✅              | ❌       | ✅                   | ❌                   |

### Tenants

| **Action / Feature**         | **Super Admin** | **User** | **Faculty (Tenant)** | **Student (Tenant)** |
| ---------------------------- | --------------- | -------- | -------------------- | -------------------- |
| View All Tenants             | ✅              | ❌       | ❌                   | ❌                   |
| View Own Tenant              | ✅              | ✅       | ✅                   | ✅                   |
| Manage Tenants (CRUD)        | ✅              | ❌       | ❌                   | ❌                   |
| Create/Update/Delete Tenants | ✅              | ❌       | ❌                   | ❌                   |

---

## **Detailed Permissions**:

- **Super Admin**:
  - Full access to **all collections** (users, assignments, subjects, submissions, tenants) across the entire platform.
  - Can create, read, update, delete (CRUD) tenants, users, and assignments.
  - Can manage roles for users and assign them specific permissions in different tenants.

- **User**:
  - Has **limited access** to their own profile and tenant.
  - Can only view assignments and subjects within their tenant.
  - Cannot manage any other data (users, assignments, subjects, tenants).

- **Faculty (Tenant)**:
  - Can manage content specific to their tenant, including assignments and subjects.
  - Can **assign** assignments and manage submissions of students within their tenant.
  - Can create and update **assignments** and **subjects**.
  - Can view submissions but may not necessarily **grade** them, depending on your use case.

- **Student (Tenant)**:
  - Can **submit assignments** and view assignments assigned to them.
  - Can only view **their own submissions** and **their own data** (profile).
  - Cannot manage any content (assignments, subjects) or data of other students.
