## Market Research & Analysis

- **14+ colleges** in Rajkot offer UG/PG CS programs (B.Tech CSE, BCA, B.Sc IT/CS, MCA).
- **Estimated student base:** \~3,000 students currently enrolled in CS-related courses.

---

### **Lab & Assignment Practices**

- Programming courses include **weekly lab sessions**.
- Assignments are typically shared and submitted via **email or Google Drive**.
- Final practical exams conducted **once or twice per semester**.
- **No standard digital tool** is used for auto-grading or lab management.

---

### **Average Tuition Fees**

- **B.Tech CSE:** ₹1.1 lakh/year (private), ₹15K/year (govt)
- **BCA / B.Sc IT:** ₹60K/year (private), ₹10K/year (govt)
- **MCA:** ₹1 lakh/year (private)

---

### **Market Opportunity (TAM)**

- **Target student base in Rajkot:** \~3,000 CS students
- **Pricing model:** ₹200 per student/month = ₹2,400 per year
- **Total addressable market:** ₹2,400 × 3,000 = **₹72 lakh/year**

_This reflects full adoption across CS departments in Rajkot._

---

### **Realistic TAM @ 5% Market Penetration**

- **Target reach:** 5% of 3,000 students = **150 students**
- **Annual revenue per student:** ₹2,400
- **Estimated revenue:** 150 × ₹2,400 = **₹3.6 lakh/year**

---

### **Current Tools & Gaps**

- Most institutes still rely on **manual processes** for coding labs and practical exams, leading to high faculty workload and inefficiency
- **No standard platform** exists for in-browser coding, automated grading, or streamlined assignment management
- A few universities have started **adopting digital solutions**, indicating a shift toward more efficient, tech-driven academic workflows
