# **Product Requirements Document (PRD)**

**Product Name:** EASELab
**Owner:** Soham
**Last Updated:** June 2, 2025
**Version:** 1.2

---

## **1. Purpose**

EASELab is a lightweight, AI-assisted lab management system built to simplify the creation, execution, and evaluation of programming lab assignments for Computer Science students. It helps instructors automate lab workflows while providing students with an intuitive coding and testing experience.

---

## **2. Scope**

The platform enables:

- Instructor-led lab and assignment management
- Student code editing and real-time testing in-browser
- AI-generated assignments with test cases
- Automatic grading and performance tracking

---

## **3. Target Users**

- **Primary:**
  - CSE Students (B.Tech, BCA, etc)
  - Instructors

- **Secondary:**
  - Department admins (for audits)

---

## **4. Features (Implemented)**

### **4.1 Assignment Management**

- Manual and AI-generated programming assignments
- Define supported language(s), difficulty, and test cases
- Toggle availability and deadline

### **4.2 Code Editor**

- In-browser IDE for:
  - **HTML**
  - **CSS**
  - **JavaScript**
  - **Java**
  - **C**

- Syntax highlighting, output console, test case feedback

### **4.3 Test Runner**

- Real-time test case execution with on-device sandboxed evaluation
- Iframe-based test rendering with debouncing
- Handles standard input/output-based testing

### **4.4 Auto-Grading**

- Score calculation based on test case success
- Instructor-defined weightage per assignment

### **4.5 Dashboards**

- **Student View:** Published assignment list, playground access
- **Instructor View:** Submissions access, Manage Assignments, Manage Subjects

### **4.6 AI Assignment Generator**

- Gemini-powered content generation
- Prompt-based topic control and difficulty selection

---

## **5. Non-Functional Requirements**

- **Performance:** Test execution ≤3 seconds
- **Security:** on-device Sandboxed execution (requires easelabs-cli)
- **Scalability:** Designed for moderate institutional usage
- **Reliability:** High availability during academic hours (99.67% uptime)

---

## **6. Exclusions (as of now)**

- ❌ Batch or group lab session handling
- ❌ Real-time collaborative coding
- ❌ Group-based project submissions

---

## **8. Roadmap**

| Milestone             | Target Date | Status      |
| --------------------- | ----------- | ----------- |
| Feature Completion    | May 2025    | ✅ Done     |
| Internal Testing & QA | June 2025   | ✅ Done     |
| Institutional Pilot   | June 2025   | 🔜 Upcoming |
| Public Rollout        | July 2025   | ⏳ Planned  |

---

## **9. Future Enhancements (Post-MVP)**

- ✅ Batch & group session support
- ✅ LMS integrations
- ✅ Mobile-friendly student view
- ✅ Collaboration tools (peer review)
- ✅ Plagiarism checking
