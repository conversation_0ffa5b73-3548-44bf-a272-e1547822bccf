# Dynamic Model System

This system allows you to switch between different OpenRouter AI models at runtime, providing flexibility for different use cases and performance requirements.

## Features

- ✅ Runtime model switching without restart
- ✅ Environment variable configuration
- ✅ Type-safe model selection
- ✅ API endpoints for model management
- ✅ React component for UI-based switching
- ✅ Backward compatibility with existing code

## Quick Start

### 1. Environment Configuration

Add to your `.env` file:

```bash
OPENROUTER_MODEL=openai/gpt-4.1-nano  # Default model
```

### 2. Basic Usage

```typescript
import { ModelManager } from '@/lib/model-manager';

// Get current model
console.log(ModelManager.getCurrentModel());

// Switch to a different model
ModelManager.switchModel('anthropic/claude-3.5-sonnet');

// Use convenience functions
import { switchToGPT4, switchToClaude } from '@/lib/model-manager';
switchToGPT4();
switchToClaude();
```

### 3. API Usage

```typescript
// Get current model info
const response = await fetch('/api/model');
const { currentModel, availableModels } = await response.json();

// Switch model
await fetch('/api/model', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ modelName: 'anthropic/claude-3.5-sonnet' }),
});
```

### 4. UI Component

```tsx
import { ModelSwitcher } from '@/components/model-switcher';

function AdminPanel() {
  return (
    <div>
      <h1>Admin Panel</h1>
      <ModelSwitcher />
    </div>
  );
}
```

## Available Models

- `openai/gpt-4.1-nano` (default)
- `openai/gpt-4o`
- `openai/gpt-4o-mini`
- `anthropic/claude-3.5-sonnet`
- `anthropic/claude-3-haiku`
- `google/gemini-2.0-flash-exp`
- `google/gemini-1.5-pro`
- `meta-llama/llama-3.1-8b-instruct`
- `meta-llama/llama-3.1-70b-instruct`
- `mistralai/mistral-7b-instruct`
- `cohere/command-r-plus`

## Use Cases

### Task-Based Model Selection

```typescript
function selectModelForTask(taskType: string) {
  switch (taskType) {
    case 'code-generation':
      ModelManager.switchModel('anthropic/claude-3.5-sonnet');
      break;
    case 'fast-analysis':
      ModelManager.switchModel('openai/gpt-4o-mini');
      break;
    case 'complex-reasoning':
      ModelManager.switchModel('openai/gpt-4o');
      break;
  }
}
```

### Cost Optimization

```typescript
// Use cheaper models for simple tasks
ModelManager.switchModel('openai/gpt-4o-mini');

// Switch to premium models only when needed
if (complexTask) {
  ModelManager.switchModel('anthropic/claude-3.5-sonnet');
}
```

### A/B Testing

```typescript
// Randomly assign models for testing
const models = ['openai/gpt-4o', 'anthropic/claude-3.5-sonnet'];
const randomModel = models[Math.floor(Math.random() * models.length)];
ModelManager.switchModel(randomModel);
```

## Migration Guide

### From Static Model

**Before:**

```typescript
import { model } from '@/mastra/config';

const agent = new Agent({
  name: 'My Agent',
  model, // Static model
});
```

**After:**

```typescript
import { getModel } from '@/mastra/config';

const agent = new Agent({
  name: 'My Agent',
  get model() {
    return getModel();
  }, // Dynamic model
});
```

## API Reference

### ModelManager

- `switchModel(modelName)` - Switch to a specific model
- `getCurrentModel()` - Get current model name
- `getAvailableModels()` - Get list of available models
- `isValidModel(modelName)` - Check if model name is valid

### API Endpoints

- `GET /api/model` - Get current model info
- `POST /api/model` - Switch model (body: `{ modelName: string }`)

### React Component

- `<ModelSwitcher />` - UI component for model switching

## Best Practices

1. **Use appropriate models for tasks**: Fast models for simple tasks, powerful models for complex ones
2. **Handle errors gracefully**: Always check if model switching was successful
3. **Monitor costs**: Different models have different pricing
4. **Test thoroughly**: Ensure your application works with all models you plan to use
5. **Use environment variables**: Configure default models per environment

## Troubleshooting

### Model not switching

- Check if the model name is valid using `ModelManager.isValidModel()`
- Ensure OpenRouter API key is configured
- Check console for error messages

### Performance issues

- Consider using faster models for non-critical tasks
- Monitor API response times for different models
- Implement caching where appropriate

## Examples

See `src/lib/model-usage-examples.ts` for comprehensive usage examples.
