services:
  payload:
    image: node:18-alpine
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - '3000:3000'
    volumes:
      - .:/home/<USER>/app
      - node_modules:/home/<USER>/app/node_modules
    working_dir: /home/<USER>/app/
    command: sh -c "corepack enable && corepack prepare pnpm@latest --activate && pnpm install && pnpm dev"
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      - DATABASE_URI=******************************************/super-labs
      - PAYLOAD_SECRET=your-secret-key-here
      - NODE_ENV=development
      - PAYLOAD_CONFIG_PATH=src/payload.config.ts
      - SEED_DB=true
    env_file:
      - .env
    networks:
      - app-network

  postgres:
    restart: always
    image: postgres:latest
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: super-labs
    volumes:
      - data:/var/lib/postgresql/data
    ports:
      - '127.0.0.1:5432:5432'
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  data:
  node_modules:
