FROM node:18-slim AS base

# Install dependencies only when needed
FROM base AS deps
# Install necessary packages for build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    # Add any other build dependencies your project needs here,
    # for example, if you have native dependencies that require build tools.
    && rm -rf /var/lib/apt/lists/*
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
RUN \
  if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm i --frozen-lockfile; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Disable Next.js telemetry during build
ENV NEXT_TELEMETRY_DISABLED=1

RUN \
  if [ -f yarn.lock ]; then yarn run build; \
  elif [ -f package-lock.json ]; then npm run build; \
  elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm run build; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Production image
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000

# Create a non-root user for security
RUN groupadd --system --gid 1001 nodejs && \
    useradd --system --uid 1001 --gid nodejs nextjs

# Copy the built application including the standalone output
# The standalone output includes the server.js and all necessary files
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./

# The standalone output usually copies the public directory as well,
# but it's good practice to copy it explicitly if needed or
# if you have static files outside the .next/static directory.
COPY --from=builder /app/public ./public

# Copy the static assets separately. The standalone output already includes
# the necessary static files, but this can be a fallback or if you have
# static files that aren't automatically included.
# COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# If using .env.production, uncomment the next line:
# COPY --from=builder /app/.env.production .env.production

USER nextjs

EXPOSE 3000

# The command to start the Next.js standalone server
CMD ["node", "server.js"]
