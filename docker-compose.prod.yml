services:
  app:
    image: ${REGISTRY:-docker.io}/${IMAGE_NAME:-your-org/your-app}:${TAG:-latest}
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${PROD_DATABASE_URL}
      - REDIS_URL=${PROD_REDIS_URL}
      # Add other environment variables as needed
    ports:
      - '3000:3000'
    depends_on:
      - db
      - redis
    restart: always
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure

  db:
    image: postgres:14-alpine
    environment:
      - POSTGRES_USER=${PROD_DB_USER}
      - POSTGRES_PASSWORD=${PROD_DB_PASSWORD}
      - POSTGRES_DB=${PROD_DB_NAME}
    volumes:
      - prod_postgres_data:/var/lib/postgresql/data
    restart: always

  redis:
    image: redis:alpine
    volumes:
      - prod_redis_data:/data
    restart: always

volumes:
  prod_postgres_data:
  prod_redis_data:
