.PHONY: dev dev-down staging staging-down prod prod-down build clean help seed

# Development environment
dev:
	docker compose -f docker-compose.dev.yml up

dev-down:
	docker compose -f docker-compose.dev.yml down

# Staging environment
staging:
	docker compose -f docker-compose.staging.yml up -d

staging-down:
	docker compose -f docker-compose.staging.yml down

# Production environment
prod:
	docker compose -f docker-compose.prod.yml up -d

prod-down:
	docker compose -f docker-compose.prod.yml down

# Build images
build:
	COMPOSE_BAKE=true docker compose -f docker-compose.dev.yml build

# Seed database
seed:
	docker compose -f docker-compose.dev.yml exec payload sh -c "SEED_DB=true pnpm dev"

# Clean up
clean:
	docker compose -f docker-compose.dev.yml down -v
	docker compose -f docker-compose.staging.yml down -v
	docker compose -f docker-compose.prod.yml down -v
	docker system prune -f

# Help command
help:
	@echo "Available commands:"
	@echo "  make dev          - Start development environment"
	@echo "  make dev-down     - Stop development environment"
	@echo "  make staging      - Start staging environment"
	@echo "  make staging-down - Stop staging environment"
	@echo "  make prod         - Start production environment"
	@echo "  make prod-down    - Stop production environment"
	@echo "  make build        - Build all images"
	@echo "  make seed         - Seed the database with initial data"
	@echo "  make clean        - Clean up all environments and remove unused resources"
	@echo "  make help         - Show this help message"
