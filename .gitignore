# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

/.idea/*
!/.idea/runConfigurations

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

.env

/media
.pnpm-store

# Playwright
node_modules/
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# Mastra
.mastra

# Sentry Config File
.env.sentry-build-plugin
server/temp/*
venv