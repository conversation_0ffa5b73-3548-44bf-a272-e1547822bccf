# EaseLabs

A streamlined platform for managing programming lab assignments in computer science courses. EASELab offers in-browser coding, automated grading, and test case execution—designed to deliver a consistent and efficient lab experience for both students and faculty. Its goal is to save time, reduce friction, and simplify lab workflows.

## **Features**

### For Students

- Browse and access active lab assignments
- Code in a Monaco-based editor with support for HTML, CSS, JavaScript, C, and Java
- Live preview through an embedded iframe
- Run test cases locally before submission
- Submit assignments with auto-evaluation

### For Faculty

- Create and manage assignments with custom test cases
- Define test suites for automated grading
- View and review all student submissions
- Validate or override auto-graded results

---

Let me know if you'd like a more formal, marketing-friendly, or technical tone.

---

## Inspired By

- CodePen, CodeSandbox, Replit, and other online code editors.

---

## For Non-Technical Users

EaseLabs simplifies computer science lab assignments. It's a straightforward platform where students can practice coding and receive immediate feedback, helping them to grasp programming concepts more effectively.

## Pricing Model

We offer flexible pricing plans tailored to your needs:

- **Pilot Program:** Ideal for small classes or trial periods. Includes support for up to 20 students, 5 assignments, and 1 faculty member for 3 months. This plan includes basic features and support.

- **Custom Plan:** Designed for larger classes or institutions with specific requirements. Pricing is determined by the number of students and the features required. Dedicated support and training are included.

  [Calculate your plan](https://superlabs.pages.dev/pricing)

## Key Benefits

Automate Grading, Save Faculty Time
Reduce repetitive tasks with built-in auto-evaluation and AI-powered assignment creation.

Boost Engagement and Learning
Provide an interactive, self-paced environment designed to deepen student understanding.

Monitor Student Performance
Get insights with detailed charts per student and per assignment.

Standardize Your Lab Curriculum
Ensure consistent quality across batches, semesters, and departments.

Effortless Setup
Get started quickly with a simple one-time CLI installation.
