{"name": "easelabs", "version": "1.0.0", "description": "", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=\"--no-deprecation --max-old-space-size=4096\" NODE_ENV=production next build", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "agent:dev": "<PERSON>ra dev", "agent:build": "mastra build", "migrate:fresh": "npm run payload migrate:fresh", "migrate": "npm run payload migrate", "generate": "npm run payload generate:db-schema", "push": "npm run payload push", "typecheck": "tsc --noEmit", "test": "echo 'No tests specified' && exit 0", "test:unit": "vitest", "cloc": "cloc . --exclude-dir=node_modules,dist,.turbo,.next,.vscode --exclude-ext=json,yaml,md,Dockerfile,yml,pl", "devsafe": "rm -rf .next && cross-env NODE_OPTIONS=--no-deprecation next dev", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "format": "prettier --write .", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "server": "node server/index.js", "start": "next start"}, "dependencies": {"@ai-sdk/google": "^1.2.22", "@mastra/core": "^0.10.15", "@mastra/libsql": "^0.11.0", "@mastra/loggers": "^0.10.3", "@monaco-editor/react": "^4.7.0", "@next/bundle-analyzer": "^15.4.2", "@openrouter/ai-sdk-provider": "0.7.0", "@payloadcms/db-postgres": "3.48.0", "@payloadcms/next": "3.48.0", "@payloadcms/plugin-multi-tenant": "^3.48.0", "@payloadcms/richtext-lexical": "3.48.0", "@payloadcms/ui": "3.48.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@sentry/nextjs": "^9.40.0", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-form": "^1.14.1", "@tanstack/react-query": "^5.83.0", "@tanstack/react-table": "^8.21.3", "@uidotdev/usehooks": "^2.4.1", "@uiw/react-markdown-preview": "^5.1.4", "@xterm/addon-fit": "^0.10.0", "@xterm/xterm": "^5.5.0", "ai": "^4.3.19", "archiver": "^7.0.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "disable-devtool": "^0.3.8", "graphql": "^16.11.0", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "monaco-editor": "^0.52.2", "motion": "^12.23.6", "next": "15.4.2", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "payload": "3.48.0", "postcss": "^8.5.6", "posthog-js": "^1.257.0", "qs-esm": "^7.0.2", "react": "19.1.0", "react-dom": "19.1.0", "react-icons": "^5.5.0", "react-resizable-panels": "^3.0.3", "recharts": "^3.1.0", "server-only": "^0.0.1", "sharp": "0.34.3", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@biomejs/biome": "2.1.2", "@eslint/eslintrc": "^3.3.1", "@types/archiver": "^6.0.3", "@types/lodash": "^4.17.20", "@types/node": "^24.0.15", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "cloc": "2.6.0-cloc", "eslint": "^9.31.0", "eslint-config-next": "15.4.2", "husky": "^9.1.7", "mastra": "^0.10.13", "prettier": "^3.6.2", "typescript": "5.8.3"}, "engines": {"node": "^18.20.2 || >=20.9.0", "pnpm": "^9 || ^10"}, "pnpm": {"onlyBuiltDependencies": ["@sentry/cli", "@tailwindcss/oxide", "bufferutil", "core-js", "esbuild", "protobufjs", "sharp", "unrs-resolver", "utf-8-validate"]}, "packageManager": "pnpm@10.13.1"}