import type { Access } from 'payload';
import type { User } from '@/payload-types';
import { isSuperAdmin } from './roles';
import { getUserTenantIDs } from '@/utilities/getUserTenantIDs';

export const isAuthenticated: Access = ({ req }) => Boolean(req.user);

export const canCreateUser: Access<User> = ({ req }) => Boolean(req.user && isSuperAdmin(req.user));

export const canUpdateUser: Access<User> = ({ req, data }) => {
  if (!req.user) return false;
  if (isSuperAdmin(req.user)) return true;

  const tenantIDs = getUserTenantIDs(req.user, 'faculty');
  const isTryingToPromoteToSuper = data?.roles?.includes('super-admin');

  return tenantIDs.length > 0 && !isTryingToPromoteToSuper;
};

export const canDeleteUser: Access<User> = ({ req }) => {
  if (!req.user) return false;
  if (isSuperAdmin(req.user)) return true;

  const tenantIDs = getUserTenantIDs(req.user, 'faculty');
  return tenantIDs.length > 0;
};
