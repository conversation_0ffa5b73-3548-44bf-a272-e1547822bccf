import { getUserTenantIDs } from '@/utilities/getUserTenantIDs';
import type { Access, FieldAccess } from 'payload';
import { isSuperAdmin } from './roles';

/**
 * Grants access to super admins and tenant admins.
 * Super admins have full access, while tenant admins can access data related to their tenants.
 */
export const canAccessTenantData: Access = ({ req }) => {
  if (!req.user) return false;
  if (isSuperAdmin(req.user)) return true;
  const tenantIDs = getUserTenantIDs(req.user, 'faculty');
  return tenantIDs.length > 0;
  // return tenantIDs.length > 0 ? { tenant: {
  //   in: tenantIDs,
  // } } : false;
};

/**
 * Grants access to fields based on user roles.
 * Super admins have full access to all fields, while faculty members only have access to their respective tenants' data.
 */
export const canEditField: FieldAccess = ({ req }) => {
  if (!req.user) return false;
  return (
    isAdminOnlyFieldAccess({ req }) ||
    getUserTenantIDs(req.user, 'faculty').length > 0 ||
    getUserTenantIDs(req.user, 'student').length > 0
  );
};

/**
 * Grants access to admin-only fields for super admins.
 * Only super admins can access these fields.
 */
export const isAdminOnlyFieldAccess: FieldAccess = ({ req }) => {
  if (!req.user) return false;
  return isSuperAdmin(req.user);
};
