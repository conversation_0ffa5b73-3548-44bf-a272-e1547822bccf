import type { User } from '@/payload-types';
import { DecodedToken } from '@/types/decoded-token';

export const isSuperAdmin = (user: User | null | undefined): boolean =>
  Boolean(user?.roles?.includes('super-admin'));

export const isFaculty = (user: User | DecodedToken | null | undefined): boolean => {
  if (Array.isArray(user?.tenants) && (user.tenants.length ?? 0) > 0) {
    return user.tenants.some(({ roles }) => roles.includes('faculty'));
  }
  return false;
};

export const isStudent = (user: User | DecodedToken | null | undefined): boolean => {
  if (Array.isArray(user?.tenants) && (user.tenants.length ?? 0) > 0) {
    return user.tenants.some(({ roles }) => roles.includes('student'));
  }
  return false;
};
