import { useState, useEffect } from 'react';
import { stringify } from 'qs-esm';
import { Assignment } from '@/payload-types';

interface UseAssignmentResult {
  assignment: Assignment | null;
  isLoading: boolean;
  error: string | null;
}

export function useAssignment(aid: string | string[]): UseAssignmentResult {
  const [assignment, setAssignment] = useState<Assignment | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAssignment = async () => {
      if (!aid || Array.isArray(aid)) {
        setError('Invalid assignment ID');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const queryParams = stringify(
          {
            depth: 2,
            select: {
              tenant: false,
            },
          },
          { addQueryPrefix: true, encode: false },
        );

        const response = await fetch(`/api/assignments/${aid}${queryParams}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Assignment not found');
          }
          throw new Error(`Failed to fetch assignment: ${response.statusText}`);
        }

        const data = await response.json();
        setAssignment(data);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
        setError(errorMessage);
        setAssignment(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAssignment();
  }, [aid]);

  return {
    assignment,
    isLoading,
    error,
  };
}
