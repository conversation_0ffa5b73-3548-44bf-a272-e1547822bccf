import { useCallback, useEffect, useRef, useState } from 'react';

type JobStatus = 'idle' | 'processing' | 'completed' | 'error';

interface JobPollingOptions {
  maxRetries?: number;
  pollingInterval?: number;
  submissionPollingInterval?: number;
  onComplete?: () => void;
  onError?: (error: string) => void;
}

interface JobPollingResult {
  status: JobStatus;
  startJob: (endpoint: string) => Promise<void>;
  reset: () => void;
}

export const useJobPolling = (options: JobPollingOptions = {}): JobPollingResult => {
  const {
    maxRetries = 5,
    pollingInterval = 2000,
    submissionPollingInterval = 3000,
    onComplete,
    onError,
  } = options;

  const [status, setStatus] = useState<JobStatus>('idle');
  const [, setJobId] = useState<string | null>(null);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const retryCountRef = useRef<number>(0);

  const stopPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
  }, []);

  const handleJobCompletion = useCallback(() => {
    setStatus('completed');
    stopPolling();
    onComplete?.();
  }, [stopPolling, onComplete]);

  const handleJobError = useCallback(
    (error: string) => {
      setStatus('error');
      stopPolling();
      onError?.(error);
    },
    [stopPolling, onError],
  );

  const pollJobStatus = useCallback(
    async (jobId: string) => {
      try {
        const response = await fetch(`/api/payload-jobs/${jobId}`);

        // If we get 404, it likely means the job was completed and cleaned up
        if (response.status === 404) {
          console.log('Job not found (404) - assuming completed successfully');
          handleJobCompletion();
          return;
        }

        if (!response.ok) {
          throw new Error(`Failed to fetch job status: ${response.status}`);
        }

        const job = await response.json();
        console.log('Job status:', job);

        // Reset retry counter on successful response
        retryCountRef.current = 0;

        if (job.completedAt) {
          // Check the log for the actual status
          const hasFailedLog = job.log?.some((logEntry: any) => logEntry.state === 'failed');
          if (job.hasError || hasFailedLog) {
            handleJobError('Job completed with errors');
          } else {
            handleJobCompletion();
          }
        } else if (job.hasError) {
          handleJobError('Job failed during execution');
        }
        // If still processing, continue polling
      } catch (error) {
        console.error('Error polling job status:', error);
        retryCountRef.current += 1;

        // If we've failed too many times, give up and set error
        if (retryCountRef.current >= maxRetries) {
          console.error('Too many polling failures, giving up');
          handleJobError('Polling failed after multiple retries');
        }
      }
    },
    [maxRetries, handleJobCompletion, handleJobError],
  );

  const pollSubmissionStatus = useCallback(
    async (submissionId: string) => {
      try {
        const response = await fetch(`/api/submissions/${submissionId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch submission');
        }

        const submission = await response.json();

        // Check if the submission has been updated with evaluation results
        if (submission.testsResult || submission.summary || submission.score !== undefined) {
          handleJobCompletion();
        }
      } catch (error) {
        console.error('Error polling submission status:', error);
        handleJobError('Failed to check submission status');
      }
    },
    [handleJobCompletion, handleJobError],
  );

  const startJobPolling = useCallback(
    (jobId: string) => {
      retryCountRef.current = 0;
      pollingIntervalRef.current = setInterval(() => {
        pollJobStatus(jobId);
      }, pollingInterval);
    },
    [pollJobStatus, pollingInterval],
  );

  const startSubmissionPolling = useCallback(
    (submissionId: string) => {
      pollingIntervalRef.current = setInterval(() => {
        pollSubmissionStatus(submissionId);
      }, submissionPollingInterval);
    },
    [pollSubmissionStatus, submissionPollingInterval],
  );

  const startJob = useCallback(
    async (endpoint: string) => {
      try {
        setStatus('processing');

        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to start evaluation job');
        }

        const data = await response.json();

        // If the response includes a job ID, start polling
        if (data.jobId) {
          setJobId(data.jobId);
          startJobPolling(data.jobId);
        } else {
          // If no job ID is returned, we'll poll the submission directly
          // Extract submission ID from endpoint
          const submissionId = endpoint.split('/')[3]; // Assumes format /api/submissions/{id}/evaluate
          startSubmissionPolling(submissionId);
        }
      } catch (error) {
        console.error('Error starting evaluation job:', error);
        handleJobError(error instanceof Error ? error.message : 'Unknown error');
      }
    },
    [startJobPolling, startSubmissionPolling, handleJobError],
  );

  const reset = useCallback(() => {
    setStatus('idle');
    setJobId(null);
    stopPolling();
  }, [stopPolling]);

  // Cleanup polling on unmount
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, [stopPolling]);

  return {
    status,
    startJob,
    reset,
  };
};
