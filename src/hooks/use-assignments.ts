import { Assignment } from '@/payload-types';
import { useEffect, useState } from 'react';

export type IAssignment = Pick<
  Assignment,
  'id' | 'title' | 'description' | 'subject' | 'points' | 'difficulty' | 'dueDate'
> & { isCompleted: boolean | undefined };

export const useAssignments = (subject?: string) => {
  const [assignments, setAssignments] = useState<IAssignment[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      let url =
        '/api/assignments?select[id]=true&select[title]=true&select[description]=true&select[subject]=true&select[points]=true&select[difficulty]=true&select[dueDate]=true&select[isCompleted]=true&depth=0&limit=100';
      if (subject !== 'all') {
        url += `&where[subject][equals]=${subject}&limit=100`;
      }
      try {
        const response = await fetch(url);
        const data = await response.json();
        setAssignments(data.docs);
      } catch (error) {
        console.error('Error fetching:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [subject]);

  return {
    assignments: assignments as IAssignment[],
    isLoading,
  };
};
