'use client';

import { useTheme as useNextTheme } from 'next-themes';
import { useTheme as usePayloadTheme } from '@payloadcms/ui';
import { useEffect, useCallback } from 'react';

export const useTheme = () => {
  const { resolvedTheme, setTheme: setNextTheme, theme: nextTheme } = useNextTheme();
  const { setTheme: setPayloadTheme } = usePayloadTheme();

  const setTheme = useCallback(
    (newTheme: 'light' | 'dark' | 'system') => {
      console.log('THEME CHANGED', newTheme);
      setNextTheme(newTheme);
      const payloadTheme =
        newTheme === 'system' ? (resolvedTheme === 'dark' ? 'dark' : 'light') : newTheme;
      setPayloadTheme(payloadTheme);
    },
    [setNextTheme, setPayloadTheme, resolvedTheme],
  );

  useEffect(() => {
    if (resolvedTheme) {
      setPayloadTheme(resolvedTheme === 'dark' ? 'dark' : 'light');
    }
  }, [resolvedTheme, setPayloadTheme]);

  return {
    theme: nextTheme,
    resolvedTheme: resolvedTheme === 'dark' ? 'dark' : 'light',
    setTheme,
  };
};
