import { useQuery } from '@tanstack/react-query';
import { Subject } from '@/payload-types';

export type ISubject = Pick<Subject, 'id' | 'name' | 'description'>;

const fetchSubjects = async (): Promise<ISubject[]> => {
  const response = await fetch(
    '/api/subjects?select[id]=true&select[name]=true&select[description]=true&depth=0',
  );
  if (!response.ok) {
    throw new Error('Failed to fetch subjects');
  }
  const data = await response.json();
  return data.docs as ISubject[];
};

export const useSubjects = () => {
  const { data, isLoading, error } = useQuery<ISubject[]>({
    queryKey: ['subjects'],
    queryFn: fetchSubjects,
  });

  return {
    subjects: data ?? [],
    isLoading,
    error,
  };
};
