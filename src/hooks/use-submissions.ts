import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { stringify } from 'qs-esm';
import type { Submission } from '@/payload-types';

export type ISubmission = Pick<
  Submission,
  'testsResult' | 'id' | 'status' | 'isLocked' | 'updatedAt'
>;

export interface SubmissionResponse {
  docs: ISubmission[];
  hasNextPage: boolean;
  hasPrevPage: boolean;
  limit: number;
  nextPage: number | null;
  page: number;
  pagingCounter: number;
  prevPage: number | null;
  totalDocs: number;
  totalPages: number;
}

const MAX_RETRIES = 3;

export const useSubmissions = (assignmentId: string) => {
  const queryClient = useQueryClient();

  const {
    data: submissions,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['submissions', assignmentId],
    queryFn: async () => {
      const query = stringify(
        {
          depth: 0,
          select: {
            testsResult: true,
            status: true,
            isLocked: true,
            updatedAt: true,
          },
          where: {
            assignment: { equals: assignmentId },
          },
          sort: '-createdAt',
          limit: 5,
        },
        { addQueryPrefix: true },
      );

      const response = await fetch(
        `/api/submissions/${query.replaceAll('%5B', '[').replaceAll('%5D', ']')}`,
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response.json() as Promise<SubmissionResponse>;
    },
    staleTime: 30 * 1000, // 30 seconds - submissions change frequently
    gcTime: 2 * 60 * 1000, // 2 minutes
    retry: (failureCount) => failureCount < MAX_RETRIES,
  });

  const retry = useCallback(() => {
    refetch();
  }, [refetch]);

  // Function to invalidate submissions when a new submission is made
  const invalidateSubmissions = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: ['submissions', assignmentId],
    });
  }, [queryClient, assignmentId]);

  return {
    submissions: submissions || null,
    isLoading,
    error: error?.message || null,
    retry,
    invalidateSubmissions,
    hasSubmissions: submissions && submissions.totalDocs > 0,
    latestSubmission: submissions?.docs[0] || null,
  };
};

// Hook to invalidate submissions from any component
export const useInvalidateSubmissions = () => {
  const queryClient = useQueryClient();

  return useCallback(
    (assignmentId: string) => {
      queryClient.invalidateQueries({
        queryKey: ['submissions', assignmentId],
      });
    },
    [queryClient],
  );
};
