'use client';

import { getUserRole } from '@/lib/user';
import { DecodedToken } from '@/types/decoded-token';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export const useUser = () => {
  const [user, setUser] = useState<DecodedToken | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const router = useRouter();

  useEffect(() => {
    const getUser = async () => {
      try {
        const response = await fetch('/api/users/me', {
          credentials: 'include',
        });
        const data = await response.json();
        if (!data.user) {
          router.replace('/login');
          return;
        }

        setUser(data.user);
      } catch (error) {
        console.error('Error fetching user:', error);
        router.replace('/login');
      } finally {
        setIsLoading(false);
      }
    };

    getUser();
  }, [router]);

  const role = user ? getUserRole(user) : null;

  return {
    user,
    isLoading,
    role,
    isAdmin: role === 'admin',
    isFaculty: role === 'faculty' || role === 'admin',
    isStudent: role === 'student',
    isAuthenticated: !!user,
  };
};
