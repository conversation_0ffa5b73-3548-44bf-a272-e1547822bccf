// import type { Config } from 'payload';
// import type { Assignment, Subject } from './payload-types';
// import { getAssignments } from './mockdata/assignments';

// export const seed: NonNullable<Config['onInit']> = async (payload): Promise<void> => {
//   // Create tenant
//   let tenant;

//   try {
//     tenant = await payload.create({
//       collection: 'tenants',
//       data: {
//         name: 'ACME Classes',
//         slug: 'acme-classes',
//         domain: 'acme-classes.localhost',
//       },
//     });
//   } catch {
//     console.log('Tenant already exists');
//   }

//   // Create super admin
//   if (tenant) {
//     await payload.create({
//       collection: 'users',
//       data: {
//         username: 'soham',
//         password: 'Hello@123',
//         roles: ['super-admin'],
//         tenants: [
//           {
//             roles: ['faculty'],
//             tenant: tenant.id,
//           },
//         ],
//       },
//     });
//   }

//   if (tenant) {
//     // Create faculty
//     await payload.create({
//       collection: 'users',
//       data: {
//         username: 'faculty',
//         password: 'facultyfaculty',
//         tenants: [
//           {
//             roles: ['faculty'],
//             tenant: tenant.id,
//           },
//         ],
//       },
//     });
//   }

//   if (!tenant) return;

//   // create student
//   await payload.create({
//     collection: 'users',
//     data: {
//       username: 'student',
//       password: 'studentstudent',
//       tenants: [
//         {
//           roles: ['student'],
//           tenant: tenant.id,
//         },
//       ],
//     },
//   });

//   // Define assignments with starter code
//   const assignments: Assignment[] = getAssignments({ tenant });

//   //   const assignments: Assignment[] = [
//   //     {
//   //       id: 1,
//   //       createdAt: '2023-05-01T00:00:00.000Z',
//   //       updatedAt: '2023-05-01T00:00:00.000Z',
//   //       title: 'Build Your First Webpage',
//   //       description: 'Create a basic webpage using HTML and CSS with proper semantic structure.',
//   //       instructions:
//   //         '- Use at least 3 different semantic HTML tags (header, nav, section, article, footer)\n- Add a navigation menu with at least 3 links\n- Include a hero section with a heading and paragraph\n- Style the page using CSS variables for colors and fonts\n- Make the layout responsive with media queries',
//   //       dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
//   //       isActive: true,
//   //       tenant: tenant.id,
//   //       starterCode: {
//   //         html: `<header>
//   //   <h1>My First Webpage</h1>
//   //   <nav>
//   //     <!-- Add your navigation here -->
//   //   </nav>
//   // </header>

//   // <main>
//   //   <!-- Add your content here -->
//   // </main>

//   // <footer>
//   //   <!-- Add your footer here -->
//   // </footer>`,
//   //         css: `:root {
//   //   --primary-color: #3498db;
//   //   --secondary-color: #2ecc71;
//   //   --text-color: #333;
//   //   --bg-color: #f9f9f9;
//   //   --font-main: 'Arial', sans-serif;
//   // }

//   // * {
//   //   margin: 0;
//   //   padding: 0;
//   //   box-sizing: border-box;
//   // }

//   // /* Add your styles here */`,
//   //         js: '// Optional JavaScript code can be added here',
//   //       },
//   //     },
//   //     {
//   //       id: 2,
//   //       createdAt: '2023-05-01T00:00:00.000Z',
//   //       updatedAt: '2023-05-01T00:00:00.000Z',
//   //       title: 'Responsive Navbar with Flex Magic',
//   //       description:
//   //         'Build a responsive navbar that transforms from horizontal to vertical layout based on screen size.',
//   //       instructions:
//   //         '- Create a horizontal navbar for desktop screens\n- Transform to a hamburger menu on mobile devices\n- Include smooth transitions for menu toggle\n- Add hover and active states for navigation items\n- Ensure accessibility with proper ARIA attributes',
//   //       dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
//   //       isActive: true,
//   //       tenant: tenant.id,
//   //       starterCode: {
//   //         html: `<header class="navbar-container">
//   //   <div class="logo">Brand Logo</div>
//   //   <button class="menu-toggle" aria-label="Toggle navigation menu">
//   //     <span class="hamburger"></span>
//   //   </button>
//   //   <nav class="main-nav">
//   //     <ul class="nav-links">
//   //       <li><a href="#home">Home</a></li>
//   //       <li><a href="#about">About</a></li>
//   //       <li><a href="#services">Services</a></li>
//   //       <li><a href="#contact">Contact</a></li>
//   //     </ul>
//   //   </nav>
//   // </header>`,
//   //         css: `.navbar-container {
//   //   display: flex;
//   //   /* Add your styles here */
//   // }

//   // .main-nav {
//   //   /* Add your styles here */
//   // }

//   // /* Mobile styles */
//   // @media (max-width: 768px) {
//   //   /* Add responsive styles here */
//   // }`,
//   //         js: `document.querySelector('.menu-toggle').addEventListener('click', function() {
//   //   // Add your toggle logic here
//   // });`,
//   //       },
//   //     },
//   //     {
//   //       id: 3,
//   //       createdAt: '2023-05-01T00:00:00.000Z',
//   //       updatedAt: '2023-05-01T00:00:00.000Z',
//   //       title: 'Blog Layout with CSS Grid',
//   //       description: 'Design a responsive blog layout with main content and sidebar using CSS Grid.',
//   //       instructions:
//   //         '- Create a grid-based layout with main content and sidebar\n- Design at least 3 different card styles for blog posts\n- Include a widget area in the sidebar with search and categories\n- Make the layout responsive (sidebar below content on mobile)\n- Add appropriate spacing and typography',
//   //       dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
//   //       isActive: true,
//   //       tenant: tenant.id,
//   //       starterCode: {
//   //         html: `<div class="blog-container">
//   //   <main class="blog-main">
//   //     <article class="blog-post featured-post">
//   //       <h2>Featured Post Title</h2>
//   //       <p class="post-meta">Posted on April 10, 2025</p>
//   //       <div class="post-content">
//   //         <!-- Post content here -->
//   //       </div>
//   //     </article>

//   //     <article class="blog-post">
//   //       <!-- Regular post structure -->
//   //     </article>

//   //     <!-- Add more posts here -->
//   //   </main>

//   //   <aside class="blog-sidebar">
//   //     <div class="widget search-widget">
//   //       <!-- Search form here -->
//   //     </div>

//   //     <div class="widget categories-widget">
//   //       <!-- Categories list here -->
//   //     </div>
//   //   </aside>
//   // </div>`,
//   //         css: `.blog-container {
//   //   display: grid;
//   //   grid-template-columns: 1fr 300px;
//   //   grid-gap: 2rem;
//   //   max-width: 1200px;
//   //   margin: 0 auto;
//   // }

//   // /* Add your styles here */

//   // @media (max-width: 768px) {
//   //   .blog-container {
//   //     grid-template-columns: 1fr;
//   //   }
//   // }`,
//   //         js: '// Optional JavaScript for dynamic features',
//   //       },
//   //     },
//   //     {
//   //       id: 4,
//   //       createdAt: '2023-05-01T00:00:00.000Z',
//   //       updatedAt: '2023-05-01T00:00:00.000Z',
//   //       title: 'Stylish Signup Form with Validation',
//   //       description:
//   //         'Create a modern registration form with built-in validation and interactive feedback.',
//   //       instructions:
//   //         '- Design a clean and accessible signup form\n- Include fields for name, email, password, and confirmation\n- Implement HTML5 validation attributes\n- Add custom JavaScript validation for password strength\n- Provide visual feedback for valid/invalid inputs\n- Include password visibility toggle',
//   //       dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
//   //       isActive: true,
//   //       tenant: tenant.id,
//   //       starterCode: {
//   //         html: `<div class="form-container">
//   //   <h2>Create Account</h2>
//   //   <form id="signup-form" novalidate>
//   //     <div class="form-group">
//   //       <label for="fullname">Full Name</label>
//   //       <input type="text" id="fullname" name="fullname" required>
//   //       <div class="error-message"></div>
//   //     </div>

//   //     <div class="form-group">
//   //       <label for="email">Email Address</label>
//   //       <input type="email" id="email" name="email" required>
//   //       <div class="error-message"></div>
//   //     </div>

//   //     <div class="form-group">
//   //       <label for="password">Password</label>
//   //       <div class="password-field">
//   //         <input type="password" id="password" name="password" required>
//   //         <button type="button" class="toggle-password">Show</button>
//   //       </div>
//   //       <div class="password-strength">
//   //         <div class="strength-bar"></div>
//   //       </div>
//   //       <div class="error-message"></div>
//   //     </div>

//   //     <div class="form-group">
//   //       <label for="confirm-password">Confirm Password</label>
//   //       <input type="password" id="confirm-password" name="confirm-password" required>
//   //       <div class="error-message"></div>
//   //     </div>

//   //     <button type="submit" class="submit-btn">Sign Up</button>
//   //   </form>
//   // </div>`,
//   //         css: `.form-container {
//   //   max-width: 450px;
//   //   margin: 0 auto;
//   //   padding: 2rem;
//   //   border-radius: 8px;
//   //   box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
//   // }

//   // .form-group {
//   //   margin-bottom: 1.5rem;
//   // }

//   // /* Add your styles here */

//   // .password-strength {
//   //   height: 5px;
//   //   background: #eee;
//   //   margin-top: 5px;
//   // }

//   // .strength-bar {
//   //   height: 100%;
//   //   width: 0;
//   //   transition: width 0.3s, background 0.3s;
//   // }`,
//   //         js: `document.addEventListener('DOMContentLoaded', function() {
//   //   const form = document.getElementById('signup-form');

//   //   // Add form validation logic here

//   //   // Password strength meter
//   //   const passwordInput = document.getElementById('password');
//   //   const strengthBar = document.querySelector('.strength-bar');

//   //   passwordInput.addEventListener('input', function() {
//   //     // Add password strength logic here
//   //   });

//   //   // Password visibility toggle
//   //   const toggleBtn = document.querySelector('.toggle-password');
//   //   toggleBtn.addEventListener('click', function() {
//   //     // Add password visibility toggle logic here
//   //   });

//   //   // Form submission
//   //   form.addEventListener('submit', function(e) {
//   //     e.preventDefault();
//   //     // Validate form before submission
//   //   });
//   // });`,
//   //       },
//   //     },
//   //     {
//   //       id: 5,
//   //       createdAt: '2023-05-01T00:00:00.000Z',
//   //       updatedAt: '2023-05-01T00:00:00.000Z',
//   //       title: 'Vanilla JS To-Do Tracker',
//   //       description:
//   //         'Build a feature-rich to-do app with persistent storage using vanilla JavaScript.',
//   //       instructions:
//   //         '- Create a clean and intuitive UI\n- Implement add, edit, complete, and delete functionality\n- Store tasks in localStorage for persistence\n- Include task filtering (All, Active, Completed)\n- Add due dates and priority levels for tasks\n- Implement drag and drop for reordering tasks',
//   //       dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
//   //       isActive: true,
//   //       tenant: tenant.id,
//   //       starterCode: {
//   //         html: `<div class="todo-app">
//   //   <header class="app-header">
//   //     <h1>To-Do List</h1>
//   //     <div class="filters">
//   //       <button class="filter-btn active" data-filter="all">All</button>
//   //       <button class="filter-btn" data-filter="active">Active</button>
//   //       <button class="filter-btn" data-filter="completed">Completed</button>
//   //     </div>
//   //   </header>

//   //   <form id="add-task-form">
//   //     <input type="text" id="new-task" placeholder="Add a new task..." required>
//   //     <input type="date" id="task-date">
//   //     <select id="task-priority">
//   //       <option value="low">Low</option>
//   //       <option value="medium" selected>Medium</option>
//   //       <option value="high">High</option>
//   //     </select>
//   //     <button type="submit">Add</button>
//   //   </form>

//   //   <ul id="task-list" class="task-list">
//   //     <!-- Tasks will be added here -->
//   //   </ul>

//   //   <div class="app-footer">
//   //     <span id="tasks-count">0 tasks left</span>
//   //     <button id="clear-completed">Clear Completed</button>
//   //   </div>
//   // </div>`,
//   //         css: `.todo-app {
//   //   max-width: 500px;
//   //   margin: 0 auto;
//   //   padding: 1rem;
//   //   box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
//   //   border-radius: 8px;
//   // }

//   // /* Add your styles here */

//   // .task-item {
//   //   display: flex;
//   //   align-items: center;
//   //   padding: 1rem;
//   //   border-bottom: 1px solid #eee;
//   // }

//   // .task-item.completed .task-text {
//   //   text-decoration: line-through;
//   //   opacity: 0.7;
//   // }

//   // .priority-high {
//   //   border-left: 3px solid #e74c3c;
//   // }

//   // .priority-medium {
//   //   border-left: 3px solid #f39c12;
//   // }

//   // .priority-low {
//   //   border-left: 3px solid #3498db;
//   // }`,
//   //         js: `document.addEventListener('DOMContentLoaded', function() {
//   //   // Elements
//   //   const taskForm = document.getElementById('add-task-form');
//   //   const taskInput = document.getElementById('new-task');
//   //   const taskList = document.getElementById('task-list');
//   //   const filterBtns = document.querySelectorAll('.filter-btn');
//   //   const tasksCount = document.getElementById('tasks-count');
//   //   const clearCompletedBtn = document.getElementById('clear-completed');

//   //   // Application state
//   //   let tasks = JSON.parse(localStorage.getItem('tasks')) || [];
//   //   let currentFilter = 'all';

//   //   // Event listeners
//   //   taskForm.addEventListener('submit', addTask);
//   //   taskList.addEventListener('click', handleTaskAction);
//   //   clearCompletedBtn.addEventListener('click', clearCompleted);
//   //   filterBtns.forEach(btn => {
//   //     btn.addEventListener('click', setFilter);
//   //   });

//   //   // Initialize
//   //   renderTasks();

//   //   // Functions
//   //   function addTask(e) {
//   //     e.preventDefault();
//   //     // Implement task addition logic
//   //   }

//   //   function renderTasks() {
//   //     // Implement task rendering logic
//   //   }

//   //   function handleTaskAction(e) {
//   //     // Implement task actions (toggle, edit, delete)
//   //   }

//   //   function setFilter(e) {
//   //     // Implement filtering logic
//   //   }

//   //   function clearCompleted() {
//   //     // Implement clear completed logic
//   //   }

//   //   function saveTasks() {
//   //     localStorage.setItem('tasks', JSON.stringify(tasks));
//   //   }
//   // });`,
//   //       },
//   //     },
//   //     {
//   //       id: 6,
//   //       createdAt: '2023-05-01T00:00:00.000Z',
//   //       updatedAt: '2023-05-01T00:00:00.000Z',
//   //       title: 'Photo Gallery for All Screens',
//   //       description:
//   //         'Create a responsive photo gallery with advanced grid layout and interactive features.',
//   //       instructions:
//   //         '- Build a masonry-style grid layout for desktop\n- Use a fluid single column layout on mobile\n- Add lightbox functionality for image preview\n- Include filtering options by category\n- Implement lazy loading for optimal performance\n- Add subtle animations and hover effects',
//   //       dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
//   //       isActive: true,
//   //       tenant: tenant.id,
//   //       starterCode: {
//   //         html: `<div class="gallery-container">
//   //   <div class="gallery-filters">
//   //     <button class="filter-btn active" data-filter="all">All</button>
//   //     <button class="filter-btn" data-filter="nature">Nature</button>
//   //     <button class="filter-btn" data-filter="architecture">Architecture</button>
//   //     <button class="filter-btn" data-filter="travel">Travel</button>
//   //   </div>

//   //   <div class="gallery-grid">
//   //     <div class="gallery-item" data-category="nature">
//   //       <img class="gallery-img" src="https://source.unsplash.com/random/300x200?nature,1" alt="Nature image">
//   //       <div class="item-overlay">
//   //         <h3>Beautiful Nature</h3>
//   //       </div>
//   //     </div>

//   //     <!-- Add more gallery items here -->
//   //   </div>

//   //   <div class="lightbox" id="lightbox">
//   //     <span class="close-btn">&times;</span>
//   //     <img class="lightbox-img" src="" alt="Lightbox image">
//   //     <div class="lightbox-caption"></div>
//   //   </div>
//   // </div>`,
//   //         css: `.gallery-container {
//   //   max-width: 1200px;
//   //   margin: 0 auto;
//   //   padding: 1rem;
//   // }

//   // .gallery-grid {
//   //   display: grid;
//   //   grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
//   //   grid-gap: 1rem;
//   //   grid-auto-flow: dense;
//   // }

//   // .gallery-item {
//   //   position: relative;
//   //   overflow: hidden;
//   //   border-radius: 4px;
//   //   cursor: pointer;
//   //   transition: transform 0.3s;
//   // }

//   // /* Add your styles here */

//   // /* Lightbox styles */
//   // .lightbox {
//   //   display: none;
//   //   position: fixed;
//   //   top: 0;
//   //   left: 0;
//   //   width: 100%;
//   //   height: 100%;
//   //   background: rgba(0, 0, 0, 0.9);
//   //   z-index: 1000;
//   //   justify-content: center;
//   //   align-items: center;
//   // }

//   // /* Responsive styles */
//   // @media (max-width: 768px) {
//   //   .gallery-grid {
//   //     grid-template-columns: 1fr;
//   //   }
//   // }`,
//   //         js: `document.addEventListener('DOMContentLoaded', function() {
//   //   // Elements
//   //   const galleryGrid = document.querySelector('.gallery-grid');
//   //   const filterBtns = document.querySelectorAll('.filter-btn');
//   //   const lightbox = document.getElementById('lightbox');
//   //   const lightboxImg = document.querySelector('.lightbox-img');
//   //   const lightboxCaption = document.querySelector('.lightbox-caption');
//   //   const closeBtn = document.querySelector('.close-btn');

//   //   // Gallery data - In a real project, this would come from a database
//   //   const galleryItems = [
//   //     // Add your gallery items here
//   //   ];

//   //   // Initialize gallery
//   //   renderGallery(galleryItems);

//   //   // Event listeners
//   //   filterBtns.forEach(btn => {
//   //     btn.addEventListener('click', filterGallery);
//   //   });

//   //   galleryGrid.addEventListener('click', openLightbox);
//   //   closeBtn.addEventListener('click', closeLightbox);

//   //   // Implement lazy loading with Intersection Observer
//   //   const lazyLoadImages = () => {
//   //     // Implement lazy loading logic
//   //   };

//   //   // Functions
//   //   function renderGallery(items) {
//   //     // Implement gallery rendering logic
//   //   }

//   //   function filterGallery(e) {
//   //     // Implement filtering logic
//   //   }

//   //   function openLightbox(e) {
//   //     // Implement lightbox open logic
//   //   }

//   //   function closeLightbox() {
//   //     lightbox.style.display = 'none';
//   //   }
//   // });`,
//   //       },
//   //     },
//   //     {
//   //       id: 7,
//   //       createdAt: '2023-05-01T00:00:00.000Z',
//   //       updatedAt: '2023-05-01T00:00:00.000Z',
//   //       title: 'FAQ Accordion with Smooth Toggle',
//   //       description: 'Build an accessible and interactive FAQ accordion with smooth animations.',
//   //       instructions:
//   //         '- Create at least 5 FAQ items with questions and answers\n- Implement expand/collapse functionality\n- Ensure only one item is expanded at a time\n- Add smooth slide animations\n- Make fully accessible with proper ARIA attributes\n- Include visual indicators for expanded state',
//   //       dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
//   //       isActive: true,
//   //       tenant: tenant.id,
//   //       starterCode: {
//   //         html: `<div class="faq-container">
//   //   <h2>Frequently Asked Questions</h2>

//   //   <div class="faq-item">
//   //     <button class="faq-question" aria-expanded="false" aria-controls="answer-1">
//   //       What is this FAQ accordion?
//   //       <span class="icon"></span>
//   //     </button>
//   //     <div class="faq-answer" id="answer-1" aria-hidden="true">
//   //       <p>This is an interactive FAQ component that allows users to expand and collapse answers to common questions.</p>
//   //     </div>
//   //   </div>

//   //   <!-- Add more FAQ items here -->
//   // </div>`,
//   //         css: `.faq-container {
//   //   max-width: 800px;
//   //   margin: 0 auto;
//   //   padding: 2rem;
//   // }

//   // .faq-item {
//   //   border-bottom: 1px solid #eee;
//   //   margin-bottom: 1rem;
//   // }

//   // .faq-question {
//   //   display: flex;
//   //   justify-content: space-between;
//   //   align-items: center;
//   //   width: 100%;
//   //   padding: 1rem 0;
//   //   background: none;
//   //   border: none;
//   //   text-align: left;
//   //   font-size: 1.1rem;
//   //   font-weight: 500;
//   //   cursor: pointer;
//   // }

//   // .faq-answer {
//   //   max-height: 0;
//   //   overflow: hidden;
//   //   transition: max-height 0.3s ease, padding 0.3s ease;
//   // }

//   // /* Add your styles here */`,
//   //         js: `document.addEventListener('DOMContentLoaded', function() {
//   //   const faqQuestions = document.querySelectorAll('.faq-question');

//   //   faqQuestions.forEach(question => {
//   //     question.addEventListener('click', toggleFAQ);
//   //   });

//   //   function toggleFAQ() {
//   //     const isExpanded = this.getAttribute('aria-expanded') === 'true';

//   //     // Close all FAQs
//   //     faqQuestions.forEach(question => {
//   //       const answerId = question.getAttribute('aria-controls');
//   //       const answer = document.getElementById(answerId);

//   //       question.setAttribute('aria-expanded', 'false');
//   //       answer.setAttribute('aria-hidden', 'true');
//   //       answer.style.maxHeight = '0';
//   //     });

//   //     // Open current FAQ if it was closed
//   //     if (!isExpanded) {
//   //       const answerId = this.getAttribute('aria-controls');
//   //       const answer = document.getElementById(answerId);

//   //       this.setAttribute('aria-expanded', 'true');
//   //       answer.setAttribute('aria-hidden', 'false');
//   //       answer.style.maxHeight = answer.scrollHeight + 'px';
//   //     }
//   //   }
//   // });`,
//   //       },
//   //     },
//   //     {
//   //       id: 8,
//   //       createdAt: '2023-05-01T00:00:00.000Z',
//   //       updatedAt: '2023-05-01T00:00:00.000Z',
//   //       title: 'Live Clock with JavaScript',
//   //       description: 'Build an interactive digital clock with multiple format options and themes.',
//   //       instructions:
//   //         '- Create a digital clock that updates in real-time\n- Include options for 12/24 hour format\n- Display date information (day, month, year)\n- Add a dark/light theme toggle\n- Implement an analog clock face (bonus challenge)\n- Add animations for time transitions',
//   //       dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
//   //       isActive: true,
//   //       tenant: tenant.id,
//   //       starterCode: {
//   //         html: `<div class="clock-container">
//   //   <div class="clock-controls">
//   //     <button id="format-toggle" class="control-btn">12h/24h</button>
//   //     <button id="theme-toggle" class="control-btn">Dark/Light</button>
//   //   </div>

//   //   <div class="digital-clock">
//   //     <div class="time-display">
//   //       <span id="hours">00</span>
//   //       <span class="colon">:</span>
//   //       <span id="minutes">00</span>
//   //       <span class="colon">:</span>
//   //       <span id="seconds">00</span>
//   //       <span id="period">AM</span>
//   //     </div>

//   //     <div class="date-display">
//   //       <span id="day-name">Monday</span>,
//   //       <span id="month">January</span>
//   //       <span id="day">01</span>,
//   //       <span id="year">2025</span>
//   //     </div>
//   //   </div>

//   //   <div class="analog-clock">
//   //     <div class="clock-face">
//   //       <div class="hand hour-hand"></div>
//   //       <div class="hand minute-hand"></div>
//   //       <div class="hand second-hand"></div>
//   //       <div class="center-dot"></div>
//   //       <!-- Clock face markers -->
//   //     </div>
//   //   </div>
//   // </div>`,
//   //         css: `.clock-container {
//   //   max-width: 500px;
//   //   margin: 0 auto;
//   //   padding: 2rem;
//   //   border-radius: 10px;
//   //   background-color: #f5f5f5;
//   //   box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
//   //   transition: background-color 0.3s;
//   // }

//   // .clock-container.dark-theme {
//   //   background-color: #1e1e1e;
//   //   color: #fff;
//   // }

//   // .digital-clock {
//   //   text-align: center;
//   //   margin: 2rem 0;
//   // }

//   // .time-display {
//   //   font-size: 3rem;
//   //   font-weight: 700;
//   // }

//   // /* Add your styles here */

//   // /* Analog clock styles */
//   // .analog-clock {
//   //   position: relative;
//   //   width: 200px;
//   //   height: 200px;
//   //   margin: 2rem auto;
//   //   border-radius: 50%;
//   //   background-color: #fff;
//   //   box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
//   // }

//   // .hand {
//   //   position: absolute;
//   //   top: 50%;
//   //   left: 50%;
//   //   transform-origin: left center;
//   //   background-color: #333;
//   // }

//   // .clock-container.dark-theme .analog-clock {
//   //   background-color: #2e2e2e;
//   // }

//   // .clock-container.dark-theme .hand {
//   //   background-color: #f5f5f5;
//   // }`,
//   //         js: `document.addEventListener('DOMContentLoaded', function() {
//   //   // Elements
//   //   const hoursElement = document.getElementById('hours');
//   //   const minutesElement = document.getElementById('minutes');
//   //   const secondsElement = document.getElementById('seconds');
//   //   const periodElement = document.getElementById('period');
//   //   const dayNameElement = document.getElementById('day-name');
//   //   const monthElement = document.getElementById('month');
//   //   const dayElement = document.getElementById('day');
//   //   const yearElement = document.getElementById('year');
//   //   const formatToggle = document.getElementById('format-toggle');
//   //   const themeToggle = document.getElementById('theme-toggle');
//   //   const clockContainer = document.querySelector('.clock-container');
//   //   const hourHand = document.querySelector('.hour-hand');
//   //   const minuteHand = document.querySelector('.minute-hand');
//   //   const secondHand = document.querySelector('.second-hand');

//   //   // Settings
//   //   let is24HourFormat = false;
//   //   let isDarkTheme = false;

//   //   // Event listeners
//   //   formatToggle.addEventListener('click', toggleTimeFormat);
//   //   themeToggle.addEventListener('click', toggleTheme);

//   //   // Initialize clock
//   //   updateClock();
//   //   setInterval(updateClock, 1000);

//   //   function updateClock() {
//   //     const now = new Date();

//   //     // Update digital clock
//   //     updateDigitalClock(now);

//   //     // Update analog clock
//   //     updateAnalogClock(now);

//   //     // Update date
//   //     updateDate(now);
//   //   }

//   //   function updateDigitalClock(now) {
//   //     // Implement digital clock logic
//   //   }

//   //   function updateAnalogClock(now) {
//   //     // Implement analog clock logic
//   //   }

//   //   function updateDate(now) {
//   //     // Implement date display logic
//   //   }

//   //   function toggleTimeFormat() {
//   //     is24HourFormat = !is24HourFormat;
//   //     updateClock();
//   //   }

//   //   function toggleTheme() {
//   //     isDarkTheme = !isDarkTheme;
//   //     clockContainer.classList.toggle('dark-theme', isDarkTheme);
//   //   }
//   // });`,
//   //       },
//   //     },
//   //     {
//   //       id: 9,
//   //       createdAt: '2023-05-01T00:00:00.000Z',
//   //       updatedAt: '2023-05-01T00:00:00.000Z',
//   //       title: 'Login Form with Smart Checks',
//   //       description:
//   //         'Create an interactive login form with real-time validation and enhanced security features.',
//   //       instructions:
//   //         '- Design a modern login interface\n- Implement client-side validation for username and password\n- Add password strength indicator\n- Show validation feedback in real-time\n- Include "remember me" functionality\n- Add login attempt limiting (simulate)',
//   //       dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
//   //       isActive: true,
//   //       tenant: tenant.id,
//   //       starterCode: {
//   //         html: `<div class="login-container">
//   //   <div class="login-header">
//   //     <h2>Sign In</h2>
//   //     <p>Enter your credentials to access your account</p>
//   //   </div>

//   //   <form id="login-form">
//   //     <div class="form-group">
//   //       <label for="username">Username or Email</label>
//   //       <input type="text" id="username" name="username" required>
//   //       <div class="error-message"></div>
//   //     </div>

//   //     <div class="form-group">
//   //       <label for="password">Password</label>
//   //       <div class="password-field">
//   //         <input type="password" id="password" name="password" required>
//   //         <button type="button" class="toggle-password" aria-label="Toggle password visibility">
//   //           <span class="eye-icon">👁️</span>
//   //         </button>
//   //       </div>
//   //       <div class="error-message"></div>
//   //     </div>

//   //     <div class="form-options">
//   //       <div class="remember-me">
//   //         <input type="checkbox" id="remember" name="remember">
//   //         <label for="remember">Remember me</label>
//   //       </div>
//   //       <a href="#" class="forgot-password">Forgot Password?</a>
//   //     </div>

//   //     <button type="submit" class="login-btn">Sign In</button>
//   //   </form>

//   //   <div class="login-footer">
//   //     <p>Don't have an account? <a href="#">Sign Up</a></p>
//   //   </div>

//   //   <div id="login-attempts" class="attempts-info">
//   //     <p>You have <span id="attempts-left">3</span> login attempts remaining</p>
//   //   </div>
//   // </div>`,
//   //         css: `.login-container {
//   //   max-width: 400px;
//   //   margin: 0 auto;
//   //   padding: 2rem;
//   //   border-radius: 8px;
//   //   box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
//   //   background-color: #fff;
//   // }

//   // .login-header {
//   //   text-align: center;
//   //   margin-bottom: 2rem;
//   // }

//   // .form-group {
//   //   margin-bottom: 1.5rem;
//   // }

//   // label {
//   //   display: block;
//   //   margin-bottom: 0.5rem;
//   //   font-weight: 500;
//   // }

//   // input[type="text"],
//   // input[type="password"] {
//   //   width: 100%;
//   //   padding: 0.75rem;
//   //   border: 1px solid #ddd;
//   //   border-radius: 4px;
//   //   font-size: 1rem;
//   // }

//   // .password-field {
//   //   position: relative;
//   //   display: flex;
//   // }

//   // .toggle-password {
//   //   position: absolute;
//   //   right: 10px;
//   //   top: 50%;
//   //   transform: translateY(-50%);
//   //   background: none;
//   //   border: none;
//   //   cursor: pointer;
//   // }

//   // .error-message {
//   //   color: #e74c3c;
//   //   font-size: 0.85rem;
//   //   margin-top: 0.5rem;
//   //   min-height: 1.2em;
//   // }

//   // .form-options {
//   //   display: flex;
//   //   justify-content: space-between;
//   //   align-items: center;
//   //   margin-bottom: 1.5rem;
//   // }

//   // .login-btn {
//   //   display: block;
//   //   width: 100%;
//   //   padding: 0.75rem;
//   //   background-color: #3498db;
//   //   color: white;
//   //   border: none;
//   //   border-radius: 4px;
//   //   font-size: 1rem;
//   //   cursor: pointer;
//   //   transition: background-color 0.3s;
//   // }

//   // .login-btn:hover {
//   //   background-color: #2980b9;
//   // }

//   // .login-footer {
//   //   text-align: center;
//   //   margin-top: 1.5rem;
//   // }

//   // .attempts-info {
//   //   margin-top: 1rem;
//   //   text-align: center;
//   //   color: #e74c3c;
//   //   display: none;
//   // }`,
//   //         js: `document.addEventListener('DOMContentLoaded', function() {
//   //   // Elements
//   //   const loginForm = document.getElementById('login-form');
//   //   const usernameInput = document.getElementById('username');
//   //   const passwordInput = document.getElementById('password');
//   //   const togglePasswordBtn = document.querySelector('.toggle-password');
//   //   const attemptsInfo = document.getElementById('login-attempts');
//   //   const attemptsLeft = document.getElementById('attempts-left');

//   //   // Validation settings
//   //   const validUsers = ['<EMAIL>', '<EMAIL>'];
//   //   const validPasswords = {
//   //     '<EMAIL>': 'User123!',
//   //     '<EMAIL>': 'Admin456!'
//   //   };
//   //   let loginAttempts = 3;

//   //   // Event listeners
//   //   loginForm.addEventListener('submit', handleLogin);
//   //   usernameInput.addEventListener('input', validateUsername);
//   //   passwordInput.addEventListener('input', validatePassword);
//   //   togglePasswordBtn.addEventListener('click', togglePasswordVisibility);

//   //   // Functions
//   //   function handleLogin(e) {
//   //     e.preventDefault();

//   //     const username = usernameInput.value.trim();
//   //     const password = passwordInput.value;
//   //     const isRemembered = document.getElementById('remember').checked;

//   //     if (validUsers.includes(username) && validPasswords[username] === password) {
//   //       // Successful login
//   //       loginForm.reset();
//   //       alert('Login successful!');

//   //       if (isRemembered) {
//   //         // Here you would typically set a cookie or localStorage item
//   //         console.log('User will be remembered');
//   //       }
//   //     } else {
//   //       // Failed login
//   //       loginAttempts--;
//   //       attemptsLeft.textContent = loginAttempts.toString();
//   //       attemptsInfo.style.display = 'block';

//   //       if (loginAttempts <= 0) {
//   //         disableForm();
//   //         alert('Too many failed attempts. Please try again later.');
//   //       }
//   //     }
//   //   }

//   //   function validateUsername() {
//   //     const username = usernameInput.value.trim();
//   //     const errorElement = usernameInput.nextElementSibling;

//   //     if (username === '') {
//   //       errorElement.textContent = 'Username is required';
//   //     } else if (!isValidEmail(username) && !isValidUsername(username)) {
//   //       errorElement.textContent = 'Enter a valid username or email';
//   //     } else {
//   //       errorElement.textContent = '';
//   //     }
//   //   }

//   //   function validatePassword() {
//   //     const password = passwordInput.value;
//   //     const errorElement = passwordInput.parentElement.nextElementSibling;

//   //     if (password === '') {
//   //       errorElement.textContent = 'Password is required';
//   //     } else if (password.length < 6) {
//   //       errorElement.textContent = 'Password must be at least 6 characters';
//   //     } else {
//   //       errorElement.textContent = '';
//   //     }
//   //   }

//   //   function togglePasswordVisibility() {
//   //     if (passwordInput.type === 'password') {
//   //       passwordInput.type = 'text';
//   //       togglePasswordBtn.querySelector('.eye-icon').textContent = '👁️‍🗨️';
//   //     } else {
//   //       passwordInput.type = 'password';
//   //       togglePasswordBtn.querySelector('.eye-icon').textContent = '👁️';
//   //     }
//   //   }

//   //   function isValidEmail(email) {
//   //     const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
//   //     return emailRegex.test(email);
//   //   }

//   //   function isValidUsername(username) {
//   //     return username.length >= 3;
//   //   }

//   //   function disableForm() {
//   //     usernameInput.disabled = true;
//   //     passwordInput.disabled = true;
//   //     document.querySelector('.login-btn').disabled = true;
//   //   }
//   // });`,
//   //       },
//   //     },
//   //     {
//   //       id: 10,
//   //       createdAt: '2023-05-01T00:00:00.000Z',
//   //       updatedAt: '2023-05-01T00:00:00.000Z',
//   //       title: 'Real-time Weather Dashboard',
//   //       description:
//   //         'Build a weather dashboard that displays current conditions and forecasts using a weather API.',
//   //       instructions:
//   //         '- Create a weather dashboard with clean UI\n- Fetch data from OpenWeatherMap API\n- Display current weather conditions\n- Show 5-day forecast\n- Add city search functionality\n- Include weather icons and visualizations\n- Handle loading states and errors',
//   //       dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
//   //       isActive: true,
//   //       tenant: tenant.id,
//   //       starterCode: {
//   //         html: `<div class="weather-dashboard">
//   //   <header class="dashboard-header">
//   //     <h1>Weather Dashboard</h1>
//   //     <form id="search-form" class="search-form">
//   //       <input type="text" id="city-input" placeholder="Enter city name..." required>
//   //       <button type="submit">Search</button>
//   //     </form>
//   //   </header>

//   //   <div class="dashboard-content">
//   //     <div id="loading" class="loading-indicator">
//   //       <div class="spinner"></div>
//   //       <p>Fetching weather data...</p>
//   //     </div>

//   //     <div id="error-message" class="error-container">
//   //       <p>Unable to fetch weather data. Please try again.</p>
//   //     </div>

//   //     <div id="current-weather" class="current-weather">
//   //       <div class="weather-location">
//   //         <h2 id="city-name">City Name</h2>
//   //         <p id="current-date">Date</p>
//   //       </div>

//   //       <div class="weather-info">
//   //         <div class="weather-icon">
//   //           <img id="weather-icon" src="" alt="Weather icon">
//   //         </div>

//   //         <div class="temperature">
//   //           <h2 id="temperature">--°C</h2>
//   //           <p id="feels-like">Feels like: --°C</p>
//   //         </div>

//   //         <div class="weather-details">
//   //           <p id="weather-description">Description</p>
//   //           <p id="humidity">Humidity: --%</p>
//   //           <p id="wind-speed">Wind: -- m/s</p>
//   //         </div>
//   //       </div>
//   //     </div>

//   //     <div class="forecast-container">
//   //       <h3>5-Day Forecast</h3>
//   //       <div id="forecast" class="forecast-cards">
//   //         <!-- Forecast cards will be generated here -->
//   //       </div>
//   //     </div>
//   //   </div>
//   // </div>`,
//   //         css: `.weather-dashboard {
//   //   max-width: 1000px;
//   //   margin: 0 auto;
//   //   padding: 1rem;
//   //   font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
//   // }

//   // .dashboard-header {
//   //   display: flex;
//   //   justify-content: space-between;
//   //   align-items: center;
//   //   margin-bottom: 2rem;
//   //   padding-bottom: 1rem;
//   //   border-bottom: 1px solid #eee;
//   // }

//   // .search-form {
//   //   display: flex;
//   // }

//   // #city-input {
//   //   padding: 0.5rem;
//   //   border: 1px solid #ddd;
//   //   border-radius: 4px 0 0 4px;
//   //   font-size: 1rem;
//   // }

//   // .search-form button {
//   //   padding: 0.5rem 1rem;
//   //   background-color: #3498db;
//   //   color: white;
//   //   border: none;
//   //   border-radius: 0 4px 4px 0;
//   //   cursor: pointer;
//   // }

//   // .loading-indicator {
//   //   display: none;
//   //   justify-content: center;
//   //   align-items: center;
//   //   flex-direction: column;
//   //   padding: 2rem;
//   // }

//   // .spinner {
//   //   width: 40px;
//   //   height: 40px;
//   //   border: 4px solid rgba(0, 0, 0, 0.1);
//   //   border-left-color: #3498db;
//   //   border-radius: 50%;
//   //   animation: spin 1s linear infinite;
//   // }

//   // @keyframes spin {
//   //   to { transform: rotate(360deg); }
//   // }

//   // .error-container {
//   //   display: none;
//   //   background-color: #f8d7da;
//   //   color: #721c24;
//   //   padding: 1rem;
//   //   border-radius: 4px;
//   //   margin-bottom: 1rem;
//   // }

//   // .current-weather {
//   //   display: none;
//   //   background-color: #f0f8ff;
//   //   border-radius: 8px;
//   //   padding: 2rem;
//   //   margin-bottom: 2rem;
//   //   box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
//   // }

//   // .weather-info {
//   //   display: flex;
//   //   align-items: center;
//   //   margin-top: 1rem;
//   // }

//   // .weather-icon {
//   //   flex: 0 0 100px;
//   // }

//   // .weather-icon img {
//   //   width: 100px;
//   //   height: 100px;
//   // }

//   // .temperature {
//   //   flex: 1;
//   //   text-align: center;
//   // }

//   // .temperature h2 {
//   //   font-size: 2.5rem;
//   //   margin: 0;
//   // }

//   // .weather-details {
//   //   flex: 1;
//   // }

//   // .forecast-container {
//   //   display: none;
//   // }

//   // .forecast-cards {
//   //   display: grid;
//   //   grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
//   //   gap: 1rem;
//   // }

//   // .forecast-card {
//   //   background-color: #f5f5f5;
//   //   border-radius: 6px;
//   //   padding: 1rem;
//   //   text-align: center;
//   //   box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
//   // }

//   // .forecast-card img {
//   //   width: 50px;
//   //   height: 50px;
//   // }

//   // @media (max-width: 768px) {
//   //   .dashboard-header {
//   //     flex-direction: column;
//   //     align-items: stretch;
//   //   }

//   //   .search-form {
//   //     margin-top: 1rem;
//   //   }

//   //   .weather-info {
//   //     flex-direction: column;
//   //   }

//   //   .weather-details {
//   //     margin-top: 1rem;
//   //     text-align: center;
//   //   }
//   // }`,
//   //         js: `document.addEventListener('DOMContentLoaded', function() {
//   //   // Elements
//   //   const searchForm = document.getElementById('search-form');
//   //   const cityInput = document.getElementById('city-input');
//   //   const loadingIndicator = document.getElementById('loading');
//   //   const errorMessage = document.getElementById('error-message');
//   //   const currentWeather = document.getElementById('current-weather');
//   //   const forecastContainer = document.querySelector('.forecast-container');
//   //   const cityName = document.getElementById('city-name');
//   //   const currentDate = document.getElementById('current-date');
//   //   const weatherIcon = document.getElementById('weather-icon');
//   //   const temperature = document.getElementById('temperature');
//   //   const feelsLike = document.getElementById('feels-like');
//   //   const weatherDescription = document.getElementById('weather-description');
//   //   const humidity = document.getElementById('humidity');
//   //   const windSpeed = document.getElementById('wind-speed');
//   //   const forecast = document.getElementById('forecast');

//   //   // API Key (replace with your OpenWeatherMap API key)
//   //   const apiKey = 'YOUR_API_KEY'; // In a real app, this would be stored securely

//   //   // Default city
//   //   const defaultCity = 'London';

//   //   // Event listeners
//   //   searchForm.addEventListener('submit', function(e) {
//   //     e.preventDefault();
//   //     const city = cityInput.value.trim();
//   //     if (city) {
//   //       getWeatherData(city);
//   //     }
//   //   });

//   //   // Initialize with default city
//   //   getWeatherData(defaultCity);

//   //   // Functions
//   //   async function getWeatherData(city) {
//   //     showLoading();

//   //     try {
//   //       // Get current weather
//   //       const currentWeatherURL = \`https://api.openweathermap.org/data/2.5/weather?q=\${city}&units=metric&appid=\${apiKey}\`;
//   //       const currentResponse = await fetch(currentWeatherURL);

//   //       if (!currentResponse.ok) {
//   //         throw new Error('City not found');
//   //       }

//   //       const currentData = await currentResponse.json();

//   //       // Get forecast data
//   //       const forecastURL = \`https://api.openweathermap.org/data/2.5/forecast?q=\${city}&units=metric&appid=\${apiKey}\`;
//   //       const forecastResponse = await fetch(forecastURL);
//   //       const forecastData = await forecastResponse.json();

//   //       // Update UI with weather data
//   //       updateCurrentWeather(currentData);
//   //       updateForecast(forecastData);

//   //       // Hide loading and show weather data
//   //       hideLoading();
//   //       showWeatherData();

//   //     } catch (error) {
//   //       console.error('Error fetching weather data:', error);
//   //       showError();
//   //     }
//   //   }

//   //   function updateCurrentWeather(data) {
//   //     // Format data and update UI elements
//   //     cityName.textContent = \`\${data.name}, \${data.sys.country}\`;
//   //     currentDate.textContent = formatDate(new Date());
//   //     temperature.textContent = \`\${Math.round(data.main.temp)}°C\`;
//   //     feelsLike.textContent = \`Feels like: \${Math.round(data.main.feels_like)}°C\`;
//   //     weatherDescription.textContent = capitalizeFirstLetter(data.weather[0].description);
//   //     humidity.textContent = \`Humidity: \${data.main.humidity}%\`;
//   //     windSpeed.textContent = \`Wind: \${data.wind.speed} m/s\`;

//   //     // Update weather icon
//   //     const iconCode = data.weather[0].icon;
//   //     weatherIcon.src = \`https://openweathermap.org/img/wn/\${iconCode}@2x.png\`;
//   //     weatherIcon.alt = data.weather[0].description;
//   //   }

//   //   function updateForecast(data) {
//   //     // Clear previous forecast
//   //     forecast.innerHTML = '';

//   //     // Get forecast for every 24 hours (5 days)
//   //     const dailyForecasts = data.list.filter((item, index) => index % 8 === 0);

//   //     // Create forecast cards
//   //     dailyForecasts.forEach(day => {
//   //       const forecastCard = document.createElement('div');
//   //       forecastCard.className = 'forecast-card';

//   //       const date = new Date(day.dt * 1000);
//   //       const iconCode = day.weather[0].icon;
//   //       const temp = Math.round(day.main.temp);
//   //       const description = day.weather[0].description;

//   //       forecastCard.innerHTML = \`
//   //         <h4>\${formatDay(date)}</h4>
//   //         <img src="https://openweathermap.org/img/wn/\${iconCode}.png" alt="\${description}">
//   //         <p class="forecast-temp">\${temp}°C</p>
//   //         <p class="forecast-desc">\${capitalizeFirstLetter(description)}</p>
//   //       \`;

//   //       forecast.appendChild(forecastCard);
//   //     });
//   //   }

//   //   function showLoading() {
//   //     loadingIndicator.style.display = 'flex';
//   //     currentWeather.style.display = 'none';
//   //     forecastContainer.style.display = 'none';
//   //     errorMessage.style.display = 'none';
//   //   }

//   //   function hideLoading() {
//   //     loadingIndicator.style.display = 'none';
//   //   }

//   //   function showWeatherData() {
//   //     currentWeather.style.display = 'block';
//   //     forecastContainer.style.display = 'block';
//   //   }

//   //   function showError() {
//   //     hideLoading();
//   //     errorMessage.style.display = 'block';
//   //     currentWeather.style.display = 'none';
//   //     forecastContainer.style.display = 'none';
//   //   }

//   //   // Helper functions
//   //   function formatDate(date) {
//   //     const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
//   //     return date.toLocaleDateString('en-US', options);
//   //   }

//   //   function formatDay(date) {
//   //     const options = { weekday: 'short' };
//   //     return date.toLocaleDateString('en-US', options);
//   //   }

//   //   function capitalizeFirstLetter(string) {
//   //     return string.charAt(0).toUpperCase() + string.slice(1);
//   //   }
//   // });`,
//   //       },
//   //     },
//   //     {
//   //       id: 11,
//   //       createdAt: '2023-05-01T00:00:00.000Z',
//   //       updatedAt: '2023-05-01T00:00:00.000Z',
//   //       title: 'Modern CSS Techniques',
//   //       description: 'Explore modern CSS features including advanced Flex box and Grid layouts.',
//   //       instructions:
//   //         '- Experiment with complex layouts\n- Incorporate responsive design best practices\n- Ensure accessibility throughout your design',
//   //       dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
//   //       isActive: true,
//   //       tenant: tenant.id,
//   //       starterCode: {
//   //         html: `<!DOCTYPE html>
//   // <html lang="en">
//   // <head>
//   //   <meta charset="UTF-8">
//   //   <meta name="viewport" content="width=device-width, initial-scale=1.0">
//   //   <title>Modern CSS Techniques</title>
//   //   <link rel="stylesheet" href="styles.css">
//   // </head>
//   // <body>
//   //   <header>
//   //     <h1>Modern CSS Layout</h1>
//   //     <nav>
//   //       <ul>
//   //         <li><a href="#flexbox">Flexbox</a></li>
//   //         <li><a href="#grid">Grid</a></li>
//   //         <li><a href="#responsive">Responsive Design</a></li>
//   //       </ul>
//   //     </nav>
//   //   </header>

//   //   <main>
//   //     <section id="flexbox" class="showcase">
//   //       <h2>Flexbox Examples</h2>
//   //       <div class="flex-container">
//   //         <!-- Add your flexbox elements here -->
//   //         <div class="flex-item">Item 1</div>
//   //         <div class="flex-item">Item 2</div>
//   //         <div class="flex-item">Item 3</div>
//   //       </div>
//   //     </section>

//   //     <section id="grid" class="showcase">
//   //       <h2>Grid Examples</h2>
//   //       <div class="grid-container">
//   //         <!-- Add your grid elements here -->
//   //         <div class="grid-item">Area 1</div>
//   //         <div class="grid-item">Area 2</div>
//   //         <div class="grid-item">Area 3</div>
//   //         <div class="grid-item">Area 4</div>
//   //         <div class="grid-item">Area 5</div>
//   //         <div class="grid-item">Area 6</div>
//   //       </div>
//   //     </section>

//   //     <section id="responsive" class="showcase">
//   //       <h2>Responsive Design</h2>
//   //       <div class="responsive-container">
//   //         <!-- Add your responsive content here -->
//   //       </div>
//   //     </section>
//   //   </main>

//   //   <footer>
//   //     <p>Experiment with modern CSS techniques and layouts</p>
//   //   </footer>
//   // </body>
//   // </html>`,
//   //         css: `:root {
//   //   --primary-color: #2563eb;
//   //   --secondary-color: #0891b2;
//   //   --accent-color: #8b5cf6;
//   //   --text-color: #1f2937;
//   //   --bg-color: #f8fafc;
//   //   --card-bg: #ffffff;
//   //   --font-main: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
//   // }

//   // * {
//   //   margin: 0;
//   //   padding: 0;
//   //   box-sizing: border-box;
//   // }

//   // body {
//   //   font-family: var(--font-main);
//   //   color: var(--text-color);
//   //   background-color: var(--bg-color);
//   //   line-height: 1.6;
//   // }

//   // header {
//   //   background-color: var(--primary-color);
//   //   color: white;
//   //   padding: 1rem;
//   // }

//   // header h1 {
//   //   margin-bottom: 1rem;
//   // }

//   // nav ul {
//   //   display: flex;
//   //   list-style: none;
//   //   gap: 1rem;
//   // }

//   // nav a {
//   //   color: white;
//   //   text-decoration: none;
//   //   padding: 0.5rem;
//   //   border-radius: 4px;
//   //   transition: background-color 0.3s;
//   // }

//   // nav a:hover {
//   //   background-color: rgba(255, 255, 255, 0.2);
//   // }

//   // main {
//   //   max-width: 1200px;
//   //   margin: 0 auto;
//   //   padding: 2rem 1rem;
//   // }

//   // .showcase {
//   //   margin-bottom: 3rem;
//   // }

//   // h2 {
//   //   margin-bottom: 1rem;
//   //   color: var(--secondary-color);
//   // }

//   // /* Flexbox container example */
//   // .flex-container {
//   //   display: flex;
//   //   /* Modify these properties to experiment with flexbox */
//   //   flex-wrap: wrap;
//   //   justify-content: space-between;
//   //   gap: 1rem;
//   //   margin-bottom: 2rem;
//   // }

//   // .flex-item {
//   //   background-color: var(--card-bg);
//   //   border: 2px solid var(--primary-color);
//   //   border-radius: 8px;
//   //   padding: 2rem;
//   //   flex: 1 1 200px;
//   //   display: flex;
//   //   justify-content: center;
//   //   align-items: center;
//   //   box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
//   // }

//   // /* Grid container example */
//   // .grid-container {
//   //   display: grid;
//   //   /* Modify these properties to experiment with grid layouts */
//   //   grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
//   //   grid-gap: 1rem;
//   //   margin-bottom: 2rem;
//   // }

//   // .grid-item {
//   //   background-color: var(--card-bg);
//   //   border: 2px solid var(--accent-color);
//   //   border-radius: 8px;
//   //   padding: 2rem;
//   //   display: flex;
//   //   justify-content: center;
//   //   align-items: center;
//   //   box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
//   // }

//   // /* Responsive design section */
//   // .responsive-container {
//   //   /* Add your responsive design experiments here */
//   // }

//   // footer {
//   //   text-align: center;
//   //   padding: 2rem;
//   //   background-color: var(--secondary-color);
//   //   color: white;
//   // }

//   // /* Responsive Media Queries */
//   // @media (max-width: 768px) {
//   //   /* Add your responsive adjustments here */
//   //   nav ul {
//   //     flex-direction: column;
//   //   }

//   //   .grid-container {
//   //     grid-template-columns: 1fr;
//   //   }
//   // }`,
//   //         js: `// Optional JavaScript to enhance your CSS demonstrations

//   // document.addEventListener("DOMContentLoaded", function() {
//   //   // You can add interactive elements to showcase CSS capabilities

//   //   // Example: Toggle a class on click
//   //   const flexItems = document.querySelectorAll('.flex-item');
//   //   flexItems.forEach(item => {
//   //     item.addEventListener('click', function() {
//   //       this.classList.toggle('active');
//   //     });
//   //   });

//   //   // Add more interactive features as needed
//   // });`,
//   //       },
//   //     },
//   //     {
//   //       id: 12,
//   //       createdAt: '2023-05-01T00:00:00.000Z',
//   //       updatedAt: '2023-05-01T00:00:00.000Z',
//   //       title: 'Dynamic Content Loader with AJAX',
//   //       description: 'Build a dynamic content loader that fetches data from a public API using AJAX.',
//   //       instructions:
//   //         '- Use the fetch API to retrieve data\n- Display loading states and handle errors\n- Update the UI dynamically without refreshing the page',
//   //       dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
//   //       isActive: true,
//   //       tenant: tenant.id,
//   //       starterCode: {
//   //         html: `<!DOCTYPE html>
//   // <html lang="en">
//   // <head>
//   //   <meta charset="UTF-8">
//   //   <meta name="viewport" content="width=device-width, initial-scale=1.0">
//   //   <title>Dynamic Content Loader</title>
//   //   <link rel="stylesheet" href="styles.css">
//   // </head>
//   // <body>
//   //   <header>
//   //     <h1>Dynamic Content Loader</h1>
//   //   </header>

//   //   <main>
//   //     <section class="controls">
//   //       <h2>Data Controls</h2>
//   //       <div class="button-group">
//   //         <button id="load-data" class="btn primary">Load Data</button>
//   //         <button id="clear-data" class="btn secondary">Clear Data</button>
//   //       </div>
//   //       <div class="api-selection">
//   //         <label for="api-endpoint">Choose an API:</label>
//   //         <select id="api-endpoint">
//   //           <option value="https://jsonplaceholder.typicode.com/posts">Blog Posts</option>
//   //           <option value="https://jsonplaceholder.typicode.com/users">Users</option>
//   //           <option value="https://jsonplaceholder.typicode.com/photos">Photos</option>
//   //         </select>
//   //       </div>
//   //     </section>

//   //     <section class="content-area">
//   //       <div id="loading-indicator" class="hidden">
//   //         <div class="spinner"></div>
//   //         <p>Loading data...</p>
//   //       </div>

//   //       <div id="error-message" class="hidden">
//   //         <p>An error occurred while fetching data. Please try again.</p>
//   //       </div>

//   //       <div id="content-container">
//   //         <!-- Dynamic content will be loaded here -->
//   //       </div>
//   //     </section>
//   //   </main>

//   //   <footer>
//   //     <p>Built with Fetch API and modern JavaScript</p>
//   //   </footer>

//   //   <script src="script.js"></script>
//   // </body>
//   // </html>`,
//   //         css: `:root {
//   //   --primary-color: #3b82f6;
//   //   --secondary-color: #10b981;
//   //   --accent-color: #f59e0b;
//   //   --text-color: #1f2937;
//   //   --bg-color: #f9fafb;
//   //   --error-color: #ef4444;
//   //   --card-bg: #ffffff;
//   //   --font-main: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
//   // }

//   // * {
//   //   margin: 0;
//   //   padding: 0;
//   //   box-sizing: border-box;
//   // }

//   // body {
//   //   font-family: var(--font-main);
//   //   color: var(--text-color);
//   //   background-color: var(--bg-color);
//   //   line-height: 1.6;
//   // }

//   // header {
//   //   background-color: var(--primary-color);
//   //   color: white;
//   //   padding: 1.5rem;
//   //   text-align: center;
//   // }

//   // main {
//   //   max-width: 1200px;
//   //   margin: 0 auto;
//   //   padding: 2rem 1rem;
//   // }

//   // section {
//   //   margin-bottom: 2rem;
//   // }

//   // .controls {
//   //   background-color: var(--card-bg);
//   //   padding: 1.5rem;
//   //   border-radius: 8px;
//   //   box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
//   // }

//   // h2 {
//   //   margin-bottom: 1rem;
//   //   color: var(--text-color);
//   // }

//   // .button-group {
//   //   display: flex;
//   //   gap: 1rem;
//   //   margin-bottom: 1rem;
//   // }

//   // .btn {
//   //   padding: 0.75rem 1.5rem;
//   //   border: none;
//   //   border-radius: 4px;
//   //   font-weight: bold;
//   //   cursor: pointer;
//   //   transition: all 0.3s ease;
//   // }

//   // .primary {
//   //   background-color: var(--primary-color);
//   //   color: white;
//   // }

//   // .primary:hover {
//   //   background-color: #2563eb;
//   // }

//   // .secondary {
//   //   background-color: #e5e7eb;
//   //   color: var(--text-color);
//   // }

//   // .secondary:hover {
//   //   background-color: #d1d5db;
//   // }

//   // .api-selection {
//   //   margin-top: 1rem;
//   // }

//   // select {
//   //   padding: 0.5rem;
//   //   border-radius: 4px;
//   //   border: 1px solid #d1d5db;
//   //   width: 100%;
//   //   max-width: 300px;
//   // }

//   // .content-area {
//   //   background-color: var(--card-bg);
//   //   padding: 1.5rem;
//   //   border-radius: 8px;
//   //   box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
//   //   min-height: 400px;
//   // }

//   // #loading-indicator {
//   //   display: flex;
//   //   flex-direction: column;
//   //   align-items: center;
//   //   justify-content: center;
//   //   padding: 2rem;
//   // }

//   // .spinner {
//   //   width: 40px;
//   //   height: 40px;
//   //   border: 4px solid rgba(0, 0, 0, 0.1);
//   //   border-left-color: var(--primary-color);
//   //   border-radius: 50%;
//   //   animation: spin 1s linear infinite;
//   // }

//   // @keyframes spin {
//   //   to {
//   //     transform: rotate(360deg);
//   //   }
//   // }

//   // #error-message {
//   //   background-color: #fee2e2;
//   //   color: var(--error-color);
//   //   padding: 1rem;
//   //   border-radius: 4px;
//   //   margin-bottom: 1rem;
//   // }

//   // .hidden {
//   //   display: none;
//   // }

//   // /* Styles for the content items */
//   // .content-item {
//   //   background-color: white;
//   //   padding: 1rem;
//   //   border-radius: 4px;
//   //   margin-bottom: 1rem;
//   //   box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
//   // }

//   // .content-item h3 {
//   //   margin-bottom: 0.5rem;
//   //   color: var(--primary-color);
//   // }

//   // footer {
//   //   text-align: center;
//   //   padding: 2rem;
//   //   background-color: var(--primary-color);
//   //   color: white;
//   // }

//   // /* Responsive adjustments */
//   // @media (max-width: 768px) {
//   //   .button-group {
//   //     flex-direction: column;
//   //   }

//   //   .btn {
//   //     width: 100%;
//   //   }
//   // }`,
//   //         js: `// Wait for the DOM to be fully loaded
//   // document.addEventListener('DOMContentLoaded', () => {
//   //   // DOM elements
//   //   const loadDataBtn = document.getElementById('load-data');
//   //   const clearDataBtn = document.getElementById('clear-data');
//   //   const apiEndpointSelect = document.getElementById('api-endpoint');
//   //   const loadingIndicator = document.getElementById('loading-indicator');
//   //   const errorMessage = document.getElementById('error-message');
//   //   const contentContainer = document.getElementById('content-container');

//   //   // Add event listeners
//   //   loadDataBtn.addEventListener('click', fetchData);
//   //   clearDataBtn.addEventListener('click', clearData);

//   //   // Fetch data from the selected API endpoint
//   //   async function fetchData() {
//   //     const apiUrl = apiEndpointSelect.value;

//   //     // Show loading indicator
//   //     showLoading(true);

//   //     // Hide previous error message
//   //     errorMessage.classList.add('hidden');

//   //     try {
//   //       // Your fetch code will go here
//   //       // Use the Fetch API to get data from the selected endpoint

//   //       // Example:
//   //       // const response = await fetch(apiUrl);
//   //       // if (!response.ok) {
//   //       //   throw new Error('Network response was not ok');
//   //       // }
//   //       // const data = await response.json();
//   //       // displayData(data);

//   //     } catch (error) {
//   //       // Show error message
//   //       console.error('Error fetching data:', error);
//   //       errorMessage.classList.remove('hidden');
//   //     } finally {
//   //       // Hide loading indicator when finished
//   //       showLoading(false);
//   //     }
//   //   }

//   //   // Display the fetched data in the content container
//   //   function displayData(data) {
//   //     // Clear previous content
//   //     contentContainer.innerHTML = '';

//   //     // Generate HTML based on the data type
//   //     // You'll need to implement this based on the API response structure

//   //     // Example for posts:
//   //     // data.forEach(item => {
//   //     //   const element = document.createElement('div');
//   //     //   element.classList.add('content-item');
//   //     //   element.innerHTML = \`
//   //     //     <h3>\${item.title}</h3>
//   //     //     <p>\${item.body}</p>
//   //     //   \`;
//   //     //   contentContainer.appendChild(element);
//   //     // });
//   //   }

//   //   // Show or hide the loading indicator
//   //   function showLoading(isLoading) {
//   //     if (isLoading) {
//   //       loadingIndicator.classList.remove('hidden');
//   //     } else {
//   //       loadingIndicator.classList.add('hidden');
//   //     }
//   //   }

//   //   // Clear all displayed data
//   //   function clearData() {
//   //     contentContainer.innerHTML = '';
//   //     errorMessage.classList.add('hidden');
//   //   }
//   // });`,
//   //       },
//   //     },
//   //   ];

//   // Create assignments and store their IDs
//   const createdAssignments = [];
//   for (const assignment of assignments) {
//     console.log(`Inserting the ${assignment.id}`);
//     const created = await payload.create({
//       collection: 'assignments',
//       data: assignment,
//     });
//     createdAssignments.push(created);
//     console.log(`Inserted the ${assignment.id}`);
//   }

//   // Create subject "Web Development" with multiple sections.
//   const subjects: Subject[] = [
//     {
//       tenant: tenant.id,
//       id: 1,
//       createdAt: '2023-05-01T00:00:00.000Z',
//       updatedAt: '2023-05-01T00:00:00.000Z',
//       name: 'Web Development',
//       description:
//         'A foundational course covering HTML, CSS, and basic JavaScript for building interactive web pages.',
//       // sections: [
//       //   {
//       //     blockType: 'assignmentSection',
//       //     blockName: 'Foundations: HTML & CSS',
//       //     assignments: [
//       //       createdAssignments[0].id, // HTML basics
//       //       createdAssignments[1].id, // CSS styling
//       //     ],
//       //   },
//       //   // {
//       //   //   blockType: 'assignmentSection',
//       //   //   blockName: 'JavaScript Fundamentals',
//       //   //   assignments: [],
//       //   // },
//       //   {
//       //     blockType: 'assignmentSection',
//       //     blockName: 'Mini Projects',
//       //     assignments: [
//       //       createdAssignments[2].id, // Counter app
//       //     ],
//       //   },
//       // ],
//     },
//     {
//       tenant: tenant.id,
//       id: 2,
//       createdAt: '2023-05-01T00:00:00.000Z',
//       updatedAt: '2023-05-01T00:00:00.000Z',
//       name: 'OOP with Java',
//       description:
//         'A hands-on course that introduces core Java and object-oriented concepts (classes, objects, inheritance, polymorphism).',
//       // sections: [
//       //   {
//       //     blockType: 'assignmentSection',
//       //     blockName: 'Java Basics',
//       //     assignments: createdAssignments.filter((as) => as.language === 'java'),
//       //   },
//       // ],
//     },
//     {
//       tenant: tenant.id,
//       id: 3,
//       createdAt: '2023-05-01T00:00:00.000Z',
//       updatedAt: '2023-05-01T00:00:00.000Z',
//       name: 'Programming in C',
//       description:
//         'A hands-on course that introduces core C programming concepts including variables, data types, operators, control structures, functions, and basic I/O operations.',
//       // sections: [
//       //   {
//       //     blockType: 'assignmentSection',
//       //     blockName: 'C Basics',
//       //     assignments: createdAssignments.filter((as) => as.language === 'c'),
//       //   },
//       // ],
//     },
//   ];

//   // Create subjects
//   for (const subject of subjects) {
//     await payload.create({
//       collection: 'subjects',
//       data: {
//         ...subject,
//         tenant: tenant.id,
//       },
//     });
//   }
// };
