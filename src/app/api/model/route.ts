import { type NextRequest, NextResponse } from 'next/server';
import { ModelManager, type ModelName } from '@/lib/model-manager';

/**
 * GET /api/model - Get current model information
 */
export async function GET() {
  try {
    const currentModel = ModelManager.getCurrentModel();
    const availableModels = ModelManager.getCommonModels();

    return NextResponse.json({
      success: true,
      data: {
        currentModel,
        availableModels,
      },
    });
  } catch (error) {
    console.error('Error getting model info:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get model information',
      },
      { status: 500 },
    );
  }
}

/**
 * POST /api/model - Switch to a different model
 * Body: { modelName: string }
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { modelName } = body;

    if (!modelName || typeof modelName !== 'string') {
      return NextResponse.json(
        {
          success: false,
          error: 'Model name is required and must be a string',
        },
        { status: 400 },
      );
    }

    // Basic validation - ModelManager will handle detailed validation
    if (!ModelManager.isValidModel(modelName)) {
      return NextResponse.json(
        {
          success: false,
          error: `Invalid model name: ${modelName}. Model name must be a non-empty string.`,
        },
        { status: 400 },
      );
    }

    const previousModel = ModelManager.getCurrentModel();
    ModelManager.switchModel(modelName as ModelName);

    return NextResponse.json({
      success: true,
      data: {
        previousModel,
        currentModel: modelName,
        message: `Model switched from ${previousModel} to ${modelName}`,
      },
    });
  } catch (error) {
    console.error('Error switching model:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to switch model',
      },
      { status: 500 },
    );
  }
}
