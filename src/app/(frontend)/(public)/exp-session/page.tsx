'use client';

import { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  User,
  FileText,
  ChevronDown,
  ChevronRight,
  Plus,
  Save,
  AlertCircle,
  Timer,
  Play,
  Pause,
  RotateCcw,
  CheckCircle,
  Clock,
  Users,
  Code,
  Star,
  MessageSquare,
  Calendar,
  Mail,
  Hash,
  BookOpen,
} from 'lucide-react';
import { ThemeToggle } from '@/components/theme-toggle';

// Types
interface Student {
  id: string;
  name: string;
  rollNo: string;
  email: string;
  sessionsCompleted: number;
}

interface Assignment {
  id: string;
  title: string;
  description: string;
  submittedAt: string;
  code: string;
  language: string;
}

interface StudentAssignment {
  student: Student;
  assignment: Assignment;
}

interface UserType {
  id: string;
  name: string;
  role: 'TA' | 'faculty';
  email: string;
}

interface RubricScores {
  understanding: string;
  clarity: string;
  consistency: string;
  problemSolving: string;
}

type SessionState = 'idle' | 'selecting_absent' | 'confirming' | 'active' | 'paused' | 'ending';

// Mock data
const mockUser: UserType = {
  id: 'ta_001',
  name: 'Soham',
  role: 'TA',
  email: '<EMAIL>',
};

const mockStudents: StudentAssignment[] = [
  {
    student: {
      id: '21020201127',
      name: 'Soham Sagathiya',
      rollNo: '101',
      email: '<EMAIL>',
      sessionsCompleted: 0,
    },
    assignment: {
      id: 'assign_factorial',
      title: 'Factorial Calculator',
      description:
        'Write a Java program to calculate the factorial of a given number n. Implement both iterative and recursive approaches with proper input validation and error handling.',
      submittedAt: '2024-01-15T10:30:00Z',
      language: 'java',
      code: `import java.util.Scanner;

public class Factorial {
    // Recursive approach
    public static long factorialRecursive(int n) {
        if (n < 0) {
            throw new IllegalArgumentException("Factorial is not defined for negative numbers");
        }
        if (n == 0 || n == 1) {
            return 1;
        }
        return n * factorialRecursive(n - 1);
    }

    // Iterative approach
    public static long factorialIterative(int n) {
        if (n < 0) {
            throw new IllegalArgumentException("Factorial is not defined for negative numbers");
        }
        long result = 1;
        for (int i = 2; i <= n; i++) {
            result *= i;
        }
        return result;
    }

    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        System.out.print("Enter a number to find its factorial: ");
        int n = scanner.nextInt();

        try {
            long recursiveResult = factorialRecursive(n);
            long iterativeResult = factorialIterative(n);

            System.out.println("Factorial of " + n + " (Recursive): " + recursiveResult);
            System.out.println("Factorial of " + n + " (Iterative): " + iterativeResult);
        } catch (IllegalArgumentException e) {
            System.out.println("Error: " + e.getMessage());
        }

        scanner.close();
    }
}`,
    },
  },
  {
    student: {
      id: '21020201122',
      name: 'Dhruv Pithwa',
      rollNo: '102',
      email: '<EMAIL>',
      sessionsCompleted: 1,
    },
    assignment: {
      id: 'assign_fibonacci',
      title: 'Fibonacci Series Generator',
      description:
        'Write a Java program to generate and display the Fibonacci series up to n terms. Implement multiple approaches including recursive, iterative, and optimized solutions with memoization.',
      submittedAt: '2024-01-16T14:20:00Z',
      language: 'java',
      code: `import java.util.Scanner;

public class Fibonacci {
    // Recursive approach (inefficient for large n)
    public static int fibonacciRecursive(int n) {
        if (n <= 1) {
            return n;
        }
        return fibonacciRecursive(n - 1) + fibonacciRecursive(n - 2);
    }

    // Iterative approach (efficient)
    public static void fibonacciIterative(int n) {
        int first = 0, second = 1;

        if (n >= 1) {
            System.out.print(first + " ");
        }
        if (n >= 2) {
            System.out.print(second + " ");
        }

        for (int i = 3; i <= n; i++) {
            int next = first + second;
            System.out.print(next + " ");
            first = second;
            second = next;
        }
        System.out.println();
    }

    // Optimized recursive with memoization
    public static int fibonacciMemo(int n, int[] memo) {
        if (n <= 1) {
            return n;
        }
        if (memo[n] != -1) {
            return memo[n];
        }
        memo[n] = fibonacciMemo(n - 1, memo) + fibonacciMemo(n - 2, memo);
        return memo[n];
    }

    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        System.out.print("Enter the number of terms in Fibonacci series: ");
        int n = scanner.nextInt();

        if (n <= 0) {
            System.out.println("Please enter a positive number");
            return;
        }

        System.out.println("Fibonacci Series (Iterative approach):");
        fibonacciIterative(n);

        System.out.println("\\nFibonacci Series (Recursive approach - first 10 terms only):");
        int limit = Math.min(n, 10);
        for (int i = 0; i < limit; i++) {
            System.out.print(fibonacciRecursive(i) + " ");
        }
        if (n > 10) {
            System.out.print("... (showing only first 10 terms for recursive)");
        }
        System.out.println();

        scanner.close();
    }
}`,
    },
  },
  {
    student: {
      id: '21020201115',
      name: 'Vansh Cholera',
      rollNo: '103',
      email: '<EMAIL>',
      sessionsCompleted: 2,
    },
    assignment: {
      id: 'assign_sorting',
      title: 'Sorting Algorithms',
      description:
        'Implement and compare different sorting algorithms including Bubble Sort, Selection Sort, and Quick Sort with time complexity analysis.',
      submittedAt: '2024-01-17T09:15:00Z',
      language: 'java',
      code: `import java.util.Arrays;

public class SortingAlgorithms {
    // Bubble Sort
    public static void bubbleSort(int[] arr) {
        int n = arr.length;
        for (int i = 0; i < n - 1; i++) {
            for (int j = 0; j < n - i - 1; j++) {
                if (arr[j] > arr[j + 1]) {
                    int temp = arr[j];
                    arr[j] = arr[j + 1];
                    arr[j + 1] = temp;
                }
            }
        }
    }

    // Selection Sort
    public static void selectionSort(int[] arr) {
        int n = arr.length;
        for (int i = 0; i < n - 1; i++) {
            int minIdx = i;
            for (int j = i + 1; j < n; j++) {
                if (arr[j] < arr[minIdx]) {
                    minIdx = j;
                }
            }
            int temp = arr[minIdx];
            arr[minIdx] = arr[i];
            arr[i] = temp;
        }
    }

    public static void main(String[] args) {
        int[] arr1 = {64, 34, 25, 12, 22, 11, 90};
        int[] arr2 = arr1.clone();

        System.out.println("Original array: " + Arrays.toString(arr1));

        bubbleSort(arr1);
        System.out.println("Bubble sorted: " + Arrays.toString(arr1));

        selectionSort(arr2);
        System.out.println("Selection sorted: " + Arrays.toString(arr2));
    }
}`,
    },
  },
  {
    student: {
      id: '21020201116',
      name: 'Premanshu Pithwa',
      rollNo: '104',
      email: '<EMAIL>',
      sessionsCompleted: 2,
    },
    assignment: {
      id: 'assign_sorting',
      title: 'Sorting Algorithms',
      description:
        'Implement and compare different sorting algorithms including Bubble Sort, Selection Sort, and Quick Sort with time complexity analysis.',
      submittedAt: '2024-01-17T09:15:00Z',
      language: 'java',
      code: `import java.util.Arrays;

public class SortingAlgorithms {
    // Bubble Sort
    public static void bubbleSort(int[] arr) {
        int n = arr.length;
        for (int i = 0; i < n - 1; i++) {
            for (int j = 0; j < n - i - 1; j++) {
                if (arr[j] > arr[j + 1]) {
                    int temp = arr[j];
                    arr[j] = arr[j + 1];
                    arr[j + 1] = temp;
                }
            }
        }
    }

    // Selection Sort
    public static void selectionSort(int[] arr) {
        int n = arr.length;
        for (int i = 0; i < n - 1; i++) {
            int minIdx = i;
            for (int j = i + 1; j < n; j++) {
                if (arr[j] < arr[minIdx]) {
                    minIdx = j;
                }
            }
            int temp = arr[minIdx];
            arr[minIdx] = arr[i];
            arr[i] = temp;
        }
    }

    public static void main(String[] args) {
        int[] arr1 = {64, 34, 25, 12, 22, 11, 90};
        int[] arr2 = arr1.clone();

        System.out.println("Original array: " + Arrays.toString(arr1));

        bubbleSort(arr1);
        System.out.println("Bubble sorted: " + Arrays.toString(arr1));

        selectionSort(arr2);
        System.out.println("Selection sorted: " + Arrays.toString(arr2));
    }
}`,
    },
  },
];

// Utility functions
const getRandomSelection = (absentRollNos: string[]): StudentAssignment | null => {
  const availableStudents = mockStudents.filter(
    (item) => !absentRollNos.includes(item.student.rollNo),
  );
  if (availableStudents.length === 0) return null;
  const randomIndex = Math.floor(Math.random() * availableStudents.length);
  return availableStudents[randomIndex];
};

const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

const getSessionProgress = (sessionsCompleted: number): number => {
  return Math.min((sessionsCompleted / 3) * 100, 100);
};

export default function TASessionPanel() {
  const [user] = useState<UserType>(mockUser);
  const [sessionState, setSessionState] = useState<SessionState>('idle');
  const [selectedData, setSelectedData] = useState<StudentAssignment>(mockStudents[0]);
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null);
  const [sessionDuration, setSessionDuration] = useState(0);
  const [pausedDuration, setPausedDuration] = useState(0);
  const [isCodeExpanded, setIsCodeExpanded] = useState(false);
  const [score, setScore] = useState('');
  const [feedback, setFeedback] = useState('');
  const [absentRollNos, setAbsentRollNos] = useState('');
  const [rubricScores, setRubricScores] = useState<RubricScores>({
    understanding: '',
    clarity: '',
    consistency: '',
    problemSolving: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAssignmentDialog, setShowAssignmentDialog] = useState(false);

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (sessionState === 'active' && sessionStartTime) {
      interval = setInterval(() => {
        setSessionDuration(
          Math.floor((Date.now() - sessionStartTime.getTime()) / 1000) - pausedDuration,
        );
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [sessionState, sessionStartTime, pausedDuration]);

  const getParticipantCount = useCallback(() => {
    const absentList = absentRollNos
      .split(',')
      .map((roll) => roll.trim())
      .filter((roll) => roll !== '');
    return (
      mockStudents.length -
      absentList.filter((rollNo) => mockStudents.some((s) => s.student.rollNo === rollNo)).length
    );
  }, [absentRollNos]);

  // Access control check
  if (!user || (user.role !== 'TA' && user.role !== 'faculty')) {
    return (
      <div className="flex items-center justify-center min-h-[400px] p-4">
        <Alert className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Access denied. This interface is restricted to faculty and TA roles only.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const handleCreateSession = () => {
    setSessionState('selecting_absent');
  };

  const handleConfirmAbsent = () => {
    const absentList = absentRollNos
      .split(',')
      .map((roll) => roll.trim())
      .filter((roll) => roll !== '');

    const selected = getRandomSelection(absentList);
    if (selected) {
      setSelectedData(selected);
      setSessionState('confirming');
    } else {
      alert('No available students after filtering absent roll numbers.');
    }
  };

  const handleStartSession = () => {
    setSessionStartTime(new Date());
    setSessionState('active');
    setShowAssignmentDialog(true);
  };

  const handlePauseSession = () => {
    if (sessionState === 'active') {
      setSessionState('paused');
      setPausedDuration(
        (prev) =>
          prev +
          (sessionStartTime
            ? Math.floor((Date.now() - sessionStartTime.getTime()) / 1000) - sessionDuration
            : 0),
      );
    } else if (sessionState === 'paused') {
      setSessionStartTime(new Date());
      setSessionState('active');
    }
  };

  const handleEndSession = () => {
    setSessionState('ending');
  };

  const handleSubmitSession = async () => {
    setIsSubmitting(true);

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1500));

    console.log('Session submitted:', {
      student: selectedData.student,
      assignment: selectedData.assignment,
      duration: sessionDuration,
      score,
      feedback,
      rubricScores,
      timestamp: new Date().toISOString(),
    });

    // Reset state
    setSessionState('idle');
    setSessionStartTime(null);
    setSessionDuration(0);
    setPausedDuration(0);
    setIsCodeExpanded(false);
    setScore('');
    setFeedback('');
    setAbsentRollNos('');
    setRubricScores({ understanding: '', clarity: '', consistency: '', problemSolving: '' });
    setIsSubmitting(false);
  };

  const handleResetTimer = () => {
    setSessionStartTime(new Date());
    setSessionDuration(0);
    setPausedDuration(0);
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto p-4 md:p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">Explanation Session Panel</h1>
            <p className="text-muted-foreground">Welcome back, {user.name}</p>
          </div>
          <div className="flex gap-2 items-center">
            <Badge variant="secondary" className="py-2 px-4">
              <User className="h-3 w-3 mr-1" />
              {user.role.toUpperCase()}
            </Badge>
            <ThemeToggle />
          </div>
        </div>

        {/* Session Statistics - Only show when idle */}
        {sessionState === 'idle' && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-blue-500" />
                  <div>
                    <p className="text-sm font-medium">Total Students</p>
                    <p className="text-2xl font-bold">{mockStudents.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <div>
                    <p className="text-sm font-medium">Sessions Today</p>
                    <p className="text-2xl font-bold">0</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-orange-500" />
                  <div>
                    <p className="text-sm font-medium">Avg. Duration</p>
                    <p className="text-2xl font-bold">12:30</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Main Content */}
        {sessionState === 'idle' && (
          <Card>
            <CardHeader className="text-center">
              <CardTitle className="flex items-center justify-center gap-2">
                <BookOpen className="h-5 w-5" />
                Ready to Start a New Session
              </CardTitle>
              <CardDescription>
                Select absent students and begin an explanation session with a randomly chosen
                student
              </CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center">
              <Button onClick={handleCreateSession} size="lg" className="gap-2">
                <Plus className="h-5 w-5" />
                Create Explanation Session
              </Button>
            </CardContent>
          </Card>
        )}

        {(sessionState === 'active' || sessionState === 'paused') && (
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
            {/* Left Column - Student & Assignment Info */}
            <div className="xl:col-span-1 space-y-4">
              {/* Timer */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Timer className="h-4 w-4" />
                      <CardTitle className="text-lg">Session Timer</CardTitle>
                    </div>
                    <Badge variant={sessionState === 'active' ? 'default' : 'secondary'}>
                      {sessionState === 'active' ? 'Active' : 'Paused'}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-3xl font-mono font-bold text-center">
                    {formatTime(sessionDuration)}
                  </div>
                  <div className="flex gap-2 justify-center">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handlePauseSession}
                      className="gap-1"
                    >
                      {sessionState === 'active' ? (
                        <>
                          <Pause className="h-3 w-3" />
                          Pause
                        </>
                      ) : (
                        <>
                          <Play className="h-3 w-3" />
                          Resume
                        </>
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleResetTimer}
                      className="gap-1"
                    >
                      <RotateCcw className="h-3 w-3" />
                      Reset
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Assignment Info */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    <CardTitle className="text-lg">Assignment Details</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <Label className="text-sm font-medium">Title</Label>
                    <p className="text-sm font-semibold">{selectedData.assignment.title}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Description</Label>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {selectedData.assignment.description}
                    </p>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3 text-muted-foreground" />
                      <span className="text-xs text-muted-foreground">
                        {new Date(selectedData.assignment.submittedAt).toLocaleDateString()}
                      </span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {selectedData.assignment.language.toUpperCase()}
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              {/* Student Info */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <CardTitle className="text-lg">Student Information</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <Label className="text-sm font-medium">Name</Label>
                        <p className="text-sm">{selectedData.student.name}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Hash className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <Label className="text-sm font-medium">Roll Number</Label>
                        <p className="text-sm font-mono">{selectedData.student.rollNo}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <Label className="text-sm font-medium">Email</Label>
                        <p className="text-sm text-muted-foreground">
                          {selectedData.student.email}
                        </p>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <Label className="text-sm font-medium">Session Progress</Label>
                      <span className="text-sm text-muted-foreground">
                        {selectedData.student.sessionsCompleted}/3
                      </span>
                    </div>
                    <Progress
                      value={getSessionProgress(selectedData.student.sessionsCompleted)}
                      className="h-2"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Right Column - Code & Session Controls */}
            <div className="xl:col-span-2 space-y-4">
              {/* Student's Code */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Code className="h-4 w-4" />
                      <CardTitle className="text-lg">Student&apos;s Code</CardTitle>
                    </div>
                    <Badge variant="outline" className="gap-1">
                      <MessageSquare className="h-3 w-3" />
                      Ask student to explain logic
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <Collapsible open={isCodeExpanded} onOpenChange={setIsCodeExpanded}>
                    <CollapsibleTrigger asChild>
                      <Button variant="outline" className="w-full justify-between mb-4">
                        {isCodeExpanded ? 'Hide Code' : 'Show Code'}
                        {isCodeExpanded ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </Button>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <ScrollArea className="h-[400px] w-full rounded-md border">
                        <pre className="bg-muted p-4 text-sm overflow-x-auto">
                          <code>{selectedData.assignment.code}</code>
                        </pre>
                      </ScrollArea>
                    </CollapsibleContent>
                  </Collapsible>
                </CardContent>
              </Card>

              {/* Session Notes */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <MessageSquare className="h-4 w-4" />
                    Session Notes
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Take notes during the session..."
                    className="min-h-[100px] resize-none"
                    value={feedback}
                    onChange={(e) => setFeedback(e.target.value)}
                  />
                </CardContent>
              </Card>

              {/* End Session Button */}
              <div className="flex justify-end">
                <Button
                  onClick={handleEndSession}
                  variant="destructive"
                  size="lg"
                  className="gap-2"
                >
                  <Save className="h-4 w-4" />
                  End Session & Evaluate
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Absent Students Modal */}
        <Dialog
          open={sessionState === 'selecting_absent'}
          onOpenChange={() => setSessionState('idle')}
        >
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Select Absent Students</DialogTitle>
              <DialogDescription>
                Enter the roll numbers of absent students (comma-separated) to exclude them from
                random selection
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="absent-students">Absent Roll Numbers</Label>
                <Textarea
                  id="absent-students"
                  value={absentRollNos}
                  onChange={(e) => setAbsentRollNos(e.target.value)}
                  placeholder="e.g., 21020201127, 21020201122"
                  className="min-h-[80px]"
                />
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="p-3 bg-muted rounded-lg">
                  <p className="font-medium">Total Students</p>
                  <p className="text-lg font-bold">{mockStudents.length}</p>
                </div>
                <div className="p-3 bg-muted rounded-lg">
                  <p className="font-medium">Available</p>
                  <p className="text-lg font-bold text-green-600">{getParticipantCount()}</p>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setSessionState('idle')}>
                Cancel
              </Button>
              <Button onClick={handleConfirmAbsent} disabled={getParticipantCount() === 0}>
                Continue
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Confirmation Modal */}
        <Dialog open={sessionState === 'confirming'} onOpenChange={() => setSessionState('idle')}>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>Confirm Session Details</DialogTitle>
              <DialogDescription>
                The system has randomly selected the following student and assignment for the
                explanation session:
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 bg-primary/10 rounded-full flex items-center justify-center">
                    <User className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-semibold">{selectedData.student.name}</h4>
                    <p className="text-sm text-muted-foreground">{selectedData.student.rollNo}</p>
                  </div>
                </div>
              </div>
              <div className="p-4 bg-muted rounded-lg">
                <div className="flex items-start gap-3">
                  <div className="h-10 w-10 bg-blue-500/10 rounded-full flex items-center justify-center">
                    <FileText className="h-5 w-5 text-blue-500" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold">{selectedData.assignment.title}</h4>
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {selectedData.assignment.description}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setSessionState('idle')}>
                Cancel
              </Button>
              <Button onClick={handleStartSession} className="gap-2">
                <Play className="h-4 w-4" />
                Start Session
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Assignment Details Dialog - Shows right after session starts */}
        <Dialog open={showAssignmentDialog} onOpenChange={() => setShowAssignmentDialog}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Assignment Details
              </DialogTitle>
              <DialogDescription>
                Please review the assignment details carefully before beginning the explanation.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6">
              <div className="p-6 bg-muted/50 rounded-lg space-y-4">
                <div>
                  <h3 className="text-xl font-semibold text-primary">
                    {selectedData.assignment.title}
                  </h3>
                  <div className="flex items-center gap-4 mt-2">
                    <Badge variant="outline" className="text-xs">
                      {selectedData.assignment.language.toUpperCase()}
                    </Badge>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      Submitted:{' '}
                      {new Date(selectedData.assignment.submittedAt).toLocaleDateString()}
                    </div>
                  </div>
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium mb-2">Assignment Description:</h4>
                  <p className="text-sm leading-relaxed text-muted-foreground">
                    {selectedData.assignment.description}
                  </p>
                </div>

                <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                  <div className="flex items-start gap-2">
                    <MessageSquare className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5" />
                    <div className="text-sm">
                      <p className="font-medium text-blue-900 dark:text-blue-100">
                        Instructions for Student:
                      </p>
                      <p className="text-blue-700 dark:text-blue-300 mt-1">
                        Please explain your code logic, approach, and any challenges you faced while
                        implementing this solution.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button onClick={() => setShowAssignmentDialog(false)} className="gap-2">
                <CheckCircle className="h-4 w-4" />
                Got it, Let&apos;s Begin
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* End Session Modal */}
        <Dialog open={sessionState === 'ending'} onOpenChange={() => setSessionState('active')}>
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" />
                Session Evaluation
              </DialogTitle>
              <DialogDescription>
                Session Duration: {formatTime(sessionDuration)} • Student:{' '}
                {selectedData.student.name}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6">
              {/* Score Input */}
              <div className="space-y-2">
                <Label htmlFor="score" className="text-base font-semibold">
                  Overall Score
                </Label>
                <div className="flex gap-4 items-end">
                  <div className="flex-1">
                    <Input
                      id="score"
                      type="number"
                      min="0"
                      max="25"
                      value={score}
                      onChange={(e) => setScore(e.target.value)}
                      placeholder="Enter score out of 25"
                      className="text-lg"
                    />
                  </div>
                  <div className="text-sm text-muted-foreground">/ 25</div>
                </div>
              </div>

              {/* Rubric Scores */}
              <div className="space-y-4">
                <Label className="text-base font-semibold">Detailed Rubric Assessment</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[
                    { key: 'understanding', label: 'Code Understanding', icon: '🧠' },
                    { key: 'clarity', label: 'Explanation Clarity', icon: '💬' },
                    { key: 'consistency', label: 'Logical Consistency', icon: '🔗' },
                    { key: 'problemSolving', label: 'Problem Solving', icon: '🔧' },
                  ].map(({ key, label, icon }) => (
                    <div key={key} className="space-y-2">
                      <Label htmlFor={key} className="flex items-center gap-2">
                        <span>{icon}</span>
                        {label}
                      </Label>
                      <Select
                        value={rubricScores[key as keyof RubricScores]}
                        onValueChange={(value) =>
                          setRubricScores((prev) => ({ ...prev, [key]: value }))
                        }
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select rating" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="excellent">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              Excellent
                            </div>
                          </SelectItem>
                          <SelectItem value="good">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                              Good
                            </div>
                          </SelectItem>
                          <SelectItem value="fair">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                              Fair
                            </div>
                          </SelectItem>
                          <SelectItem value="poor">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                              Poor
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  ))}
                </div>
              </div>

              {/* Feedback */}
              <div className="space-y-2">
                <Label htmlFor="feedback" className="text-base font-semibold">
                  Detailed Feedback
                </Label>
                <Textarea
                  id="feedback"
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  placeholder="Provide constructive feedback for the student's understanding and explanation..."
                  className="min-h-[120px] resize-none"
                />
                <p className="text-xs text-muted-foreground">
                  This feedback will be shared with the student to help them improve.
                </p>
              </div>

              {/* Summary */}
              {score && (
                <div className="p-4 bg-muted rounded-lg">
                  <h4 className="font-semibold mb-2">Session Summary</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Duration:</span>{' '}
                      {formatTime(sessionDuration)}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Score:</span> {score}/25
                    </div>
                    <div>
                      <span className="text-muted-foreground">Grade:</span>{' '}
                      {Number.parseInt(score) >= 20
                        ? 'A'
                        : Number.parseInt(score) >= 15
                          ? 'B'
                          : Number.parseInt(score) >= 10
                            ? 'C'
                            : 'D'}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Status:</span>{' '}
                      {Number.parseInt(score) >= 12 ? 'Pass' : 'Needs Improvement'}
                    </div>
                  </div>
                </div>
              )}
            </div>
            <DialogFooter className="gap-2">
              <Button
                variant="outline"
                onClick={() => setSessionState('active')}
                disabled={isSubmitting}
              >
                Back to Session
              </Button>
              <Button
                onClick={handleSubmitSession}
                disabled={!score || isSubmitting}
                className="gap-2"
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4" />
                    Submit Evaluation
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
