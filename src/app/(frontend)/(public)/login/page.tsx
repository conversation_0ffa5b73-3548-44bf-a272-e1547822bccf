import Image from 'next/image';
import Link from 'next/link';
import Logo from '@/components/Logo';
import { LoginForm } from '@/components/login-form';
import { ThemeToggle } from '@/components/theme-toggle';

export default function LoginPage() {
  return (
    <div className="grid min-h-screen lg:grid-cols-3">
      <div className="flex flex-col gap-4 p-6 md:p-10">
        <header className="flex justify-between items-center gap-2">
          <Link href="/" aria-label="Home">
            <Logo />
          </Link>
          <ThemeToggle />
        </header>
        <main className="flex flex-1 items-center justify-center">
          <LoginForm className="w-full max-w-sm" />
        </main>
      </div>

      {/* Background image for larger screens */}
      <div className="relative hidden lg:block col-span-2 w-full border-l bg-[#fafafa] p-8">
        <div className="relative w-full h-full">
          <Image className="object-contain" src="/diagram.svg" alt="diagram" fill />
        </div>
      </div>
    </div>
  );
}
