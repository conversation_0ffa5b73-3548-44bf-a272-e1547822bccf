'use client';
import { <PERSON><PERSON><PERSON>, ArrowRight } from 'lucide-react';
import Link from 'next/link';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { ISubject } from '@/hooks/use-subjects';
import { useQuery } from '@tanstack/react-query';
import { subjectsQueryOptions } from '@/queries/subjects.query';

export function SubjectsSection() {
  const {
    data: subjects,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['subjects'],
    ...subjectsQueryOptions,
  });

  if (error) {
    console.error(error);
    return <div>Something went wrong</div>;
  }

  return (
    <section className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h2 className="text-2xl font-semibold">All Subjects</h2>
        </div>
        <Button variant="outline" asChild>
          <Link href="/subjects">
            View All
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 3 }).map((_, i) => (
            <SkeletonSubjectCard key={i} />
          ))}
        </div>
      ) : subjects?.length === 0 ? (
        <Card className="flex flex-col items-center justify-center py-12">
          <BookOpen className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No subjects available</h3>
          <p className="text-muted-foreground text-center max-w-sm text-sm">
            Subjects will appear here once created by instructors.
          </p>
        </Card>
      ) : (
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3">
          {subjects?.slice(0, 6).map((subject) => (
            <SubjectCard key={subject.id} subject={subject} />
          ))}
        </div>
      )}
    </section>
  );
}

export function SubjectCard({ subject }: { subject: ISubject }) {
  return (
    <Card className="group relative overflow-hidden transition-all duration-200 hover:shadow-lg hover:-translate-y-1 border-border/50 gap-2 p-4">
      <CardHeader className="p-0">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10 text-primary group-hover:bg-primary group-hover:text-primary-foreground transition-all duration-200 group-hover:scale-110">
              <BookOpen className="h-5 w-5" />
            </div>
            <div className="space-y-1">
              <CardTitle className="text-lg leading-none group-hover:text-primary transition-colors">
                {subject.name}
              </CardTitle>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0 text-muted-foreground text-sm line-clamp-2">
        {subject.description}
      </CardContent>
      <Link
        href={`/assignments?subject=${subject.id}`}
        className="absolute inset-0 z-10"
        aria-label={`View ${subject.name} subject`}
      />
    </Card>
  );
}

export function SkeletonSubjectCard() {
  return (
    <Card className="overflow-hidden gap-2 p-4">
      <CardHeader className="p-0">
        <div className="flex items-center gap-3">
          <Skeleton className="h-10 w-10 rounded-lg" />
          <div className="space-y-1">
            <Skeleton className="h-5 w-32" />
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0 mt-2">
        <Skeleton className="h-[.88rem] w-full" />
        <Skeleton className="h-[.88rem] w-3/4 mt-1" />
      </CardContent>
    </Card>
  );
}
