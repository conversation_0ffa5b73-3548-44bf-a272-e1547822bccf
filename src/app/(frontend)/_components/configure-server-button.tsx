'use client';

import { Server } from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { useCodeEditorStore } from '../(protected)/assignments/[aid]/_components/code-editor/store';

interface ConfigureServerButtonProps {
  serverIsUp: boolean;
  idle: boolean;
  className?: string;
}

export function ConfigureServerButton({ serverIsUp, idle, className }: ConfigureServerButtonProps) {
  const { serverUrl, setServerUrl } = useCodeEditorStore();
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');

  // Determine status text for tooltip and aria-label
  const statusText = idle ? 'Server Idle' : serverIsUp ? 'Server Connected' : 'Server Disconnected';

  const handleOpenDialog = useCallback(() => {
    setInputValue(serverUrl || 'http://localhost:9016');
    setOpen(true);
  }, [serverUrl]);

  const handleSave = useCallback(() => {
    const url = inputValue.trim().replace(/\/+$/, '');
    if (url) {
      localStorage.setItem('ServerUrl', url);
      setServerUrl(url);
      toast.success('Server URL configured');
      setOpen(false);
    }
  }, [inputValue, setServerUrl]);

  const handleCancel = useCallback(() => {
    setOpen(false);
    setInputValue(serverUrl || 'http://localhost:9016');
  }, [serverUrl]);

  return (
    <TooltipProvider>
      <Dialog open={open} onOpenChange={setOpen}>
        <Tooltip>
          <TooltipTrigger asChild>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                onClick={handleOpenDialog}
                className={cn('size-8 relative flex items-center gap-1.5', className)}
                aria-label={`Configure Server (${statusText})`}
              >
                <Server className={cn('w-4 h-4', idle && 'animate-pulse text-muted-foreground')} />

                {/* Status indicator */}
                {serverUrl && (
                  <span className="absolute -top-0.5 -right-0.5 flex size-2">
                    {/* Animated ping effect when server is active (not idle) */}
                    {!idle && (
                      <span
                        className={cn(
                          'animate-ping absolute h-full w-full rounded-full opacity-75',
                          serverIsUp ? 'bg-emerald-400' : 'bg-rose-400',
                        )}
                      />
                    )}

                    {/* Solid dot indicator */}
                    <span
                      className={cn(
                        'relative size-2 rounded-full',
                        idle ? 'bg-amber-400' : serverIsUp ? 'bg-emerald-400' : 'bg-rose-400',
                      )}
                    />
                  </span>
                )}
              </Button>
            </DialogTrigger>
          </TooltipTrigger>
          <TooltipContent side="top">
            <div className="flex flex-col text-center">
              <span className="font-medium">Configure Server</span>
              <span className="text-xs text-muted">{statusText}</span>
            </div>
          </TooltipContent>
        </Tooltip>

        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Configure Server URL</DialogTitle>
            <DialogDescription>
              Enter the URL for your development server. This will be used to connect to your code
              execution environment.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="server-url">Server URL</Label>
              <Input
                id="server-url"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="http://localhost:9016"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleSave();
                  } else if (e.key === 'Escape') {
                    handleCancel();
                  }
                }}
              />
            </div>
          </div>

          <DialogFooter className="flex gap-2">
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={!inputValue.trim()}>
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </TooltipProvider>
  );
}
