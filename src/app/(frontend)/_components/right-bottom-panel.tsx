import { useIdle } from '@uidotdev/usehooks';
import {
  AlertCircleIcon,
  Loader2Icon,
  PlayIcon,
  ServerIcon,
  TerminalIcon,
  TrashIcon,
  ZapIcon,
} from 'lucide-react';
import { useParams } from 'next/navigation';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useDebounce } from '@/hooks/use-debounce';
import { cn } from '@/lib/utils';
import type { Assignment } from '@/payload-types';
// import type { TestSuites } from '@/types/assignment';
import { combineHTML } from '@/utils/html';
import { useCodeEditorStore } from '../(protected)/assignments/[aid]/_components/code-editor/store';
import { ConfigureServerButton } from './configure-server-button';
import { FasterExecution } from './executions/faster';
import { InteractiveExecution } from './executions/interactive';
import { useServerPolling } from './use-server-status';

// Constants
// const IFRAME_LOAD_DELAY = 500;
// const STYLE_RECALC_DELAY = 100;

const STORAGE_KEYS = {
  SERVER_URL: 'ServerUrl',
} as const;

// Types
interface EmptyStateProps {
  isRunning: boolean;
  hasTests: boolean | null | undefined;
  onRunTests: () => void;
  isIOLab: boolean;
  serverUrl?: string;
}

interface ExecutionResult {
  output?: string;
  stdout?: string;
  stderr?: string;
  error?: string;
  executionTime?: number;
  memoryUsage?: number;
}

// Define proper type instead of 'any'
interface ServerExecutionResponse {
  output?: string;
  stdout?: string;
  stderr?: string;
  error?: string;
  executionTime?: number;
  time?: number;
  memoryUsage?: number;
  memory?: number;
}

interface RightBottomPanelProps {
  // testSuites?: TestSuites;
  language: Assignment['language'];
  // testCases: Assignment['javaTestCases'] | Assignment['cTestCases'];
  requiresCommandLineArgs?: boolean | null;
}

// Utility functions
// const createErrorResult = (id: string, title: string, error: string): TestResult => ({
//   id,
//   title,
//   success: false,
//   error,
//   expected: '',
//   actual: '',
// });

// const formatTestResult = (
//   test: any,
//   index: number,
//   format: 'results' | 'tests' | 'data',
// ): TestResult => {
//   const baseResult = {
//     id: test.id || `test-${index}`,
//     expected: test.expected || '',
//     actual: test.actual || test.output || '',
//   };

//   switch (format) {
//     case 'results':
//       return {
//         ...baseResult,
//         title: test.title || `Test ${index + 1}`,
//         success: test.pass || false,
//         error: test.error || '',
//         input: test.input || '',
//       };

//     case 'tests':
//       return {
//         ...baseResult,
//         title: test.name || `Test ${index + 1}`,
//         success: test.passed || false,
//         error: test.error || (test.passed ? '' : 'Test failed'),
//       };

//     case 'data':
//       return {
//         ...baseResult,
//         title: test.testName || test.title || `Test ${index + 1}`,
//         success: test.success || test.passed || false,
//         error: test.error || test.message || '',
//       };

//     default:
//       return createErrorResult('unknown', 'Unknown Test', 'Unknown test format');
//   }
// };

// Custom hooks

const useIframeManager = (
  debouncedHTML: string,
  activeTab: string,
  isJava: boolean,
  isC: boolean,
) => {
  const testIframeRef = useRef<HTMLIFrameElement>(null);
  const previewIframeRef = useRef<HTMLIFrameElement>(null);
  const [iframeKey, setIframeKey] = useState(0);

  // Update preview iframe when HTML changes or tab is switched to preview
  useEffect(() => {
    if (!isJava && !isC && previewIframeRef.current && activeTab === 'preview') {
      previewIframeRef.current.srcdoc = debouncedHTML;
    }
  }, [debouncedHTML, activeTab, isJava, isC]);

  // Update test iframe whenever HTML changes for web assignments
  useEffect(() => {
    if (!isJava && !isC && testIframeRef.current) {
      testIframeRef.current.srcdoc = debouncedHTML;
    }
  }, [debouncedHTML, isJava, isC]);

  // Force preview refresh when switching to preview tab
  useEffect(() => {
    if (!isJava && !isC && activeTab === 'preview') {
      setIframeKey((prev) => prev + 1);
    }
  }, [activeTab, isJava, isC]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      [testIframeRef, previewIframeRef].forEach((ref) => {
        if (ref.current) {
          ref.current.srcdoc = '';
        }
      });
    };
  }, []);

  return { testIframeRef, previewIframeRef, iframeKey };
};

// Components
const EmptyState = React.memo<EmptyStateProps>(
  ({ isRunning, hasTests, onRunTests, isIOLab, serverUrl }) => {
    if (isRunning) {
      return (
        <div className="text-center py-10">
          <Loader2Icon className="w-6 h-6 animate-spin mx-auto mb-3 text-primary" />
          <p className="text-sm text-muted-foreground">Running tests...</p>
        </div>
      );
    }

    if (!hasTests) {
      return (
        <div className="text-center py-10">
          <AlertCircleIcon className="w-6 h-6 mx-auto mb-3 text-muted-foreground" />
          <p className="text-sm text-muted-foreground">No tests available for this assignment</p>
        </div>
      );
    }

    if (isIOLab && !serverUrl) {
      return (
        <div className="text-center py-10">
          <ServerIcon className="w-6 h-6 mx-auto mb-3 text-amber-500" />
          <p className="text-sm text-muted-foreground mb-2">Server not configured</p>
          <p className="text-xs text-muted-foreground mb-4 max-w-xs mx-auto">
            A server connection is required to run tests
          </p>
        </div>
      );
    }

    return (
      <div className="text-center py-10">
        <PlayIcon className="w-6 h-6 mx-auto mb-3 text-primary" />
        <p className="text-sm text-muted-foreground mb-4">Run tests to check your solution</p>
        <Button variant="outline" size="sm" onClick={onRunTests} className="px-4">
          <PlayIcon className="w-4 h-4 mr-2" />
          Run Tests
        </Button>
      </div>
    );
  },
);

EmptyState.displayName = 'EmptyState';

// Custom hook for keyboard shortcuts
// const useKeyboardShortcuts = (
//   handleRunTests: () => void,
//   canRunTests: boolean,
//   setActiveTab: React.Dispatch<React.SetStateAction<string>>,
// ) => {
//   useEffect(() => {
//     const handleKeyDown = (event: KeyboardEvent) => {
//       // Check for Ctrl+Semicolon (Ctrl + ;)
//       if (event.ctrlKey && event.key === ';') {
//         setActiveTab('output');
//       }

//       // Check for Ctrl+quote (Ctrl + ')
//       if (event.ctrlKey && ['"', "'"].includes(event.key)) {
//         event.preventDefault();
//         if (canRunTests) {
//           handleRunTests();
//         }
//       }
//     };

//     document.addEventListener('keydown', handleKeyDown);

//     return () => {
//       document.removeEventListener('keydown', handleKeyDown);
//     };
//   }, [handleRunTests, canRunTests, setActiveTab]);
// };

// Main component
export const RightBottomPanel = React.memo<RightBottomPanelProps>(
  ({
    language,
    requiresCommandLineArgs,
    // testSuites, testCases
  }) => {
    const { aid: id } = useParams();
    // const invalidateSubmissions = useInvalidateSubmissions();
    const {
      getCode,
      getStdin,
      // setStat,
      isTesting,
      setIsTesting,
      serverUrl,
      setServerUrl,
    } = useCodeEditorStore();
    const assignmentId = id?.toString() || '';

    // const [testSuiteResults, setTestSuiteResults] = useState<TestSuiteResult[]>([]);
    // const [isSubmitting, setIsSubmitting] = useState(false);
    const [isRunning, setIsRunning] = useState(false);
    const [error, setError] = useState('');
    const [output, setOutput] = useState('');
    const [progress, setProgress] = useState(0);
    const isIOPlayground = ['java', 'c'].includes(language);
    const isJava = language === 'java';
    const isC = language === 'c';

    const [runMode, setRunMode] = useState<'interactive' | 'faster'>('interactive');
    const idle = useIdle(5000);
    const serverIsUp = useServerPolling(serverUrl, isIOPlayground, idle);
    const abortControllerRef = useRef<AbortController | null>(null);
    const isMountedRef = useRef(true); // Track component mount status
    const { html, css, js, java, c } = getCode(assignmentId);
    const input = getStdin(assignmentId) ?? '';
    const combinedHTML = useMemo(() => {
      if (isJava || isC) return '';
      return combineHTML({ html, css, js });
    }, [html, css, js, isJava, isC]);

    const debouncedHTML = useDebounce(combinedHTML, 500);
    const { testIframeRef } = useIframeManager(
      debouncedHTML,
      'preview', // activeTab not needed anymore
      isJava,
      isC,
    );

    // Memoize validation function to prevent recreation on every render
    const validateInputs = useMemo(
      () => (): string | null => {
        if (!serverUrl) return 'Server URL not configured';
        if (!serverIsUp) return 'Server is not available';
        const code = isJava ? java : c;
        if (!code.trim()) return 'No code to execute';
        return null;
      },
      [serverUrl, serverIsUp, isJava, java, c],
    );

    // Memoize canRun to prevent unnecessary recalculations
    const canRun = useMemo(() => {
      const code = isJava ? java : c;
      return !isRunning && serverUrl && serverIsUp && code?.trim();
    }, [isRunning, serverUrl, serverIsUp, isJava, java, c]);

    // Fixed: Proper typing instead of 'any'
    const parseExecutionResult = useCallback(
      (result: ServerExecutionResponse): ExecutionResult => ({
        output: result.output ?? result.stdout ?? '',
        error: result.error ?? result.stderr ?? '',
        executionTime: result.executionTime ?? result.time,
        memoryUsage: result.memoryUsage ?? result.memory,
      }),
      [],
    );

    const runCode = useCallback(async () => {
      const validationError = validateInputs();
      if (validationError) {
        setError(validationError);
        return;
      }

      const code = isJava ? java : c;

      // For interactive mode, just set isRunning to true
      // The InteractiveExecution component will handle the WebSocket communication
      if (runMode === 'interactive') {
        setIsRunning(true);
        setError('');
        return;
      }

      // For faster mode, continue with HTTP request
      // Cancel previous request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      abortControllerRef.current = new AbortController();

      // Batch state updates
      setIsRunning(true);
      setError('');
      setOutput('');

      try {
        const response = await fetch(`${serverUrl}/run`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            languageId: language,
            input: input,
            code: code,
            useCommandLineArguments: requiresCommandLineArgs,
          }),
          signal: abortControllerRef.current.signal,
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Server error (${response.status}): ${errorText}`);
        }

        const result: ServerExecutionResponse = await response.json();
        const parsedResult = parseExecutionResult(result);

        // Fixed: Check if component is still mounted before updating state
        if (isMountedRef.current) {
          // Batch state updates for better performance
          setOutput(parsedResult.output || '(No output)');
          if (parsedResult.error) setError(parsedResult.error);
        }
      } catch (err) {
        if (isMountedRef.current) {
          if (err instanceof Error && err.name === 'AbortError') {
            setError('Execution cancelled');
          } else {
            console.error('Code execution error:', err);
            setError(err instanceof Error ? err.message : 'Unknown error occurred');
          }
        }
      } finally {
        if (isMountedRef.current) {
          setIsRunning(false);
        }
        abortControllerRef.current = null;
      }
    }, [
      serverUrl,
      requiresCommandLineArgs,
      isJava,
      java,
      c,
      input,
      language,
      runMode,
      validateInputs,
      parseExecutionResult,
    ]);

    const stopExecution = useCallback(() => {
      if (runMode === 'interactive') {
        // For interactive mode, just set isRunning to false
        // The InteractiveExecution component will handle stopping via WebSocket
        setIsRunning(false);
      } else {
        // For faster mode, abort the HTTP request
        abortControllerRef.current?.abort();
      }
    }, [runMode]);

    const toggleMode = () => {
      setRunMode((prev) => (prev === 'interactive' ? 'faster' : 'interactive'));
    };

    const Icon = runMode === 'interactive' ? TerminalIcon : ZapIcon;
    const tooltipText =
      runMode === 'interactive' ? 'Switch to Faster Mode' : 'Switch to Interactive Mode';

    const clearOutput = useCallback(() => {
      setOutput('');
      setError('');
    }, []);

    // Handle assignment submission
    // const handleSubmit = useCallback(async () => {
    //   if (isSubmitting) return;

    //   setIsSubmitting(true);

    //   try {
    //     const passedTestCases = -1; // Not tracking test results for now
    //     const failedTestCases = -1; // Not tracking test results for now

    //     const payload = isC
    //       ? { c, passedTestCases, failedTestCases }
    //       : isJava
    //         ? { java, passedTestCases, failedTestCases }
    //         : { html, css, js, passedTestCases, failedTestCases };

    //     const res = await fetch(`/api/assignments/${assignmentId}/submit`, {
    //       method: 'POST',
    //       headers: { 'Content-Type': 'application/json' },
    //       body: JSON.stringify(payload),
    //     });

    //     const data = await res.json();

    //     if (!res.ok) {
    //       toast.error(data?.message || 'Failed to submit the assignment');
    //     } else {
    //       posthog.capture('submit', {
    //         assignmentId,
    //         type: isJava || isC ? 'io' : 'web',
    //         language: isJava ? 'java' : isC ? 'c' : 'web',
    //       });
    //       toast.success(data.message || 'Assignment submitted successfully');
    //       // Invalidate submissions query to refresh the data
    //       invalidateSubmissions(assignmentId);
    //     }
    //   } catch (error) {
    //     console.error('Submission error:', error);
    //     toast.error((error as Error)?.message || 'Failed to submit the assignment');
    //   } finally {
    //     setIsSubmitting(false);
    //   }
    // }, [
    //   isSubmitting,
    //   isC,
    //   isJava,
    //   c,
    //   java,
    //   html,
    //   css,
    //   js,
    //   assignmentId,
    //   posthog,
    //   invalidateSubmissions,
    // ]);

    // Progress simulation effect with cleanup
    useEffect(() => {
      if (!isRunning) {
        setProgress(0);
        return;
      }

      const interval = setInterval(() => {
        if (isMountedRef.current) {
          setProgress((prev) => (prev >= 90 ? 90 : prev + 10));
        }
      }, 500);

      return () => clearInterval(interval);
    }, [isRunning]);

    // Cleanup on unmount - Fixed: Track mount status
    useEffect(() => {
      isMountedRef.current = true;
      return () => {
        isMountedRef.current = false;
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
      };
    }, []);

    // Keyboard shortcut handler
    useEffect(() => {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.ctrlKey && event.key === ';') {
          event.preventDefault();
          if (canRun) {
            runCode();
          }
        }
      };

      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }, [canRun, runCode]);

    // Initialize server URL from localStorage
    useEffect(() => {
      if (isJava || isC) {
        const savedUrl = localStorage.getItem(STORAGE_KEYS.SERVER_URL);
        if (savedUrl) {
          setServerUrl(savedUrl);
        }
      }
    }, [isJava, isC, setServerUrl]);

    // Reset testing state when assignment changes
    useEffect(() => {
      if (isTesting) {
        setIsTesting(false);
      }
    }, [assignmentId, setIsTesting]);

    // Calculate flattened test results for stats
    // const flattenedTestResults = useMemo(() => {
    //   return testSuiteResults.flatMap((suite) => suite.results);
    // }, [testSuiteResults]);

    // Update stats when test results change
    // useEffect(() => {
    //   if (!assignmentId) return;

    //   const passedTestcases = flattenedTestResults.filter((ts) => ts.success).length;
    //   const totalTestcases = flattenedTestResults.length;

    //   setStat(assignmentId, {
    //     passedTestcases,
    //     totalTestcases,
    //   });
    // }, [flattenedTestResults, assignmentId, setStat]);

    // Process test results from server response
    // const processTestResults = useCallback((responseData: ServerResponse): TestResult[] => {
    //   try {
    //     // Handle different response formats
    //     if (responseData.results?.length) {
    //       return responseData.results.map((r, i) => formatTestResult(r, i, 'results'));
    //     }

    //     if (responseData.tests?.length) {
    //       return responseData.tests.map((test, i) => formatTestResult(test, i, 'tests'));
    //     }

    //     if (responseData.data?.length) {
    //       return responseData.data.map((item, i) => formatTestResult(item, i, 'data'));
    //     }

    //     return [
    //       createErrorResult(
    //         'format-error',
    //         'Invalid Test Results Format',
    //         'The Server returned results in an unexpected format',
    //       ),
    //     ];
    //   } catch (err) {
    //     console.error('Error processing test results:', err);
    //     return [
    //       createErrorResult(
    //         'process-error',
    //         'Test Processing Error',
    //         err instanceof Error ? err.message : 'Unknown error processing test results',
    //       ),
    //     ];
    //   }
    // }, []);

    // Run IO tests (Java/C)
    // const runIOTests = useCallback(async (): Promise<TestResult[]> => {
    //   if (!serverUrl) {
    //     const errorMessage = 'Server URL not configured. Please configure the server to run tests.';
    //     toast.error(errorMessage);
    //     return [createErrorResult('config-error', 'Configuration Error', errorMessage)];
    //   }

    //   setIsTesting(true);

    //   try {
    //     const response = await fetch(`${serverUrl}/test`, {
    //       method: 'POST',
    //       headers: { 'Content-Type': 'application/json' },
    //       body: JSON.stringify({
    //         code: isJava ? java : c,
    //         testCases: testCases || [],
    //         languageId: isJava ? 'java' : 'c',
    //         useCommandLineArguments: requiresCommandLineArgs,
    //       }),
    //     });

    //     if (!response.ok) {
    //       const errorText = await response.text();
    //       throw new Error(`Server error (${response.status}): ${errorText}`);
    //     }

    //     const responseText = await response.text();
    //     const parsedResults = JSON.parse(responseText);
    //     const testResults = processTestResults(parsedResults);

    //     // Organize results into visible and hidden test suites
    //     const visibleResults = testResults.slice(0, testCases?.length || 0);
    //     const hiddenResults = testResults.slice(testCases?.length || 0);

    //     const suitesToAdd: TestSuiteResult[] = [];

    //     if (visibleResults.length > 0) {
    //       suitesToAdd.push({
    //         id: 'visible-tests',
    //         name: 'Visible Tests',
    //         results: visibleResults,
    //         isExpanded: true,
    //       });
    //     }

    //     if (hiddenResults.length > 0) {
    //       suitesToAdd.push({
    //         id: 'hidden-tests',
    //         name: 'Hidden Tests',
    //         results: hiddenResults,
    //         isExpanded: true,
    //       });
    //     }

    //     if (suitesToAdd.length === 0 && testResults.length > 0) {
    //       suitesToAdd.push({
    //         id: 'tests',
    //         name: 'Tests',
    //         results: testResults,
    //         isExpanded: true,
    //       });
    //     }

    //     setTestSuiteResults(suitesToAdd);
    //     return testResults;
    //   } catch (err) {
    //     console.error('Test execution error:', err);
    //     const errorMessage =
    //       err instanceof Error ? err.message : 'Unknown error during test execution';
    //     toast.error('Network error occurred while running tests. Please check the server.');

    //     const errorResult = [createErrorResult('error', 'Test Execution Error', errorMessage)];
    //     setTestSuiteResults([
    //       {
    //         id: 'error-suite',
    //         name: 'Error',
    //         results: errorResult,
    //         isExpanded: true,
    //       },
    //     ]);

    //     return errorResult;
    //   } finally {
    //     setIsTesting(false);
    //   }
    // }, [
    //   serverUrl,
    //   isJava,
    //   c,
    //   java,
    //   testCases,
    //   requiresCommandLineArgs,
    //   processTestResults,
    //   setIsTesting,
    // ]);

    // Run web tests (HTML/CSS/JS) - FIXED VERSION
    // const runWebTests = useCallback(async (): Promise<TestResult[]> => {
    //   if (!testSuites?.length) {
    //     setTestSuiteResults([]);
    //     return [];
    //   }

    //   setIsTesting(true);
    //   setTestSuiteResults([]);

    //   try {
    //     if (!testIframeRef.current) return [];

    //     // Helper function to refresh iframe and wait for it to be ready
    //     const refreshIframeAndWait = async (): Promise<void> => {
    //       if (!testIframeRef.current) return;

    //       // Refresh the iframe content
    //       testIframeRef.current.srcdoc = debouncedHTML;

    //       // Wait for iframe to load
    //       await new Promise<void>((resolve) => {
    //         if (testIframeRef.current?.contentDocument?.readyState === 'complete') {
    //           setTimeout(resolve, IFRAME_LOAD_DELAY);
    //         } else {
    //           const onLoad = () => {
    //             setTimeout(resolve, IFRAME_LOAD_DELAY);
    //             testIframeRef.current?.removeEventListener('load', onLoad);
    //           };
    //           testIframeRef.current?.addEventListener('load', onLoad);
    //         }
    //       });

    //       // Force style recalculation
    //       if (testIframeRef.current?.contentWindow) {
    //         void testIframeRef.current.contentWindow.document.body.offsetHeight;
    //       }

    //       await new Promise((resolve) => setTimeout(resolve, STYLE_RECALC_DELAY));
    //     };

    //     // Initial iframe setup
    //     await refreshIframeAndWait();

    //     // Run all test suites
    //     const suiteResults: TestSuiteResult[] = [];
    //     const allResults: TestResult[] = [];

    //     for (let i = 0; i < testSuites.length; i++) {
    //       const suite = testSuites[i];

    //       if (suite.tests?.length) {
    //         // Refresh iframe before each test suite (except the first one which was already refreshed)
    //         if (i > 0) {
    //           await refreshIframeAndWait();
    //         }

    //         const results = await runTests(testIframeRef.current, suite.tests);
    //         allResults.push(...results);

    //         suiteResults.push({
    //           id: suite.id || `suite-${suiteResults.length}`,
    //           name: (suite as any)?.name || `Test Suite ${suiteResults.length + 1}`,
    //           results,
    //           isExpanded: !results.every((r) => r.success),
    //         });
    //       }
    //     }

    //     setTestSuiteResults(suiteResults);
    //     return allResults;
    //   } catch (error) {
    //     console.error('Test execution error:', error);
    //     const errorResult = [
    //       createErrorResult(
    //         'error',
    //         'Test Execution Error',
    //         error instanceof Error ? error.message : 'Unknown error occurred during test execution',
    //       ),
    //     ];

    //     setTestSuiteResults([
    //       {
    //         id: 'error-suite',
    //         name: 'Error',
    //         results: errorResult,
    //         isExpanded: true,
    //       },
    //     ]);

    //     return errorResult;
    //   } finally {
    //     setIsTesting(false);
    //   }
    // }, [testSuites, setIsTesting, testIframeRef, debouncedHTML]);

    // Unified test runner
    // const handleRunTests = useCallback(async (): Promise<TestResult[]> => {
    //   setActiveTab('test-results');

    //   posthog.capture('test_run', {
    //     assignmentId,
    //     type: isJava || isC ? 'io' : 'web',
    //     language: isJava ? 'java' : isC ? 'c' : 'web',
    //   });

    //   return isJava || isC ? runIOTests() : runWebTests();
    // }, [assignmentId, isJava, isC, runIOTests, runWebTests, posthog]);

    // Set up keyboard shortcuts
    // useKeyboardShortcuts(handleRunTests, canRunTests, setActiveTab);

    return (
      <div className="flex flex-col w-full h-full bg-background flex-1">
        {/* Hidden iframe for web tests */}
        {!isJava && !isC && (
          <iframe
            ref={testIframeRef}
            srcDoc={debouncedHTML}
            className="hidden"
            sandbox="allow-scripts allow-same-origin"
            title="Test Runner"
            aria-hidden="true"
          />
        )}

        {(isJava || isC) && (
          <div className={cn(runMode === 'interactive' && 'bg-black', 'flex-1')}>
            {/* Sticky Toolbar */}
            <div className="sticky p-2 top-0 z-10 bg-background backdrop-blur-sm border-b pb-2">
              <div className="flex items-center gap-2 justify-between">
                <div className="flex items-center gap-2">
                  {/* Run and Submit buttons moved to right-top-panel */}
                </div>

                <div className="flex gap-2">
                  {isRunning && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          onClick={stopExecution}
                          variant="outline"
                          size="icon"
                          aria-label="Stop execution"
                          className="transition-all duration-200 size-8 select-none"
                        >
                          <AlertCircleIcon className="w-4 h-4 text-red-600" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Stop code execution</TooltipContent>
                    </Tooltip>
                  )}

                  {(output || error) && !isRunning && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              className="transition-all duration-200 size-8 select-none"
                              variant="outline"
                              size="icon"
                              aria-label="Clear output"
                            >
                              <TrashIcon className="size-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Clear Output?</AlertDialogTitle>
                              <AlertDialogDescription>
                                This will clear the current output and execution stats. This action
                                cannot be undone.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction onClick={clearOutput}>Clear</AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </TooltipTrigger>
                      <TooltipContent>Clear output and stats</TooltipContent>
                    </Tooltip>
                  )}

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="icon"
                        className="relative transition-all duration-300 hover:scale-105 hover:shadow-md size-8"
                        onClick={toggleMode}
                      >
                        <Icon
                          className={`w-4 h-4 transition-all duration-300 ${
                            runMode === 'faster' ? 'text-yellow-500' : 'text-blue-500'
                          }`}
                        />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent className="" side="left">
                      <p>{tooltipText}</p>
                    </TooltipContent>
                  </Tooltip>

                  <ConfigureServerButton idle={idle} serverIsUp={serverIsUp} />
                </div>
              </div>
            </div>

            {/* Execution Components */}
            {/* <div className="w-full h-full overflow-auto bg-red-500"> */}
            {runMode === 'interactive' ? (
              <InteractiveExecution
                language={language}
                code={isJava ? java : c}
                isRunning={runMode === 'interactive' ? isRunning : false}
                setIsRunning={setIsRunning}
                clearOutput={runMode === 'interactive' && output.trim() === ''}
                serverUrl={serverUrl}
                serverIsUp={serverIsUp}
              />
            ) : (
              <FasterExecution
                assignmentId={assignmentId}
                isRunning={runMode === 'faster' ? isRunning : false}
                setIsRunning={setIsRunning}
                error={error}
                setError={setError}
                isMountedRef={isMountedRef}
                output={output}
                setOutput={setOutput}
                progress={progress}
                setProgress={setProgress}
              />
            )}
            {/* </div> */}
          </div>
        )}
      </div>
    );
  },
);

RightBottomPanel.displayName = 'RightBottomPanel';
