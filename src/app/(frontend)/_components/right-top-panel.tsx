import { useIdle } from '@uidotdev/usehooks';
import { Loader2Icon, PlayIcon, SendIcon, Square, TerminalIcon } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useInvalidateSubmissions } from '@/hooks/use-submissions';
import { useCustomPosthog } from '@/lib/posthog';
import { cn } from '@/lib/utils';
import type { Assignment } from '@/payload-types';
import type { DecodedToken } from '@/types/decoded-token';
import CodeEditor from '../(protected)/assignments/[aid]/_components/code-editor';
import { useCodeEditorStore } from '../(protected)/assignments/[aid]/_components/code-editor/store';
import { InteractiveExecution } from './executions/interactive';
import { useServerPolling } from './use-server-status';

export function RightTopPanel({
  starterCode,
  language,
  user,
}: {
  starterCode: Assignment['starterCode'];
  language: Assignment['language'];
  user: DecodedToken | null;
}) {
  const posthog = useCustomPosthog();
  const { aid: _id } = useParams();
  const id = _id?.toString() ?? '';
  const invalidateSubmissions = useInvalidateSubmissions();
  const setStarter = useCodeEditorStore((s) => s.setStarter);
  const { getCode, serverUrl, isTesting } = useCodeEditorStore();

  // Check if this is an IO playground (Java or C)
  const isIOPlayground = language === 'java' || language === 'c';

  // Use idle detection and server polling for IO playgrounds
  const idle = useIdle(5000);
  const serverIsUp = useServerPolling(serverUrl, isIOPlayground, idle);
  const [isTerminalOpen, setIsTerminalOpen] = useState(false);
  const [isRunning, setIsRunning] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  // const [, setError] = useState('');
  // const [, setOutput] = useState('');

  // Get code and input data
  const currentCode = getCode(id);
  const code = language === 'java' ? currentCode.java : currentCode.c;
  // const input = getStdin(id) ?? '';
  const { html, css, js, java, c } = getCode(id);
  const isJava = language === 'java';
  const isC = language === 'c';

  // Memoize validation function to prevent recreation on every render
  const validateInputs = useMemo(
    () => (): string | null => {
      if (!serverUrl) return 'Server URL not configured';
      if (!serverIsUp) return 'Server is not available';
      const code = isJava ? java : c;
      if (!code.trim()) return 'No code to execute';
      return null;
    },
    [serverUrl, serverIsUp, isJava, java, c],
  );

  // Memoize canRun to prevent unnecessary recalculations
  const canRun = useMemo(() => {
    const code = isJava ? java : c;
    return !isRunning && serverUrl && serverIsUp && code?.trim();
  }, [isRunning, serverUrl, serverIsUp, isJava, java, c]);

  const toggleTerminal = useCallback(() => {
    setIsTerminalOpen((prev) => !prev);
  }, []);

  const closeTerminal = useCallback(() => {
    setIsTerminalOpen(false);
  }, []);

  const runCode = useCallback(async () => {
    const validationError = validateInputs();
    if (validationError) {
      // setError(validationError);
      return;
    }

    // For interactive execution (needed for scanf), just set isRunning to true
    // The InteractiveExecution component in the terminal will handle the WebSocket communication
    setIsRunning(true);
    // setError('');
    // setOutput('');

    // Open the terminal to show the interactive execution
    if (!isTerminalOpen) {
      setIsTerminalOpen(true);
    }
  }, [validateInputs, isTerminalOpen, setIsTerminalOpen]);

  const stopExecution = useCallback(() => {
    // For interactive mode, just set isRunning to false
    // The InteractiveExecution component will handle stopping via WebSocket
    setIsRunning(false);
  }, []);

  // Handle assignment submission
  const handleSubmit = useCallback(async () => {
    if (isSubmitting) return;

    setIsSubmitting(true);

    try {
      const passedTestCases = -1; // Not tracking test results for now
      const failedTestCases = -1; // Not tracking test results for now

      const payload = isC
        ? { c, passedTestCases, failedTestCases }
        : isJava
          ? { java, passedTestCases, failedTestCases }
          : { html, css, js, passedTestCases, failedTestCases };

      const res = await fetch(`/api/assignments/${id}/submit`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });

      const data = await res.json();

      if (!res.ok) {
        toast.error(data?.message || 'Failed to submit the assignment');
      } else {
        posthog.capture('submit', {
          assignmentId: id,
          type: isJava || isC ? 'io' : 'web',
          language: isJava ? 'java' : isC ? 'c' : 'web',
        });
        toast.success(data.message || 'Assignment submitted successfully');
        // Invalidate submissions query to refresh the data
        invalidateSubmissions(id);
      }
    } catch (error) {
      console.error('Submission error:', error);
      toast.error((error as Error)?.message || 'Failed to submit the assignment');
    } finally {
      setIsSubmitting(false);
    }
  }, [isSubmitting, isC, isJava, c, java, html, css, js, id, posthog, invalidateSubmissions]);

  useEffect(() => {
    if (starterCode) setStarter(id, starterCode);
  }, [id, starterCode, setStarter]);

  // Keyboard shortcuts for terminal
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Toggle terminal with Ctrl/Cmd + `
      if ((event.ctrlKey || event.metaKey) && event.key === '`') {
        event.preventDefault();
        toggleTerminal();
      }
      // Close terminal with Escape (only if terminal is open)
      else if (event.key === 'Escape' && isTerminalOpen) {
        event.preventDefault();
        closeTerminal();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [toggleTerminal, closeTerminal, isTerminalOpen]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Cleanup if needed
    };
  }, []);

  // Keyboard shortcut handler for run/stop code
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.key === ';') {
        event.preventDefault();
        if (isRunning) {
          stopExecution();
        } else if (canRun) {
          runCode();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [canRun, runCode, isRunning, stopExecution]);

  return (
    id && (
      <div className="flex-1 flex flex-col h-full relative">
        <CodeEditor id={id.toString()} language={language} starterCode={starterCode} user={user} />

        {/* Terminal Toggle Bar */}
        <div
          className={cn(
            'border-t bg-background cursor-pointer transition-all duration-200 ease-in-out select-none absolute w-full z-50 flex flex-col bottom-0',
            // isTerminalOpen ? "bottom-3/5" : "bottom-0",
          )}
          onClick={toggleTerminal}
        >
          <div className={cn('p-3 flex items-center justify-around gap-2 w-full z-50')}>
            <div className="flex-1 flex items-center gap-2">
              {/* Only show run and submit buttons for Java/C assignments */}
              {isIOPlayground && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (isRunning) {
                            stopExecution();
                          } else {
                            runCode();
                          }
                        }}
                        disabled={!isRunning && !canRun}
                        className="transition-all duration-200 w-22 h-7 select-none"
                        aria-label={isRunning ? 'Stop execution' : 'Run code'}
                        variant={isRunning ? 'destructive' : 'outline'}
                      >
                        {isRunning ? (
                          <>
                            <Square className="w-4 h-4 mr-0.5" />
                            Stop
                          </>
                        ) : (
                          <>
                            <PlayIcon className="w-4 h-4 mr-0.5" />
                            Run
                          </>
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <span className="font-medium">
                        {isRunning ? (
                          'Stop execution'
                        ) : (
                          <>
                            <kbd>Ctrl</kbd> + ;
                          </>
                        )}
                      </span>
                    </TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="default"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSubmit();
                        }}
                        disabled={isSubmitting || isTesting}
                        className="w-22 h-7 select-none"
                        aria-label="Submit code"
                      >
                        {isSubmitting ? (
                          <Loader2Icon className="w-4 h-4 animate-spin" />
                        ) : (
                          <>
                            <SendIcon className="w-4 h-4 mr-0.5" />
                            Submit
                          </>
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <span className="font-medium">Submit assignment</span>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>

            <div className="justify-center flex-1 flex gap-2">
              <TerminalIcon className="w-4 h-4" />
              <span className="text-sm font-medium">TERMINAL</span>

              {/* Server Status Indicator for IO Playgrounds */}
              {isIOPlayground && (
                <div className="flex items-center gap-1 ml-2">
                  <div
                    className={cn(
                      'w-2 h-2 rounded-full transition-colors duration-200',
                      idle ? 'bg-amber-400' : serverIsUp ? 'bg-emerald-400' : 'bg-rose-400',
                    )}
                  />
                  <span className="text-xs text-muted-foreground">
                    {idle ? 'Idle' : serverIsUp ? 'Connected' : 'Disconnected'}
                  </span>
                </div>
              )}
            </div>

            <div className="flex-1 flex items-center justify-end gap-2 ml-auto">
              <span className="text-xs text-muted-foreground transition-opacity duration-200">
                Click to {isTerminalOpen ? 'hide' : 'show'}
              </span>
              <div className="flex items-center gap-1">
                <kbd className="px-1.5 py-0.5 text-xs bg-muted rounded border text-muted-foreground">
                  Ctrl + `
                </kbd>
                {isTerminalOpen && (
                  <>
                    <span className="text-xs text-muted-foreground">or</span>
                    <kbd className="px-1.5 py-0.5 text-xs bg-muted rounded border text-muted-foreground">
                      Esc
                    </kbd>
                  </>
                )}
              </div>
            </div>
          </div>

          <div
            className={cn(
              'flex-1 transition-all ease-in-out bottom-0',
              isTerminalOpen ? 'block' : 'hidden',
            )}
            onClick={(e) => e.stopPropagation()}
          >
            <InteractiveExecution
              language={language}
              code={code}
              isRunning={isRunning}
              setIsRunning={setIsRunning}
              clearOutput={false}
              serverUrl={serverUrl}
              serverIsUp={serverIsUp}
            />
          </div>

          {/* Terminal Overlay */}
          {/* <div
            className={cn(
              // isTerminalOpen
              //   ? "translate-y-0 opacity-100"
              //   : "translate-y-full opacity-0 pointer-events-none"
            )}
            style={{
              height: isTerminalOpen ? "70%" : "0%",
              minHeight: isTerminalOpen ? "300px" : "0px"
            }}
          >
            <div className="h-full overflow-hidden">
              {isTerminalOpen && (
                <InteractiveExecution
                  code={code}
                  isRunning={isRunning}
                  setIsRunning={setIsRunning}
                  clearOutput={false}
                  serverUrl={serverUrl}
                  serverIsUp={serverIsUp}
                />
              )}
            </div>
          </div> */}
        </div>
      </div>
    )
  );
}
