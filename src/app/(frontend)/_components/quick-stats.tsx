'use client';

import { StatCard, StatSkeleton } from '@/components/dashboard/stat-card';
import { Skeleton } from '@/components/ui/skeleton';
import { useUser } from '@/hooks/use-user';
import {
  BookOpenIcon,
  CalendarIcon,
  CheckCircleIcon,
  CodeIcon,
  UsersIcon,
  ClockIcon,
  TrendingUpIcon,
  type LucideIcon,
} from 'lucide-react';
import { useMemo } from 'react';
import { isStudentStats, userStatsOptions } from '@/queries/stats.query';
import { useQuery } from '@tanstack/react-query';

type StatConfig = {
  title: string;
  metric: number;
  icon: LucideIcon;
  trend: 'positive' | 'negative' | 'neutral' | 'warning';
  href: string;
};

const ADMIN_ROUTES = {
  assignments: '/admin/collections/assignments',
  submissions: '/admin/collections/submissions',
  users: '/admin/collections/users',
} as const;

const STUDENT_ROUTES = {
  assignments: '/assignments',
  completed: '/assignments?filter=completed',
  pending: '/assignments?filter=pending',
} as const;

const createStudentStats = (stats: any): StatConfig[] => {
  const { activeAssignments, completedAssignments, remainingSubmissions } = stats;

  return [
    {
      title: 'Active Assignments',
      metric: activeAssignments,
      icon: CalendarIcon,
      trend: 'neutral',
      href: STUDENT_ROUTES.assignments,
    },
    {
      title: 'Completed',
      metric: completedAssignments,
      icon: CheckCircleIcon,
      trend: 'positive',
      href: STUDENT_ROUTES.completed,
    },
    {
      title: 'Pending',
      metric: remainingSubmissions,
      icon: ClockIcon,
      trend: remainingSubmissions > 0 ? 'warning' : 'positive',
      href: STUDENT_ROUTES.pending,
    },
  ];
};

const createInstructorStats = (stats: any): StatConfig[] => [
  {
    title: 'Total Assignments',
    metric: stats.totalAssignments,
    icon: BookOpenIcon,
    trend: 'positive',
    href: ADMIN_ROUTES.assignments,
  },
  {
    title: 'Total Submissions',
    metric: stats.totalSubmissions,
    icon: CodeIcon,
    trend: 'positive',
    href: ADMIN_ROUTES.submissions,
  },
  {
    title: 'Total Users',
    metric: stats.totalUsers,
    icon: UsersIcon,
    trend: 'positive',
    href: ADMIN_ROUTES.users,
  },
];

export const QuickStats = () => {
  const { user, role, isLoading: isUserLoading } = useUser();
  const {
    data: stats,
    isLoading: isStatsLoading,
    error: statsError,
  } = useQuery({
    ...userStatsOptions(role as string, user?.id),
    queryKey: ['userStats', role, user?.id],
  });

  const displayStats = useMemo((): StatConfig[] => {
    if (!stats || isStatsLoading) return [];
    if (isStudentStats(stats)) {
      return createStudentStats(stats);
    }
    return createInstructorStats(stats);
  }, [stats, isStatsLoading]);

  const renderOverviewContent = () => {
    if (!stats) return null;
    if (isStudentStats(stats)) {
      const { completedAssignments } = stats;
      return (
        <div className="space-y-1">
          <h3 className="text-md text-muted-foreground">
            {completedAssignments === 0 && (
              <>
                You haven&apos;t completed any assignments yet.{' '}
                <span className="max-sm:hidden inline">Let&apos;s get started!</span>
              </>
            )}
            {completedAssignments > 0 && completedAssignments <= 5 && (
              <>
                You&apos;ve completed{' '}
                <span className="text-primary font-semibold">{completedAssignments}</span>{' '}
                assignment{completedAssignments > 1 && 's'}.{' '}
                <span className="max-sm:hidden inline">Nice start - keep going!</span>
              </>
            )}
            {completedAssignments > 5 && completedAssignments <= 15 && (
              <>
                You&apos;ve completed{' '}
                <span className="text-primary font-semibold">{completedAssignments}</span>{' '}
                assignments. <span className="max-sm:hidden inline">Great consistency!</span>
              </>
            )}
            {completedAssignments > 15 && (
              <>
                You&apos;ve completed{' '}
                <span className="text-primary font-semibold">{completedAssignments}</span>{' '}
                assignments.{' '}
                <span className="max-sm:hidden inline">
                  Incredible dedication - keep up the momentum!
                </span>
              </>
            )}
          </h3>
        </div>
      );
    }

    return <p className="text-muted-foreground">Monitor your platform metrics and user activity</p>;
  };

  const renderStatsGrid = () => {
    const overallLoading = isUserLoading || isStatsLoading;

    if (overallLoading) {
      return Array.from({ length: 3 }, (_, idx) => <StatSkeleton key={`skeleton-${idx}`} />);
    }

    if (statsError) {
      return (
        <div className="col-span-full text-center text-muted-foreground">
          Error: {statsError.message}
        </div>
      );
    }

    return displayStats.map((stat) => <StatCard key={stat.title} {...stat} />);
  };

  return (
    <section className="space-y-6">
      <div className="space-y-2">
        <div className="flex items-center gap-3">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-purple-600">
            <TrendingUpIcon className="h-4 w-4 text-white" />
          </div>
          <h2 className="text-3xl font-bold">Overview</h2>
        </div>
        {isUserLoading || isStatsLoading ? (
          <Skeleton className="w-64 h-5 py-3" />
        ) : (
          renderOverviewContent()
        )}
      </div>

      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        {renderStatsGrid()}
      </div>
    </section>
  );
};
