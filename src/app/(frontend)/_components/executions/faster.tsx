import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import {
  AlertCircleIcon,
  CheckCircleIcon,
  CopyIcon,
  HelpCircleIcon,
  Loader2Icon,
  TerminalIcon,
  TrashIcon,
} from 'lucide-react';
import { Dispatch, memo, SetStateAction, useCallback, useMemo, useState } from 'react';
import { useCodeEditorStore } from '../../(protected)/assignments/[aid]/_components/code-editor/store';

const LoadingState = memo(({ progress }: { progress: number }) => (
  <div className="space-y-2">
    <Progress value={progress} className="w-full" />
    <div className="text-center py-4 text-muted-foreground">
      <Loader2Icon className="w-6 h-6 mx-auto mb-2 animate-spin" />
      <p className="text-sm">Executing code...</p>
      <p className="text-xs mt-1 opacity-75">This may take a moment</p>
    </div>
  </div>
));

LoadingState.displayName = 'LoadingState';

const EmptyOutput = memo(() => (
  <div className="text-center py-8 text-muted-foreground border border-dashed rounded-md">
    <TerminalIcon className="w-8 h-8 mx-auto mb-2 opacity-50" />
    <p className="text-sm">No output yet</p>
    <p className="text-xs mt-1 opacity-75">Run your code to see results</p>
  </div>
));

EmptyOutput.displayName = 'EmptyOutput';

const OutputDisplay = memo(
  ({
    output,
    // outputRef,
  }: {
    output: string;
    // outputRef: React.RefObject<HTMLDivElement | null>;
  }) => (
    <div
      // ref={outputRef}
      className="bg-muted border rounded-md p-3 max-h-96 overflow-auto"
      role="log"
      aria-label="Program output"
    >
      <pre className="text-sm font-mono whitespace-pre-wrap text-foreground">{output}</pre>
    </div>
  ),
);

OutputDisplay.displayName = 'OutputDisplay';

const ErrorDisplay = memo(({ error }: { error: string }) => (
  <div className="p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/40 rounded-md">
    <div className="flex items-start gap-2">
      <AlertCircleIcon className="w-4 h-4 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
      <div className="min-w-0 flex-1">
        <h4 className="text-sm font-medium text-red-800 dark:text-red-200 mb-1">Runtime Error</h4>
        <pre className="text-xs font-mono whitespace-pre-wrap text-red-700 dark:text-red-300 overflow-auto max-h-48">
          {error}
        </pre>
      </div>
    </div>
  </div>
));

ErrorDisplay.displayName = 'ErrorDisplay';

export const FasterExecution = ({
  assignmentId,
  isRunning,
  error,
  progress,
  isMountedRef,
  output,
}: {
  assignmentId: string;
  isRunning: boolean;
  setIsRunning: Dispatch<SetStateAction<boolean>>;
  error: string;
  setError: Dispatch<SetStateAction<string>>;
  progress: number;
  setProgress: Dispatch<SetStateAction<number>>;
  isMountedRef: React.RefObject<boolean | null>;
  output: string;
  setOutput: Dispatch<SetStateAction<string>>;
}) => {
  const { getStdin, setStdin } = useCodeEditorStore();
  const input = getStdin(assignmentId) ?? '';
  const [copied, setCopied] = useState(false);

  const inputsCount = useMemo(
    () => (input ? input.trim().split(/\s+/).filter(Boolean).length : 0),
    [input],
  );

  const copyOutput = useCallback(async () => {
    if (!output) return;

    try {
      // Check if clipboard API is supported
      if (!navigator.clipboard) {
        throw new Error('Clipboard API not supported');
      }

      await navigator.clipboard.writeText(output);
      setCopied(true);
      setTimeout(() => {
        if (isMountedRef?.current) {
          setCopied(false);
        }
      }, 2000);
    } catch (err) {
      console.error('Failed to copy output:', err);
      // Fallback: Create temporary textarea for copying
      const textArea = document.createElement('textarea');
      textArea.value = output;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        setCopied(true);
        setTimeout(() => {
          if (isMountedRef.current) {
            setCopied(false);
          }
        }, 2000);
      } catch (fallbackErr) {
        console.error('Fallback copy also failed:', fallbackErr);
      }
      document.body.removeChild(textArea);
    }
  }, [output, isMountedRef]);

  return (
    <div className="space-y-4 px-2 bg-background rounded-lg shadow-sm">
      {/* Input Section */}
      <Accordion type="single" collapsible className="py-0 my-0">
        <AccordionItem value="item-1">
          <AccordionTrigger className="py-2 text-sm font-medium">
            <div className="flex items-center gap-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <HelpCircleIcon className="w-4 h-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  Input passed to your program&apos;s standard input stream
                </TooltipContent>
              </Tooltip>
              Standard Input
              {input && (
                <Badge variant="secondary" className="text-xs">
                  {inputsCount} input{inputsCount !== 1 ? 's' : ''}
                </Badge>
              )}
            </div>
          </AccordionTrigger>
          <AccordionContent className="p-2 pt-0">
            <div className="space-y-2">
              <Textarea
                id="stdin-input"
                value={input}
                onChange={(e) => {
                  const newValue = e.target.value;
                  setStdin(assignmentId, newValue);
                }}
                placeholder="Enter input for your program (e.g., numbers, strings)..."
                className="min-h-[80px] font-mono text-sm resize-y border-input"
                disabled={isRunning}
                aria-describedby="stdin-help"
              />
              {input && (
                <div className="flex justify-end">
                  <Button
                    onClick={() => {
                      setStdin(assignmentId, '');
                    }}
                    variant="ghost"
                    size="sm"
                    className="text-xs"
                    disabled={isRunning}
                  >
                    <TrashIcon className="w-3 h-3 mr-1" />
                    Clear Input
                  </Button>
                </div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      {/* Execution Stats */}
      {/* <ExecutionStats
            time={executionStats.time}
            memory={executionStats.memory}
            lastRunTime={lastRunTime}
          /> */}

      {/* Output Section */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <TerminalIcon className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm font-medium">Output</span>
          </div>
          <Tooltip>
            <TooltipTrigger asChild disabled={!output?.trim()}>
              <Button
                onClick={copyOutput}
                variant="ghost"
                size="sm"
                aria-label={copied ? 'Output copied' : 'Copy output'}
                disabled={!output?.trim()}
              >
                {copied ? (
                  <CheckCircleIcon className="w-4 h-4 text-green-600" />
                ) : (
                  <CopyIcon className="w-4 h-4" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {copied ? 'Copied to clipboard!' : 'Copy output to clipboard'}
            </TooltipContent>
          </Tooltip>
        </div>

        {error && <ErrorDisplay error={error} />}

        {output?.trim() && !isRunning && !error ? (
          <OutputDisplay output={output} />
        ) : !error && !isRunning ? (
          <EmptyOutput />
        ) : null}

        {isRunning && <LoadingState progress={progress} />}
      </div>
    </div>
  );
};
