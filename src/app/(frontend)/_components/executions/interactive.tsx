'use client';

import { FitAddon } from '@xterm/addon-fit';
import { Terminal } from '@xterm/xterm';
import React, { type Dispatch, type SetStateAction, useCallback, useEffect, useRef } from 'react';
import '@xterm/xterm/css/xterm.css';

interface InteractiveExecutionProps {
  code: string;
  isRunning: boolean;
  clearOutput: boolean;
  language: string;
  setIsRunning: Dispatch<SetStateAction<boolean>>;
  serverUrl: string | undefined;
  serverIsUp: boolean | undefined;
}

export const InteractiveExecution = ({
  code,
  isRunning,
  language,
  setIsRunning,
  clearOutput,
  serverUrl,
  serverIsUp,
}: InteractiveExecutionProps) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const ws = useRef<WebSocket | null>(null);
  const terminalInstance = useRef<{
    term: Terminal;
    fitAddon: FitAddon;
  } | null>(null);
  const isWaitingForInput = useRef(false);
  const inputBuffer = useRef('');
  const isConnected = useRef(false);
  const hasExecutedRef = useRef(false);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const checkServerIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);
  const resizeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Debounced resize function to prevent excessive fitting
  const debouncedFit = useCallback(() => {
    if (resizeTimeoutRef.current) {
      clearTimeout(resizeTimeoutRef.current);
    }
    resizeTimeoutRef.current = setTimeout(() => {
      if (terminalInstance.current?.fitAddon) {
        try {
          terminalInstance.current.fitAddon.fit();
        } catch (error) {
          console.warn('Terminal fit error:', error);
        }
      }
    }, 100);
  }, []);

  // Initialize terminal only once
  useEffect(() => {
    if (terminalRef.current && !terminalInstance.current) {
      const term = new Terminal({
        theme: {
          foreground: '#f9fafb',
          cursor: '#f9fafb',
          cursorAccent: '#030712',
          selectionBackground: 'rgba(250, 250, 250, 0.25)',
          red: '#ef4444',
          green: '#22c55e',
          yellow: '#eab308',
          blue: '#3b82f6',
          magenta: '#d946ef',
          cyan: '#06b6d4',
          white: '#ffffff',
        },
        cols: 4,
        fontSize: 14,
        fontFamily: 'Consolas, "Courier New", monospace',
        cursorBlink: true,
        convertEol: true,
        scrollback: 1000,
        allowProposedApi: true,
      });

      const fitAddon = new FitAddon();
      term.loadAddon(fitAddon);
      term.open(terminalRef.current);

      // Initial fit with a small delay to ensure DOM is ready
      setTimeout(() => {
        fitAddon.fit();
      }, 50);

      terminalInstance.current = { term, fitAddon };

      // Handle user input
      term.onData((data) => {
        if (!ws.current || ws.current.readyState !== WebSocket.OPEN) return;

        if (isWaitingForInput.current) {
          if ((data === '\r' || data === '\n') && inputBuffer.current.trim()) {
            term.write('\r\n');
            ws.current.send(JSON.stringify({ type: 'stdin', data: inputBuffer.current }));
            inputBuffer.current = '';
          } else if (data === '\u007f') {
            if (inputBuffer.current.length > 0) {
              inputBuffer.current = inputBuffer.current.slice(0, -1);
              term.write('\b \b');
            }
          } else if (data >= ' ' && data <= '~') {
            inputBuffer.current += data;
            term.write(data);
          }
        }
      });

      // Window resize handler
      const handleWindowResize = () => {
        debouncedFit();
      };
      window.addEventListener('resize', handleWindowResize);

      // ResizeObserver for container size changes
      if (terminalRef.current && 'ResizeObserver' in window) {
        resizeObserverRef.current = new ResizeObserver((entries) => {
          for (const entry of entries) {
            if (entry.target === terminalRef.current) {
              debouncedFit();
            }
          }
        });
        resizeObserverRef.current.observe(terminalRef.current);
      }

      return () => {
        window.removeEventListener('resize', handleWindowResize);
        if (resizeObserverRef.current) {
          resizeObserverRef.current.disconnect();
          resizeObserverRef.current = null;
        }
        if (resizeTimeoutRef.current) {
          clearTimeout(resizeTimeoutRef.current);
          resizeTimeoutRef.current = null;
        }
      };
    }
  }, [debouncedFit]);

  // Effect to handle dynamic resizing when component props change or container updates
  useEffect(() => {
    const handleDynamicResize = () => {
      if (terminalInstance.current?.fitAddon && terminalRef.current) {
        // Check if the terminal container is visible and has dimensions
        const rect = terminalRef.current.getBoundingClientRect();
        if (rect.width > 0 && rect.height > 0) {
          debouncedFit();
        }
      }
    };

    // Use MutationObserver to detect when the terminal becomes visible or container changes
    let mutationObserver: MutationObserver | null = null;
    if (terminalRef.current && 'MutationObserver' in window) {
      mutationObserver = new MutationObserver(() => {
        handleDynamicResize();
      });

      // Observe the parent container for attribute changes (like style changes)
      const parentElement = terminalRef.current.parentElement;
      if (parentElement) {
        mutationObserver.observe(parentElement, {
          attributes: true,
          attributeFilter: ['style', 'class'],
          subtree: true,
        });
      }
    }

    // Also trigger resize when the component updates
    handleDynamicResize();

    return () => {
      if (mutationObserver) {
        mutationObserver.disconnect();
      }
    };
  }, [debouncedFit, isRunning, clearOutput]);

  // Handle code execution
  const executeCode = useCallback(() => {
    if (!ws.current || ws.current.readyState !== WebSocket.OPEN || !code.trim()) {
      terminalInstance.current?.term.writeln(
        '\x1b[31mCannot run: Not connected or no code provided.\x1b[0m',
      );
      setIsRunning(false);
      return;
    }

    isWaitingForInput.current = false;
    inputBuffer.current = '';
    terminalInstance.current?.term.clear();
    terminalInstance.current?.term.focus();

    ws.current.send(
      JSON.stringify({
        type: 'compile_and_run',
        code: code,
        language: language,
      }),
    );
  }, [code, setIsRunning]);

  // WebSocket connection management
  useEffect(() => {
    if (!serverUrl) {
      terminalInstance.current?.term.clear();
      terminalInstance.current?.term.writeln(
        '\r\n\x1b[31mError: Server URL not provided. Cannot connect to execution service.\x1b[0m',
      );
      setIsRunning(false);
      return;
    }

    const maxRetries = 5;
    let retryCount = 0;

    const connectWebSocket = () => {
      if (ws.current && ws.current.readyState === WebSocket.OPEN) {
        return; // Prevent creating new connection if already connected
      }

      if (ws.current) {
        ws.current.close();
      }

      const socket = new WebSocket(`ws://${new URL(serverUrl).hostname}:9017`);
      ws.current = socket;

      socket.onopen = () => {
        isConnected.current = true;
        retryCount = 0;
        if (retryTimeoutRef.current) {
          clearTimeout(retryTimeoutRef.current);
          retryTimeoutRef.current = null;
        }
        terminalInstance.current?.term.clear();
        terminalInstance.current?.term.writeln('\x1b[32mConnected to execution server.\x1b[0m');
        if (isRunning && hasExecutedRef.current === false) {
          executeCode();
        }
      };

      socket.onclose = () => {
        isConnected.current = false;
        if (serverIsUp && retryCount < maxRetries) {
          retryTimeoutRef.current = setTimeout(() => {
            retryCount += 1;
            terminalInstance.current?.term.writeln(
              `\x1b[33mAttempting to reconnect (${retryCount}/${maxRetries})...\x1b[0m`,
            );
            connectWebSocket();
          }, 3000);
        } else if (!serverIsUp) {
          terminalInstance.current?.term.writeln(
            '\r\n\x1b[31mServer is down, please ensure the server is running.\x1b[0m',
          );
        } else {
          terminalInstance.current?.term.writeln(
            '\r\n\x1b[31mMax retries reached. Please check the server.\x1b[0m',
          );
          setIsRunning(false);
        }
      };

      socket.onerror = (error) => {
        console.log(error);
        isConnected.current = false;
        terminalInstance.current?.term.writeln(
          '\r\n\x1b[31mConnection error, run `npx easelabs start`\x1b[0m',
        );
      };

      socket.onmessage = (event) => {
        const msg = JSON.parse(event.data);
        const term = terminalInstance.current?.term;
        if (!term) return;

        switch (msg.type) {
          case 'status':
            if (msg.data === 'Compilation successful!') {
              term.writeln(`\x1b[33m${msg.data}\x1b[0m`);
            } else if (msg.data === 'Program started, ready for input') {
              isWaitingForInput.current = true;
              term.writeln(`\x1b[33m${msg.data}\x1b[0m`);
            } else {
              term.writeln(`\x1b[33m${msg.data}\x1b[0m`);
            }
            break;
          case 'compile_error':
            term.writeln(`\x1b[31mCompilation Failed:\x1b[0m`);
            term.write(msg.data);
            setIsRunning(false);
            isWaitingForInput.current = false;
            break;
          case 'stdout':
            term.write(msg.data);
            break;
          case 'stderr':
            term.write(`\x1b[31m${msg.data}\x1b[0m`);
            break;
          case 'runtime_error':
            term.write(`\x1b[31m${msg.data}\x1b[0m`);
            setIsRunning(false);
            isWaitingForInput.current = false;
            break;
          case 'exit': {
            let data = msg.data;
            if (String(data).startsWith('\nProgram finished successfully!')) {
              data = `\x1b[32m${data}\x1b[0m`;
            }
            term.write(data);
            term.blur();
            setIsRunning(false);
            isWaitingForInput.current = false;
            break;
          }
          case 'error':
            term.writeln(`\x1b[31mError: ${msg.data}\x1b[0m`);
            setIsRunning(false);
            isWaitingForInput.current = false;
            break;
        }
      };
    };

    // Start or monitor connection based on serverIsUp
    if (serverIsUp && !isConnected.current) {
      connectWebSocket();
    } else if (!serverIsUp && !isConnected.current) {
      terminalInstance.current?.term.clear();
      terminalInstance.current?.term.writeln(
        '\r\x1b[31mServer is not up. run the "easelabs start" command...\x1b[0m',
      );
      checkServerIntervalRef.current = setInterval(() => {
        if (serverIsUp && !isConnected.current) {
          terminalInstance.current?.term.writeln(
            '\x1b[33mServer is up, attempting to connect...\x1b[0m',
          );
          connectWebSocket();
          if (checkServerIntervalRef.current) {
            clearInterval(checkServerIntervalRef.current);
            checkServerIntervalRef.current = null;
          }
        }
      }, 3000);
    }

    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
        retryTimeoutRef.current = null;
      }
      if (checkServerIntervalRef.current) {
        clearInterval(checkServerIntervalRef.current);
        checkServerIntervalRef.current = null;
      }
      if (ws.current) {
        ws.current.close();
      }
    };
  }, [serverUrl, serverIsUp, setIsRunning]);

  // Handle clear execution
  const clearExecution = useCallback(() => {
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      ws.current.send(JSON.stringify({ type: 'clear' }));
    }
    terminalInstance.current?.term.clear();
    isWaitingForInput.current = false;
    inputBuffer.current = '';
  }, []);

  // Execute code when isRunning changes
  useEffect(() => {
    if (isRunning && hasExecutedRef.current === false) {
      hasExecutedRef.current = true;
      executeCode();
    } else if (!isRunning && hasExecutedRef.current === true) {
      if (ws.current && ws.current.readyState === WebSocket.OPEN) {
        ws.current.send(JSON.stringify({ type: 'stop_execution' }));
      }
      hasExecutedRef.current = false;
      isWaitingForInput.current = false;
    }
  }, [isRunning, executeCode]);

  // Handle clear output
  useEffect(() => {
    if (clearOutput) {
      clearExecution();
    }
  }, [clearOutput, clearExecution]);

  // Cleanup effect on unmount
  useEffect(() => {
    return () => {
      // Clean up all timeouts and observers
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      if (checkServerIntervalRef.current) {
        clearInterval(checkServerIntervalRef.current);
      }
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }

      // Close WebSocket connection
      if (ws.current) {
        ws.current.close();
      }

      // Dispose terminal
      if (terminalInstance.current) {
        terminalInstance.current.term.dispose();
        terminalInstance.current = null;
      }
    };
  }, []);

  return (
    <div className="flex flex-col p-3 bg-black h-full w-full">
      <div ref={terminalRef} className="w-full flex-1" />
    </div>
  );
};
