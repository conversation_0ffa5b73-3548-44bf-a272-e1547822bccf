import { useEffect, useState } from 'react';

const POLLING_INTERVALS = {
  UP: 5000,
  DOWN: 1000,
} as const;

export const useServerPolling = (serverUrl: string, isIOPlayground: boolean, idle: boolean) => {
  const [serverIsUp, setServerIsUp] = useState(false);

  useEffect(() => {
    if (!isIOPlayground) return;

    let isMounted = true;
    let pollId: NodeJS.Timeout | null = null;
    let currentDelay: number = POLLING_INTERVALS.DOWN;

    const ping = async () => {
      if (idle) return;

      try {
        const res = await fetch(serverUrl, { method: 'HEAD' });
        if (!isMounted) return;

        const up = res.ok;
        setServerIsUp(up);

        const desiredDelay = up ? POLLING_INTERVALS.UP : POLLING_INTERVALS.DOWN;
        if (desiredDelay !== currentDelay) {
          currentDelay = desiredDelay;
          if (pollId) clearInterval(pollId);
          pollId = setInterval(ping, currentDelay);
        }
      } catch {
        if (!isMounted) return;
        setServerIsUp(false);
        if (currentDelay !== POLLING_INTERVALS.DOWN) {
          currentDelay = POLLING_INTERVALS.DOWN;
          if (pollId) clearInterval(pollId);
          pollId = setInterval(ping, currentDelay);
        }
      }
    };

    ping();
    pollId = setInterval(ping, currentDelay);

    return () => {
      isMounted = false;
      if (pollId) clearInterval(pollId);
    };
  }, [serverUrl, idle, isIOPlayground]);

  return serverIsUp;
};
