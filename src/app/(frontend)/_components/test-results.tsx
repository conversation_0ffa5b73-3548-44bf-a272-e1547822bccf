import React from 'react';
import {
  CheckCircleIcon,
  XCircleIcon,
  AlertCircleIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  ClockIcon,
  PlayIcon,
} from 'lucide-react';

// Enhanced type definitions
interface TestResult {
  id: string;
  title: string;
  success: boolean;
  expected?: string;
  actual?: string;
  error?: string;
  duration?: number; // in milliseconds
  status?: 'passed' | 'failed' | 'skipped' | 'pending';
}

interface TestSuiteResult {
  id: string;
  name: string;
  results: TestResult[];
  isExpanded: boolean;
  duration?: number;
  metadata?: Record<string, any>;
}

// Utility function for class names (assuming you have a cn utility)
const cn = (...classes: (string | undefined | boolean)[]) => classes.filter(Boolean).join(' ');

// Format duration helper
const formatDuration = (ms?: number): string => {
  if (!ms) return '';
  if (ms < 1000) return `${ms}ms`;
  return `${(ms / 1000).toFixed(2)}s`;
};

// Individual test result display item
const TestResultItem = React.memo<{
  result: TestResult;
  index: number;
}>((props) => {
  const { result, index } = props;

  const getStatusIcon = () => {
    switch (result.status || (result.success ? 'passed' : 'failed')) {
      case 'passed':
        return (
          <CheckCircleIcon className="w-4 h-4 text-green-600 dark:text-green-400 flex-shrink-0" />
        );
      case 'failed':
        return <XCircleIcon className="w-4 h-4 text-red-600 dark:text-red-400 flex-shrink-0" />;
      case 'skipped':
        return (
          <AlertCircleIcon className="w-4 h-4 text-yellow-600 dark:text-yellow-400 flex-shrink-0" />
        );
      case 'pending':
        return <ClockIcon className="w-4 h-4 text-blue-600 dark:text-blue-400 flex-shrink-0" />;
      default:
        return <PlayIcon className="w-4 h-4 text-gray-600 dark:text-gray-400 flex-shrink-0" />;
    }
  };

  const getStatusColor = () => {
    switch (result.status || (result.success ? 'passed' : 'failed')) {
      case 'passed':
        return 'border-green-200 bg-green-50/50 dark:bg-green-950/20 dark:border-green-800/40';
      case 'failed':
        return 'border-red-200 bg-red-50/50 dark:bg-red-950/20 dark:border-red-800/40';
      case 'skipped':
        return 'border-yellow-200 bg-yellow-50/50 dark:bg-yellow-950/20 dark:border-yellow-800/40';
      case 'pending':
        return 'border-blue-200 bg-blue-50/50 dark:bg-blue-950/20 dark:border-blue-800/40';
      default:
        return 'border-gray-200 bg-gray-50/50 dark:bg-gray-950/20 dark:border-gray-800/40';
    }
  };

  const hasFailureDetails = !result.success && (result.expected || result.actual || result.error);

  return (
    <div
      className={cn('p-3 border rounded-lg transition-colors', getStatusColor())}
      role="listitem"
      aria-label={`Test ${index}: ${result.title} - ${result.success ? 'passed' : 'failed'}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 min-w-0 flex-1">
          {getStatusIcon()}
          <span className="text-sm font-medium truncate">
            {index}. {result.title}
          </span>
        </div>
        {result.duration && (
          <span className="text-xs text-muted-foreground ml-2 flex-shrink-0">
            {formatDuration(result.duration)}
          </span>
        )}
      </div>

      {/* Failure details section */}
      {hasFailureDetails && (
        <div className="mt-4 space-y-4">
          {/* Expected vs Actual comparison */}
          {(result.expected || result.actual !== undefined) && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {result.expected && (
                <div className="space-y-2">
                  <h4 className="text-xs font-semibold text-green-700 dark:text-green-300 uppercase tracking-wide">
                    Expected Output
                  </h4>
                  <pre className="text-xs bg-white dark:bg-gray-900 border border-green-200 dark:border-green-800 rounded-md p-3 overflow-x-auto whitespace-pre-wrap font-mono leading-relaxed">
                    {result.expected}
                  </pre>
                </div>
              )}

              {result.actual !== undefined && (
                <div className="space-y-2">
                  <h4 className="text-xs font-semibold text-red-700 dark:text-red-300 uppercase tracking-wide">
                    Actual Output
                  </h4>
                  <pre className="text-xs bg-white dark:bg-gray-900 border border-red-200 dark:border-red-800 rounded-md p-3 overflow-x-auto whitespace-pre-wrap font-mono leading-relaxed">
                    {result.actual || '(no output)'}
                  </pre>
                </div>
              )}
            </div>
          )}

          {/* Error message */}
          {result.error && (
            <div className="space-y-2">
              <h4 className="text-xs font-semibold text-red-700 dark:text-red-300 uppercase tracking-wide">
                Error Details
              </h4>
              <pre className="text-xs bg-red-50 dark:bg-red-950/50 border border-red-200 dark:border-red-800 rounded-md p-3 overflow-x-auto whitespace-pre-wrap font-mono text-red-700 dark:text-red-300 leading-relaxed">
                {result.error}
              </pre>
            </div>
          )}
        </div>
      )}
    </div>
  );
});

TestResultItem.displayName = 'TestResultItem';

// Test suite group component
const TestSuiteResultGroup = React.memo<{
  suiteResult: TestSuiteResult;
  toggleExpand: (suiteId: string) => void;
}>((props) => {
  const { suiteResult, toggleExpand } = props;

  const passedTests = suiteResult.results.filter((r) => r.success).length;
  const failedTests = suiteResult.results.filter(
    (r) => !r.success && r.status !== 'skipped',
  ).length;
  const skippedTests = suiteResult.results.filter((r) => r.status === 'skipped').length;
  const totalTests = suiteResult.results.length;
  const allPassed = passedTests === totalTests && totalTests > 0;
  const hasFailures = failedTests > 0;

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      toggleExpand(suiteResult.id);
    }
  };

  return (
    <div className="rounded-lg overflow-hidden border border-border shadow-sm">
      <div
        className={cn(
          'flex items-center justify-between p-4 cursor-pointer transition-colors hover:bg-muted/50',
          allPassed
            ? 'bg-green-50/50 dark:bg-green-950/20 hover:bg-green-50 dark:hover:bg-green-950/30'
            : hasFailures
              ? 'bg-red-50/50 dark:bg-red-950/20 hover:bg-red-50 dark:hover:bg-red-950/30'
              : 'bg-muted hover:bg-muted/80',
        )}
        onClick={() => toggleExpand(suiteResult.id)}
        onKeyDown={handleKeyDown}
        role="button"
        tabIndex={0}
        aria-expanded={suiteResult.isExpanded}
        aria-controls={`suite-${suiteResult.id}-content`}
        aria-label={`Test suite: ${suiteResult.name}, ${passedTests} of ${totalTests} tests passing`}
      >
        <div className="flex items-center gap-3 min-w-0 flex-1">
          {suiteResult.isExpanded ? (
            <ChevronDownIcon className="w-4 h-4 text-muted-foreground flex-shrink-0" />
          ) : (
            <ChevronRightIcon className="w-4 h-4 text-muted-foreground flex-shrink-0" />
          )}
          <div className="min-w-0 flex-1">
            <h3 className="font-semibold text-foreground truncate">
              {suiteResult.name || 'Test Results'}
            </h3>
            {suiteResult.duration && (
              <p className="text-xs text-muted-foreground">
                Duration: {formatDuration(suiteResult.duration)}
              </p>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2 flex-shrink-0">
          {/* Status indicators */}
          <div className="flex items-center gap-1">
            {passedTests > 0 && (
              <span className="text-xs bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 px-2 py-1 rounded-full font-medium">
                {passedTests} passed
              </span>
            )}
            {failedTests > 0 && (
              <span className="text-xs bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 px-2 py-1 rounded-full font-medium">
                {failedTests} failed
              </span>
            )}
            {skippedTests > 0 && (
              <span className="text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 px-2 py-1 rounded-full font-medium">
                {skippedTests} skipped
              </span>
            )}
          </div>

          {/* Overall status badge */}
          <span
            className={cn(
              'text-xs font-semibold px-2.5 py-1 rounded-full',
              allPassed
                ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                : hasFailures
                  ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                  : 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300',
            )}
          >
            {Math.round((passedTests / totalTests) * 100)}%
          </span>
        </div>
      </div>

      {suiteResult.isExpanded && (
        <div
          id={`suite-${suiteResult.id}-content`}
          className="p-4 space-y-3 border-t bg-background/50"
          role="region"
          aria-label={`Test results for ${suiteResult.name}`}
        >
          <div role="list" className="space-y-3">
            {suiteResult.results.map((result, idx) => (
              <TestResultItem key={result.id || `test-${idx}`} result={result} index={idx + 1} />
            ))}
          </div>
        </div>
      )}
    </div>
  );
});

TestSuiteResultGroup.displayName = 'TestSuiteResultGroup';

// Enhanced test results summary component
const TestResultsSummary = React.memo<{
  testSuiteResults: TestSuiteResult[];
}>((props) => {
  const { testSuiteResults } = props;

  const allResults = testSuiteResults.flatMap((suite) => suite.results);
  const passedTests = allResults.filter((r) => r.success).length;
  const failedTests = allResults.filter((r) => !r.success && r.status !== 'skipped').length;
  const totalTests = allResults.length;
  const allPassed = passedTests === totalTests && totalTests > 0;
  const hasFailures = failedTests > 0;

  const totalDuration = testSuiteResults.reduce((sum, suite) => sum + (suite.duration || 0), 0);

  if (totalTests === 0) {
    return (
      <div className="p-4 mb-4 rounded-lg border border-gray-200 bg-gray-50 dark:bg-gray-950/30 dark:border-gray-800/50">
        <div className="flex items-center gap-2">
          <AlertCircleIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          <span className="font-medium text-gray-700 dark:text-gray-300">No tests found</span>
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        'p-4 mb-4 rounded-lg border transition-colors',
        allPassed
          ? 'bg-green-50 border-green-200 dark:bg-green-950/30 dark:border-green-800/50'
          : hasFailures
            ? 'bg-red-50 border-red-200 dark:bg-red-950/30 dark:border-red-800/50'
            : 'bg-amber-50 border-amber-200 dark:bg-amber-950/30 dark:border-amber-800/50',
      )}
      role="status"
      aria-live="polite"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {allPassed ? (
            <CheckCircleIcon className="w-6 h-6 text-green-600 dark:text-green-400" />
          ) : hasFailures ? (
            <XCircleIcon className="w-6 h-6 text-red-600 dark:text-red-400" />
          ) : (
            <AlertCircleIcon className="w-6 h-6 text-amber-600 dark:text-amber-400" />
          )}
          <div>
            <h2 className="font-semibold text-md">
              {allPassed
                ? 'All tests passed!'
                : hasFailures
                  ? 'Some tests failed'
                  : 'Tests completed with warnings'}
            </h2>
            <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
              <span>{totalTests} total tests</span>
              {totalDuration > 0 && <span>{formatDuration(totalDuration)} total time</span>}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <div className="text-right">
            <span
              className={cn(
                'text-sm font-bold px-3 py-1.5 rounded-full',
                allPassed
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                  : hasFailures
                    ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                    : 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300',
              )}
            >
              {Math.round((passedTests / totalTests) * 100)}% success
            </span>
          </div>
        </div>
      </div>
    </div>
  );
});

TestResultsSummary.displayName = 'TestResultsSummary';

// Export all components
export {
  TestResultItem,
  TestSuiteResultGroup,
  TestResultsSummary,
  type TestResult,
  type TestSuiteResult,
};
