'use client';
import {
  AlertCircleIcon,
  Loader2Icon,
  PlayIcon,
  TerminalIcon,
  TrashIcon,
  ZapIcon,
} from 'lucide-react';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import type { Assignment } from '@/payload-types';
import { useCodeEditorStore } from '../(protected)/assignments/[aid]/_components/code-editor/store';
import { FasterExecution } from './executions/faster';
import { InteractiveExecution } from './executions/interactive';

interface ExecutionResult {
  output?: string;
  stdout?: string;
  stderr?: string;
  error?: string;
  executionTime?: number;
  memoryUsage?: number;
}

// Define proper type instead of 'any'
interface ServerExecutionResponse {
  output?: string;
  stdout?: string;
  stderr?: string;
  error?: string;
  executionTime?: number;
  time?: number;
  memoryUsage?: number;
  memory?: number;
}

interface OutputTabProps {
  serverUrl: string | undefined;
  serverIsUp: boolean;
  code: string;
  language: Assignment['language'];
  assignmentId: string;
  requiresCommandLineArgs?: boolean | null;
}

const ExecutionStats = memo(
  ({
    time,
    memory,
    lastRunTime,
  }: {
    time?: number;
    memory?: number;
    lastRunTime?: Date | null;
  }) => {
    const formatExecutionTime = useCallback((time: number): string => {
      return time < 1000 ? `${time}ms` : `${(time / 1000).toFixed(2)}s`;
    }, []);

    const formatMemoryUsage = useCallback((memory: number): string => {
      if (memory < 1024) return `${memory}B`;
      if (memory < 1024 * 1024) return `${(memory / 1024).toFixed(1)}KB`;
      return `${(memory / (1024 * 1024)).toFixed(1)}MB`;
    }, []);

    if (!time && !memory && !lastRunTime) return null;

    return (
      <div className="flex flex-wrap gap-2">
        {time && (
          <Badge variant="secondary" className="text-xs font-mono">
            Time: {formatExecutionTime(time)}
          </Badge>
        )}
        {memory && (
          <Badge variant="secondary" className="text-xs font-mono">
            Memory: {formatMemoryUsage(memory)}
          </Badge>
        )}
        Run at:{' '}
        {lastRunTime ? (
          <Badge variant="outline" className="text-xs font-mono">
            {lastRunTime.toLocaleTimeString('en-US', {
              hour: 'numeric',
              minute: 'numeric',
              second: 'numeric',
              hour12: true,
            })}
          </Badge>
        ) : (
          'Not run yet'
        )}
      </div>
    );
  },
);

ExecutionStats.displayName = 'ExecutionStats';

export const OutputTab = memo(
  ({
    serverUrl,
    serverIsUp,
    code,
    language,
    assignmentId,
    requiresCommandLineArgs,
  }: OutputTabProps) => {
    const [runMode, setRunMode] = useState<'interactive' | 'faster'>('interactive');
    const [progress, setProgress] = useState(0);
    const [isRunning, setIsRunning] = useState(false);
    const [error, setError] = useState('');

    const [output, setOutput] = useState('');
    const abortControllerRef = useRef<AbortController | null>(null);
    const isMountedRef = useRef(true); // Track component mount status

    const { getStdin } = useCodeEditorStore();
    const input = getStdin(assignmentId) ?? '';

    const toggleMode = () => {
      setRunMode((prev) => (prev === 'interactive' ? 'faster' : 'interactive'));
    };

    const Icon = runMode === 'interactive' ? TerminalIcon : ZapIcon;
    const tooltipText =
      runMode === 'interactive' ? 'Switch to Faster Mode' : 'Switch to Interactive Mode';

    // Memoize validation function to prevent recreation on every render
    const validateInputs = useMemo(
      () => (): string | null => {
        if (!serverUrl) return 'Server URL not configured';
        if (!serverIsUp) return 'Server is not available';
        if (!code.trim()) return 'No code to execute';
        return null;
      },
      [serverUrl, serverIsUp, code],
    );

    // Memoize canRun to prevent unnecessary recalculations
    const canRun = useMemo(
      () => !isRunning && serverUrl && serverIsUp && code?.trim(),
      [isRunning, serverUrl, serverIsUp, code],
    );

    // Optimized auto-scroll effect
    // useEffect(() => {
    //   if (outputRef.current && output) {
    //     // Use requestAnimationFrame for smoother scrolling
    //     requestAnimationFrame(() => {
    //       if (outputRef.current && isMountedRef.current) {
    //         outputRef.current.scrollTop = outputRef.current.scrollHeight;
    //       }
    //     });
    //   }
    // }, [output]);

    // Progress simulation effect with cleanup
    useEffect(() => {
      if (!isRunning) {
        setProgress(0);
        return;
      }

      const interval = setInterval(() => {
        if (isMountedRef.current) {
          setProgress((prev) => (prev >= 90 ? 90 : prev + 10));
        }
      }, 500);

      return () => clearInterval(interval);
    }, [isRunning]);

    // Cleanup on unmount - Fixed: Track mount status
    useEffect(() => {
      isMountedRef.current = true;
      return () => {
        isMountedRef.current = false;
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
      };
    }, []);

    // Fixed: Proper typing instead of 'any'
    const parseExecutionResult = useCallback(
      (result: ServerExecutionResponse): ExecutionResult => ({
        output: result.output ?? result.stdout ?? '',
        error: result.error ?? result.stderr ?? '',
        executionTime: result.executionTime ?? result.time,
        memoryUsage: result.memoryUsage ?? result.memory,
      }),
      [],
    );

    const runCode = useCallback(async () => {
      const validationError = validateInputs();
      if (validationError) {
        setError(validationError);
        return;
      }

      // For interactive mode, just set isRunning to true
      // The InteractiveExecution component will handle the WebSocket communication
      if (runMode === 'interactive') {
        setIsRunning(true);
        setError('');
        return;
      }

      // For faster mode, continue with HTTP request
      // Cancel previous request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      abortControllerRef.current = new AbortController();

      // Batch state updates
      setIsRunning(true);
      setError('');
      setOutput('');

      try {
        const response = await fetch(`${serverUrl}/run`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            languageId: language,
            input: input,
            code: code,
            useCommandLineArguments: requiresCommandLineArgs,
          }),
          signal: abortControllerRef.current.signal,
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Server error (${response.status}): ${errorText}`);
        }

        const result: ServerExecutionResponse = await response.json();
        const parsedResult = parseExecutionResult(result);

        // Fixed: Check if component is still mounted before updating state
        if (isMountedRef.current) {
          // Batch state updates for better performance
          setOutput(parsedResult.output || '(No output)');
          if (parsedResult.error) setError(parsedResult.error);
          // if (parsedResult.executionTime || parsedResult.memoryUsage) {
          //   setExecutionStats({
          //     time: parsedResult.executionTime,
          //     memory: parsedResult.memoryUsage,
          //   });
          // }
        }
      } catch (err) {
        if (isMountedRef.current) {
          if (err instanceof Error && err.name === 'AbortError') {
            setError('Execution cancelled');
          } else {
            console.error('Code execution error:', err);
            setError(err instanceof Error ? err.message : 'Unknown error occurred');
          }
        }
      } finally {
        if (isMountedRef.current) {
          setIsRunning(false);
        }
        abortControllerRef.current = null;
      }
    }, [
      serverUrl,
      requiresCommandLineArgs,
      code,
      input,
      language,
      runMode,
      validateInputs,
      parseExecutionResult,
    ]);

    const stopExecution = useCallback(() => {
      if (runMode === 'interactive') {
        // For interactive mode, just set isRunning to false
        // The InteractiveExecution component will handle stopping via WebSocket
        setIsRunning(false);
      } else {
        // For faster mode, abort the HTTP request
        abortControllerRef.current?.abort();
      }
    }, [runMode]);

    const clearOutput = useCallback(() => {
      setOutput('');
      setError('');
    }, []);

    // Keyboard shortcut handler
    useEffect(() => {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.ctrlKey && event.key === ';') {
          event.preventDefault();
          if (canRun) {
            runCode();
          }
        }
      };

      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }, [canRun, runCode]);

    return (
      <div className="w-full h-full">
        {runMode === 'interactive' ? (
          <InteractiveExecution
            code={code}
            language={language}
            isRunning={runMode === 'interactive' ? isRunning : false}
            setIsRunning={setIsRunning}
            clearOutput={runMode === 'interactive' && output.trim() === ''}
            serverUrl={serverUrl}
            serverIsUp={serverIsUp}
          />
        ) : (
          <FasterExecution
            assignmentId={assignmentId}
            isRunning={runMode === 'faster' ? isRunning : false}
            setIsRunning={setIsRunning}
            error={error}
            setError={setError}
            isMountedRef={isMountedRef}
            output={output}
            setOutput={setOutput}
            progress={progress}
            setProgress={setProgress}
          />
        )}
      </div>
    );

    return (
      <TooltipProvider>
        <div className={cn(runMode === 'interactive' && 'bg-black')}>
          {/* Sticky Toolbar */}
          <div className="sticky p-2 top-0 z-10 bg-background backdrop-blur-sm border-b pb-2">
            <div className="flex items-center gap-2 justify-between">
              <Tooltip>
                <TooltipTrigger asChild className="">
                  <Button
                    onClick={runCode}
                    disabled={!canRun}
                    className="transition-all duration-200 w-48 h-8 select-none"
                    aria-label={isRunning ? 'Running code' : 'Run code'}
                  >
                    {isRunning ? (
                      <>
                        <Loader2Icon className="w-4 h-4 mr-0.5 animate-spin" />
                      </>
                    ) : (
                      <>
                        <PlayIcon className="w-4 h-4 mr-0.5" />
                        Run
                      </>
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <span className="font-medium">
                    <kbd>Ctrl</kbd> + ;
                  </span>
                </TooltipContent>
              </Tooltip>

              <div className="space-x-2">
                {isRunning && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={stopExecution}
                        variant="outline"
                        size="icon"
                        aria-label="Stop execution"
                        className="transition-all duration-200 size-8 select-none"
                      >
                        <AlertCircleIcon className="w-4 h-4 text-red-600" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Stop code execution</TooltipContent>
                  </Tooltip>
                )}

                {(output || error) && !isRunning && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            className="transition-all duration-200 size-8 select-none"
                            variant="outline"
                            size="icon"
                            aria-label="Clear output"
                          >
                            <TrashIcon className="size-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Clear Output?</AlertDialogTitle>
                            <AlertDialogDescription>
                              This will clear the current output and execution stats. This action
                              cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={clearOutput}>Clear</AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </TooltipTrigger>
                    <TooltipContent>Clear output and stats</TooltipContent>
                  </Tooltip>
                )}

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      className="relative transition-all duration-300 hover:scale-105 hover:shadow-md size-8"
                      onClick={toggleMode}
                    >
                      <Icon
                        className={`w-4 h-4 transition-all duration-300 ${
                          runMode === 'faster' ? 'text-yellow-500' : 'text-blue-500'
                        }`}
                      />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent className="" side="left">
                    <p>{tooltipText}</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </div>

            {/* {!canRun && !isRunning && (
            <p className="text-xs text-muted-foreground flex items-center gap-1 mt-2">
              <InfoIcon className="w-3 h-3" />
              {!serverUrl && 'Server URL not configured'}
              {serverUrl && !serverIsUp && 'Server is not available'}
              {serverUrl && serverIsUp && !code?.trim() && 'Write some code to execute'}
            </p>
          )} */}
          </div>

          {runMode === 'interactive' ? (
            <InteractiveExecution
              code={code}
              language={language}
              isRunning={runMode === 'interactive' ? isRunning : false}
              setIsRunning={setIsRunning}
              clearOutput={runMode === 'interactive' && output.trim() === ''}
              serverUrl={serverUrl}
              serverIsUp={serverIsUp}
            />
          ) : (
            <FasterExecution
              assignmentId={assignmentId}
              isRunning={runMode === 'faster' ? isRunning : false}
              setIsRunning={setIsRunning}
              error={error}
              setError={setError}
              isMountedRef={isMountedRef}
              output={output}
              setOutput={setOutput}
              progress={progress}
              setProgress={setProgress}
            />
          )}
        </div>
      </TooltipProvider>
    );
  },
);

OutputTab.displayName = 'OutputTab';
