'use client';
import { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, RotateCcw, Code2 } from 'lucide-react';

// Enhanced type definitions
interface VariableInfo {
  name: string;
  type: 'int' | 'float' | 'double' | 'char' | 'string' | 'bool' | 'long' | 'short' | 'unknown';
  isArray?: boolean;
  arraySize?: number;
  isPointer?: boolean;
  source: 'declaration' | 'scanf' | 'input';
}

interface ValidationResult {
  isValid: boolean;
  message?: string;
}

interface CCodeInputsProps {
  code: string;
  onInputsChange?: (inputs: Record<string, string>) => void;
  onValidationChange?: (isValid: boolean, errors: string[]) => void;
  className?: string;
}

export function CCodeInputs({
  code,
  onInputsChange,
  onValidationChange,
  className = '',
}: CCodeInputsProps) {
  const [variables, setVariables] = useState<VariableInfo[]>([]);
  const [inputs, setInputs] = useState<Record<string, string>>({});
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Enhanced variable extraction with better regex patterns
  const extractVariables = useCallback((code: string): VariableInfo[] => {
    const variableMap = new Map<string, VariableInfo>();

    // Remove comments and strings to avoid false matches
    const cleanCode = code
      .replace(/\/\*[\s\S]*?\*\//g, ' ') // Remove block comments
      .replace(/\/\/.*$/gm, ' ') // Remove line comments
      .replace(/"[^"]*"/g, '""') // Remove string literals
      .replace(/'[^']*'/g, "''"); // Remove char literals

    // Enhanced declaration patterns
    const patterns = [
      // Standard declarations: int a, b, c;
      /(int|float|double|char|long|short|bool|unsigned\s+int|signed\s+int)\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\s*,\s*[a-zA-Z_][a-zA-Z0-9_]*)*)\s*;/g,

      // Array declarations: int arr[10], char str[100];
      /(int|float|double|char|long|short|bool)\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\[\s*(\d*)\s*\]/g,

      // Pointer declarations: int *ptr, char *str;
      /(int|float|double|char|long|short|bool)\s*\*\s*([a-zA-Z_][a-zA-Z0-9_]*)/g,
    ];

    patterns.forEach((pattern) => {
      let match;
      while ((match = pattern.exec(cleanCode)) !== null) {
        const type = match[1].replace(/\s+/g, ' ').trim();

        if (pattern.toString().includes('\\[')) {
          // Array declaration
          const varName = match[2];
          const arraySize = match[3] ? parseInt(match[3]) : undefined;

          variableMap.set(varName, {
            name: varName,
            type: normalizeType(type),
            isArray: true,
            arraySize,
            source: 'declaration',
          });
        } else if (pattern.toString().includes('\\*')) {
          // Pointer declaration
          const varName = match[2];
          variableMap.set(varName, {
            name: varName,
            type: normalizeType(type),
            isPointer: true,
            source: 'declaration',
          });
        } else {
          // Regular declarations
          const varNames = match[2].split(',').map((v) => v.trim());
          varNames.forEach((name) => {
            variableMap.set(name, {
              name,
              type: normalizeType(type),
              source: 'declaration',
            });
          });
        }
      }
    });

    // Extract from scanf statements with improved pattern
    const scanfPattern = /scanf\s*\(\s*"([^"]+)"\s*,\s*([^)]+)\)/g;
    let scanfMatch;
    while ((scanfMatch = scanfPattern.exec(cleanCode)) !== null) {
      const formatStr = scanfMatch[1];
      const vars = scanfMatch[2].split(',').map((v) => v.trim().replace(/&/, ''));

      vars.forEach((varName) => {
        if (!variableMap.has(varName)) {
          // Try to infer type from format specifier
          const inferredType = inferTypeFromScanf(formatStr);
          variableMap.set(varName, {
            name: varName,
            type: inferredType,
            source: 'scanf',
          });
        }
      });
    }

    return Array.from(variableMap.values()).sort((a, b) => a.name.localeCompare(b.name));
  }, []);

  // Helper function to normalize type names
  const normalizeType = (type: string): VariableInfo['type'] => {
    const normalized = type.toLowerCase().replace(/\s+/g, '');
    switch (normalized) {
      case 'int':
      case 'signedint':
      case 'unsignedint':
        return 'int';
      case 'float':
        return 'float';
      case 'double':
        return 'double';
      case 'char':
        return 'char';
      case 'long':
        return 'long';
      case 'short':
        return 'short';
      case 'bool':
        return 'bool';
      default:
        return 'unknown';
    }
  };

  // Infer type from scanf format specifier
  const inferTypeFromScanf = (formatStr: string): VariableInfo['type'] => {
    if (formatStr.includes('%d') || formatStr.includes('%i')) return 'int';
    if (formatStr.includes('%f')) return 'float';
    if (formatStr.includes('%lf')) return 'double';
    if (formatStr.includes('%c')) return 'char';
    if (formatStr.includes('%s')) return 'string';
    if (formatStr.includes('%ld')) return 'long';
    if (formatStr.includes('%hd')) return 'short';
    return 'unknown';
  };

  // Enhanced input validation
  const validateInput = (variable: VariableInfo, value: string): ValidationResult => {
    if (!value.trim()) {
      return { isValid: true }; // Allow empty values
    }

    switch (variable.type) {
      case 'int':
      case 'long':
      case 'short':
        const intValue = parseInt(value);
        if (isNaN(intValue)) {
          return { isValid: false, message: 'Must be a valid integer' };
        }
        if (variable.type === 'short' && (intValue < -32768 || intValue > 32767)) {
          return { isValid: false, message: 'Short value out of range (-32768 to 32767)' };
        }
        break;

      case 'float':
      case 'double':
        const floatValue = parseFloat(value);
        if (isNaN(floatValue)) {
          return { isValid: false, message: 'Must be a valid number' };
        }
        break;

      case 'char':
        if (value.length > 1) {
          return { isValid: false, message: 'Must be a single character' };
        }
        break;

      case 'bool':
        if (!['true', 'false', '1', '0'].includes(value.toLowerCase())) {
          return { isValid: false, message: 'Must be true/false or 1/0' };
        }
        break;
    }

    return { isValid: true };
  };

  // Get input properties based on variable type
  const getInputProps = (variable: VariableInfo) => {
    const baseProps = {
      placeholder: `Enter ${variable.name}${variable.isArray ? ' (array values)' : ''}`,
      className: validationErrors[variable.name] ? 'border-red-500' : '',
    };

    switch (variable.type) {
      case 'int':
      case 'long':
      case 'short':
        return { ...baseProps, type: 'number', step: '1' };
      case 'float':
      case 'double':
        return { ...baseProps, type: 'number', step: 'any' };
      case 'char':
        return { ...baseProps, type: 'text', maxLength: 1 };
      case 'bool':
        return { ...baseProps, type: 'text', placeholder: 'true/false or 1/0' };
      default:
        return { ...baseProps, type: 'text' };
    }
  };

  // Handle input changes with validation
  const handleInputChange = useCallback(
    (varName: string, value: string) => {
      const variable = variables.find((v) => v.name === varName);
      if (!variable) return;

      const validation = validateInput(variable, value);

      setInputs((prev) => ({ ...prev, [varName]: value }));

      setValidationErrors((prev) => {
        const newErrors = { ...prev };
        if (validation.isValid) {
          delete newErrors[varName];
        } else {
          newErrors[varName] = validation.message || 'Invalid input';
        }
        return newErrors;
      });
    },
    [variables],
  );

  // Reset all inputs
  const resetInputs = useCallback(() => {
    const resetValues = variables.reduce(
      (acc, variable) => {
        acc[variable.name] = '';
        return acc;
      },
      {} as Record<string, string>,
    );

    setInputs(resetValues);
    setValidationErrors({});
    onInputsChange?.(resetValues);
  }, [variables, onInputsChange]);

  // Update variables when code changes
  useEffect(() => {
    const extractedVars = extractVariables(code);
    setVariables(extractedVars);

    // Initialize inputs for new variables
    const newInputs = extractedVars.reduce(
      (acc, variable) => {
        acc[variable.name] = inputs[variable.name] || '';
        return acc;
      },
      {} as Record<string, string>,
    );

    setInputs(newInputs);
    setValidationErrors({});
  }, [code, extractVariables, inputs]);

  // Notify parent of input changes
  useEffect(() => {
    onInputsChange?.(inputs);
  }, [inputs, onInputsChange]);

  // Notify parent of validation changes
  useEffect(() => {
    const errors = Object.values(validationErrors).filter(Boolean);
    const isValid = errors.length === 0;
    onValidationChange?.(isValid, errors);
  }, [validationErrors, onValidationChange]);

  // Memoized stats
  const stats = useMemo(
    () => ({
      total: variables.length,
      byType: variables.reduce(
        (acc, v) => {
          acc[v.type] = (acc[v.type] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>,
      ),
      errors: Object.keys(validationErrors).length,
    }),
    [variables, validationErrors],
  );

  if (variables.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center text-muted-foreground">
            <Code2 className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>No variables detected in the C code</p>
            <p className="text-sm">Add variable declarations or scanf statements</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header with stats */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Code2 className="h-5 w-5" />
              Detected Variables ({stats.total})
            </CardTitle>
            <div className="flex gap-2">
              {Object.entries(stats.byType).map(([type, count]) => (
                <Badge key={type} variant="secondary">
                  {type}: {count}
                </Badge>
              ))}
              {stats.errors > 0 && (
                <Badge variant="destructive">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  {stats.errors} errors
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Input form */}
      <Accordion type="single" collapsible defaultValue="inputs" className="w-full">
        <AccordionItem value="inputs">
          <AccordionTrigger>
            <div className="flex items-center justify-between w-full">
              <span>Input Variables</span>
              <span className="text-sm text-muted-foreground mr-4">
                {variables.length} variables
              </span>
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <div className="space-y-4">
              {variables.map((variable) => (
                <div key={variable.name} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-base font-medium">
                      {variable.name}
                      <div className="flex items-center gap-1 mt-1">
                        <Badge variant="outline" className="text-xs">
                          {variable.type}
                        </Badge>
                        {variable.isArray && (
                          <Badge variant="outline" className="text-xs">
                            array{variable.arraySize ? `[${variable.arraySize}]` : '[]'}
                          </Badge>
                        )}
                        {variable.isPointer && (
                          <Badge variant="outline" className="text-xs">
                            pointer
                          </Badge>
                        )}
                        <Badge variant="outline" className="text-xs">
                          {variable.source}
                        </Badge>
                      </div>
                    </Label>
                  </div>
                  <Input
                    {...getInputProps(variable)}
                    value={inputs[variable.name] || ''}
                    onChange={(e) => handleInputChange(variable.name, e.target.value)}
                  />
                  {validationErrors[variable.name] && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {validationErrors[variable.name]}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      {/* Action buttons */}
      <div className="flex gap-2">
        <Button onClick={resetInputs} variant="outline" className="flex-1">
          <RotateCcw className="h-4 w-4 mr-2" />
          Reset All
        </Button>
      </div>

      {/* Current values preview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Current Values</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="text-sm bg-muted p-3 rounded overflow-x-auto">
            {JSON.stringify(inputs, null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  );
}
