'use client';

import { LogOutIcon } from 'lucide-react';
import LogoutButton from '@/components/dashboard/logout-button';
import { useUser } from '@/hooks/use-user';
import { ThemeToggle } from '@/components/theme-toggle';

export const Header = () => {
  const { user, isLoading } = useUser();

  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <div>
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <p className="text-muted-foreground">
          {isLoading ? 'Loading...' : `Welcome back, ${user && (user.username || user.email)}`}
        </p>
      </div>
      <div className="flex gap-3">
        {/* <Button variant="outline" size="sm" asChild>
          <Link href="/admin/account">
            <UserIcon className="h-4 w-4 mr-1" />
            Account
          </Link>
        </Button> */}
        <ThemeToggle />
        <LogoutButton size="sm" disabled={!user?.id}>
          <LogOutIcon className="h-4 w-4" />
          Logout
        </LogoutButton>
      </div>
    </div>
  );
};
