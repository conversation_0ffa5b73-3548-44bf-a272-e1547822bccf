import { cn } from '@/lib/utils';
import { CheckCircle2, AlertCircle, Clock } from 'lucide-react';

interface StatusIndicatorProps {
  serverIsUp: boolean;
  idle: boolean;
  serverUrl?: string;
  className?: string;
}

export function StatusIndicator({ serverIsUp, serverUrl, idle, className }: StatusIndicatorProps) {
  const noServerUrl = !serverUrl?.trim();

  const statusColorClass = cn(
    idle && 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300',
    !idle &&
      !noServerUrl &&
      serverIsUp &&
      'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300',
    !idle &&
      (!serverIsUp || noServerUrl) &&
      'bg-rose-100 text-rose-800 dark:bg-rose-900/30 dark:text-rose-300',
  );

  return (
    <span
      className={cn(
        'inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium',
        statusColorClass,
        className,
      )}
      role="status"
      aria-live="polite"
    >
      {idle ? (
        <>
          <Clock size={14} className="animate-pulse" />
          <span>Idle</span>
        </>
      ) : noServerUrl ? (
        <>
          <AlertCircle size={14} />
          <span>No Server URL</span>
        </>
      ) : serverIsUp ? (
        <>
          <CheckCircle2 size={14} className="animate-pulse" />
          <span>Connected</span>
        </>
      ) : (
        <>
          <AlertCircle size={14} />
          <span>Not Connected</span>
        </>
      )}
    </span>
  );
}
