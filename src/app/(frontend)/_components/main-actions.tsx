'use client';

import type React from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useUser } from '@/hooks/use-user';
import { ArrowRightIcon, BookOpenIcon, FileTextIcon, UsersIcon, SettingsIcon } from 'lucide-react';
import Link from 'next/link';

export const MainActions = () => {
  const { isLoading, isAdmin, isFaculty } = useUser();

  if (isLoading || (!isAdmin && !isFaculty)) {
    return null;
  }

  return (
    <section className="space-y-8">
      {isFaculty && (
        <div className="space-y-6">
          <div className="space-y-1">
            <h2 className="text-2xl font-semibold tracking-tight">Faculty Tools</h2>
            <p className="text-muted-foreground">Manage courses and assignments</p>
          </div>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <ActionCard
              title="Assignments"
              description="Create and manage assignments"
              href="/admin/collections/assignments"
              buttonText="Manage"
              icon={BookOpenIcon}
            />
            <ActionCard
              title="Submissions"
              description="Review student work"
              href="/admin/collections/submissions"
              buttonText="Review"
              icon={FileTextIcon}
            />
          </div>
        </div>
      )}

      {isAdmin && (
        <>
          {isFaculty && <Separator className="my-8" />}
          <div className="space-y-6">
            <div className="space-y-1">
              <h2 className="text-2xl font-semibold tracking-tight">Administration</h2>
              <p className="text-muted-foreground">System management</p>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <ActionCard
                title="Users"
                description="Manage students and faculty"
                href="/admin/collections/users"
                buttonText="Manage"
                icon={UsersIcon}
              />
              <ActionCard
                title="Tenants"
                description="Configure tenant settings"
                href="/admin/collections/tenants"
                buttonText="Configure"
                icon={SettingsIcon}
              />
            </div>
          </div>
        </>
      )}
    </section>
  );
};

interface ActionCardProps {
  title: string;
  description: string;
  href: string;
  buttonText: string;
  icon: React.ComponentType<{ className?: string }>;
}

function ActionCard({ title, description, href, buttonText, icon: Icon }: ActionCardProps) {
  return (
    <Card className="group relative overflow-hidden transition-all duration-200 hover:shadow-lg hover:-translate-y-1 gap-2">
      <CardHeader className="">
        <div className="flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10 text-primary group-hover:bg-primary group-hover:text-primary-foreground transition-all duration-200 group-hover:scale-110">
            <Icon className="h-5 w-5" />
          </div>
          <div className="space-y-1">
            <CardTitle className="text-lg leading-none group-hover:text-primary transition-colors">
              {title}
            </CardTitle>
            <CardDescription className="text-sm">{description}</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Button asChild size="sm" className="w-full">
          <Link href={href}>
            {buttonText}
            <ArrowRightIcon className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardContent>
    </Card>
  );
}
