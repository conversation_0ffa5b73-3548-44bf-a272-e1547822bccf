import './styles.css';
import React from 'react';
import { SharedLayout } from '@/components/layouts/shared-layout';
import { Metadata } from 'next';
import { companyName } from '@/config/site';

export const metadata: Metadata = {
  title: {
    default: companyName,
    template: `${companyName} | %s`,
  },
  description: 'Next generation CS labs for students and instructors',
};

export default async function RootLayout(props: { children: React.ReactNode }) {
  const { children } = props;

  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <SharedLayout>{children}</SharedLayout>
      </body>
    </html>
  );
}
