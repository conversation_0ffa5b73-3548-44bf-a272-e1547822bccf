'use client';

import {
  <PERSON>ert<PERSON>ircle,
  Award,
  CheckCircle,
  ChevronDown,
  ChevronRight,
  Clock,
  Code2,
  FileText,
  Play,
  XCircle,
} from 'lucide-react';
import { KeyCode } from 'monaco-editor';
import { useParams } from 'next/navigation';
import { useQueryState } from 'nuqs';
import { stringify } from 'qs-esm';
import { useCallback, useEffect, useState } from 'react';
import { BackButton } from '@/components/back-button';
import { CodeEditor } from '@/components/code-editor';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import type { Submission } from '@/payload-types';
import { useCopiedContentStore } from '@/stores/useCopiedContentStore';
import { getColor } from '@/lib/colors';
import { Navbar } from '@/components/dashboard/navbar';

interface TestResult {
  id: string;
  input: string;
  expected: string;
  actual: string;
  status: 'PASS' | 'FAIL';
}

interface SubmissionVersion {
  id: number;
  parent: number;
  version: Submission & {
    testsResult?: TestResult[];
  };
  createdAt: string;
  updatedAt: string;
}

interface SubmissionsResponse {
  docs: Array<SubmissionVersion>;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  limit: number;
  nextPage: number | null;
  page: number;
  pagingCounter: number;
  prevPage: number | null;
  totalDocs: number;
  totalPages: number;
}

export default function SubmissionsPage() {
  const { sid } = useParams();
  const [versions, setVersions] = useState<SubmissionsResponse>();
  const [selectedVersion, setSelectedVersion] = useState<SubmissionVersion | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [curVersionTab, setCurVersionTab] = useQueryState('ver');
  const [curFileTab, setCurFileTab] = useQueryState('file');
  const [expandedTests, setExpandedTests] = useState<Set<string>>(new Set());

  const fetchAllVersions = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const stringifiedQuery = stringify(
        {
          depth: 0,
          select: {
            tenant: false,
          },
          where: {
            parent: {
              equals: sid,
            },
          },
        },
        { addQueryPrefix: true },
      );

      const res = await fetch(
        `/api/submissions/versions/${stringifiedQuery.replaceAll('%5B', '[').replaceAll('%5D', ']')}`,
      );

      if (!res.ok) {
        throw new Error(`Failed to fetch versions: ${res.status}`);
      }

      const data = await res.json();
      setVersions(data);
      if (data.docs.length > 0) {
        setSelectedVersion(data.docs[0]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load submission versions');
    } finally {
      setIsLoading(false);
    }
  }, [sid]);

  useEffect(() => {
    fetchAllVersions();
  }, [fetchAllVersions]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'review':
        return (
          <Badge variant="secondary" className="gap-1">
            <Clock className="h-3 w-3" />
            Under Review
          </Badge>
        );
      case 'graded':
      case 'completed':
        return (
          <Badge variant="default" className="gap-1">
            <CheckCircle className="h-3 w-3" />
            Graded
          </Badge>
        );
      case 'resubmit':
        return (
          <Badge variant="destructive" className="gap-1">
            <XCircle className="h-3 w-3" />
            Resubmit
          </Badge>
        );
      default:
        return <Badge variant="outline">{status || 'Unknown'}</Badge>;
    }
  };

  const toggleTestExpansion = (testId: string) => {
    setExpandedTests((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(testId)) {
        newSet.delete(testId);
      } else {
        newSet.add(testId);
      }
      return newSet;
    });
  };

  const renderTestResults = (testResults: TestResult[]) => {
    if (!testResults || testResults.length === 0) {
      return (
        <div className="text-center py-12 text-muted-foreground">
          <Play className="h-12 w-12 mx-auto mb-3 opacity-50" />
          <p className="font-medium mb-1">No test results available</p>
          <p className="text-sm">Tests haven&apos;t been run for this submission</p>
        </div>
      );
    }

    const passedTests = testResults.filter((test) => test.status === 'PASS');
    const failedTests = testResults.filter((test) => test.status === 'FAIL');

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="font-medium">{passedTests.length} Passed</span>
            </div>
            <div className="flex items-center gap-2">
              <XCircle className="h-4 w-4 text-red-600" />
              <span className="font-medium">{failedTests.length} Failed</span>
            </div>
          </div>
          <div className="text-sm text-muted-foreground">
            {passedTests.length}/{testResults.length} tests passed
          </div>
        </div>

        <Progress value={(passedTests.length / testResults.length) * 100} className="h-2" />

        <div className="space-y-3">
          {testResults.map((test, index) => (
            <Card key={test.id} className="border p-3">
              <Collapsible
                open={expandedTests.has(test.id)}
                onOpenChange={() => toggleTestExpansion(test.id)}
              >
                <CollapsibleTrigger asChild>
                  <CardHeader className="cursor-pointer p-0 flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {test.status === 'PASS' ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-600" />
                      )}
                      <div>
                        <CardTitle className="text-base">Test Case {index + 1}</CardTitle>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge
                        variant={test.status === 'PASS' ? 'default' : 'destructive'}
                        className={cn('text-xs', test.status === 'PASS' && 'bg-green-500')}
                      >
                        {test.status}
                      </Badge>
                      {expandedTests.has(test.id) ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </div>
                  </CardHeader>
                </CollapsibleTrigger>

                <CollapsibleContent className="pt-3">
                  <CardContent className="p-0 space-y-4">
                    <div>
                      <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                        <Play className="h-4 w-4" />
                        Input
                      </h4>
                      <pre className="bg-muted p-3 rounded-md font-mono text-sm overflow-scroll">
                        {test.input || 'No input provided'}
                      </pre>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="text-sm font-medium mb-2">Expected Output</h4>
                        <pre className="bg-muted p-3 rounded-md font-mono text-sm overflow-scroll">
                          {test.expected}
                        </pre>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium mb-2">Your Output</h4>
                        <pre className="bg-muted p-3 rounded-md font-mono text-sm overflow-scroll">
                          {test.actual}
                        </pre>
                      </div>
                    </div>
                    {test.status === 'FAIL' && (
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                          Output doesn&apos;t match expected result. Check your logic and try again.
                        </AlertDescription>
                      </Alert>
                    )}
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  const CodeFiles = ({
    aid,
    solutionCode,
  }: {
    aid: string;
    solutionCode: {
      html?: string | null;
      css?: string | null;
      js?: string | null;
      java?: string | null;
      c?: string | null;
    };
  }) => {
    const { copiedContent, resetCopied } = useCopiedContentStore();
    const files = [];
    if (solutionCode?.html)
      files.push({
        name: 'index.html',
        content: solutionCode?.html,
        language: 'html',
      });
    if (solutionCode?.css)
      files.push({
        name: 'styles.css',
        content: solutionCode?.css,
        language: 'css',
      });
    if (solutionCode?.js)
      files.push({
        name: 'script.js',
        content: solutionCode?.js,
        language: 'javascript',
      });
    if (solutionCode?.java)
      files.push({
        name: 'Main.java',
        content: solutionCode?.java,
        language: 'java',
      });
    if (solutionCode?.c) files.push({ name: 'main.c', content: solutionCode?.c, language: 'c' });

    const getEditorTabKey = useCallback((tabName: string) => `${aid}-${tabName}`, [aid]);

    if (files.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <Code2 className="h-16 w-16 text-muted-foreground/50 mb-4" />
          <h3 className="text-lg font-medium mb-2">No code submitted</h3>
          <p className="text-sm text-muted-foreground">
            This submission doesn&apos;t contain any code files.
          </p>
        </div>
      );
    }

    return (
      <Tabs
        value={curFileTab || files[0].name}
        onValueChange={(val) => setCurFileTab(val)}
        className="w-full"
      >
        {files.length > 1 && (
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
            {files.map((file) => (
              <TabsTrigger key={file.name} value={file.name} className="text-xs">
                {file.name}
              </TabsTrigger>
            ))}
          </TabsList>
        )}
        {files.map((file) => (
          <TabsContent key={file.name} value={file.name} className={cn(files.length > 1 && 'mt-3')}>
            <CodeEditor
              options={{
                mouseWheelZoom: true,
                minimap: { enabled: false },
                scrollBeyondLastLine: false,
              }}
              onChange={() => {}}
              value={file.content}
              language={file.language}
              readOnly
              height="500px"
              onMount={(editor) => {
                editor.focus();

                // Track copy operations to maintain allowed clipboard content
                editor.onKeyDown((event) => {
                  const { keyCode, ctrlKey, metaKey } = event;
                  const isModifierPressed = ctrlKey || metaKey;

                  // Handle copy operation - track what's being copied
                  if (keyCode === KeyCode.KeyC && isModifierPressed) {
                    const selection = editor.getSelection();
                    if (selection && !selection.isEmpty()) {
                      const selectedText = editor.getModel()?.getValueInRange(selection);
                      if (selectedText) {
                        const editorKey = getEditorTabKey(curFileTab ?? '');
                        if (!copiedContent.has(editorKey)) {
                          copiedContent.set(editorKey, new Set());
                          resetCopied();
                        }
                        copiedContent.get(editorKey)?.add(selectedText);
                      }
                    }
                  }

                  // Track cut operation
                  if (keyCode === KeyCode.KeyX && isModifierPressed) {
                    const selection = editor.getSelection();
                    if (selection && !selection.isEmpty()) {
                      const selectedText = editor.getModel()?.getValueInRange(selection);
                      if (selectedText) {
                        const editorKey = getEditorTabKey(curFileTab ?? '');
                        if (!copiedContent.has(editorKey)) {
                          copiedContent.set(editorKey, new Set());
                          resetCopied();
                        }
                        copiedContent.get(editorKey)?.add(selectedText);
                      }
                    }
                  }

                  // Handle paste operation - validate against tracked content
                  if (keyCode === KeyCode.KeyV && isModifierPressed) {
                    event.preventDefault();

                    // Only attempt clipboard access if `window` is defined (client-side)
                    if (typeof window !== 'undefined') {
                      navigator.clipboard
                        .readText()
                        .then((clipboardText) => {
                          if (clipboardText) {
                            const editorKey = getEditorTabKey(curFileTab ?? '');
                            const allowedContent = copiedContent.get(editorKey);

                            // Check if the clipboard content was copied from this editor tab
                            if (allowedContent && allowedContent.has(clipboardText)) {
                              // Allow paste by inserting the text at cursor position
                              const position = editor.getPosition();
                              if (position) {
                                editor.executeEdits('paste', [
                                  {
                                    range: {
                                      startLineNumber: position.lineNumber,
                                      startColumn: position.column,
                                      endLineNumber: position.lineNumber,
                                      endColumn: position.column,
                                    },
                                    text: clipboardText,
                                  },
                                ]);
                              }
                            }
                            // If content wasn't copied from this tab, silently ignore (block paste)
                          }
                        })
                        .catch(() => {
                          // Clipboard access failed, ignore
                        });
                    }
                  }
                });
              }}
            />
          </TabsContent>
        ))}
      </Tabs>
    );
  };

  const getCurrentVersionStats = () => {
    if (!selectedVersion) return null;

    const passedTests =
      selectedVersion.version.testsResult?.filter((test) => test.status === 'PASS')?.length || 0;
    const totalTests = selectedVersion.version.testsResult?.length || 0;
    const score = selectedVersion.version.score;
    const status = selectedVersion.version.status;

    return { passedTests, totalTests, score, status };
  };

  if (error) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Card>
          <CardContent className="pt-6">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
            <Button onClick={fetchAllVersions} className="w-full mt-4">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Card>
          <CardHeader className="border-b">
            <div className="flex items-center">
              <Skeleton className="h-10 w-10 mr-4" />
              <div>
                <Skeleton className="h-7 w-64 mb-2" />
                <Skeleton className="h-4 w-32" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-96 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const stats = getCurrentVersionStats();

  return (
    <>
      <Navbar Title={`Submission #${sid}`} />

      <div className="container mx-auto py-8 px-4 max-w-7xl">
        <Card>
          <CardHeader className="border-b">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <BackButton />
                <div>
                  <h1 className="text-2xl font-bold">Submission History</h1>
                  <p className="text-muted-foreground">Submission #{sid}</p>
                </div>
              </div>

              {/* Version selector and stats moved to the right */}
              <div className="flex items-center gap-6">
                {stats && (
                  <div className="flex items-center gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <span className="text-muted-foreground">Tests:</span>
                      <Badge variant="outline" className="text-xs">
                        {stats.passedTests}/{stats.totalTests}
                      </Badge>
                    </div>
                    {stats.score !== null && (
                      <div className="flex items-center gap-2">
                        <span className="text-muted-foreground">Score:</span>
                        <Badge
                          variant="outline"
                          className="text-xs"
                          style={{
                            color: getColor(stats.score),
                          }}
                        >
                          {stats.score}%
                        </Badge>
                      </div>
                    )}
                    <div className="flex items-center gap-2">
                      <span className="text-muted-foreground">Status:</span>
                      {getStatusBadge(stats.status as string)}
                    </div>
                  </div>
                )}

                {versions && versions.docs.length > 0 && (
                  <Select
                    value={curVersionTab || selectedVersion?.id.toString()}
                    onValueChange={(value) => {
                      const version = versions.docs.find((v) => v.id.toString() === value);
                      if (version) setSelectedVersion(version);
                      setCurVersionTab(value);
                    }}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="Version" />
                    </SelectTrigger>
                    <SelectContent>
                      {versions.docs.map((version, index) => (
                        <SelectItem key={version.id} value={version.id.toString()}>
                          <div className="flex items-center gap-2">
                            <span>v{versions.docs.length - index}</span>
                            {index === 0 && (
                              <Badge variant="outline" className="text-xs px-1 py-0">
                                Latest
                              </Badge>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>
          </CardHeader>

          {selectedVersion ? (
            <CardContent className="p-6">
              <Tabs defaultValue="code" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="code" className="flex items-center gap-2">
                    <Code2 className="h-4 w-4" />
                    Code
                  </TabsTrigger>
                  <TabsTrigger value="tests" className="flex items-center gap-2">
                    <Play className="h-4 w-4" />
                    Test Results
                  </TabsTrigger>
                  <TabsTrigger value="feedback" className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Feedback
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="code" className="mt-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Code2 className="h-5 w-5" />
                        Solution Code
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CodeFiles
                        aid={String(selectedVersion?.version?.assignment) ?? ''}
                        solutionCode={{
                          html: selectedVersion?.version?.html,
                          css: selectedVersion?.version?.css,
                          js: selectedVersion?.version?.js,
                          java: selectedVersion?.version?.java,
                          c: selectedVersion?.version?.c,
                        }}
                      />
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="tests" className="mt-6">
                  <Card>
                    <CardContent>
                      {renderTestResults(selectedVersion.version.testsResult || [])}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="feedback" className="mt-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Award className="h-5 w-5" />
                          Performance Summary
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="text-center p-4 bg-muted/30 rounded-lg">
                            <div
                              className="text-2xl font-bold mb-1"
                              style={{
                                color: getColor(selectedVersion.version.score),
                              }}
                            >
                              {selectedVersion.version.score !== null
                                ? `${selectedVersion.version.score}%`
                                : 'N/A'}
                            </div>
                            <div className="text-sm text-muted-foreground">Score</div>
                          </div>
                          <div className="text-center p-4 bg-muted/30 rounded-lg">
                            {(() => {
                              // Calculate score based on passed and total tests
                              const totalTests = stats?.totalTests ?? 0;
                              const passedTests = stats?.passedTests ?? 0;
                              const score = totalTests > 0 ? (passedTests / totalTests) * 100 : 0; // Avoid division by zero

                              return (
                                <div
                                  className="text-2xl font-bold mb-1"
                                  style={{
                                    color: getColor(score),
                                  }}
                                >
                                  {stats?.passedTests || 0}/{stats?.totalTests || 0}
                                </div>
                              );
                            })()}
                            <div className="text-sm text-muted-foreground">Tests Passed</div>
                          </div>
                        </div>

                        <div className="space-y-3 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Status</span>
                            {getStatusBadge(selectedVersion.version.status as string)}
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Submitted</span>
                            <span className="font-medium">
                              {formatDate(selectedVersion.version.createdAt)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Last Updated</span>
                            <span className="font-medium">
                              {formatDate(selectedVersion.version.updatedAt)}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <FileText className="h-5 w-5" />
                          Instructor Feedback
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="text-justify">
                        {selectedVersion.version.feedback ? (
                          <p className="leading-relaxed">{selectedVersion.version.feedback}</p>
                        ) : (
                          <div className="text-center py-8 text-muted-foreground">
                            <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <h3 className="font-medium mb-2">No feedback yet</h3>
                            <p className="text-sm">
                              {selectedVersion.version.status === 'review'
                                ? 'Your submission is being reviewed. Feedback will be provided soon.'
                                : 'No feedback has been provided for this submission.'}
                            </p>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          ) : (
            <CardContent className="text-center py-16">
              <FileText className="h-16 w-16 mx-auto mb-4 text-muted-foreground/50" />
              <h3 className="text-lg font-medium mb-2">No submissions found</h3>
              <p className="text-muted-foreground">
                This submission doesn&apos;t have any versions available.
              </p>
            </CardContent>
          )}
        </Card>
      </div>
    </>
  );
}
