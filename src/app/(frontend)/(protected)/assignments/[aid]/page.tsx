'use client';

import dynamic from 'next/dynamic';
import { notFound, useParams } from 'next/navigation';
import { useAssignment } from '@/hooks/use-assignment';
import { useUser } from '@/hooks/use-user';
import Loading from './loading';

const ResizableLayout = dynamic(
  () => import('./_components/playground').then((mod) => mod.ResizableLayout),
  { ssr: false, loading: () => <Loading /> },
);
const AssignmentPanel = dynamic(
  () => import('./_components/assignment-panel').then((mod) => mod.default),
  { ssr: false },
);
const RightPanel = dynamic(
  () => import('@/app/(frontend)/_components/right-top-panel').then((mod) => mod.RightTopPanel),
  { ssr: false },
);

export default function AssignmentPage() {
  const { aid } = useParams();
  const { assignment, isLoading, error } = useAssignment(String(aid));
  const { isAdmin, isFaculty, user } = useUser();

  // const testCases = useMemo(() => {
  //   if (!assignment) return undefined;
  //   return assignment.language === 'java' ? assignment.javaTestCases : assignment.cTestCases;
  // }, [assignment]);

  if (isLoading || !assignment) {
    return <Loading />;
  }

  if (error?.toLowerCase().includes('not found')) {
    notFound();
  }

  return (
    <ResizableLayout
      leftPanel={<AssignmentPanel assignment={assignment} />}
      rightPanel={
        <RightPanel
          language={assignment.language}
          starterCode={isFaculty || isAdmin ? assignment.solutionCode : assignment.starterCode}
          user={user}
        />
      }
    />
  );
}
