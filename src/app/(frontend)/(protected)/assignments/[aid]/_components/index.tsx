'use client';

import { DashboardLayout } from '@/components/dashboard-layout';
import { MonacoEditor } from '@/components/monaco-editor';
import {
  CheckCircleIcon,
  XCircleIcon,
  PlayIcon,
  SendIcon,
  AlertTriangleIcon,
  MaximizeIcon,
  ArrowLeftIcon,
  CalendarIcon,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatDate } from '@/lib/date-utils';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

// This would normally be fetched from an API
const fetchAssignment = async (_id: string) => {
  // Simulating API call with the provided data
  return {
    id: 1,
    title: 'Build Your First Webpage',
    description: 'Create a basic webpage using HTML and CSS with proper semantic structure.',
    instructions:
      '- Use at least 3 different semantic HTML tags (header, nav, section, article, footer)\n- Add a navigation menu with at least 3 links\n- Include a hero section with a heading and paragraph\n- Style the page using CSS variables for colors and fonts\n- Make the layout responsive with media queries',
    dueDate: '2025-04-15T00:00:00.000Z',
    isActive: true,
    starterCode: {
      html: '<header>\n  <h1>My First Webpage</h1>\n  <nav>\n    <!-- Add your navigation here -->\n  </nav>\n</header>\n\n<main>\n  <!-- Add your content here -->\n</main>\n\n<footer>\n  <!-- Add your footer here -->\n</footer>',
      css: ":root {\n  --primary-color: #3498db;\n  --secondary-color: #2ecc71;\n  --text-color: #333;\n  --bg-color: #f9f9f9;\n  --font-main: 'Arial', sans-serif;\n}\n\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n/* Add your styles here */",
      js: '// Optional JavaScript code can be added here',
    },
    solutionCode: {
      html: null,
      css: null,
      js: null,
    },
    submissions: {
      docs: [
        {
          id: 1,
          tenant: 1,
          student: 3,
          assignment: 1,
          feedback: null,
          solutionCode: {
            html: '<div>LoL</div>',
            css: 'body { background-color: red; }',
            js: "console.log('LoL')",
          },
          score: 100,
          passedTestCases: 10,
          failedTestCases: 0,
          updatedAt: '2025-04-15T05:04:42.471Z',
          createdAt: '2025-04-15T05:04:42.448Z',
        },
      ],
      hasNextPage: false,
    },
    updatedAt: '2025-04-15T05:04:42.391Z',
    createdAt: '2023-05-01T00:00:00.000Z',
  };
};

// Mock test cases based on the instructions
const generateTestCases = (instructions: string) => {
  const lines = instructions.split('\n');
  return lines
    .map((line, index) => {
      if (!line.trim().startsWith('-')) return null;

      const title = line.trim().substring(2);
      return {
        id: index + 1,
        title,
        status: 'pending' as 'pending' | 'passed' | 'failed',
      };
    })
    .filter(Boolean);
};

type TestResult = {
  id: number;
  title: string;
  status: 'pending' | 'passed' | 'failed';
  score: number;
};

export default function PlaygroundPage() {
  const params = useParams();
  const id = params.id as string;

  const [assignment, setAssignment] = useState<{
    id: number;
    title: string;
    description: string;
    instructions: string;
    dueDate: string;
    isActive: boolean;
    starterCode: {
      html: string;
      css: string;
      js: string;
    };
    solutionCode: {
      html: null;
      css: null;
      js: null;
    };
    updatedAt: string;
    createdAt: string;
  }>();
  const [loading, setLoading] = useState(true);
  const [htmlCode, setHtmlCode] = useState('');
  const [cssCode, setCssCode] = useState('');
  const [jsCode, setJsCode] = useState('');
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [previewKey, setPreviewKey] = useState(0);
  const [activeTab, setActiveTab] = useState('preview');
  const [zenMode, setZenMode] = useState(false);
  const [editorLayout, setEditorLayout] = useState<'horizontal' | 'vertical'>('horizontal');

  // Fetch assignment data
  useEffect(() => {
    const loadAssignment = async () => {
      try {
        const data = await fetchAssignment(id);
        setAssignment(data);

        // Initialize code editors with starter code
        setHtmlCode(data.starterCode.html || '');
        setCssCode(data.starterCode.css || '');
        setJsCode(data.starterCode.js || '');

        // Generate test cases from instructions
        const tests = generateTestCases(data.instructions);
        setTestResults(tests.filter((test): test is TestResult => test !== null));

        setLoading(false);
      } catch (error) {
        console.error('Error loading assignment:', error);
        setLoading(false);
      }
    };

    loadAssignment();
  }, [id]);

  const runTests = () => {
    // Simulate running tests
    const results = testResults.map((test) => ({
      ...test,
      status: (Math.random() > 0.3 ? 'passed' : 'failed') as 'passed' | 'failed',
    }));
    setTestResults(results);
    setActiveTab('tests');
  };

  const refreshPreview = () => {
    setPreviewKey((prev) => prev + 1);
  };

  const submitAssignment = () => {
    // Simulate submitting assignment
    alert('Assignment submitted successfully!');
  };

  const toggleZenMode = () => {
    setZenMode(!zenMode);
  };

  const toggleEditorLayout = () => {
    setEditorLayout(editorLayout === 'horizontal' ? 'vertical' : 'horizontal');
  };

  const combinedCode = `
    <html>
      <head>
        <style>${cssCode}</style>
      </head>
      <body>
        ${htmlCode}
        <script>${jsCode}</script>
      </body>
    </html>
  `;

  // Exit zen mode with Escape key
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && zenMode) {
        setZenMode(false);
      }
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('keydown', handleKeyDown);
      return () => window.removeEventListener('keydown', handleKeyDown);
    }
  }, [zenMode]);

  if (loading) {
    return (
      <DashboardLayout userRole="student">
        <div className="flex items-center justify-center h-[calc(100vh-2rem)]">
          <div className="animate-pulse flex flex-col items-center">
            <div className="h-8 w-64 bg-gray-200 rounded mb-4"></div>
            <div className="h-4 w-48 bg-gray-200 rounded"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!assignment) {
    return (
      <DashboardLayout userRole="student">
        <div className="flex flex-col items-center justify-center h-[calc(100vh-2rem)]">
          <h2 className="text-2xl font-bold mb-4">Assignment Not Found</h2>
          <p className="text-muted-foreground mb-6">
            The assignment you&apos;re looking for doesn&apos;t exist or you don&apos;t have access
            to it.
          </p>
          <Button asChild>
            <Link href="/student">Return to Dashboard</Link>
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  if (zenMode) {
    return (
      <div className="fixed inset-0 bg-background z-50 flex flex-col">
        <div className="flex items-center justify-between p-2 border-b">
          <div className="flex items-center">
            <Button variant="ghost" size="sm" onClick={() => setZenMode(false)} className="mr-2">
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Exit Zen Mode
            </Button>
            <h2 className="text-sm font-medium">{assignment.title}</h2>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={toggleEditorLayout}>
              {editorLayout === 'horizontal' ? 'Vertical Layout' : 'Horizontal Layout'}
            </Button>
            <Button variant="outline" size="sm" onClick={refreshPreview}>
              <PlayIcon className="w-4 h-4 mr-2" />
              Run
            </Button>
            <Button variant="outline" size="sm" onClick={runTests}>
              <AlertTriangleIcon className="w-4 h-4 mr-2" />
              Test
            </Button>
            <Button size="sm" onClick={submitAssignment}>
              <SendIcon className="w-4 h-4 mr-2" />
              Submit
            </Button>
          </div>
        </div>

        <div
          className={cn(
            'flex flex-1 overflow-hidden',
            editorLayout === 'horizontal' ? 'flex-col' : 'flex-row',
          )}
        >
          <div
            className={cn(
              'flex overflow-hidden',
              editorLayout === 'horizontal' ? 'flex-row h-1/2' : 'flex-col w-1/2',
            )}
          >
            <Tabs defaultValue="html" className="flex-1 flex flex-col overflow-hidden">
              <TabsList className="mx-1">
                <TabsTrigger value="html">HTML</TabsTrigger>
                <TabsTrigger value="css">CSS</TabsTrigger>
                <TabsTrigger value="js">JavaScript</TabsTrigger>
                <TabsTrigger value="instructions">Instructions</TabsTrigger>
              </TabsList>
              <TabsContent value="html" className="flex-1 overflow-hidden p-0 m-0">
                <MonacoEditor language="html" value={htmlCode} onChange={setHtmlCode} />
              </TabsContent>
              <TabsContent value="css" className="flex-1 overflow-hidden p-0 m-0">
                <MonacoEditor language="css" value={cssCode} onChange={setCssCode} />
              </TabsContent>
              <TabsContent value="js" className="flex-1 overflow-hidden p-0 m-0">
                <MonacoEditor language="javascript" value={jsCode} onChange={setJsCode} />
              </TabsContent>
              <TabsContent value="instructions" className="flex-1 overflow-auto p-4 m-0">
                <div className="prose max-w-none">
                  <h2>Instructions</h2>
                  <div className="whitespace-pre-line">{assignment.instructions}</div>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          <div
            className={cn(
              'flex overflow-hidden',
              editorLayout === 'horizontal' ? 'h-1/2' : 'w-1/2',
            )}
          >
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="flex-1 flex flex-col overflow-hidden"
            >
              <TabsList className="mx-1">
                <TabsTrigger value="preview">Preview</TabsTrigger>
                <TabsTrigger value="tests">Tests</TabsTrigger>
              </TabsList>
              <TabsContent value="preview" className="flex-1 overflow-hidden p-0 m-0">
                <div className="w-full h-full bg-white border rounded-md overflow-hidden">
                  <iframe
                    key={previewKey}
                    srcDoc={combinedCode}
                    title="preview"
                    className="w-full h-full"
                    sandbox="allow-scripts"
                  />
                </div>
              </TabsContent>
              <TabsContent value="tests" className="flex-1 overflow-auto p-4 m-0">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle>Test Results</CardTitle>
                    <CardDescription>
                      {testResults.filter((t) => t.status === 'passed').length} of{' '}
                      {testResults.length} tests passing
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {testResults.map((test) => (
                        <div key={test.id} className="flex items-center p-2 rounded-md bg-muted/50">
                          {test.status === 'passed' ? (
                            <CheckCircleIcon className="w-5 h-5 text-green-500 mr-2" />
                          ) : test.status === 'failed' ? (
                            <XCircleIcon className="w-5 h-5 text-red-500 mr-2" />
                          ) : (
                            <div className="w-5 h-5 rounded-full border-2 border-muted-foreground mr-2" />
                          )}
                          <span>{test.title}</span>
                          <Badge
                            variant={
                              test.status === 'passed'
                                ? 'default'
                                : test.status === 'failed'
                                  ? 'destructive'
                                  : 'outline'
                            }
                            className="ml-auto"
                          >
                            {test.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    );
  }

  return (
    <DashboardLayout userRole="student">
      <div className="flex flex-col h-[calc(100vh-2rem)]">
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <div>
              <Link
                href="/student"
                className="text-muted-foreground hover:text-foreground flex items-center mb-2"
              >
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back to Assignments
              </Link>
              <h1 className="text-2xl font-bold">{assignment.title}</h1>
              <p className="text-muted-foreground">{assignment.description}</p>
            </div>
            <div className="flex flex-col items-end">
              <div className="flex items-center text-sm text-muted-foreground mb-2">
                <CalendarIcon className="h-4 w-4 mr-1" />
                <span>Due: {formatDate(assignment.dueDate)}</span>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={toggleZenMode}>
                  <MaximizeIcon className="w-4 h-4 mr-2" />
                  Zen Mode
                </Button>
                <Button variant="outline" onClick={refreshPreview}>
                  <PlayIcon className="w-4 h-4 mr-2" />
                  Run
                </Button>
                <Button variant="outline" onClick={runTests}>
                  <AlertTriangleIcon className="w-4 h-4 mr-2" />
                  Test
                </Button>
                <Button onClick={submitAssignment}>
                  <SendIcon className="w-4 h-4 mr-2" />
                  Submit
                </Button>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 flex-1 overflow-hidden">
          <div className="flex flex-col overflow-hidden">
            <Tabs defaultValue="html" className="flex-1 flex flex-col overflow-hidden">
              <TabsList className="mx-1">
                <TabsTrigger value="html">HTML</TabsTrigger>
                <TabsTrigger value="css">CSS</TabsTrigger>
                <TabsTrigger value="js">JavaScript</TabsTrigger>
                <TabsTrigger value="instructions">Instructions</TabsTrigger>
              </TabsList>
              <TabsContent value="html" className="flex-1 overflow-hidden p-0 m-0">
                <MonacoEditor language="html" value={htmlCode} onChange={setHtmlCode} />
              </TabsContent>
              <TabsContent value="css" className="flex-1 overflow-hidden p-0 m-0">
                <MonacoEditor language="css" value={cssCode} onChange={setCssCode} />
              </TabsContent>
              <TabsContent value="js" className="flex-1 overflow-hidden p-0 m-0">
                <MonacoEditor language="javascript" value={jsCode} onChange={setJsCode} />
              </TabsContent>
              <TabsContent value="instructions" className="flex-1 overflow-auto p-4 m-0">
                <div className="prose max-w-none">
                  <h2>Instructions</h2>
                  <div className="whitespace-pre-line">{assignment.instructions}</div>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          <div className="flex flex-col overflow-hidden">
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="flex-1 flex flex-col overflow-hidden"
            >
              <TabsList className="mx-1">
                <TabsTrigger value="preview">Preview</TabsTrigger>
                <TabsTrigger value="tests">Tests</TabsTrigger>
              </TabsList>
              <TabsContent value="preview" className="flex-1 overflow-hidden p-0 m-0">
                <div className="w-full h-full bg-white border rounded-md overflow-hidden">
                  <iframe
                    key={previewKey}
                    srcDoc={combinedCode}
                    title="preview"
                    className="w-full h-full"
                    sandbox="allow-scripts"
                  />
                </div>
              </TabsContent>
              <TabsContent value="tests" className="flex-1 overflow-auto p-4 m-0">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle>Test Results</CardTitle>
                    <CardDescription>
                      {testResults.filter((t) => t.status === 'passed').length} of{' '}
                      {testResults.length} tests passing
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {testResults.map((test) => (
                        <div key={test.id} className="flex items-center p-2 rounded-md bg-muted/50">
                          {test.status === 'passed' ? (
                            <CheckCircleIcon className="w-5 h-5 text-green-500 mr-2" />
                          ) : test.status === 'failed' ? (
                            <XCircleIcon className="w-5 h-5 text-red-500 mr-2" />
                          ) : (
                            <div className="w-5 h-5 rounded-full border-2 border-muted-foreground mr-2" />
                          )}
                          <span>{test.title}</span>
                          <Badge
                            variant={
                              test.status === 'passed'
                                ? 'default'
                                : test.status === 'failed'
                                  ? 'destructive'
                                  : 'outline'
                            }
                            className="ml-auto"
                          >
                            {test.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
