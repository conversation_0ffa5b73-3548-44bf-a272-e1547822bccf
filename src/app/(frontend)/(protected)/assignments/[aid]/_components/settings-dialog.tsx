'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, PanelLeft, PanelTop, RotateCcw, Settings2, Trash2 } from 'lucide-react';
import { useParams } from 'next/navigation';
import type React from 'react';
import { useState } from 'react';
import { ThemeToggle } from '@/components/theme-toggle';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@/components/ui/drawer';

import { ScrollArea } from '@/components/ui/scroll-area';
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { useCodeEditorStore } from './code-editor/store';
import { FontSizeControl } from './font-size-control';

interface SettingsDrawerProps {
  leftPanelVisible: boolean;
  rightPanelVisible: boolean;
  fontSize: number;
  setLeftPanelVisible: (value: boolean) => void;
  setRightPanelVisible: (value: boolean) => void;
  setFontSize: (value: number) => void;
  resetLayout: () => void;
}

interface ToggleCardProps {
  title: string;
  icon: React.ReactNode;
  isActive: boolean;
  onClick: () => void;
}

export function SettingsDrawer({
  leftPanelVisible,
  rightPanelVisible,
  fontSize,
  setLeftPanelVisible,
  setRightPanelVisible,
  setFontSize,
  resetLayout,
}: SettingsDrawerProps) {
  const [open, setOpen] = useState(false);
  const { aid } = useParams();

  return (
    <Drawer direction="right" open={open} onOpenChange={setOpen}>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <DrawerTrigger asChild>
              <Button
                size="icon"
                className="fixed top-6 right-6 z-[9] size-8 rounded-full shadow-lg transition-all duration-200 hover:shadow-xl bg-primary text-primary-foreground border border-secondary"
                aria-label="Open settings drawer"
              >
                <Settings2 size={18} />
              </Button>
            </DrawerTrigger>
          </TooltipTrigger>
          <TooltipContent side="left">
            <p>Customize Layout</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <DrawerContent className="flex max-w-md flex-col border-l">
        <DrawerHeader className="shrink-0 border-b px-6 py-4">
          <DrawerTitle className="text-xl font-semibold">Customize Layout</DrawerTitle>
          <DrawerDescription>Customize your workspace layout and appearance</DrawerDescription>
        </DrawerHeader>

        {/* <ScrollArea className="flex-grow px-6 py-4"> */}
        <ScrollArea className="px-4 overflow-y-auto flex-grow flex-1 space-y-6 overflow-hidden">
          <Accordion type="single" collapsible defaultValue="panels" className="w-full">
            <AccordionItem value="panels">
              <AccordionTrigger className="text-base font-medium">
                Panel Visibility
              </AccordionTrigger>
              <AccordionContent>
                <PanelVisibilitySection
                  leftPanelVisible={leftPanelVisible}
                  rightPanelVisible={rightPanelVisible}
                  setLeftPanelVisible={setLeftPanelVisible}
                  setRightPanelVisible={setRightPanelVisible}
                />
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="appearance">
              <AccordionTrigger className="text-base font-medium">Appearance</AccordionTrigger>
              <AccordionContent>
                <div className="space-y-4">
                  <FontSizeControl defaultValue={fontSize} onChange={setFontSize} />
                  <ThemeToggle varient="large" />
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="actions">
              <AccordionTrigger className="text-base font-medium">Actions</AccordionTrigger>
              <AccordionContent>
                <ActionsSection id={aid?.toString() ?? ''} />
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </ScrollArea>

        <DrawerFooter className="shrink-0 border-t px-6 py-4">
          <div className="flex flex-col sm:flex-row gap-3 w-full">
            <Button
              variant="outline"
              className="flex-1 flex items-center justify-center gap-2"
              onClick={resetLayout}
            >
              <RotateCcw size={16} />
              Reset Layout
            </Button>
            <Button
              className="flex-1"
              onClick={() => {
                setOpen(false);
              }}
            >
              Close
            </Button>
          </div>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}

function ToggleCard({ title, icon, isActive, onClick }: ToggleCardProps) {
  return (
    <Card
      className={cn(
        'flex flex-col items-center justify-center p-3 cursor-pointer transition-all duration-200 hover:shadow-md',
        isActive
          ? 'bg-primary/10 border-primary shadow-sm'
          : 'bg-background border-muted hover:border-muted-foreground/20',
      )}
      onClick={onClick}
    >
      <div className={cn('p-2 rounded-full mb-1', isActive ? 'bg-primary/20' : 'bg-muted')}>
        <div className={cn(isActive ? 'text-primary' : 'text-muted-foreground')}>{icon}</div>
      </div>
      <span
        className={cn('text-xs font-medium', isActive ? 'text-primary' : 'text-muted-foreground')}
      >
        {title}
      </span>
    </Card>
  );
}

interface PanelVisibilitySectionProps {
  leftPanelVisible: boolean;
  rightPanelVisible: boolean;
  setLeftPanelVisible: (value: boolean) => void;
  setRightPanelVisible: (value: boolean) => void;
}

function PanelVisibilitySection({
  leftPanelVisible,
  rightPanelVisible,
  setLeftPanelVisible,
  setRightPanelVisible,
}: PanelVisibilitySectionProps) {
  return (
    <div className="space-y-3 px-1">
      <div className="grid grid-cols-2 gap-3">
        <ToggleCard
          title="Lab Instructions"
          icon={<PanelLeft size={18} />}
          isActive={leftPanelVisible}
          onClick={() => setLeftPanelVisible(!leftPanelVisible)}
        />
        <ToggleCard
          title="Code Editor"
          icon={<PanelTop size={18} />}
          isActive={rightPanelVisible}
          onClick={() => setRightPanelVisible(!rightPanelVisible)}
        />
      </div>
    </div>
  );
}

interface ActionsSectionProps {
  id: string;
}

function ActionsSection({ id }: ActionsSectionProps) {
  const { clear } = useCodeEditorStore();

  return (
    <div className="space-y-3">
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button
            variant="outline"
            className="flex w-full items-center justify-between gap-2 py-4 h-auto border-destructive/30 hover:bg-destructive/5 hover:border-destructive/50"
          >
            <div className="flex flex-col items-start gap-1 text-left">
              <span className="font-medium">Clear Local Cached Data</span>
              <p className="text-xs text-muted-foreground">
                This will clear all locally saved playground data
              </p>
            </div>
            <Trash2 size={16} className="text-destructive" />
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action will permanently delete all locally stored editor data and cannot be
              undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              onClick={() => clear(id)}
            >
              <AlertTriangle className="mr-2 h-4 w-4" />
              Clear Data
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
