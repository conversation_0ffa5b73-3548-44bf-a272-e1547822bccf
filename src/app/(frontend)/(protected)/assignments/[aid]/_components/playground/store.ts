import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { createLocalStore } from '@/lib/local-store';

type Layout = {
  // panel visibility
  leftPanelVisible: boolean;
  rightPanelVisible: boolean;
  setLeftPanelVisible: (visible: boolean) => void;
  setRightPanelVisible: (visible: boolean) => void;
  resetLayout: () => void;

  // Panel sizes
  leftPanelSize: number;
  setLeftPanelSize: (size: number) => void;
};

export const useLayoutStore = create<Layout>()(
  persist(
    (set) => ({
      // Panel sizes
      leftPanelSize: 50,
      setLeftPanelSize: (size: number) => set({ leftPanelSize: size }),

      // Panel visibility
      leftPanelVisible: true,
      rightPanelVisible: true,
      setLeftPanelVisible: (visible: boolean) => set({ leftPanelVisible: visible }),
      setRightPanelVisible: (visible: boolean) => set({ rightPanelVisible: visible }),
      resetLayout: () =>
        set({
          leftPanelVisible: true,
          rightPanelVisible: true,
          leftPanelSize: 50,
        }),
    }),
    createLocalStore({
      name: 'layout-settings',
    }),
  ),
);
