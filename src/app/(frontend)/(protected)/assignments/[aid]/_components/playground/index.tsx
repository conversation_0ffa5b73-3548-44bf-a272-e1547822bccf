'use client';

import { debounce } from 'lodash';
import type React from 'react';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable';
import { cn } from '@/lib/utils';
import { useCodeEditorStore } from '../code-editor/store';
import { SettingsDrawer } from '../settings-dialog';
import { useLayoutStore } from './store';

export const ResizableLayout = ({
  leftPanel,
  rightPanel,
}: {
  leftPanel?: React.ReactNode;
  rightPanel?: React.ReactNode;
}) => {
  const fontSize = useCodeEditorStore((s) => s.fontSize);
  const setFontSize = useCodeEditorStore((s) => s.setFontSize);

  const {
    leftPanelVisible,
    rightPanelVisible,
    setLeftPanelVisible,
    setRightPanelVisible,
    resetLayout,
    leftPanelSize,
    setLeftPanelSize,
  } = useLayoutStore();

  // Calculate active panels count
  const activePanels = [
    leftPanelVisible ? 'left' : null,
    rightPanelVisible ? 'right' : null,
  ].filter(Boolean);

  const anyPanelVisible = activePanels.length > 0;

  // Debounced resize handler
  const handleLeftPanelResize = debounce((size: number) => {
    setLeftPanelSize(size);
  }, 200);

  // Render the two-panel layout
  const renderTwoPanelLayout = () => {
    return (
      <ResizablePanelGroup
        direction="horizontal"
        className="h-[calc(100vh-2rem)] border rounded-lg"
      >
        <ResizablePanel
          collapsible
          minSize={20}
          defaultSize={leftPanelSize}
          onResize={handleLeftPanelResize}
          className={cn(!leftPanelVisible && 'hidden')}
        >
          {leftPanel}
        </ResizablePanel>
        {leftPanelVisible && rightPanelVisible && <ResizableHandle withHandle />}
        <ResizablePanel
          collapsible
          minSize={20}
          defaultSize={100 - leftPanelSize}
          className={cn(!rightPanelVisible && 'hidden')}
        >
          {rightPanel}
        </ResizablePanel>
      </ResizablePanelGroup>
    );
  };

  // Render single panel layout
  const renderSinglePanelLayout = () => {
    const panel = leftPanelVisible ? leftPanel : rightPanel;

    return <div className="h-[calc(100vh-2rem)] border rounded-lg">{panel}</div>;
  };

  return (
    <div className="relative h-screen w-full overflow-hidden p-4 max-sm:p-1 bg-muted/50">
      <SettingsDrawer
        fontSize={fontSize}
        setFontSize={setFontSize}
        leftPanelVisible={leftPanelVisible}
        rightPanelVisible={rightPanelVisible}
        resetLayout={resetLayout}
        setLeftPanelVisible={setLeftPanelVisible}
        setRightPanelVisible={setRightPanelVisible}
      />

      {!anyPanelVisible && (
        <div className="h-full flex items-center w-full justify-center">
          <div
            className="flex h-full w-full justify-center items-center text-muted-foreground"
            style={{
              transition: 'opacity 0.5s ease-out',
            }}
          >
            Please select and make any pane(s) visible
          </div>
        </div>
      )}

      {anyPanelVisible && (
        <>
          {activePanels.length === 1 && renderSinglePanelLayout()}
          {activePanels.length === 2 && renderTwoPanelLayout()}
        </>
      )}
    </div>
  );
};
