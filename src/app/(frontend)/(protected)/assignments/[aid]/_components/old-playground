'use client';

import { useState, useEffect, useRef } from 'react';
import { ResizableH<PERSON>le, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { AssignmentDetails } from '@/types/assignment';
import { combineHTML } from '@/utils/html';
import CodeEditor from './code-editor';
import { PlayIcon, RefreshCwIcon, CheckCircleIcon, XCircleIcon } from 'lucide-react';
// import AssignmentPanel from './assignment-panel';

type TestResult = {
  name: string;
  description: string;
  passed: boolean;
  error?: string;
};

const assignment = {
  id: 1,
  title: 'JavaScript Counter Application',
  points: 10,
  description:
    'Build a simple counter application with increment, decrement, and reset functionality',
  instructions:
    '# Counter Application\n\nIn this assignment, you will create a simple counter application using HTML, CSS, and JavaScript.\n\n## Requirements:\n\n1. Create a counter display that shows the current count value (starting at 0)\n2. Add an increment button that increases the counter by 1\n3. Add a decrement button that decreases the counter by 1\n4. Add a reset button that resets the counter to 0\n5. Style the application to look clean and professional\n\n## Success Criteria:\n\n- The counter should display the correct value after each operation\n- Buttons should be clearly labeled and functional\n- The interface should be user-friendly and visually appealing\n- Your code should be well-organized and commented where necessary',
  dueDate: '2025-05-01T23:59:59Z',
  isActive: true,
  starterCode: {
    html: '<div class="container">\n    <h1>Counter App</h1>\n    <!-- Create your counter interface here -->\n    \n  </div>',
    css: '* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: Arial, sans-serif;\n  background-color: #f5f5f5;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n}\n\n.container {\n  background-color: white;\n  padding: 2rem;\n  border-radius: 10px;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n  text-align: center;\n}\n\nh1 {\n  margin-bottom: 2rem;\n  color: #333;\n}\n\n/* Add your custom styles below */\n',
    js: '// Counter App JavaScript\n// Your code goes here\n',
  },
  updatedAt: '2025-04-10T14:30:00Z',
  createdAt: '2025-04-10T10:15:00Z',
  resources: [
    {
      title: 'JavaScript DOM Manipulation',
      url: 'https://developer.mozilla.org/en-US/docs/Web/API/Document_Object_Model/Introduction',
    },
    {
      title: 'Event Listeners in JavaScript',
      url: 'https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener',
    },
    {
      title: 'CSS Flexbox Guide',
      url: 'https://css-tricks.com/snippets/css/a-guide-to-flexbox/',
    },
  ],
  hints: [
    'Start by creating the HTML structure for your counter and buttons',
    'Use document.getElementById() to get references to your HTML elements',
    'Store the counter value in a variable and update the display whenever it changes',
    'Remember to handle edge cases, like preventing negative numbers if required',
    'Consider using CSS transitions for smoother visual feedback when the counter changes',
  ],
  tests: [
    {
      name: 'Counter Display Test',
      description: 'Checks if the counter display element exists and shows 0 initially',
      testFunction:
        "document.querySelector('#counter') && document.querySelector('#counter').textContent === '0'",
    },
    {
      name: 'Increment Button Test',
      description: 'Checks if the increment button exists and has the correct label',
      testFunction:
        "document.querySelector('#increment') && document.querySelector('#increment').textContent.toLowerCase().includes('increment')",
    },
    {
      name: 'Decrement Button Test',
      description: 'Checks if the decrement button exists and has the correct label',
      testFunction:
        "document.querySelector('#decrement') && document.querySelector('#decrement').textContent.toLowerCase().includes('decrement')",
    },
    {
      name: 'Reset Button Test',
      description: 'Checks if the reset button exists and has the correct label',
      testFunction:
        "document.querySelector('#reset') && document.querySelector('#reset').textContent.toLowerCase().includes('reset')",
    },
    {
      name: 'Increment Functionality Test',
      description: 'Checks if clicking the increment button increases the counter value',
      testFunction:
        "(() => { const counterEl = document.querySelector('#counter'); const incrementBtn = document.querySelector('#increment'); if (!counterEl || !incrementBtn) return false; const initialValue = parseInt(counterEl.textContent); incrementBtn.click(); return parseInt(counterEl.textContent) === initialValue + 1; })()",
    },
    {
      name: 'Decrement Functionality Test',
      description: 'Checks if clicking the decrement button decreases the counter value',
      testFunction:
        "(() => { const counterEl = document.querySelector('#counter'); const decrementBtn = document.querySelector('#decrement'); if (!counterEl || !decrementBtn) return false; const initialValue = parseInt(counterEl.textContent); decrementBtn.click(); return parseInt(counterEl.textContent) === initialValue - 1; })()",
    },
    {
      name: 'Reset Functionality Test',
      description: 'Checks if clicking the reset button resets the counter to 0',
      testFunction:
        "(() => { const counterEl = document.querySelector('#counter'); const resetBtn = document.querySelector('#reset'); if (!counterEl || !resetBtn) return false; const incrementBtn = document.querySelector('#increment'); if (incrementBtn) incrementBtn.click(); resetBtn.click(); return parseInt(counterEl.textContent) === 0; })()",
    },
  ],
};

export function Playground({}: { assignment: AssignmentDetails }) {
  const [html, setHtml] = useState(assignment.starterCode?.html || '');
  const [css, setCss] = useState(assignment.starterCode?.css || '');
  const [js, setJs] = useState(assignment.starterCode?.js || '');
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);

  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Create a combined HTML with test scripts injected
  const createOutputWithTests = () => {
    // Create test script to run within iframe
    const testScripts =
      assignment.tests
        ?.map((test, index) => {
          return `
        try {
          const testResult${index} = (function() { return ${test.testFunction}; })();
          window.parent.postMessage({
            type: 'TEST_RESULT',
            index: ${index},
            passed: !!testResult${index},
            error: null
          }, '*');
        } catch(error) {
          window.parent.postMessage({
            type: 'TEST_RESULT',
            index: ${index},
            passed: false,
            error: error.message
          }, '*');
        }
      `;
        })
        .join('\n') || '';

    // Combine HTML with test scripts that post messages back to parent
    let combinedHTML = combineHTML({ html, css, js });

    // Insert script before closing body tag to run tests and send results
    const testRunner = `
      <script>
        // Wait for content to load then run tests
        window.addEventListener('load', function() {
          setTimeout(function() {
            // Notify parent that tests are starting
            window.parent.postMessage({ type: 'TESTS_STARTED' }, '*');

            // Run each test
            ${testScripts}

            // Notify parent that all tests are complete
            window.parent.postMessage({ type: 'TESTS_COMPLETE' }, '*');
          }, 100);
        });
      </script>
    `;

    // Insert the test runner script before the closing body tag
    if (combinedHTML.includes('</body>')) {
      combinedHTML = combinedHTML.replace('</body>', `${testRunner}</body>`);
    } else {
      combinedHTML = `${combinedHTML}${testRunner}`;
    }

    return combinedHTML;
  };

  // Manage the output state and handle refreshing
  const [output, setOutput] = useState('');

  // Update the preview whenever code changes
  useEffect(() => {
    const combinedCode = combineHTML({ html, css, js });
    setOutput(combinedCode);
  }, [html, css, js]);

  // Set up message listener for test results
  useEffect(() => {
    // Initialize results array with pending status
    const pendingResults =
      assignment.tests?.map((test) => ({
        name: test.name,
        description: test.description,
        passed: false,
        pending: true,
      })) || [];

    if (pendingResults.length > 0) {
      setTestResults(pendingResults as TestResult[]);
    }

    // Set up message event listener
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'TESTS_STARTED') {
        setIsRunningTests(true);
      } else if (event.data.type === 'TEST_RESULT') {
        setTestResults((prev) => {
          const newResults = [...prev];
          const testConfig = assignment.tests?.[event.data.index];
          if (testConfig) {
            newResults[event.data.index] = {
              name: testConfig.name,
              description: testConfig.description,
              passed: event.data.passed,
              error: event.data.error,
            };
          }
          return newResults;
        });
      } else if (event.data.type === 'TESTS_COMPLETE') {
        setIsRunningTests(false);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  // Function to run tests
  const runTests = () => {
    // Reset test results to pending state
    const pendingResults =
      assignment.tests?.map((test) => ({
        name: test.name,
        description: test.description,
        passed: false,
        pending: true,
      })) || [];

    setTestResults(pendingResults as TestResult[]);
    setIsRunningTests(true);

    // Create output with tests injected and load into iframe
    const outputWithTests = createOutputWithTests();
    setOutput('');
    setTimeout(() => setOutput(outputWithTests), 10);
  };

  // Refresh preview without tests
  const refreshPreview = () => {
    setOutput('');
    setTimeout(() => setOutput(combineHTML({ html, css, js })), 10);
  };

  // Calculate test summary
  const passedTests = testResults.filter((test) => test.passed).length;
  const totalTests = testResults.length;

  return (
    <div className="min-h-screen w-full flex flex-col overflow-hidden bg-background">
      <ResizablePanelGroup
        direction="horizontal"
        className="flex-1 border rounded-lg overflow-hidden"
      >
        {/* Left panel - Instructions */}
        {/* <AssignmentPanel assignment={assignment} /> */}
        <ResizableHandle withHandle />

        {/* Right panel - Code editors and preview */}
        <ResizablePanel defaultSize={70} className="flex flex-col">
          <ResizablePanelGroup direction="vertical" className="h-full">
            <ResizablePanel defaultSize={50} minSize={15} className="flex flex-col overflow-hidden">
              <Tabs defaultValue="html" className="h-full flex flex-col overflow-hidden">
                <div className="border-b p-2 flex items-center justify-between">
                  <TabsList>
                    <TabsTrigger value="html">HTML</TabsTrigger>
                    <TabsTrigger value="css">CSS</TabsTrigger>
                    <TabsTrigger value="js">JavaScript</TabsTrigger>
                  </TabsList>
                  <button
                    className="text-xs bg-primary text-primary-foreground px-2 py-1 rounded flex gap-2 items-center"
                    onClick={refreshPreview}
                  >
                    <RefreshCwIcon size={14} />
                    <span>Refresh</span>
                  </button>
                </div>

                <div className="flex-1 overflow-hidden">
                  <TabsContent value="html" className="h-full p-0 m-0">
                    <CodeEditor value={html} onChange={setHtml} language="html" />
                  </TabsContent>

                  <TabsContent value="css" className="h-full p-0 m-0">
                    <CodeEditor value={css} onChange={setCss} language="css" />
                  </TabsContent>

                  <TabsContent value="js" className="h-full p-0 m-0">
                    <CodeEditor value={js} onChange={setJs} language="javascript" />
                  </TabsContent>
                </div>
              </Tabs>
            </ResizablePanel>

            <ResizableHandle withHandle />

            <ResizablePanel defaultSize={50} minSize={15} className="flex flex-col overflow-hidden">
              <ResizablePanelGroup direction="horizontal" className="h-full">
                <ResizablePanel
                  defaultSize={65}
                  minSize={30}
                  className="flex flex-col overflow-hidden"
                >
                  <div className="border-b p-2 bg-muted flex items-center">
                    <h3 className="text-sm font-medium">Preview</h3>
                  </div>
                  <div className="flex-1 overflow-hidden bg-white">
                    <iframe
                      ref={iframeRef}
                      className="w-full h-full border-0"
                      srcDoc={output}
                      sandbox="allow-scripts allow-modals"
                      title="preview"
                    />
                  </div>
                </ResizablePanel>

                <ResizableHandle withHandle />

                <ResizablePanel
                  defaultSize={35}
                  minSize={20}
                  className="flex flex-col overflow-hidden"
                >
                  <div className="border-b p-2 bg-muted flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <h3 className="text-sm font-medium">Test Results</h3>
                      {totalTests > 0 && (
                        <span className="text-xs bg-secondary text-secondary-foreground px-2 py-0.5 rounded-full">
                          {passedTests}/{totalTests} passed
                        </span>
                      )}
                    </div>
                    <button
                      className="text-xs bg-primary text-primary-foreground px-2 py-1 rounded flex gap-2 items-center"
                      onClick={runTests}
                      disabled={isRunningTests}
                    >
                      {isRunningTests ? (
                        <>
                          <span className="animate-spin h-3 w-3 border-2 border-primary-foreground border-t-transparent rounded-full mr-1" />
                          Running...
                        </>
                      ) : (
                        <>
                          <PlayIcon size={14} />
                          <span>Run Tests</span>
                        </>
                      )}
                    </button>
                  </div>
                  <div className="flex-1 p-3 overflow-auto">
                    {testResults.length === 0 ? (
                      <div className="text-center text-muted-foreground text-sm p-4">
                        {assignment.tests?.length
                          ? 'Click "Run Tests" to check your solution'
                          : 'No tests available for this assignment'}
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {testResults.map((result, index) => {
                          // Handle pending state
                          const isPending = 'pending' in result && result.pending;

                          return (
                            <div
                              key={index}
                              className={`p-3 border rounded ${
                                isPending
                                  ? 'bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-800'
                                  : result.passed
                                    ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-900'
                                    : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-900'
                              }`}
                            >
                              <div className="flex items-center gap-2">
                                {isPending ? (
                                  <div className="h-4 w-4 rounded-full bg-gray-300 dark:bg-gray-700" />
                                ) : result.passed ? (
                                  <CheckCircleIcon size={16} className="text-green-500" />
                                ) : (
                                  <XCircleIcon size={16} className="text-red-500" />
                                )}
                                <span className="font-medium text-sm">{result.name}</span>
                              </div>
                              <p className="text-xs text-muted-foreground mt-1 ml-6">
                                {result.description}
                              </p>
                              {!isPending && result.error && (
                                <p className="text-xs text-red-500 mt-1 ml-6 font-mono p-1 bg-red-50 dark:bg-red-900/30 rounded">
                                  {result.error}
                                </p>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                </ResizablePanel>
              </ResizablePanelGroup>
            </ResizablePanel>
          </ResizablePanelGroup>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
}
