import { BackButton } from '@/components/back-button';
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import type { Assignment } from '@/payload-types';
import { AssignmentTabs } from './assignment-tabs';

const AssignmentPanel = ({ assignment }: { assignment: Assignment }) => {
  return (
    <div className="flex flex-col h-full overflow-hidden bg-gradient-to-b from-background to-background/80">
      {/* Header */}
      <div className="p-2 flex-shrink-0 border-b bg-muted/20">
        <div className="flex items-center gap-1">
          <BackButton href="/assignments" />
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <h1 className="text-lg font-bold line-clamp-1">{assignment.title}</h1>
              </TooltipTrigger>
              <TooltipContent className="ml-6 max-w-96">
                <p className="text-sm font-semibold">{assignment.title}</p>
                <p className="text-md line-clamp-1">{assignment.description}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <AssignmentTabs assignment={assignment} />
    </div>
  );
};

export default AssignmentPanel;
