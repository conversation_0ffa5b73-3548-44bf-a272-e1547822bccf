import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { createLocalStore } from '@/lib/local-store';
import { Assignment } from '@/payload-types';

type Stat = { passedTestcases: number; totalTestcases: number };

type WebLang = 'html' | 'css' | 'js';
type CodeLang = WebLang | 'java' | 'c';
type UiLang = 'web' | 'java' | 'c';

type CodeBundle = Record<CodeLang, string>;

interface CodeEditorConfig {
  /* state */
  fontSize: number;
  codes: Record<string, CodeBundle>;
  starterCodes: Assignment['starterCode'];
  stats: Record<string, Stat>;
  isTesting: boolean;
  language: UiLang;
  serverUrl: string;
  stdins: Record<string, string>;

  /* actions */
  setServerUrl: (url: string) => void;
  setFontSize: (size: number) => void;
  setIsTesting: (flag: boolean) => void;
  getCode: (id: string) => CodeBundle;
  setCode: (id: string, lang: UiLang | CodeLang, code: string) => void;
  setStarter: (id: string, starter: Assignment['starterCode']) => void;
  clear: (id: string) => void;
  setStat: (id: string, stat: Stat) => void;
  setStdin: (id: string, stdin: string) => void;
  getStdin: (id: string) => string | undefined;
}

const EMPTY_BUNDLE: CodeBundle = { html: '', css: '', js: '', java: '', c: '' };

export const useCodeEditorStore = create<CodeEditorConfig>()(
  persist(
    (set, get) => ({
      /* ---------- state ---------- */
      fontSize: get()?.fontSize ?? 14,
      stdins: get()?.stdins,
      codes: {},
      starterCodes: {},
      stats: {},
      isTesting: false,
      language: get()?.language ?? 'web',
      serverUrl: get()?.serverUrl ?? 'http://localhost:9016',

      /* ---------- actions ---------- */
      setServerUrl: (url) => {
        // Ensure URL is trimmed and without trailing slashes
        const trimmedUrl = url.trim().replace(/\/+$/, '');
        set({ serverUrl: trimmedUrl });
      },

      setStdin: (id, stdin) =>
        set((state) => ({
          stdins: {
            ...state.stdins,
            [id]: stdin,
          },
        })),
      getStdin: (id) => get()?.stdins?.[id] ?? undefined,

      setFontSize: (size) => set({ fontSize: size }),
      setIsTesting: (flag) => set({ isTesting: flag }),

      getCode: (id) => get().codes[id] ?? EMPTY_BUNDLE,

      setCode: (id, lang, code) => {
        // If the UI tab is "web", write to html/css/js at once
        if (lang === 'web') {
          (['html', 'css', 'js'] as const).forEach((l) => get().setCode(id, l, code));
          return;
        }

        // Otherwise lang is now a concrete bundle key ('html' | 'css' | 'js' | 'java')
        set(({ codes }) => ({
          codes: {
            ...codes,
            [id]: { ...(codes[id] ?? EMPTY_BUNDLE), [lang]: code },
          },
        }));
      },

      setStarter: (id, starter) =>
        set(({ starterCodes }) => ({
          starterCodes: { ...starterCodes, [id]: starter },
        })),

      clear: (id) => {
        // @ts-expect-error starterCode
        const starter = { ...EMPTY_BUNDLE, ...(get().starterCodes[id] ?? {}) };

        // reset java explicitly
        get().setCode(id, 'java', starter.java);
        get().setCode(id, 'c', starter.c);

        // reset web trio
        (['html', 'css', 'js'] as const).forEach((l) => get().setCode(id, l, starter[l]));
      },

      setStat: (id, stat) => set(({ stats }) => ({ stats: { ...stats, [id]: stat } })),
    }),
    createLocalStore({ name: 'code-editor' }),
  ),
);
