'use client';

import { KeyCode } from 'monaco-editor';
import dynamic from 'next/dynamic';
import {
  type HTMLAttributes,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import type { IconType } from 'react-icons';
import { FaCss3Alt, FaHtml5, FaJs } from 'react-icons/fa';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useTheme } from '@/hooks/use-theme';
import { cn } from '@/lib/utils';
import type { Assignment } from '@/payload-types';
// import { isFaculty } from '@/access';
import { useCopiedContentStore } from '@/stores/useCopiedContentStore';
import type { DecodedToken } from '@/types/decoded-token';
import { useCodeEditorStore } from './store';

const MonacoEditor = dynamic(() => import('@monaco-editor/react'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-full w-full bg-muted/50">
      <div className="animate-pulse text-muted-foreground">Loading editor...</div>
    </div>
  ),
});

type IStandaloneCodeEditor = import('monaco-editor').editor.IStandaloneCodeEditor;

type CodeEditorProps = {
  starterCode: Assignment['starterCode'];
  id: string;
  language: Assignment['language'];
  user: DecodedToken | null;
} & HTMLAttributes<HTMLDivElement>;

type EditorLanguage = 'html' | 'css' | 'javascript' | 'java' | 'c';
type EditorTab = 'html' | 'css' | 'js' | 'java' | 'c';
type WebEditorTab = 'html' | 'css' | 'js';

interface BaseEditorProps {
  value: string;
  onChange: (value: string | undefined) => void;
  handleEditorDidMount: (editor: IStandaloneCodeEditor) => void;
  language: EditorLanguage;
}

const EDITOR_OPTIONS = {
  fontSize: 14,
  contextmenu: false,
  padding: { top: 0, bottom: 200 },
  minimap: { enabled: false },
  wordWrap: 'on',
  smoothScrolling: true,
  mouseWheelZoom: false,
  cursorSmoothCaretAnimation: 'on',
  scrollBeyondLastLine: false,
  lineNumbers: 'on',
  tabSize: 2,
  automaticLayout: true,
  folding: true,
  formatOnPaste: true,
  formatOnType: true,
  suggestOnTriggerCharacters: true,
  acceptSuggestionOnEnter: 'on',
  quickSuggestions: true,
  parameterHints: { enabled: true },
  hover: { enabled: true },
  multiCursorModifier: 'alt',
  renderWhitespace: 'selection',
  renderControlCharacters: true,
  renderIndentGuides: true,
  renderLineHighlight: 'all',
  renderValidationDecorations: 'on',
  renderIndicators: true,
  renderLineNumbers: true,
  renderGlyphMargin: true,
  renderLineHighlightOnlyWhenFocus: false,
} as const;

const createLanguageEditor = (language: EditorLanguage) => {
  const LanguageEditor = (props: Omit<BaseEditorProps, 'language'>) => (
    <BaseEditor language={language} {...props} />
  );
  LanguageEditor.displayName = `${language.charAt(0).toUpperCase() + language.slice(1)}Editor`;
  return memo(LanguageEditor);
};

const BaseEditor = memo(({ language, value, onChange, handleEditorDidMount }: BaseEditorProps) => {
  const { resolvedTheme } = useTheme();
  const fontSize = useCodeEditorStore((state) => state.fontSize);

  const editorOptions = useMemo(() => ({ ...EDITOR_OPTIONS, fontSize }), [fontSize]);

  return (
    <MonacoEditor
      theme={resolvedTheme === 'dark' ? 'vs-dark' : 'light'}
      className="flex-1 h-full px-0 mx-0"
      defaultLanguage={language}
      value={value}
      onChange={onChange}
      onMount={handleEditorDidMount}
      options={editorOptions}
    />
  );
});

BaseEditor.displayName = 'BaseEditor';

const HtmlEditor = createLanguageEditor('html');
const CssEditor = createLanguageEditor('css');
const JsEditor = createLanguageEditor('javascript');
const JavaEditor = createLanguageEditor('java');
const CEditor = createLanguageEditor('c');

interface FileTabProps {
  value: EditorTab;
  label: string;
  Icon: IconType;
  iconClass: string;
  isActive: boolean;
}

const FileTab = memo(({ value, label, Icon, iconClass, isActive }: FileTabProps) => (
  <TabsTrigger
    value={value}
    className={cn(
      'hover:bg-accent hover:text-foreground data-[state=active]:after:bg-primary data-[state=active]:hover:bg-accent relative after:absolute after:inset-x-0 after:bottom-0 after:-mb-1 after:h-0.5 data-[state=active]:bg-transparent data-[state=active]:shadow-none',
      isActive && 'text-foreground',
    )}
  >
    <Icon className={cn('-ms-0.5 me-1.5 opacity-60', iconClass)} size={20} aria-hidden="true" />
    {label}
  </TabsTrigger>
));

FileTab.displayName = 'FileTab';

const WEB_TAB_CONFIG: Record<WebEditorTab, { label: string; Icon: IconType; iconClass: string }> = {
  html: {
    label: 'HTML',
    Icon: FaHtml5,
    iconClass: 'text-orange-600 dark:text-orange-400',
  },
  css: {
    label: 'CSS',
    Icon: FaCss3Alt,
    iconClass: 'text-blue-600 dark:text-blue-400',
  },
  js: {
    label: 'JavaScript',
    Icon: FaJs,
    iconClass: 'text-yellow-500 dark:text-yellow-400',
  },
};

const LANGUAGE_DEFAULT_TAB: Record<Assignment['language'], EditorTab> = {
  web: 'html',
  java: 'java',
  c: 'c',
};

const EDITOR_COMPONENTS: Record<
  EditorTab,
  React.ComponentType<Omit<BaseEditorProps, 'language'>>
> = {
  html: HtmlEditor,
  css: CssEditor,
  js: JsEditor,
  java: JavaEditor,
  c: CEditor,
};

export function CodeEditor({ starterCode, id, language, className }: CodeEditorProps) {
  const editorRef = useRef<IStandaloneCodeEditor | null>(null);
  const [currentTab, setCurrentTab] = useState<EditorTab>(LANGUAGE_DEFAULT_TAB[language] || 'html');
  const [initialized, setInitialized] = useState(false);

  // Track copied content for each tab to validate paste operations
  const { copiedContent, resetCopied } = useCopiedContentStore();

  // Generate unique identifier for this editor instance and tab
  const getEditorTabKey = useCallback((tabName: EditorTab) => `${id}-${tabName}`, [id]);

  const { getCode, setCode } = useCodeEditorStore();
  const { html, css, js, java, c } = getCode(id);

  const handleEditorDidMount = useCallback(
    (editor: IStandaloneCodeEditor) => {
      editorRef.current = editor;
      editor.focus();

      // Track copy operations to maintain allowed clipboard content
      editor.onKeyDown((event) => {
        const { keyCode, ctrlKey, metaKey } = event;
        const isModifierPressed = ctrlKey || metaKey;

        // temporary FIX for allow all the copy
        if (true) return;
        // isFaculty(user)

        // Handle copy operation - track what's being copied
        if (keyCode === KeyCode.KeyC && isModifierPressed) {
          const selection = editor.getSelection();
          if (selection && !selection?.isEmpty()) {
            const selectedText = editor.getModel()?.getValueInRange(selection!);
            if (selectedText) {
              const editorKey = getEditorTabKey(currentTab);
              if (!copiedContent.has(editorKey)) {
                copiedContent.set(editorKey, new Set());
                resetCopied();
              }
              copiedContent.get(editorKey)?.add(selectedText ?? '');
            }
          }
        }

        // Track cut operation
        if (keyCode === KeyCode.KeyX && isModifierPressed) {
          const selection = editor.getSelection();
          if (selection && !selection?.isEmpty()) {
            const selectedText = editor.getModel()?.getValueInRange(selection!);
            if (selectedText) {
              const editorKey = getEditorTabKey(currentTab);
              if (!copiedContent.has(editorKey)) {
                copiedContent.set(editorKey, new Set());
                resetCopied();
              }
              copiedContent.get(editorKey)?.add(selectedText ?? '');
            }
          }
        }

        // Handle paste operation - validate against tracked content
        if (keyCode === KeyCode.KeyV && isModifierPressed) {
          event.preventDefault();

          // Get clipboard content
          navigator.clipboard
            .readText()
            .then((clipboardText) => {
              if (clipboardText) {
                const editorKey = getEditorTabKey(currentTab);
                const allowedContent = copiedContent.get(editorKey);

                // Check if the clipboard content was copied from this editor tab
                if (allowedContent && allowedContent.has(clipboardText)) {
                  // Allow paste by inserting the text at cursor position
                  const position = editor.getPosition();
                  if (position) {
                    editor.executeEdits('paste', [
                      {
                        range: {
                          startLineNumber: position.lineNumber,
                          startColumn: position.column,
                          endLineNumber: position.lineNumber,
                          endColumn: position.column,
                        },
                        text: clipboardText,
                      },
                    ]);
                  }
                }
                // If content wasn't copied from this tab, silently ignore (block paste)
              }
            })
            .catch(() => {
              // Clipboard access failed, ignore
            });
        }
      });

      // Also handle context menu paste
      editor.getContainerDomNode().addEventListener(
        'paste',
        (event) => {
          event.preventDefault();
          event.stopPropagation();

          const clipboardText = event.clipboardData?.getData('text');
          if (clipboardText) {
            const editorKey = getEditorTabKey(currentTab);
            const allowedContent = copiedContent.get(editorKey);

            if (allowedContent && allowedContent.has(clipboardText)) {
              const position = editor.getPosition();
              if (position) {
                editor.executeEdits('paste', [
                  {
                    range: {
                      startLineNumber: position.lineNumber,
                      startColumn: position.column,
                      endLineNumber: position.lineNumber,
                      endColumn: position.column,
                    },
                    text: clipboardText,
                  },
                ]);
              }
            }
          }
        },
        true,
      );
    },
    [currentTab, getEditorTabKey, copiedContent, resetCopied],
  );

  const handleChange = useCallback(
    (value: string | undefined) => {
      if (value !== undefined) {
        setCode(id, currentTab, value);
      }
    },
    [id, currentTab, setCode],
  );

  useEffect(() => {
    if (typeof window == 'undefined') return;
    // prevent the ctrl+s shortcut from triggering
    window.addEventListener('keydown', (event) => {
      if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault();
      }
    });
  }, []);

  // Clear copied content when switching tabs to prevent cross-tab pasting
  useEffect(() => {
    // Clean up copied content for previous tab to prevent memory leaks
    // Keep only recent entries to avoid unlimited growth
    const currentEditorKey = getEditorTabKey(currentTab);
    const allKeys = Array.from(copiedContent.keys());

    // Keep only the current tab's copied content and clean up others
    allKeys.forEach((key) => {
      if (key !== currentEditorKey) {
        copiedContent.delete(key);
      }
    });
  }, [currentTab, getEditorTabKey, copiedContent]);

  // Get code for current tab
  const currentCode = useMemo(() => {
    const codeMap = { html, css, js, java, c };
    return codeMap[currentTab] || '';
  }, [currentTab, html, css, js, java, c]);

  // Get editor component for current tab/language
  const EditorComponent = useMemo(
    () =>
      language === 'java' ? JavaEditor : language === 'c' ? CEditor : EDITOR_COMPONENTS[currentTab],
    [currentTab, language],
  );

  // Initialize with starter code
  useEffect(() => {
    if (initialized) return;

    // Get current stored code
    const storedCode = getCode(id);
    const hasStoredContent = Object.values(storedCode).some((code) => Boolean(code?.trim()));

    // Only initialize if no existing stored content
    if (!hasStoredContent && starterCode) {
      // Set default/starter code for each available tab
      Object.entries(starterCode).forEach(([key, value]) => {
        if (value !== undefined) {
          setCode(id, key as EditorTab, value || '');
        }
      });
    }

    setInitialized(true);
  }, [id, starterCode, setCode, initialized, getCode]);

  // Only show tabs for web language
  const showTabs = language === 'web';

  // Filter tabs to only show those with starter code
  const availableTabs = useMemo(() => {
    if (!showTabs) return [];

    // Only include html, css, js tabs for web language
    const webTabs = ['html', 'css', 'js'] as WebEditorTab[];

    return webTabs.filter((tab) => starterCode && starterCode[tab] !== undefined);
  }, [starterCode, showTabs]);

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {showTabs && availableTabs.length > 0 && (
        <Tabs
          onValueChange={(tab) => setCurrentTab(tab as EditorTab)}
          value={currentTab}
          className="flex-shrink-0"
        >
          <ScrollArea className="w-full">
            <TabsList className="text-foreground h-auto gap-2 w-full rounded-none border-b bg-transparent px-0 py-1">
              {availableTabs.map((tab) => {
                const { label, Icon, iconClass } = WEB_TAB_CONFIG[tab];
                return (
                  <FileTab
                    key={tab}
                    value={tab}
                    label={label}
                    Icon={Icon}
                    iconClass={cn(iconClass, 'transition')}
                    isActive={currentTab === tab}
                  />
                );
              })}
            </TabsList>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        </Tabs>
      )}
      <div className="flex-1 overflow-hidden">
        <EditorComponent
          onChange={handleChange}
          value={currentCode}
          handleEditorDidMount={handleEditorDidMount}
        />
      </div>
    </div>
  );
}

export default memo(CodeEditor);
