'use client';

import type React from 'react';

import { useState, useEffect } from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';

interface FontSizeControlProps {
  defaultValue?: number;
  min?: number;
  max?: number;
  onChange?: (value: number) => void;
}

export function FontSizeControl({
  defaultValue = 16,
  min = 8,
  max = 72,
  onChange,
}: FontSizeControlProps) {
  const [fontSize, setFontSize] = useState<number>(defaultValue);

  const handleSliderChange = (value: number[]) => {
    setFontSize(value[0]);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(e.target.value);
    if (value >= min && value <= max) {
      setFontSize(value);
    }
  };

  useEffect(() => {
    onChange?.(fontSize);
  }, [fontSize, onChange]);

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <Label htmlFor="font-size" className="font-medium">
          Font Size
        </Label>
        <div className="text-xs text-muted-foreground ml-auto">{fontSize}px</div>
      </div>

      <div className="flex items-center space-x-4">
        <Slider
          id="font-size-slider"
          min={min}
          max={max}
          step={1}
          value={[fontSize]}
          onValueChange={handleSliderChange}
          className="flex-1"
          onMouseDown={(e) => {
            e.stopPropagation();
          }}
          onTouchStart={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
          onPointerDown={(e) => {
            e.stopPropagation();
          }}
        />

        <Input
          type="number"
          id="font-size"
          value={fontSize}
          onChange={handleInputChange}
          className="w-16 text-center"
          min={min}
          max={max}
        />
      </div>
    </div>
  );
}
