'use client';

import MarkdownPreview from '@uiw/react-markdown-preview';
import { format, formatDistanceToNow, isPast } from 'date-fns';
import {
  AlertTriangleIcon,
  ArrowUpRightIcon,
  BookOpenIcon,
  CalendarIcon,
  CheckCircle2Icon,
  CheckCircleIcon,
  CircleHelpIcon,
  ClockIcon,
  ExternalLinkIcon,
  FileTextIcon,
  GraduationCapIcon,
  HistoryIcon,
  InfoIcon,
  LockIcon,
  RefreshCwIcon,
  TestTubeDiagonalIcon,
  UnlockIcon,
  XCircleIcon,
} from 'lucide-react';
import Link from 'next/link';
import { useQueryState } from 'nuqs';
import { useMemo } from 'react';
import { isStudent } from '@/access';
import { DifficultyBadge } from '@/components/difficulty-badge';
import { PointsBadge } from '@/components/points-badge';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs-one';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import type { ISubmission, SubmissionResponse } from '@/hooks/use-submissions';
import { useSubmissions } from '@/hooks/use-submissions';
import { useTheme } from '@/hooks/use-theme';
import { useUser } from '@/hooks/use-user';
import type { Assignment } from '@/payload-types';
import { useCodeEditorStore } from './code-editor/store';

// Types

interface TabPanelProps {
  assignment: Assignment;
  className: string;
}

interface StatusConfig {
  icon: React.ReactNode;
  variant: 'secondary' | 'default' | 'destructive' | 'outline';
  label: string;
  info?: string;
}

// Constants
const SUBMISSION_STATUSES = {
  review: 'In Review',
  graded: 'Graded',
  resubmit: 'Needs Resubmission',
} as const;

const URGENCY_THRESHOLD_DAYS = 2;

// Utility functions
const getStatusConfig = (status: string): StatusConfig => {
  const configs: Record<string, StatusConfig> = {
    review: {
      icon: <ClockIcon className="w-4 h-4" />,
      variant: 'secondary',
      label: SUBMISSION_STATUSES.review,
      info: 'Your submission is being reviewed and will be graded soon.',
    },
    graded: {
      icon: <CheckCircle2Icon className="w-4 h-4" />,
      variant: 'default',
      label: SUBMISSION_STATUSES.graded,
      info: 'Your submission has been graded.',
    },
    resubmit: {
      icon: <XCircleIcon className="w-4 h-4" />,
      variant: 'destructive',
      label: SUBMISSION_STATUSES.resubmit,
      info: 'Your submission needs to be resubmitted.',
    },
  };

  return (
    configs[status] || {
      icon: <ClockIcon className="w-4 h-4" />,
      variant: 'outline',
      label: status,
    }
  );
};

const calculateProgress = (passed: number, total: number): number => {
  if (total === 0) return 0;
  return Math.round((passed / total) * 100);
};

const formatDueDate = (dueDate: string) => {
  return format(new Date(dueDate), "MMM d, yyyy 'at' h:mm a");
};

// Custom hooks

const useDueDateStatus = (dueDate: string) => {
  return useMemo(() => {
    const due = new Date(dueDate);
    const isOverdue = isPast(due);
    const daysRemaining = Math.ceil((due.getTime() - Date.now()) / (1000 * 60 * 60 * 24));

    if (isOverdue) {
      return {
        icon: <AlertTriangleIcon className="w-4 h-4 text-destructive" aria-hidden="true" />,
        text: `Overdue by ${formatDistanceToNow(due)}`,
        className: 'text-destructive font-medium',
        urgency: 'overdue' as const,
      };
    }

    if (daysRemaining <= URGENCY_THRESHOLD_DAYS) {
      return {
        icon: <ClockIcon className="w-4 h-4 text-warning" aria-hidden="true" />,
        text: `Due soon: ${formatDistanceToNow(due)} remaining`,
        className: 'text-warning font-medium',
        urgency: 'urgent' as const,
      };
    }

    return {
      icon: <CalendarIcon className="w-4 h-4 text-success" aria-hidden="true" />,
      text: `Due in ${formatDistanceToNow(due)}`,
      className: 'text-success font-medium',
      urgency: 'normal' as const,
    };
  }, [dueDate]);
};

// Components
const ProgressSection = ({
  isTesting,
  latestSubmission,
}: {
  assignment: Assignment;
  isTesting: boolean;
  latestSubmission: any;
}) => {
  const hasNoTests = !latestSubmission || latestSubmission.totalTestcases === 0;

  if (hasNoTests) {
    return (
      <div className="space-y-2">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <InfoIcon className="w-4 h-4" aria-hidden="true" />
          <span>No tests run yet. Start coding to check your progress.</span>
        </div>
      </div>
    );
  }

  const { passedTestcases, totalTestcases } = latestSubmission;
  const progress = calculateProgress(passedTestcases, totalTestcases);
  const isComplete = progress === 100;

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between mt-1">
        <p className="text-sm font-medium flex items-center gap-1.5">
          <GraduationCapIcon className="w-4 h-4 text-muted-foreground" aria-hidden="true" />
          Progress Summary
        </p>
        {isTesting ? (
          <span className="text-sm text-muted-foreground">Testing...</span>
        ) : (
          <div className="flex items-center gap-2 text-sm">
            {isComplete ? (
              <CheckCircleIcon className="w-4 h-4 text-success" aria-hidden="true" />
            ) : (
              <XCircleIcon className="w-4 h-4 text-muted-foreground" aria-hidden="true" />
            )}
            <span className="font-medium">
              {passedTestcases} / {totalTestcases} tests passed
            </span>
          </div>
        )}
      </div>
      {/* <div className="space-y-1">
        <Progress value={progress} className="h-2.5" aria-label={`${progress}% of tests passing`} />
        <p className="text-xs text-muted-foreground text-right">{progress}% complete</p>
      </div> */}
    </div>
  );
};

// const LearningObjectives = ({
//   objectives,
// }: {
//   objectives?: Array<{ objective: string; id: string }>;
// }) => {
//   if (!objectives?.length) return null;

//   return (
//     <section className="space-y-3" aria-labelledby="learning-objectives-heading">
//       <h3
//         id="learning-objectives-heading"
//         className="text-md font-semibold flex items-center gap-2"
//       >
//         <BookOpenIcon className="w-5 h-5 text-muted-foreground" aria-hidden="true" />
//         Learning Objectives
//       </h3>
//       <Card className="p-0">
//         <CardContent className="p-3">
//           <ul className="space-y-3" role="list">
//             {objectives.map(({ objective, id }) => (
//               <li key={id} className="flex items-start gap-3">
//                 <CheckCircleIcon
//                   className="size-4 text-success mt-0.5 flex-shrink-0"
//                   aria-hidden="true"
//                 />
//                 <span className="text-sm leading-relaxed">{objective}</span>
//               </li>
//             ))}
//           </ul>
//         </CardContent>
//       </Card>
//     </section>
//   );
// };

const SubmissionSection = ({
  submissions,
  latestSubmission,
  isLoading,
  error,
  retry,
}: {
  assignment: Assignment;
  submissions: SubmissionResponse | null;
  latestSubmission: any;
  isLoading: boolean;
  error: string | null;
  retry: () => void;
}) => {
  const getTestCaseStats = (submission: ISubmission) => {
    if (!submission) {
      return { total: 0, percentage: 0, passed: 0, failed: 0 };
    }

    const passedTestCases =
      submission?.testsResult?.filter((tr) => tr.status === 'PASS')?.length ?? 0;

    const failedTestCases =
      submission?.testsResult?.filter((tr) => tr.status === 'FAIL')?.length ?? 0;

    const total = submission?.testsResult?.length ?? 0;

    const percentage = total > 0 ? calculateProgress(passedTestCases, total) : 0;

    return {
      total,
      percentage,
      passed: passedTestCases,
      failed: failedTestCases,
    };
  };

  const stats = getTestCaseStats(latestSubmission);
  const { user } = useUser();
  const hasSubmissions = submissions && submissions.totalDocs > 0;

  return (
    <section className="space-y-3" aria-labelledby="submission-heading">
      <h3 id="submission-heading" className="text-md font-semibold flex items-center gap-2">
        <FileTextIcon className="w-5 h-5 text-muted-foreground" aria-hidden="true" />
        Your Submission
      </h3>

      {error ? (
        <Alert variant="destructive">
          <AlertTriangleIcon className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>Failed to load submissions: {error}</span>
            <Button variant="outline" size="sm" onClick={retry} className="ml-4 h-8 px-3">
              <RefreshCwIcon className="w-3 h-3 mr-1" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      ) : (
        <Card className="bg-muted/40 p-0">
          <CardContent className="p-3">
            {isLoading ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Skeleton className="h-4 w-4 rounded" />
                    <Skeleton className="h-4 w-32" />
                  </div>
                  <Skeleton className="h-6 w-20 rounded-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-2 w-full rounded-full" />
                </div>
                <div className="flex gap-2">
                  <Skeleton className="h-9 w-32" />
                </div>
              </div>
            ) : hasSubmissions ? (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  {isStudent(user) && (
                    <Tooltip>
                      <TooltipTrigger>
                        <Badge
                          variant={getStatusConfig(latestSubmission?.status || '').variant}
                          className="font-medium"
                        >
                          {getStatusConfig(latestSubmission?.status || '').label}
                        </Badge>
                      </TooltipTrigger>
                      <TooltipContent className="ml-6 font-semibold">
                        {getStatusConfig(latestSubmission?.status || '').info}
                      </TooltipContent>
                    </Tooltip>
                  )}

                  {latestSubmission?.id && (
                    <Button asChild size="icon" className="size-6" variant="outline">
                      <Link
                        href={`/submissions/${latestSubmission.id}`}
                        aria-label="View full submission details"
                      >
                        <ArrowUpRightIcon className="size-4" />
                      </Link>
                    </Button>
                  )}
                </div>

                {latestSubmission && (
                  <div className="space-y-3 p-4 bg-muted border rounded-lg">
                    <div className="flex items-center gap-2 justify-between">
                      <span className="text-sm font-medium">Test Results</span>
                      <span className="font-semibold">{stats.percentage}%</span>
                    </div>
                    <Progress value={stats.percentage} className="h-2" />
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>
                        {stats.passed} passed, {stats.failed} failed
                      </span>
                      <span>{stats.total} total tests</span>
                    </div>
                  </div>
                )}

                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <HistoryIcon className="w-4 h-4" />
                    <span className="font-medium">
                      {latestSubmission?.updatedAt &&
                        `Updated ${formatDistanceToNow(new Date(latestSubmission.updatedAt), { addSuffix: true })}`}
                    </span>
                  </div>

                  <div className="flex items-center gap-1">
                    {latestSubmission?.isLocked ? (
                      <LockIcon className="w-4 h-4 text-muted-foreground" />
                    ) : (
                      <UnlockIcon className="w-4 h-4 text-muted-foreground" />
                    )}
                    <span className="text-xs text-muted-foreground">
                      {latestSubmission?.isLocked ? 'Locked' : 'Unlocked'}
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-6">
                <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 rounded-full bg-muted">
                  <InfoIcon className="w-6 h-6 text-muted-foreground" />
                </div>
                <h4 className="text-sm font-medium mb-2">No submissions yet</h4>
                <p className="text-sm text-muted-foreground">
                  Submit your work to track progress here.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </section>
  );
};

const DetailsTab = ({ assignment }: TabPanelProps) => {
  const { stats, isTesting } = useCodeEditorStore();
  const { submissions, latestSubmission, isLoading, error, retry } = useSubmissions(
    String(assignment.id),
  );
  const dueDateStatus = useDueDateStatus(assignment.dueDate);
  const latestSubmissionFromStore = stats[assignment.id];
  const formattedDueDate = useMemo(() => formatDueDate(assignment.dueDate), [assignment.dueDate]);
  const { user, isStudent: isUserStudent } = useUser();
  const { resolvedTheme } = useTheme();
  const testScenarios = assignment?.testsRequirement || '';

  return (
    <div className="space-y-6 pb-16 animate-in fade-in py-3" role="tabpanel">
      <Card className="bg-muted/40 p-0">
        <CardContent className="p-3">
          <div className="flex justify-between w-full gap-2 pb-1">
            <div className="flex items-center gap-2">
              {isStudent(user) ? (
                <Tooltip>
                  <TooltipTrigger>
                    {dueDateStatus.icon}
                    <span className={dueDateStatus.className}>{dueDateStatus.text}</span>
                  </TooltipTrigger>
                  <TooltipContent className="ml-6 font-semibold">
                    {latestSubmission?.status ? (
                      getStatusConfig(latestSubmission?.status).info
                    ) : (
                      <time className="text-xs font-semibold ml-1" dateTime={assignment.dueDate}>
                        {formattedDueDate}
                      </time>
                    )}
                  </TooltipContent>
                </Tooltip>
              ) : (
                <>
                  {dueDateStatus.icon}
                  <span className={dueDateStatus.className}>{dueDateStatus.text}</span>
                </>
              )}
            </div>
            <div className="flex flex-wrap items-center gap-3">
              <PointsBadge points={assignment.points} />
              <DifficultyBadge difficulty={assignment.difficulty} />
            </div>
          </div>

          <ProgressSection
            assignment={assignment}
            isTesting={isTesting}
            latestSubmission={latestSubmissionFromStore}
          />
        </CardContent>
      </Card>

      {assignment?.description?.trim() && (
        <section className="space-y-3" aria-labelledby="description-heading">
          <h3 id="description-heading" className="text-md font-semibold flex items-center gap-2">
            <InfoIcon className="w-5 h-5 text-muted-foreground" aria-hidden="true" />
            Brief Description
          </h3>
          <Card className="p-0">
            <CardContent className="p-3">
              <p className="text-sm leading-relaxed whitespace-pre-wrap">
                {assignment.description}
              </p>
            </CardContent>
          </Card>
        </section>
      )}

      {/* <LearningObjectives
        objectives={assignment?.learningObjectives?.map((objective) => ({
          ...objective,
          id: String(objective.id),
        }))}
      /> */}

      <Accordion type="single" collapsible>
        <AccordionItem value="item-1">
          <AccordionTrigger>
            <h3 id="submission-heading" className="text-md font-semibold flex items-center gap-2">
              <TestTubeDiagonalIcon className="w-5 h-5 text-muted-foreground" aria-hidden="true" />
              Test Scenarios
            </h3>
          </AccordionTrigger>
          <AccordionContent>
            <Card className="p-0">
              <CardContent className="p-2">
                <MarkdownPreview
                  source={testScenarios}
                  style={{ backgroundColor: 'transparent' }}
                  wrapperElement={{
                    'data-color-mode': resolvedTheme as 'light' | 'dark',
                  }}
                  disableCopy
                  className="flex-1 max-w-full overflow-visible whitespace-normal testScenarios"
                />
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      {/* <section className="space-y-3" aria-labelledby="description-heading">
        <h3 id="description-heading" className="text-md font-semibold flex items-center gap-2">
          <TestTubeDiagonalIcon className="w-5 h-5 text-muted-foreground" aria-hidden="true" />
          Test Scenarios
        </h3>
        <Card className="p-0">
          <CardContent className="p-2">
            <MarkdownPreview
              source={testScenarios}
              style={{ backgroundColor: 'transparent' }}
              wrapperElement={{
                'data-color-mode': resolvedTheme as 'light' | 'dark',
              }}
              disableCopy
              className="flex-1 max-w-full overflow-visible whitespace-normal testScenarios"
            />
          </CardContent>
        </Card>
      </section> */}

      {isUserStudent ? (
        <SubmissionSection
          assignment={assignment}
          submissions={submissions}
          latestSubmission={latestSubmission}
          isLoading={isLoading}
          error={error}
          retry={retry}
        />
      ) : (
        <Alert>
          <AlertTitle>Your Submission</AlertTitle>
          <AlertDescription>
            This section is only available to students. Please switch to a student account to view
            and submit assignments.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

const InstructionsTab = ({ assignment, className }: TabPanelProps) => {
  const content = assignment?.instructions ?? '';
  const { resolvedTheme } = useTheme();

  if (!content.trim()) {
    return (
      <div className="w-full pb-18 py-3">
        <Card className="p-0">
          <CardContent className="p-6 text-center">
            <BookOpenIcon className="size-6 text-muted-foreground mx-auto mb-3" />
            <p className="text-muted-foreground">No instructions available for this assignment.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full pb-18 py-3">
      <MarkdownPreview
        source={content}
        style={{ backgroundColor: 'transparent' }}
        wrapperElement={{
          'data-color-mode': resolvedTheme as 'light' | 'dark',
        }}
        className={`flex-1 max-w-full overflow-visible whitespace-normal ${className}`}
      />
    </div>
  );
};

const SupportTab = ({ assignment }: TabPanelProps) => {
  const hints = assignment?.hints ?? [];
  const resources = assignment?.resources ?? [];
  const hasHints = hints.length > 0;
  const hasResources = resources.length > 0;

  const renderCodeSnippets = (text: string) => {
    return text.split(/(`[^`]+`)/g).map((part, i) => {
      if (part.startsWith('`') && part.endsWith('`')) {
        return (
          <code key={i} className="bg-muted px-1 py-0.5 rounded text-sm font-mono">
            {part.slice(1, -1)}
          </code>
        );
      }
      return <span key={i}>{part}</span>;
    });
  };

  return (
    <div className="space-y-6 py-2 pb-16">
      <section>
        <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
          <CircleHelpIcon className="w-5 h-5 text-muted-foreground" aria-hidden="true" />
          Hints
          {hasHints && (
            <Badge variant="secondary" className="text-xs">
              {hints.length}
            </Badge>
          )}
        </h3>

        {hasHints ? (
          <Accordion type="multiple" className="w-full">
            {hints.map((hint, idx) => (
              <AccordionItem value={`hint-${idx}`} key={`hint-${idx}`} className="py-2">
                <AccordionTrigger className="justify-start gap-3 py-1 text-[15px] leading-6 hover:no-underline [&>svg]:-order-1">
                  {hint.question}
                </AccordionTrigger>
                <AccordionContent className="text-muted-foreground ps-7 pb-2">
                  {renderCodeSnippets(hint.answer)}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        ) : (
          <Card className="p-0">
            <CardContent className="p-6 text-center">
              <CircleHelpIcon className="size-6 text-muted-foreground mx-auto mb-3" />
              <p className="text-muted-foreground">No hints available for this assignment.</p>
            </CardContent>
          </Card>
        )}
      </section>

      <section>
        <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
          <ExternalLinkIcon className="w-5 h-5 text-muted-foreground" aria-hidden="true" />
          Resources
          {hasResources && (
            <Badge variant="secondary" className="text-xs">
              {resources.length}
            </Badge>
          )}
        </h3>

        {hasResources ? (
          <ul className="space-y-3" role="list">
            {resources.map((resource, index) => (
              <li key={index} className="group">
                <a
                  href={resource.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center p-3 rounded-lg border border-border bg-muted/30 hover:bg-primary/5 hover:border-primary/20 transition-colors justify-between"
                  aria-label={`Open ${resource.title} in new tab`}
                >
                  <span className="font-medium text-foreground group-hover:text-primary transition-colors break-all">
                    {index + 1}. {resource.title}
                  </span>
                  <ExternalLinkIcon size={16} aria-hidden="true" />
                </a>
              </li>
            ))}
          </ul>
        ) : (
          <Card className="p-0">
            <CardContent className="p-6 text-center">
              <ExternalLinkIcon className="size-6 text-muted-foreground mx-auto mb-3" />
              <p className="text-muted-foreground">No resources available for this assignment.</p>
            </CardContent>
          </Card>
        )}
      </section>
    </div>
  );
};

interface AssignmentTabsProps {
  assignment: Assignment;
}

export function AssignmentTabs({ assignment }: AssignmentTabsProps) {
  const tabsConfig = [
    {
      id: 'details',
      label: 'Details',
      icon: <InfoIcon size={16} aria-hidden="true" />,
      badge: null,
      component: DetailsTab,
    },
    {
      id: 'instructions',
      label: 'Instructions',
      icon: <BookOpenIcon size={16} aria-hidden="true" />,
      badge: null,
      component: InstructionsTab,
    },
    {
      id: 'support',
      label: 'Support',
      icon: <CircleHelpIcon size={16} aria-hidden="true" />,
      badge:
        (assignment?.resources?.length ?? 0) + (assignment?.hints?.length ?? 0) > 0
          ? (assignment?.resources?.length ?? 0) + (assignment?.hints?.length ?? 0)
          : null,
      component: SupportTab,
    },
  ];

  const [currentTab, setCurrentTab] = useQueryState('tab', {
    clearOnDefault: true,
    defaultValue: 'details',
  });

  const current = tabsConfig.find((tab) => tab.id === currentTab);

  return (
    <div className="flex flex-col h-full">
      <Tabs onValueChange={(value) => setCurrentTab(value)} defaultValue={currentTab}>
        <ScrollArea className="w-full flex-1 border-b">
          <TabsList className="text-foreground h-auto gap-2 w-full rounded-none bg-transparent px-4 py-1 min-w-fit">
            {tabsConfig.map((tab) => (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                className="hover:bg-accent hover:text-foreground data-[state=active]:after:bg-primary data-[state=active]:hover:bg-accent relative after:absolute after:inset-x-0 after:bottom-0 after:-mb-1 after:h-0.5 data-[state=active]:bg-transparent data-[state=active]:shadow-none whitespace-nowrap"
              >
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-2 px-2">
                      {tab.icon}
                      {tab.label}
                      {tab.badge && (
                        <Badge
                          className="bg-primary/15 ml-1 min-w-5 px-1.5 py-0.5 text-xs"
                          variant="secondary"
                        >
                          {tab.badge}
                        </Badge>
                      )}
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>View {tab.label.toLowerCase()}</TooltipContent>
                </Tooltip>
              </TabsTrigger>
            ))}
          </TabsList>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </Tabs>
      <ScrollArea className="flex-1 overflow-auto px-3">
        {current && <current.component assignment={assignment} className="" />}
      </ScrollArea>
    </div>
  );
}
