'use client';

import { IAssignment, useAssignments } from '@/hooks/use-assignments';
import { ISubject, useSubjects } from '@/hooks/use-subjects';
import { DataTable } from './data-table';
import { createColumns } from './columns';
import { isPast } from 'date-fns';
import { Skeleton } from '@/components/ui/skeleton';
import { SidebarProvider, SidebarTrigger, useSidebar } from '@/components/ui/sidebar';
import { AppSidebar } from './app-sidebar';
import { ThemeToggle } from '@/components/theme-toggle';
import LogoutButton from '@/components/dashboard/logout-button';
import { LogOutIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { parseAsString, useQueryState } from 'nuqs';
import { useMemo } from 'react';

// Types
interface MainComponentProps {
  assignmentsLoading: boolean;
  filteredAssignments: IAssignment[];
  subjects: ISubject[];
  assignmentCount: number;
}

interface FilterMatchCriteria {
  matchesDifficulty: boolean;
  matchesStatus: boolean;
  matchesSubject: boolean;
  matchesCompletion: boolean;
}

// Custom hooks
const useFilteredAssignments = (
  assignments: IAssignment[] | undefined,
  subjectFilter: string,
  difficultyFilter: string,
  statusFilter: string,
  completionFilter: string,
) => {
  return useMemo(() => {
    if (!assignments) return [];

    return assignments.filter((assignment): boolean => {
      const isOverdue = isPast(new Date(assignment.dueDate));

      const criteria: FilterMatchCriteria = {
        matchesDifficulty: difficultyFilter === 'all' || assignment.difficulty === difficultyFilter,

        matchesSubject: subjectFilter === 'all' || assignment.subject === Number(subjectFilter),

        matchesStatus:
          statusFilter === 'all' ||
          (statusFilter === 'upcoming' && !isOverdue) ||
          (statusFilter === 'overdue' && isOverdue),

        matchesCompletion:
          completionFilter === 'all' ||
          (completionFilter === 'completed' && (assignment as any).isCompleted) ||
          (completionFilter === 'pending' && !(assignment as any).isCompleted),
      };

      return Object.values(criteria).every(Boolean);
    });
  }, [assignments, subjectFilter, difficultyFilter, statusFilter, completionFilter]);
};

// Header component
const PageHeader: React.FC<{
  assignmentsLoading: boolean;
  assignmentCount: number;
}> = ({ assignmentsLoading, assignmentCount }) => (
  <div className="flex w-full border-b-1 items-center p-2 bg-sidebar sticky top-0 z-10">
    <div className="flex-1 flex gap-3">
      <SidebarTrigger />
      <h1 className="text-lg font-bold text-foreground">
        Assignments {!assignmentsLoading && `(${assignmentCount})`}
      </h1>
    </div>

    <div className="flex w-fit gap-2 sm:mt-0">
      <ThemeToggle />
      <LogoutButton size="sm" variant="outline" className="gap-2">
        <LogOutIcon className="h-4 w-4" />
        Logout
      </LogoutButton>
    </div>
  </div>
);

// Content component
const AssignmentsContent: React.FC<{
  assignmentsLoading: boolean;
  filteredAssignments: IAssignment[];
  subjects: ISubject[];
}> = ({ assignmentsLoading, filteredAssignments, subjects }) => {
  const tableColumns = useMemo(() => createColumns(subjects), [subjects]);

  if (assignmentsLoading) {
    return (
      <div className="px-3">
        <Skeleton className="h-[calc(100vh-4.5rem)] w-full" />
      </div>
    );
  }

  if (filteredAssignments.length === 0) {
    return (
      <div className="px-3">
        <div className="text-center text-muted-foreground mt-10">
          No assignments match the selected filters.
        </div>
      </div>
    );
  }

  return (
    <div className="px-3">
      <DataTable columns={tableColumns} data={filteredAssignments} />
    </div>
  );
};

// Main component
const MainComponent: React.FC<MainComponentProps> = ({
  assignmentsLoading,
  filteredAssignments,
  subjects,
  assignmentCount,
}) => {
  const { state, isMobile } = useSidebar();

  const mainClasses = cn(
    'space-y-3 transition-all duration-300 ease-in-out max-sm:w-full',
    state === 'expanded' && !isMobile ? 'w-[calc(100%-var(--sidebar-width))]' : 'w-full',
  );

  return (
    <main className={mainClasses}>
      <PageHeader assignmentsLoading={assignmentsLoading} assignmentCount={assignmentCount} />

      <AssignmentsContent
        assignmentsLoading={assignmentsLoading}
        filteredAssignments={filteredAssignments}
        subjects={subjects}
      />
    </main>
  );
};

// Main page component
export default function AssignmentsPage() {
  // Query state management
  const [subjectFilter, setSubjectFilter] = useQueryState(
    'subject',
    parseAsString.withDefault('all'),
  );

  const [completionFilter, setCompletionFilter] = useQueryState(
    'filter',
    parseAsString.withDefault('all'),
  );

  const [difficultyFilter, setDifficultyFilter] = useQueryState(
    'difficulty',
    parseAsString.withDefault('all'),
  );

  const [statusFilter, setStatusFilter] = useQueryState('status', parseAsString.withDefault('all'));

  // Data fetching
  const { assignments, isLoading: assignmentsLoading } = useAssignments(subjectFilter);
  const { subjects, isLoading: subjectsLoading } = useSubjects();

  // Filtered data
  const filteredAssignments = useFilteredAssignments(
    assignments,
    subjectFilter,
    difficultyFilter,
    statusFilter,
    completionFilter,
  );

  return (
    <SidebarProvider>
      <AppSidebar
        subjects={subjects}
        subjectFilter={subjectFilter}
        setSubjectFilter={setSubjectFilter}
        subjectsLoading={subjectsLoading}
        difficultyFilter={difficultyFilter}
        setDifficultyFilter={setDifficultyFilter}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        completionFilter={completionFilter}
        setCompletionFilter={setCompletionFilter}
      />
      <MainComponent
        assignmentsLoading={assignmentsLoading}
        filteredAssignments={filteredAssignments}
        subjects={subjects}
        assignmentCount={filteredAssignments.length}
      />
    </SidebarProvider>
  );
}
