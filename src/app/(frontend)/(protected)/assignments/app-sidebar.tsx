import Logo from '@/components/Logo';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
} from '@/components/ui/sidebar';
import { Skeleton } from '@/components/ui/skeleton';
import { ISubject } from '@/hooks/use-subjects';
import { cn } from '@/lib/utils';
import { FilterX, BookOpen, TrendingUp, Clock, CheckIcon } from 'lucide-react';
import Link from 'next/link';
import { useCallback, useMemo } from 'react';

// Types
type FilterValue = string;
type CompletionFilter = 'all' | 'pending' | 'completed';
type StatusFilter = 'all' | 'upcoming' | 'overdue';
type DifficultyLevel = 'all' | 'easy' | 'medium' | 'hard';

interface FilterSectionProps {
  icon: React.ReactNode;
  label: string;
  value: FilterValue;
  onValueChange: (value: FilterValue) => void;
  isLoading?: boolean;
  activeIndicator?: React.ReactNode;
  children: React.ReactNode;
}

interface AppSidebarProps {
  subjects: ISubject[];
  subjectFilter: FilterValue;
  setSubjectFilter: (filter: FilterValue) => void;
  subjectsLoading: boolean;
  difficultyFilter: FilterValue;
  setDifficultyFilter: (filter: FilterValue) => void;
  statusFilter: FilterValue;
  setStatusFilter: (filter: FilterValue) => void;
  completionFilter: FilterValue;
  setCompletionFilter: (filter: FilterValue) => void;
}

// Constants
const DIFFICULTY_CONFIG: {
  [key in DifficultyLevel]: {
    color: string;
    label: string;
  };
} = {
  all: {
    color: '',
    label: 'All Difficulties',
  },
  easy: { color: 'bg-green-500', label: 'Easy' },
  medium: { color: 'bg-amber-500', label: 'Medium' },
  hard: { color: 'bg-red-500', label: 'Hard' },
} as const;

const STATUS_CONFIG: {
  [key in StatusFilter]: { color: string; label: string };
} = {
  all: { color: '', label: 'All Statuses' },
  upcoming: { color: 'bg-blue-500', label: 'Upcoming' },
  overdue: { color: 'bg-red-500', label: 'Overdue' },
} as const;

const COMPLETION_CONFIG: {
  [key in CompletionFilter]: { color: string; label: string };
} = {
  all: { color: '', label: 'All Submissions' },
  pending: { color: 'bg-orange-500', label: 'Pending' },
  completed: { color: 'bg-emerald-500', label: 'Completed' },
} as const;

// Utility functions
const getBadgeColor = (status: string): string => {
  switch (status) {
    // statuses
    case 'upcoming':
      return 'text-blue-600 bg-blue-50 border-blue-200 dark:text-blue-400 dark:bg-blue-950 dark:border-blue-800';
    case 'overdue':
      return 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-950 dark:border-red-800';

    // submission status
    case 'pending':
      return 'text-orange-600 bg-orange-50 border-orange-200 dark:text-orange-400 dark:bg-orange-950 dark:border-orange-800';
    case 'completed':
      return 'text-emerald-600 bg-emerald-50 border-emerald-200 dark:text-emerald-400 dark:bg-emerald-950 dark:border-emerald-800';

    // difficulty
    case 'easy':
      return 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-950 dark:border-green-800';
    case 'medium':
      return 'text-amber-600 bg-amber-50 border-amber-200 dark:text-amber-400 dark:bg-amber-950 dark:border-amber-800';
    case 'hard':
      return 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-950 dark:border-red-800';
    default:
      return '';
  }
};

const capitalizeFirst = (str: string): string => str.charAt(0).toUpperCase() + str.slice(1);

// Reusable Components
const FilterSection: React.FC<FilterSectionProps> = ({
  icon,
  label,
  value,
  onValueChange,
  isLoading = false,
  activeIndicator,
  children,
}) => (
  <div className="space-y-2">
    <div className="flex items-center gap-2">
      {icon}
      <label className="text-sm font-medium text-foreground">{label}</label>
      {activeIndicator}
    </div>
    {isLoading ? (
      <Skeleton className="w-full h-[2.3rem]" />
    ) : (
      <Select value={value} onValueChange={onValueChange}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder={`Select ${label.toLowerCase()}`} />
        </SelectTrigger>
        <SelectContent>{children}</SelectContent>
      </Select>
    )}
  </div>
);

const ColorIndicator: React.FC<{ color: string }> = ({ color }) => (
  <div className={cn('w-2 h-2 rounded-full', color)} />
);

const SelectItemWithIcon: React.FC<{
  value: string;
  color: string;
  label: string;
}> = ({ value, color, label }) => (
  <SelectItem value={value}>
    <div className="flex items-center gap-2">
      <ColorIndicator color={color} />
      {label}
    </div>
  </SelectItem>
);

export function AppSidebar({
  subjects,
  subjectFilter,
  setSubjectFilter,
  subjectsLoading,
  difficultyFilter,
  setDifficultyFilter,
  statusFilter,
  setStatusFilter,
  completionFilter,
  setCompletionFilter,
}: AppSidebarProps) {
  // Memoized calculations
  const activeFilters = useMemo(() => {
    return [
      subjectFilter !== 'all',
      difficultyFilter !== 'all',
      statusFilter !== 'all',
      completionFilter !== 'all',
    ].filter(Boolean).length;
  }, [subjectFilter, difficultyFilter, statusFilter, completionFilter]);

  const isButtonDisabled = useMemo(() => {
    return (
      (subjectFilter === 'all' &&
        difficultyFilter === 'all' &&
        statusFilter === 'all' &&
        completionFilter === 'all') ||
      subjectsLoading
    );
  }, [subjectFilter, difficultyFilter, statusFilter, completionFilter, subjectsLoading]);

  // Event handlers
  const handleClearFilters = useCallback(() => {
    setSubjectFilter('all');
    setDifficultyFilter('all');
    setStatusFilter('all');
    setCompletionFilter('all');
  }, [setSubjectFilter, setDifficultyFilter, setStatusFilter, setCompletionFilter]);

  // Active indicators
  const subjectActiveIndicator = subjectFilter !== 'all' && (
    <Badge variant="secondary" className="text-xs">
      Active
    </Badge>
  );

  const difficultyActiveIndicator = difficultyFilter !== 'all' && (
    <Badge variant="outline" className={cn('text-xs', getBadgeColor(difficultyFilter))}>
      {capitalizeFirst(difficultyFilter)}
    </Badge>
    // <DifficultyBadge difficulty={difficultyFilter as DifficultyLevel} variant="minimal" />
  );

  const statusActiveIndicator = statusFilter !== 'all' && (
    <Badge variant="outline" className={cn('text-xs', getBadgeColor(statusFilter))}>
      {capitalizeFirst(statusFilter)}
    </Badge>
  );

  const completionActiveIndicator = completionFilter !== 'all' && (
    <Badge variant="outline" className={cn('text-xs', getBadgeColor(completionFilter))}>
      {capitalizeFirst(completionFilter)}
    </Badge>
  );

  return (
    <Sidebar variant="sidebar" collapsible="offcanvas">
      <SidebarHeader className="border-b-1 p-[.63rem]">
        <Link href="/">
          <Logo />
        </Link>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-4 py-4">
              {/* Subject Filter */}
              <FilterSection
                icon={<BookOpen className="h-4 w-4 text-muted-foreground" />}
                label="Subject"
                value={subjectFilter}
                onValueChange={setSubjectFilter}
                isLoading={subjectsLoading}
                activeIndicator={subjectActiveIndicator}
              >
                <SelectItem value="all">All Subjects</SelectItem>
                {subjects?.map((subject) => (
                  <SelectItem key={subject.id} value={subject.id.toString()}>
                    {subject.name}
                  </SelectItem>
                ))}
              </FilterSection>

              {/* Difficulty Filter */}
              <FilterSection
                icon={<TrendingUp className="h-4 w-4 text-muted-foreground" />}
                label="Difficulty"
                value={difficultyFilter}
                onValueChange={setDifficultyFilter}
                isLoading={subjectsLoading}
                activeIndicator={difficultyActiveIndicator}
              >
                {Object.entries(DIFFICULTY_CONFIG).map(([key, config]) => (
                  <SelectItemWithIcon
                    key={key}
                    value={key}
                    color={config.color}
                    label={config.label}
                  />
                ))}
              </FilterSection>

              {/* Status Filter */}
              <FilterSection
                icon={<Clock className="h-4 w-4 text-muted-foreground" />}
                label="Status"
                value={statusFilter}
                onValueChange={setStatusFilter}
                isLoading={subjectsLoading}
                activeIndicator={statusActiveIndicator}
              >
                {Object.entries(STATUS_CONFIG).map(([key, config]) => (
                  <SelectItemWithIcon
                    key={key}
                    value={key}
                    color={config.color}
                    label={config.label}
                  />
                ))}
              </FilterSection>

              {/* Completion Filter */}
              <FilterSection
                icon={<CheckIcon className="h-4 w-4 text-muted-foreground" />}
                label="Submission"
                value={completionFilter}
                onValueChange={setCompletionFilter}
                isLoading={subjectsLoading}
                activeIndicator={completionActiveIndicator}
              >
                {Object.entries(COMPLETION_CONFIG).map(([key, config]) => (
                  <SelectItemWithIcon
                    key={key}
                    value={key}
                    color={config.color}
                    label={config.label}
                  />
                ))}
              </FilterSection>

              {/* Clear Filters Button */}
              <Button
                variant={activeFilters > 0 ? 'default' : 'outline'}
                size="sm"
                className={cn('w-full mt-4', isButtonDisabled && 'cursor-not-allowed opacity-50')}
                disabled={isButtonDisabled}
                onClick={handleClearFilters}
              >
                <FilterX className="h-4 w-4 mr-2" />
                Clear Filters
                {activeFilters > 0 && (
                  <Badge variant="secondary" className="ml-2 text-xs">
                    {activeFilters}
                  </Badge>
                )}
              </Button>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="p-4">
        <div className="text-xs text-muted-foreground text-center">
          {activeFilters > 0 && `${activeFilters} filter${activeFilters > 1 ? 's' : ''} active`}
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
