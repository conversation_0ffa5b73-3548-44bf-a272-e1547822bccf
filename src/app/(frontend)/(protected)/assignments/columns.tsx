'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { format, isPast } from 'date-fns';
import { ArrowUpDownIcon, MoreHorizontal } from 'lucide-react';
import Link from 'next/link';
import { DifficultyBadge } from '@/components/difficulty-badge';
import { PointsBadge } from '@/components/points-badge';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { IAssignment } from '@/hooks/use-assignments';
import type { ISubject } from '@/hooks/use-subjects';
import { cn } from '@/lib/utils';

// Constants for better maintainability
const DIFFICULTY_ORDER = { easy: 1, medium: 2, hard: 3 } as const;

export const createColumns = (subjects: ISubject[]): ColumnDef<IAssignment>[] => [
  {
    accessorKey: 'id',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-auto p-0 font-medium"
      >
        ID
        <ArrowUpDownIcon className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => (
      <div className="font-medium line-clamp-1 text-center" title={row.original.id.toString()}>
        {row.original.id}
      </div>
    ),
  },
  {
    accessorKey: 'title',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-auto p-0 font-medium"
      >
        Title
        <ArrowUpDownIcon className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => (
      <div
        className={cn(
          'font-medium line-clamp-1 hover:underline underline-offset-2',
          row.original?.isCompleted && 'line-through hover:line-through',
        )}
      >
        <Link href={`/assignments/${row.original.id}`} className="block" title={row.original.title}>
          {row.original.title}
        </Link>
      </div>
    ),
  },
  {
    accessorKey: 'subject',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-auto p-0 font-medium"
      >
        Subject
        <ArrowUpDownIcon className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const subject = subjects?.find((s) => s.id === row.original.subject);
      return (
        <Badge variant="outline" title={subject?.name || 'Unknown Subject'}>
          {subject?.name || 'Unknown'}
        </Badge>
      );
    },
    // Add sorting for subject names
    sortingFn: (rowA, rowB) => {
      const subjectA = subjects?.find((s) => s.id === rowA.original.subject)?.name || 'Unknown';
      const subjectB = subjects?.find((s) => s.id === rowB.original.subject)?.name || 'Unknown';
      return subjectA.localeCompare(subjectB);
    },
  },
  {
    accessorKey: 'difficulty',
    header: ({ column }) => (
      <div className="flex justify-center">
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="h-auto p-0 font-medium"
        >
          Difficulty
          <ArrowUpDownIcon className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="flex justify-center">
        <DifficultyBadge difficulty={row.original.difficulty} variant="minimal" />
      </div>
    ),
    accessorFn: (row) => {
      const difficulty = row.difficulty?.toLowerCase() as keyof typeof DIFFICULTY_ORDER;
      return DIFFICULTY_ORDER[difficulty] || 0;
    },
  },
  {
    accessorKey: 'points',
    header: ({ column }) => (
      <div className="flex justify-center">
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="h-auto p-0 font-medium"
        >
          Points
          <ArrowUpDownIcon className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="flex justify-center">
        <PointsBadge points={row.original.points} variant="minimal" />
      </div>
    ),
  },
  {
    accessorKey: 'dueDate',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-auto p-0 font-medium"
      >
        Due Date
        <ArrowUpDownIcon className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const date = new Date(row.original.dueDate);
      return (
        <span className="text-center" title={date.toLocaleString('en-IN')}>
          {format(date, 'dd-MM-yyyy')}
        </span>
      );
    },
    // Ensure proper date sorting
    sortingFn: (rowA, rowB) => {
      const dateA = new Date(rowA.original.dueDate);
      const dateB = new Date(rowB.original.dueDate);
      return dateA.getTime() - dateB.getTime();
    },
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        className="h-auto p-0 font-medium"
      >
        Status
        <ArrowUpDownIcon className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const isOverdue = isPast(new Date(row.original.dueDate));
      const status = isOverdue ? 'Overdue' : 'Upcoming';
      return <Badge variant={isOverdue ? 'destructive' : 'default'}>{status}</Badge>;
    },
    // Add sorting for status
    accessorFn: (row) => {
      const isOverdue = isPast(new Date(row.dueDate));
      return isOverdue ? 1 : 0; // Overdue first when ascending
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuItem asChild>
            <Link href={`/assignments/${row.original.id}`}>Visit Playground</Link>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => {
              navigator.clipboard.writeText(row.original.id.toString());
              // Consider adding a toast notification here
            }}
          >
            Copy Assignment ID
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    ),
  },
];
