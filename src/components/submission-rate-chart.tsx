// 'use client';

// import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer } from 'recharts';

// import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';

// interface SubmissionRateChartProps {
//   submitted: number;
//   notSubmitted: number;
// }

// export function SubmissionRateChart({ submitted, notSubmitted }: SubmissionRateChartProps) {
//   const data = [
//     { name: 'Submitted', value: submitted },
//     { name: 'Not Submitted', value: notSubmitted },
//   ];

//   return (
//     <ChartContainer
//       config={{
//         Submitted: {
//           label: 'Submitted',
//           color: 'hsl(var(--primary))',
//         },
//         'Not Submitted': {
//           label: 'Not Submitted',
//           color: 'hsl(var(--destructive))',
//         },
//       }}
//     >
//       <ResponsiveContainer width="100%" height="100%">
//         <PieChart>
//           <Pie
//             data={data}
//             cx="50%"
//             cy="50%"
//             innerRadius={60}
//             outerRadius={80}
//             paddingAngle={5}
//             dataKey="value"
//             nameKey="name"
//             label={({ name, percent }) => percent && `${name}: ${(percent * 100).toFixed(0)}%`}
//             labelLine={false}
//           >
//             {data.map((entry, index) => (
//               <Cell
//                 key={`cell-${index}`}
//                 fill={index === 0 ? 'var(--color-Submitted)' : 'var(--color-Not\\ Submitted)'}
//               />
//             ))}
//           </Pie>
//           <ChartTooltip content={<ChartTooltipContent />} />
//         </PieChart>
//       </ResponsiveContainer>
//     </ChartContainer>
//   );
// }
