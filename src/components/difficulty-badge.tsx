import { FlameIcon, GaugeIcon, LeafIcon } from 'lucide-react';
import type React from 'react';
import { cn } from '@/lib/utils';
import { Badge } from './ui/badge';

interface DifficultyBadgeProps {
  difficulty: 'easy' | 'medium' | 'hard' | null;
  variant?: 'minimal';
  className?: string;
}

const DIFFICULTY_CONFIG = {
  easy: {
    icon: LeafIcon,
    colors: {
      text: 'dark:text-green-300 text-green-700',
      background: 'dark:bg-green-500/10 bg-green-100',
      border: 'border-green-500',
    },
  },
  medium: {
    icon: GaugeIcon,
    colors: {
      text: 'dark:text-yellow-300 text-yellow-700',
      background: 'dark:bg-yellow-500/10 bg-yellow-100',
      border: 'border-yellow-500',
    },
  },
  hard: {
    icon: FlameIcon,
    colors: {
      text: 'dark:text-red-400 text-red-600',
      background: 'dark:bg-red-500/10 bg-red-100',
      border: 'border-red-500',
    },
  },
} as const;

export const DifficultyBadge: React.FC<DifficultyBadgeProps> = ({
  difficulty,
  variant,
  className,
}) => {
  if (!difficulty) return null;

  const config = DIFFICULTY_CONFIG[difficulty];
  const IconComponent = config.icon;
  const { text, background, border } = config.colors;

  const badgeClasses = cn(
    'capitalize rounded-full shadow-inner backdrop-blur-sm transition flex items-center gap-1.5',
    text,
    background,
    variant !== 'minimal' ? `px-3 py-1 border ${border}` : 'border-transparent',
    className,
  );

  return (
    <Badge variant="secondary" className={badgeClasses}>
      {variant !== 'minimal' && <IconComponent className="w-4 h-4" />}
      <span>{difficulty}</span>
    </Badge>
  );
};
