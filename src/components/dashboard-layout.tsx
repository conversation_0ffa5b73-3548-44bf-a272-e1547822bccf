'use client';

import type React from 'react';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  SidebarProvider,
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarFooter,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarInset,
} from '@/components/ui/sidebar';
import {
  BookOpenIcon,
  CodeIcon,
  HomeIcon,
  LogOutIcon,
  UsersIcon,
  SettingsIcon,
} from 'lucide-react';
import { useTheme } from '@/hooks/use-theme';

interface DashboardLayoutProps {
  children: React.ReactNode;
  userRole: 'student' | 'faculty';
}

export function DashboardLayout({ children, userRole }: DashboardLayoutProps) {
  const pathname = usePathname();
  const [isMounted, setIsMounted] = useState(false);

  // Hydration fix
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const studentNavItems = [
    {
      title: 'Dashboard',
      href: '/student',
      icon: HomeIcon,
    },
    {
      title: 'Assignments',
      href: '/student',
      icon: BookOpenIcon,
    },
    {
      title: 'Playground',
      href: '/student/playground/1',
      icon: CodeIcon,
    },
  ];

  const facultyNavItems = [
    {
      title: 'Dashboard',
      href: '/faculty',
      icon: HomeIcon,
    },
    {
      title: 'Assignments',
      href: '/faculty',
      icon: BookOpenIcon,
    },
    {
      title: 'Students',
      href: '/faculty/students',
      icon: UsersIcon,
    },
  ];

  const navItems = userRole === 'student' ? studentNavItems : facultyNavItems;

  const { setTheme, theme } = useTheme();

  if (!isMounted) {
    return null;
  }

  return (
    <SidebarProvider defaultOpen={true}>
      <div className="flex min-h-screen w-full">
        <Sidebar>
          <SidebarHeader>
            <div className="flex items-center gap-2 px-4 py-2">
              <div className="relative w-8 h-8 bg-primary rounded-md flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="w-4 h-4 text-white"
                >
                  <path d="m18 16 4-4-4-4" />
                  <path d="m6 8-4 4 4 4" />
                  <path d="m14.5 4-5 16" />
                </svg>
              </div>
              <span className="font-semibold text-lg">CS Lab</span>
            </div>
          </SidebarHeader>
          <SidebarContent>
            <SidebarMenu>
              {navItems.map((item) => (
                <SidebarMenuItem key={item.href}>
                  <SidebarMenuButton asChild isActive={pathname === item.href}>
                    <Link href={item.href}>
                      <item.icon className="h-5 w-5" />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarContent>
          <SidebarFooter>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Link href="/settings">
                    <SettingsIcon className="h-5 w-5" />
                    <span>Settings</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton
                  onClick={() => {
                    setTheme(theme === 'light' ? 'dark' : 'light');
                  }}
                >
                  Theme Toggle
                </SidebarMenuButton>
                <SidebarMenuButton asChild>
                  <Link href="/">
                    <LogOutIcon className="h-5 w-5" />
                    <span>Logout</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarFooter>
        </Sidebar>

        <SidebarInset>
          {/* <div className="flex flex-col h-full">
            <header className="h-16 border-b flex items-center justify-between px-6">
              <div className="flex items-center">
                <SidebarTrigger className="mr-4" />
                <h1 className="text-xl font-semibold">
                  {userRole === 'student' ? 'Student Portal' : 'Faculty Portal'}
                </h1>
              </div>
              <div className="flex items-center gap-4">
                <Button variant="ghost" size="icon" className="relative">
                  <BellIcon className="h-5 w-5" />
                  <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="rounded-full">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src="" alt="User" />
                        <AvatarFallback className="bg-primary text-primary-foreground">
                          {userRole === 'student' ? 'S' : 'F'}
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>
                      {userRole === 'student' ? 'Student Account' : 'Faculty Account'}
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <UserIcon className="w-4 h-4 mr-2" />
                      Profile
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <SettingsIcon className="w-4 h-4 mr-2" />
                      Settings
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/" className="flex items-center cursor-pointer">
                        <LogOutIcon className="w-4 h-4 mr-2" />
                        Logout
                      </Link>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </header> */}
          <main className="flex-1 overflow-auto">{children}</main>
          {/* </div> */}
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
}
