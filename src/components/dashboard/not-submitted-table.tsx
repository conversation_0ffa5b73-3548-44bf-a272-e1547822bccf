import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface Student {
  id: number;
  name: string;
  email: string;
  lastActive: string;
}

interface NotSubmittedTableProps {
  students: Student[];
}

export const NotSubmittedTable = ({ students }: NotSubmittedTableProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Students Who Haven&apos;t Submitted</CardTitle>
        <CardDescription>These students still need to submit their assignments</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Student</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Last Active</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {students.map((student) => (
              <TableRow key={student.id}>
                <TableCell className="font-medium">{student.name}</TableCell>
                <TableCell>{student.email}</TableCell>
                <TableCell>{student.lastActive}</TableCell>
                <TableCell className="text-right">
                  <Button variant="ghost" size="sm">
                    Remind
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};
