'use client';

import { Button, buttonVariants } from '@/components/ui/button';
import { VariantProps } from 'class-variance-authority';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

type Props = React.ComponentProps<'button'> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  };

export default function LogoutButton({ children, ...props }: Props) {
  const router = useRouter();

  const handleSubmit = async () => {
    try {
      const res = await fetch(`/api/users/logout`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!res.ok) {
        toast.error('Something went wrong');
        return;
      }
      const data = await res.json();
      if (data.error) {
        toast.error(data.error);
        return;
      }
      toast.success('Logged out successfully');
      // clear localstorage
      localStorage.clear();
      // clear session storage
      sessionStorage.clear();
      router.refresh();
      router.push('/login');
    } catch (err) {
      console.log(err);
      toast.error('Something went wrong');
    }
  };

  return (
    <Button
      {...props}
      variant="destructive"
      className="cursor-pointer"
      onClick={() => handleSubmit()}
    >
      {children}
    </Button>
  );
}
