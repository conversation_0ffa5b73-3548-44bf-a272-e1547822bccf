import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { formatDistanceToNow } from 'date-fns';

interface Submission {
  id: number;
  studentName: string;
  submittedAt: string;
  testResults: string;
  status: 'Passed' | 'Failed';
}

interface SubmissionsTableProps {
  submissions: Submission[];
}

export const SubmissionsTable = ({ submissions }: SubmissionsTableProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Latest Submissions</CardTitle>
        <CardDescription>View the most recent submissions from your students</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Student</TableHead>
              <TableHead>Submitted</TableHead>
              <TableHead>Test Results</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {submissions.map((submission) => (
              <TableRow key={submission.id}>
                <TableCell className="font-medium">{submission.studentName}</TableCell>
                <TableCell>
                  {formatDistanceToNow(new Date(submission.submittedAt), {
                    includeSeconds: true,
                  })}
                </TableCell>
                <TableCell>{submission.testResults}</TableCell>
                <TableCell>
                  {submission.status === 'Passed' ? (
                    <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Passed</Badge>
                  ) : (
                    <Badge variant="destructive">Failed</Badge>
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <Button variant="ghost" size="sm">
                    View
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};
