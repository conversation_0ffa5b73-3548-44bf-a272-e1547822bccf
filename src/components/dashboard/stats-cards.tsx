import { <PERSON><PERSON><PERSON>, CheckCircle2, Clock, FileText } from 'lucide-react';
import { StatCard } from './stat-card';

interface StatsCardsProps {
  data: {
    totalStudents: number;
    submitted: number;
    notSubmitted: number;
    onTime: number;
    late: number;
    averageScore: number;
    highestScore: number;
    lowestScore: number;
  };
}

export const StatsCards = ({ data }: StatsCardsProps) => {
  const stats = [
    {
      title: 'Total Students',
      metric: data.totalStudents,
      description: 'Enrolled in this assignment',
      icon: FileText,
      href: '#',
    },
    {
      title: 'Submission Rate',
      metric: Math.round((data.submitted / data.totalStudents) * 100),
      description: `${data.submitted} submitted, ${data.notSubmitted} pending`,
      icon: CheckCircle2,
      href: '#',
    },
    {
      title: 'On-Time Rate',
      metric: Math.round((data.onTime / data.submitted) * 100),
      description: `${data.onTime} on time, ${data.late} late`,
      icon: Clock,
      href: '#',
    },
    {
      title: 'Average Score',
      metric: data.averageScore,
      description: `Range: ${data.lowestScore}% - ${data.highestScore}%`,
      icon: BarChart,
      href: '#',
    },
  ];

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat) => (
        <StatCard key={stat.title} {...stat} />
      ))}
    </div>
  );
};
