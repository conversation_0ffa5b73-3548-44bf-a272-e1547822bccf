import { Card, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import type { LucideIcon } from 'lucide-react';
import Link from 'next/link';

interface StatCardProps {
  title: string;
  metric: number;
  icon: LucideIcon;
  trend?: 'positive' | 'negative' | 'neutral' | 'warning';
  href: string;
}

export function StatCard({ title, metric, icon: Icon, trend = 'neutral', href }: StatCardProps) {
  const trendColors = {
    positive: 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-950',
    negative: 'text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-950',
    warning: 'text-amber-600 bg-amber-50 dark:text-amber-400 dark:bg-amber-950',
    neutral: 'text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-950',
  };

  return (
    <Link href={href} className="block group">
      <Card className="relative overflow-hidden transition-all duration-200 hover:shadow-lg hover:-translate-y-1 cursor-pointer">
        <CardHeader className="flex flex-row items-center justify-between space-y-0">
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground group-hover:text-foreground transition-colors">
              {title}
            </p>
            <div className="flex items-baseline gap-2">
              <p className="text-3xl font-bold tracking-tight group-hover:text-primary transition-colors font-mono">
                {metric.toLocaleString()}
              </p>
            </div>
          </div>
          <div
            className={cn(
              'flex h-12 w-12 items-center justify-center rounded-lg transition-all duration-200 group-hover:scale-110',
              trendColors[trend],
            )}
          >
            <Icon className="h-6 w-6" />
          </div>
        </CardHeader>
      </Card>
    </Link>
  );
}

export function StatSkeleton() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <div className="space-y-2">
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-[2.2rem] w-16" />
        </div>
        <Skeleton className="h-12 w-12 rounded-lg" />
      </CardHeader>
    </Card>
  );
}
