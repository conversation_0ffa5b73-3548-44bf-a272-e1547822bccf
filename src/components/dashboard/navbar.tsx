'use client';

import Link from 'next/link';
import Logo from '@/components/Logo';
import { ThemeToggle } from '@/components/theme-toggle';
import LogoutButton from '@/components/dashboard/logout-button';
import { LogOutIcon } from 'lucide-react';
import React from 'react';

export const Navbar = ({ Title }: { Title?: React.ReactNode }) => {
  return (
    <div className="border-b bg-muted/30 w-full sticky top-0 flex justify-center backdrop-blur-lg items-center z-50">
      <div className="container py-3 mx-auto space-y-8 max-w-7xl px-4 sm:px-6 lg:px-8 flex flex-row items-center justify-between gap-4">
        {Title ?? (
          <Link href="/" className="mb-0">
            <Logo />
          </Link>
        )}
        <div className="flex w-fit gap-2 sm:mt-0">
          <ThemeToggle />
          <LogoutButton size="sm" variant="outline" className="gap-2">
            <LogOutIcon className="h-4 w-4" />
            Logout
          </LogoutButton>
        </div>
      </div>
    </div>
  );
};
