import { Filter, Search, SlidersHorizontal } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface SearchControlsProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;
}

export const SearchControls = ({ searchQuery, onSearchChange }: SearchControlsProps) => {
  return (
    <div className="flex items-center gap-2 justify-end">
      <Input
        placeholder="Search students..."
        value={searchQuery}
        onChange={(e) => onSearchChange(e.target.value)}
        className="max-w-sm"
      />
      <Button variant="outline" size="icon" className="aspect-square">
        <Search className="h-4 w-4" />
        <span className="sr-only">Search</span>
      </Button>
      <Button variant="outline" size="icon" className="aspect-square">
        <Filter className="h-4 w-4" />
        <span className="sr-only">Filter</span>
      </Button>
      <Button variant="outline" size="icon" className="aspect-square">
        <SlidersHorizontal className="h-4 w-4" />
        <span className="sr-only">View options</span>
      </Button>
    </div>
  );
};
