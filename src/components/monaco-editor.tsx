'use client';

import Editor, { type Monaco, type OnMount } from '@monaco-editor/react';
import type { editor } from 'monaco-editor';
import { useRef } from 'react';
import { useTheme } from '@/hooks/use-theme';

interface MonacoEditorProps {
  language: string;
  value: string;
  onChange: (value: string) => void;
  readOnly?: boolean;
  height?: string;
  options?: editor.IStandaloneEditorConstructionOptions;
  onMount?: OnMount;
}

export function MonacoEditor({
  language,
  value,
  onChange,
  readOnly = false,
  height = '100%',
  options,
  onMount,
}: MonacoEditorProps) {
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);
  const monacoRef = useRef<Monaco | null>(null);

  const handleEditorDidMount: OnMount = (editor, monaco) => {
    editorRef.current = editor;
    monacoRef.current = monaco;

    // Set up HTML intellisense
    if (language === 'html') {
      monaco.languages.html.htmlDefaults.setOptions({
        // format: {
        //   tabSize: 2,
        //   insertSpaces: true,
        // },
        suggest: {
          html5: true,
        },
      });
    }

    // Set up CSS intellisense
    if (language === 'css') {
      monaco.languages.css.cssDefaults.setOptions({
        // format: {
        //   tabSize: 2,
        //   insertSpaces: true,
        // },
        validate: true,
        lint: {
          compatibleVendorPrefixes: 'warning',
          vendorPrefix: 'warning',
          duplicateProperties: 'warning',
          emptyRules: 'warning',
        },
      });
    }

    // Set up JavaScript intellisense
    if (language === 'javascript') {
      monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions({
        noSemanticValidation: false,
        noSyntaxValidation: false,
      });

      monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
        target: monaco.languages.typescript.ScriptTarget.ES2020,
        allowNonTsExtensions: true,
        moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
        module: monaco.languages.typescript.ModuleKind.CommonJS,
        noEmit: true,
        lib: ['dom', 'es2020'],
      });
    }

    // Focus the editor
    editor.focus();
  };

  const { resolvedTheme } = useTheme();

  return (
    <Editor
      height={height}
      language={language}
      value={value}
      theme={resolvedTheme === 'dark' ? 'vs-dark' : 'light'}
      onChange={(value) => onChange(value || '')}
      onMount={onMount || handleEditorDidMount}
      options={{
        minimap: { enabled: false },
        scrollBeyondLastLine: false,
        fontSize: 14,
        lineNumbers: 'on',
        readOnly,
        // wordWrap: '',
        tabSize: 2,
        insertSpaces: true,
        automaticLayout: true,
        padding: { top: 10 },
        scrollbar: {
          useShadows: false,
          verticalHasArrows: false,
          horizontalHasArrows: false,
          vertical: 'auto',
          horizontal: 'auto',
        },
        ...options,
      }}
    />
  );
}
