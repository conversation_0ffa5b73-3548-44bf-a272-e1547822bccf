// 'use client';

// import {
//   Area,
//   AreaChart,
//   CartesianGrid,
//   Legend,
//   ResponsiveContainer,
//   XAxis,
//   YAxis,
// } from 'recharts';

// import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';

// // Mock data - replace with your actual data
// const data = [
//   { category: 'Assignments', student: 82, classAverage: 78 },
//   { category: 'Quizzes', student: 88, classAverage: 75 },
//   { category: 'Projects', student: 85, classAverage: 80 },
//   { category: 'Participation', student: 75, classAverage: 72 },
//   { category: 'Tests', student: 90, classAverage: 82 },
// ];

// export function ComparisonChart() {
//   return (
//     <ChartContainer
//       config={{
//         student: {
//           label: 'This Student',
//           color: 'hsl(var(--primary))',
//         },
//         classAverage: {
//           label: 'Class Average',
//           color: 'hsl(var(--muted-foreground))',
//         },
//       }}
//     >
//       <ResponsiveContainer width="100%" height="100%">
//         <AreaChart
//           data={data}
//           margin={{
//             top: 20,
//             right: 30,
//             left: 20,
//             bottom: 5,
//           }}
//         >
//           <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
//           <XAxis
//             dataKey="category"
//             tickLine={false}
//             axisLine={false}
//             className="text-xs text-muted-foreground"
//           />
//           <YAxis
//             tickLine={false}
//             axisLine={false}
//             className="text-xs text-muted-foreground"
//             domain={[0, 100]}
//             ticks={[0, 20, 40, 60, 80, 100]}
//           />
//           <ChartTooltip content={<ChartTooltipContent />} />
//           <Legend />
//           <Area
//             type="monotone"
//             dataKey="student"
//             stroke="var(--color-student)"
//             fill="var(--color-student)"
//             fillOpacity={0.3}
//             strokeWidth={2}
//           />
//           <Area
//             type="monotone"
//             dataKey="classAverage"
//             stroke="var(--color-classAverage)"
//             fill="var(--color-classAverage)"
//             fillOpacity={0.3}
//             strokeWidth={2}
//           />
//         </AreaChart>
//       </ResponsiveContainer>
//     </ChartContainer>
//   );
// }
