// 'use client';

// import { useState } from 'react';
// import { ArrowLeft, Calendar, Download, Mail, Phone } from 'lucide-react';
// import Link from 'next/link';

// import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
// import { Badge } from '@/components/ui/badge';
// import { Button } from '@/components/ui/button';
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
// import { Progress } from '@/components/ui/progress';
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableHead,
//   TableHeader,
//   TableRow,
// } from '@/components/ui/table';
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from '@/components/ui/select';
// import { PerformanceChart } from './performance-chart';
// import { SubmissionTimeline } from './submission-timeline';
// // import { Skill<PERSON>adar<PERSON>hart } from "../skill-radar-chart"
// import { ComparisonChart } from './comparison-chart';

// // Mock student data - replace with your actual data fetching logic
// const studentData = {
//   id: '1',
//   name: 'Emma Davis',
//   email: '<EMAIL>',
//   phone: '+****************',
//   enrollmentDate: 'September 2, 2024',
//   avatar: '/placeholder.svg?height=128&width=128',
//   overallGrade: 'A-',
//   overallScore: 87,
//   submissionRate: 95,
//   onTimeRate: 90,
//   averageTestScore: 85,
//   strengths: ['Data Analysis', 'Problem Solving', 'Critical Thinking'],
//   areasForImprovement: ['Time Management', 'Documentation'],
//   skillScores: {
//     'Problem Solving': 85,
//     'Critical Thinking': 82,
//     'Data Analysis': 90,
//     Documentation: 70,
//     'Time Management': 65,
//   },
// };

// // Mock submission history
// const submissionHistory = [
//   {
//     id: 1,
//     assignment: 'Final Project',
//     submittedAt: 'Apr 28, 2025 - 14:32',
//     dueDate: 'Apr 30, 2025',
//     status: 'On Time',
//     version: 'v3',
//     testResults: '85%',
//     grade: 'A-',
//   },
//   {
//     id: 2,
//     assignment: 'Midterm Project',
//     submittedAt: 'Mar 15, 2025 - 09:45',
//     dueDate: 'Mar 15, 2025',
//     status: 'On Time',
//     version: 'v2',
//     testResults: '92%',
//     grade: 'A',
//   },
//   {
//     id: 3,
//     assignment: 'Assignment 3',
//     submittedAt: 'Feb 22, 2025 - 23:58',
//     dueDate: 'Feb 22, 2025',
//     status: 'On Time',
//     version: 'v1',
//     testResults: '78%',
//     grade: 'B+',
//   },
//   {
//     id: 4,
//     assignment: 'Assignment 2',
//     submittedAt: 'Feb 08, 2025 - 16:30',
//     dueDate: 'Feb 07, 2025',
//     status: 'Late',
//     version: 'v2',
//     testResults: '75%',
//     grade: 'B',
//   },
//   {
//     id: 5,
//     assignment: 'Assignment 1',
//     submittedAt: 'Jan 25, 2025 - 14:15',
//     dueDate: 'Jan 25, 2025',
//     status: 'On Time',
//     version: 'v1',
//     testResults: '88%',
//     grade: 'A-',
//   },
// ];

// // Mock performance data for charts
// const performanceData = [
//   { assignment: 'Assignment 1', score: 88, classAverage: 82 },
//   { assignment: 'Assignment 2', score: 75, classAverage: 78 },
//   { assignment: 'Assignment 3', score: 78, classAverage: 75 },
//   { assignment: 'Midterm', score: 92, classAverage: 80 },
//   { assignment: 'Final', score: 85, classAverage: 79 },
// ];

// export default function StudentAnalysis() {
//   const [selectedTimeframe, setSelectedTimeframe] = useState('semester');

//   return (
//     <div className="flex flex-col min-h-screen">
//       <div className="flex-1 space-y-6 p-6 md:p-8">
//         <div className="flex items-center gap-3">
//           <Button variant="outline" size="icon" asChild>
//             <Link href="/admin/students">
//               <ArrowLeft className="h-4 w-4" />
//               <span className="sr-only">Back to students</span>
//             </Link>
//           </Button>
//           <h1 className="text-2xl font-bold tracking-tight">Student Analysis</h1>
//           <div className="ml-auto flex items-center gap-2">
//             <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
//               <SelectTrigger className="w-[180px]">
//                 <SelectValue placeholder="Select timeframe" />
//               </SelectTrigger>
//               <SelectContent>
//                 <SelectItem value="semester">Current Semester</SelectItem>
//                 <SelectItem value="year">Academic Year</SelectItem>
//                 <SelectItem value="all">All Time</SelectItem>
//               </SelectContent>
//             </Select>
//             <Button variant="outline" size="icon">
//               <Download className="h-4 w-4" />
//               <span className="sr-only">Download report</span>
//             </Button>
//           </div>
//         </div>

//         <div className="grid gap-6 md:grid-cols-3">
//           <Card className="md:col-span-1">
//             <CardHeader className="flex flex-row items-center gap-4">
//               <Avatar className="h-16 w-16">
//                 <AvatarImage
//                   src={studentData.avatar || '/placeholder.svg'}
//                   alt={studentData.name}
//                 />
//                 <AvatarFallback>{studentData.name.charAt(0)}</AvatarFallback>
//               </Avatar>
//               <div>
//                 <CardTitle>{studentData.name}</CardTitle>
//                 <CardDescription>Student ID: {studentData.id}</CardDescription>
//                 <Badge className="mt-1">{studentData.overallGrade}</Badge>
//               </div>
//             </CardHeader>
//             <CardContent className="grid gap-4">
//               <div className="flex items-center gap-2 text-sm">
//                 <Mail className="h-4 w-4 text-muted-foreground" />
//                 <span>{studentData.email}</span>
//               </div>
//               <div className="flex items-center gap-2 text-sm">
//                 <Phone className="h-4 w-4 text-muted-foreground" />
//                 <span>{studentData.phone}</span>
//               </div>
//               <div className="flex items-center gap-2 text-sm">
//                 <Calendar className="h-4 w-4 text-muted-foreground" />
//                 <span>Enrolled: {studentData.enrollmentDate}</span>
//               </div>
//               <div className="grid gap-2 pt-4">
//                 <div className="flex items-center justify-between text-sm">
//                   <span>Overall Score</span>
//                   <span className="font-medium">{studentData.overallScore}%</span>
//                 </div>
//                 <Progress value={studentData.overallScore} className="h-2" />
//               </div>
//               <div className="grid gap-2">
//                 <div className="flex items-center justify-between text-sm">
//                   <span>Submission Rate</span>
//                   <span className="font-medium">{studentData.submissionRate}%</span>
//                 </div>
//                 <Progress value={studentData.submissionRate} className="h-2" />
//               </div>
//               <div className="grid gap-2">
//                 <div className="flex items-center justify-between text-sm">
//                   <span>On-Time Rate</span>
//                   <span className="font-medium">{studentData.onTimeRate}%</span>
//                 </div>
//                 <Progress value={studentData.onTimeRate} className="h-2" />
//               </div>
//               <div className="grid gap-2">
//                 <div className="flex items-center justify-between text-sm">
//                   <span>Average Test Score</span>
//                   <span className="font-medium">{studentData.averageTestScore}%</span>
//                 </div>
//                 <Progress value={studentData.averageTestScore} className="h-2" />
//               </div>
//             </CardContent>
//           </Card>

//           <Card className="md:col-span-2">
//             <CardHeader>
//               <CardTitle>Performance Overview</CardTitle>
//               <CardDescription>Scores compared to class average</CardDescription>
//             </CardHeader>
//             <CardContent className="h-[300px]">
//               <PerformanceChart data={performanceData} />
//             </CardContent>
//           </Card>
//         </div>

//         <div className="grid gap-6 md:grid-cols-2">
//           <Card>
//             <CardHeader>
//               <CardTitle>Submission Timeline</CardTitle>
//               <CardDescription>History of assignment submissions</CardDescription>
//             </CardHeader>
//             <CardContent className="h-[300px]">
//               <SubmissionTimeline />
//             </CardContent>
//           </Card>
//           <Card>
//             <CardHeader>
//               <CardTitle>Skill Assessment</CardTitle>
//               <CardDescription>Strengths and areas for improvement</CardDescription>
//             </CardHeader>
//             <CardContent className="h-[300px]">
//               {/* <SkillRadarChart data={studentData.skillScores} /> */}
//             </CardContent>
//           </Card>
//         </div>

//         <Card>
//           <CardHeader>
//             <CardTitle>Submission History</CardTitle>
//             <CardDescription>All assignments submitted by this student</CardDescription>
//           </CardHeader>
//           <CardContent>
//             <Table>
//               <TableHeader>
//                 <TableRow>
//                   <TableHead>Assignment</TableHead>
//                   <TableHead>Submitted</TableHead>
//                   <TableHead>Due Date</TableHead>
//                   <TableHead>Status</TableHead>
//                   <TableHead>Version</TableHead>
//                   <TableHead>Test Results</TableHead>
//                   <TableHead>Grade</TableHead>
//                   <TableHead className="text-right">Actions</TableHead>
//                 </TableRow>
//               </TableHeader>
//               <TableBody>
//                 {submissionHistory.map((submission) => (
//                   <TableRow key={submission.id}>
//                     <TableCell className="font-medium">{submission.assignment}</TableCell>
//                     <TableCell>{submission.submittedAt}</TableCell>
//                     <TableCell>{submission.dueDate}</TableCell>
//                     <TableCell>
//                       {submission.status === 'On Time' ? (
//                         <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
//                           On Time
//                         </Badge>
//                       ) : (
//                         <Badge
//                           variant="secondary"
//                           className="bg-amber-100 text-amber-800 hover:bg-amber-100"
//                         >
//                           Late
//                         </Badge>
//                       )}
//                     </TableCell>
//                     <TableCell>{submission.version}</TableCell>
//                     <TableCell>{submission.testResults}</TableCell>
//                     <TableCell>{submission.grade}</TableCell>
//                     <TableCell className="text-right">
//                       <Button variant="ghost" size="sm">
//                         View
//                       </Button>
//                     </TableCell>
//                   </TableRow>
//                 ))}
//               </TableBody>
//             </Table>
//           </CardContent>
//         </Card>

//         <div className="grid gap-6 md:grid-cols-2">
//           <Card>
//             <CardHeader>
//               <CardTitle>Strengths</CardTitle>
//               <CardDescription>Areas where the student excels</CardDescription>
//             </CardHeader>
//             <CardContent>
//               <ul className="space-y-2">
//                 {studentData.strengths.map((strength, index) => (
//                   <li key={index} className="flex items-center gap-2">
//                     <Badge
//                       variant="outline"
//                       className="bg-green-50 text-green-700 hover:bg-green-50"
//                     >
//                       {strength}
//                     </Badge>
//                     {/* <Progress value={studentData.skillScores[strength]} className="h-2 flex-1" />
//                     <span className="text-sm font-medium">{studentData.skillScores[strength]}%</span> */}
//                   </li>
//                 ))}
//               </ul>
//             </CardContent>
//           </Card>
//           <Card>
//             <CardHeader>
//               <CardTitle>Areas for Improvement</CardTitle>
//               <CardDescription>Skills that need additional focus</CardDescription>
//             </CardHeader>
//             <CardContent>
//               <ul className="space-y-2">
//                 {studentData.areasForImprovement.map((area, index) => (
//                   <li key={index} className="flex items-center gap-2">
//                     <Badge
//                       variant="outline"
//                       className="bg-amber-50 text-amber-700 hover:bg-amber-50"
//                     >
//                       {area}
//                     </Badge>
//                     {/* <Progress value={studentData.skillScores[area]} className="h-2 flex-1" />
//                     <span className="text-sm font-medium">{studentData.skillScores[area]}%</span> */}
//                   </li>
//                 ))}
//               </ul>
//             </CardContent>
//           </Card>
//         </div>

//         <Card>
//           <CardHeader>
//             <CardTitle>Performance Comparison</CardTitle>
//             <CardDescription>How this student compares to the class average</CardDescription>
//           </CardHeader>
//           <CardContent className="h-[300px]">
//             <ComparisonChart />
//           </CardContent>
//         </Card>
//       </div>
//     </div>
//   );
// }
