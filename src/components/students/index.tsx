'use client';

import { useState } from 'react';
import {
  ArrowLeftIcon,
  Download,
  Filter,
  MoreHorizontal,
  Search,
  SlidersHorizontal,
} from 'lucide-react';
import Link from 'next/link';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

// Mock student data - replace with your actual data fetching logic
const students = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    grade: 'A-',
    submissionRate: 95,
    testAverage: 85,
    lastSubmission: 'Apr 28, 2025',
  },
  {
    id: '2',
    name: '<PERSON> <PERSON>',
    email: '<EMAIL>',
    grade: 'A',
    submissionRate: 100,
    testAverage: 92,
    lastSubmission: 'Apr 28, 2025',
  },
  {
    id: '3',
    name: 'Sophia Chen',
    email: '<EMAIL>',
    grade: 'B+',
    submissionRate: 90,
    testAverage: 78,
    lastSubmission: 'Apr 28, 2025',
  },
  {
    id: '4',
    name: 'Daniel Kim',
    email: '<EMAIL>',
    grade: 'C+',
    submissionRate: 75,
    testAverage: 65,
    lastSubmission: 'Apr 27, 2025',
  },
  {
    id: '5',
    name: 'Isabella Lopez',
    email: '<EMAIL>',
    grade: 'A-',
    submissionRate: 95,
    testAverage: 88,
    lastSubmission: 'Apr 27, 2025',
  },
  {
    id: '6',
    name: 'Noah Patel',
    email: '<EMAIL>',
    grade: 'B',
    submissionRate: 85,
    testAverage: 73,
    lastSubmission: 'Apr 27, 2025',
  },
  {
    id: '7',
    name: 'Olivia Brown',
    email: '<EMAIL>',
    grade: 'B-',
    submissionRate: 80,
    testAverage: 70,
    lastSubmission: 'Apr 26, 2025',
  },
  {
    id: '8',
    name: 'James Wilson',
    email: '<EMAIL>',
    grade: 'A',
    submissionRate: 100,
    testAverage: 95,
    lastSubmission: 'Apr 28, 2025',
  },
];

export default function StudentsPage() {
  const [searchQuery, setSearchQuery] = useState('');

  // Filter students based on search query
  const filteredStudents = students.filter(
    (student) =>
      student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      student.email.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  return (
    <div className="flex flex-col min-h-screen">
      <div className="flex-1 space-y-6 p-6 md:p-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button variant="outline" size="icon" asChild>
              <Link href="/admin">
                <ArrowLeftIcon className="h-4 w-4" />
                <span className="sr-only">Back to students</span>
              </Link>
            </Button>
            <h1 className="text-2xl font-bold tracking-tight">List of Students</h1>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon">
              <Download className="h-4 w-4" />
              <span className="sr-only">Download report</span>
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Input
              placeholder="Search students..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-sm"
            />
            <Button variant="outline" size="icon">
              <Search className="h-4 w-4" />
              <span className="sr-only">Search</span>
            </Button>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
              <span className="sr-only">Filter</span>
            </Button>
            <Button variant="outline" size="icon">
              <SlidersHorizontal className="h-4 w-4" />
              <span className="sr-only">View options</span>
            </Button>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Student List</CardTitle>
              <CardDescription>View and analyze student performance</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Student</TableHead>
                    <TableHead>Grade</TableHead>
                    <TableHead>Submission Rate</TableHead>
                    <TableHead>Test Average</TableHead>
                    <TableHead>Last Submission</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredStudents.map((student) => (
                    <TableRow key={student.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage
                              src={`/placeholder.svg?height=32&width=32`}
                              alt={student.name}
                            />
                            <AvatarFallback>{student.name.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{student.name}</div>
                            <div className="text-sm text-muted-foreground">{student.email}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={
                            student.grade.startsWith('A')
                              ? 'bg-green-50 text-green-700 hover:bg-green-50'
                              : student.grade.startsWith('B')
                                ? 'bg-blue-50 text-blue-700 hover:bg-blue-50'
                                : 'bg-amber-50 text-amber-700 hover:bg-amber-50'
                          }
                        >
                          {student.grade}
                        </Badge>
                      </TableCell>
                      <TableCell>{student.submissionRate}%</TableCell>
                      <TableCell>{student.testAverage}%</TableCell>
                      <TableCell>{student.lastSubmission}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/students/${student.id}`}>View Analysis</Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem>Send Message</DropdownMenuItem>
                            <DropdownMenuItem>Download Report</DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
