// 'use client';

// import { Line, LineChart, CartesianGrid, ResponsiveContainer, XAxis, <PERSON><PERSON><PERSON><PERSON> } from 'recharts';

// import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';

// // Mock data - replace with your actual data
// const data = [
//   { date: 'Jan 25', onTime: 1, late: 0 },
//   { date: 'Feb 08', onTime: 0, late: 1 },
//   { date: 'Feb 22', onTime: 1, late: 0 },
//   { date: 'Mar 15', onTime: 1, late: 0 },
//   { date: 'Apr 28', onTime: 1, late: 0 },
// ];

// export function SubmissionTimeline() {
//   return (
//     <ChartContainer
//       config={{
//         onTime: {
//           label: 'On Time',
//           color: 'hsl(var(--success))',
//         },
//         late: {
//           label: 'Late',
//           color: 'hsl(var(--warning))',
//         },
//       }}
//     >
//       <ResponsiveContainer width="100%" height="100%">
//         <LineChart
//           data={data}
//           margin={{
//             top: 20,
//             right: 30,
//             left: 20,
//             bottom: 5,
//           }}
//         >
//           <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
//           <XAxis
//             dataKey="date"
//             tickLine={false}
//             axisLine={false}
//             className="text-xs text-muted-foreground"
//           />
//           <YAxis
//             tickLine={false}
//             axisLine={false}
//             className="text-xs text-muted-foreground"
//             domain={[0, 1]}
//             ticks={[0, 1]}
//             tickFormatter={(value) => (value === 1 ? 'Yes' : 'No')}
//           />
//           <ChartTooltip content={<ChartTooltipContent />} />
//           <Line
//             type="monotone"
//             dataKey="onTime"
//             stroke="var(--color-onTime)"
//             strokeWidth={2}
//             dot={{ r: 4, strokeWidth: 2 }}
//             activeDot={{ r: 6 }}
//           />
//           <Line
//             type="monotone"
//             dataKey="late"
//             stroke="var(--color-late)"
//             strokeWidth={2}
//             dot={{ r: 4, strokeWidth: 2 }}
//             activeDot={{ r: 6 }}
//           />
//         </LineChart>
//       </ResponsiveContainer>
//     </ChartContainer>
//   );
// }
