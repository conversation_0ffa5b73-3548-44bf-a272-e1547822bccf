// 'use client';

// import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, Legend, ResponsiveContainer, XAxis, Y<PERSON><PERSON><PERSON> } from 'recharts';

// import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';

// interface PerformanceChartProps {
//   data: {
//     assignment: string;
//     score: number;
//     classAverage: number;
//   }[];
// }

// export function PerformanceChart({ data }: PerformanceChartProps) {
//   return (
//     <ChartContainer
//       config={{
//         score: {
//           label: 'Student Score',
//           color: 'hsl(var(--primary))',
//         },
//         classAverage: {
//           label: 'Class Average',
//           color: 'hsl(var(--muted-foreground))',
//         },
//       }}
//     >
//       <ResponsiveContainer width="100%" height="100%">
//         <BarChart
//           data={data}
//           margin={{
//             top: 20,
//             right: 30,
//             left: 20,
//             bottom: 5,
//           }}
//         >
//           <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
//           <XAxis
//             dataKey="assignment"
//             tickLine={false}
//             axisLine={false}
//             className="text-xs text-muted-foreground"
//           />
//           <YAxis
//             tickLine={false}
//             axisLine={false}
//             className="text-xs text-muted-foreground"
//             domain={[0, 100]}
//             ticks={[0, 20, 40, 60, 80, 100]}
//           />
//           <ChartTooltip content={<ChartTooltipContent />} />
//           <Legend />
//           <Bar dataKey="score" fill="var(--color-score)" radius={[4, 4, 0, 0]} maxBarSize={50} />
//           <Bar
//             dataKey="classAverage"
//             fill="var(--color-classAverage)"
//             radius={[4, 4, 0, 0]}
//             maxBarSize={50}
//           />
//         </BarChart>
//       </ResponsiveContainer>
//     </ChartContainer>
//   );
// }
