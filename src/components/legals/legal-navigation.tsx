import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { FileText, Shield } from 'lucide-react';

export function LegalNavigation() {
  return (
    <div className="flex flex-col sm:flex-row gap-4 justify-center items-center p-6 bg-muted/50 rounded-lg border">
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <FileText className="size-4" />
        <span>Legal Documents:</span>
      </div>
      <div className="flex gap-2">
        <Button variant="outline" size="sm" asChild>
          <Link href="/privacy" className="flex items-center gap-2">
            <Shield className="size-4" />
            Privacy Policy
          </Link>
        </Button>
        <Button variant="outline" size="sm" asChild>
          <Link href="/terms" className="flex items-center gap-2">
            <FileText className="size-4" />
            Terms of Service
          </Link>
        </Button>
      </div>
    </div>
  );
}
