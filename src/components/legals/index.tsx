import type { Metadata } from 'next';
import { CalendarIcon } from 'lucide-react';
import { LegalNavigation } from './legal-navigation';

interface LegalSection {
  id: string;
  title?: string;
  content?: string;
  list?: string[];
}

interface LegalData {
  title: string;
  lastUpdated: string;
  companyName: string;
  contactEmail: string;
  sections: LegalSection[];
}

interface LegalPageLayoutProps {
  data: LegalData;
  pageType: 'privacy' | 'terms' | 'general';
}

// Generate metadata for legal pages
export function generateLegalMetadata(data: LegalData, pageType: string): Metadata {
  const descriptions = {
    privacy: `${data.companyName} Privacy Policy - Learn how we collect, use, and protect your personal information.`,
    terms: `${data.companyName} Terms of Service - Review the terms and conditions for using our platform and services.`,
    general: `${data.companyName} ${data.title} - Important legal information and policies.`,
  };

  return {
    title: data.title,
    description: descriptions[pageType as keyof typeof descriptions] || descriptions.general,
    openGraph: {
      title: `${data.title} | ${data.companyName}`,
      description: descriptions[pageType as keyof typeof descriptions] || descriptions.general,
      type: 'website',
    },
    twitter: {
      card: 'summary',
      title: `${data.title} | ${data.companyName}`,
      description: descriptions[pageType as keyof typeof descriptions] || descriptions.general,
    },
  };
}

export function LegalPageLayout({ data, pageType }: LegalPageLayoutProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getFooterText = () => {
    const formattedDate = formatDate(data.lastUpdated);
    switch (pageType) {
      case 'privacy':
        return `This privacy policy is effective as of ${formattedDate} and applies to all users of ${data.companyName} services.`;
      case 'terms':
        return `These terms of service are effective as of ${formattedDate} and apply to all users of ${data.companyName} services.`;
      default:
        return `This document is effective as of ${formattedDate} and applies to all users of ${data.companyName} services.`;
    }
  };

  const renderSection = (section: LegalSection) => {
    return (
      <div key={section.id} className="space-y-3">
        {section.title && (
          <h2 className="text-xl font-semibold text-foreground">{section.title}</h2>
        )}

        {section.content && (
          <p className="text-muted-foreground leading-relaxed">
            {section.id === 'contact' ? (
              <>
                {section.content}{' '}
                <a
                  href={`mailto:${data.contactEmail}`}
                  className="text-primary hover:text-primary/80 underline underline-offset-4 transition-colors"
                >
                  {data.contactEmail}
                </a>
                .
              </>
            ) : (
              section.content
            )}
          </p>
        )}

        {section.list && (
          <ul className="space-y-2 text-muted-foreground">
            {section.list.map((item: string, index: number) => (
              <li key={index} className="flex items-start gap-3">
                <span className="text-primary mt-2 h-1.5 w-1.5 rounded-full bg-current flex-shrink-0" />
                <span className="leading-relaxed">{item}</span>
              </li>
            ))}
          </ul>
        )}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-12 md:py-16">
        <div className="mx-auto max-w-4xl">
          {/* Header */}
          <div className="mb-12 text-center">
            <h1 className="mb-4 text-4xl font-bold tracking-tight text-foreground">{data.title}</h1>
            <div className="inline-flex items-center rounded-full bg-muted px-4 py-2 text-sm text-muted-foreground">
              <CalendarIcon className="size-4 mr-2" />
              Last updated: {formatDate(data.lastUpdated)}
            </div>
          </div>

          {/* Content */}
          <div className="prose prose-gray max-w-none">
            <div className="space-y-8">{data.sections.map(renderSection)}</div>
          </div>

          {/* Legal Navigation */}
          <div className="mt-12">
            <LegalNavigation />
          </div>

          {/* Footer */}
          <div className="mt-16 border-t pt-8">
            <div className="text-center text-sm text-muted-foreground">
              <p>{getFooterText()}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
