'use client';

import { ArrowLeftIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Button } from './ui/button';

export const BackButton = ({ href }: { href?: string }) => {
  const router = useRouter();
  const [hasHistory, setHasHistory] = useState(false);

  useEffect(() => {
    if (typeof window == 'undefined') return;
    setHasHistory(window.history.length > 1);
  }, []);

  if (!hasHistory && !href) {
    return null;
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      className="rounded-full hover:bg-primary/10"
      onClick={() => (href ? router.push(href) : router.back())}
      aria-label="Go back"
    >
      <ArrowLeftIcon className="size-5" />
    </Button>
  );
};
