// 'use client';

// import { Area, AreaChart, CartesianGrid, ResponsiveContainer, XAxis, YAxis } from 'recharts';
// import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';

// // Mock data - replace with your actual data
// const data = [
//   { date: 'Apr 20', submissions: 5 },
//   { date: 'Apr 21', submissions: 8 },
//   { date: 'Apr 22', submissions: 12 },
//   { date: 'Apr 23', submissions: 15 },
//   { date: 'Apr 24', submissions: 20 },
//   { date: 'Apr 25', submissions: 25 },
//   { date: 'Apr 26', submissions: 18 },
//   { date: 'Apr 27', submissions: 30 },
//   { date: 'Apr 28', submissions: 45 },
// ];

// export function SubmissionChart() {
//   return (
//     <ChartContainer
//       config={{
//         submissions: {
//           label: 'Submissions',
//           color: 'hsl(var(--primary))',
//         },
//       }}
//     >
//       <ResponsiveContainer width="100%" height="100%">
//         <AreaChart
//           data={data}
//           margin={{
//             top: 10,
//             right: 10,
//             left: 0,
//             bottom: 0,
//           }}
//         >
//           <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
//           <XAxis
//             dataKey="date"
//             tickLine={false}
//             axisLine={false}
//             className="text-xs text-muted-foreground"
//           />
//           <YAxis tickLine={false} axisLine={false} className="text-xs text-muted-foreground" />
//           <ChartTooltip content={<ChartTooltipContent />} />
//           <Area
//             type="monotone"
//             dataKey="submissions"
//             fill="var(--color-submissions)"
//             fillOpacity={0.2}
//             stroke="var(--color-submissions)"
//             strokeWidth={2}
//           />
//         </AreaChart>
//       </ResponsiveContainer>
//     </ChartContainer>
//   );
// }
