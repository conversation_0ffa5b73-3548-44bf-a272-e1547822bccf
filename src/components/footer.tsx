import type React from 'react';
import { companyName } from '@/config/site';
import { cn } from '@/lib/utils';
import Link from 'next/link';

interface FooterProps extends React.ComponentProps<'footer'> {
  year?: number;
  showLinks?: boolean;
  links?: Array<{
    label: string;
    href: string;
  }>;
  variant?: 'small' | 'default';
}

export const Footer = ({
  className,
  year = new Date().getFullYear(),
  showLinks = true,
  links = [
    { label: 'Privacy Policy', href: '/privacy' },
    { label: 'Terms of Service', href: '/terms' },
  ],
  variant = 'default',
  ...props
}: FooterProps) => {
  const copyrightText = `${year} ${companyName}. All rights reserved.`;

  if (variant === 'small') {
    return (
      <footer
        className={cn(
          'bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60',
          className,
        )}
        {...props}
      >
        <div className="container mx-auto px-4 py-3">
          <p className="text-center text-sm text-muted-foreground">&copy; {copyrightText}</p>
        </div>
      </footer>
    );
  }

  return (
    <footer
      className={cn(
        'border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60',
        className,
      )}
      {...props}
    >
      <div className="container mx-auto px-4 pt-6">
        <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
          {/* Copyright */}
          <p className="text-sm text-muted-foreground">&copy; {copyrightText}</p>

          {/* Links */}
          {showLinks && links.length > 0 && (
            <nav className="flex flex-wrap items-center gap-4 text-sm">
              {links.map((link, index) => (
                <Link
                  key={index}
                  href={link.href}
                  className="text-muted-foreground transition-colors hover:text-foreground underline underline-offset-2"
                >
                  {link.label}
                </Link>
              ))}
            </nav>
          )}
        </div>
      </div>
    </footer>
  );
};
