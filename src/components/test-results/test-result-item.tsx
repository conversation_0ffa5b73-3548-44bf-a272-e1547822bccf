'use client';

import { CheckCircle, XCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import type { TestResult } from './types';
import { motion } from 'motion/react';

interface TestResultItemProps {
  result: TestResult;
  index: number;
}

export function TestResultItem({ result, index }: TestResultItemProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2, delay: index * 0.05 }}
      className={cn(
        'p-3 border rounded-md',
        result.success
          ? 'border-green-200 bg-green-50/50 dark:bg-green-950/20 dark:border-green-800/40'
          : 'border-red-200 bg-red-50/50 dark:bg-red-950/20 dark:border-red-800/40',
      )}
    >
      <div className="flex items-center gap-2">
        {result.success ? (
          <CheckCircle
            className="w-4 h-4 text-green-600 dark:text-green-400 flex-shrink-0"
            aria-hidden="true"
          />
        ) : (
          <XCircle
            className="w-4 h-4 text-red-600 dark:text-red-400 flex-shrink-0"
            aria-hidden="true"
          />
        )}
        <span className="text-sm font-medium">
          {index}. {result.title}
        </span>
        {!result.success && (
          <Badge
            variant="outline"
            className="ml-auto text-xs bg-red-50 text-red-700 border-red-200 dark:bg-red-950/30 dark:text-red-300 dark:border-red-800/40"
          >
            Failed
          </Badge>
        )}
      </div>

      {/* Show details for failed tests */}
      {!result.success && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          transition={{ duration: 0.3 }}
          className="mt-3 space-y-3"
        >
          {/* Input, Expected, and Actual Output */}
          {(result.input || result.expected || result.actual !== undefined) && (
            <div className="flex flex-wrap gap-3">
              {result.input && (
                <div className="flex-1">
                  <h4 className="text-xs font-semibold text-blue-700 dark:text-blue-300 mb-1">
                    Input:
                  </h4>
                  <pre className="text-xs bg-white dark:bg-gray-900 border rounded p-2 overflow-x-auto whitespace-pre-wrap font-mono max-h-60">
                    {result.input}
                  </pre>
                </div>
              )}

              {result.expected && (
                <div className="flex-1">
                  <h4 className="text-xs font-semibold text-green-700 dark:text-green-300 mb-1">
                    Expected Output:
                  </h4>
                  <pre className="text-xs bg-white dark:bg-gray-900 border rounded p-2 overflow-x-auto whitespace-pre-wrap font-mono max-h-60">
                    {result.expected}
                  </pre>
                </div>
              )}

              {result.actual !== undefined && (
                <div className="flex-1">
                  <h4 className="text-xs font-semibold text-red-700 dark:text-red-300 mb-1">
                    Actual Output:
                  </h4>
                  <pre className="text-xs bg-white dark:bg-gray-900 border rounded p-2 overflow-x-auto whitespace-pre-wrap font-mono max-h-60">
                    {result.actual || '(no output)'}
                  </pre>
                </div>
              )}
            </div>
          )}

          {/* Error Message */}
          {result.error && (
            <div>
              <h4 className="text-xs font-semibold text-red-700 dark:text-red-300 mb-1">Error:</h4>
              <pre className="text-xs bg-white dark:bg-gray-900 border rounded p-2 overflow-x-auto whitespace-pre-wrap font-mono text-red-600 dark:text-red-400 max-h-60">
                {result.error}
              </pre>
            </div>
          )}
        </motion.div>
      )}
    </motion.div>
  );
}
