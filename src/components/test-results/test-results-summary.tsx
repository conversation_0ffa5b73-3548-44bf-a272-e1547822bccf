'use client';

import { AlertCircle, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { TestSuiteResult } from './types';
import { motion } from 'motion/react';

interface TestResultsSummaryProps {
  testSuiteResults: TestSuiteResult[];
}

export function TestResultsSummary({ testSuiteResults }: TestResultsSummaryProps) {
  const allResults = testSuiteResults.flatMap((suite) => suite.results);
  const passedTests = allResults.filter((r) => r.success).length;
  const totalTests = allResults.length;
  const allPassed = passedTests === totalTests && totalTests > 0;
  const percentage = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;

  if (totalTests === 0) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        'p-3 mb-3 rounded-md border flex items-center justify-between',
        allPassed
          ? 'bg-green-50 border-green-200 dark:bg-green-950/30 dark:border-green-800/50'
          : percentage >= 50
            ? 'bg-amber-50 border-amber-200 dark:bg-amber-950/30 dark:border-amber-800/50'
            : 'bg-red-50 border-red-200 dark:bg-red-950/30 dark:border-red-800/50',
      )}
      role="status"
      aria-live="polite"
    >
      <div className="flex items-center gap-2">
        {allPassed ? (
          <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" aria-hidden="true" />
        ) : (
          <AlertCircle className="w-5 h-5 text-amber-600 dark:text-amber-400" aria-hidden="true" />
        )}
        <span className="font-medium">
          {allPassed
            ? 'All tests passed!'
            : `${passedTests} of ${totalTests} tests passing (${percentage}%)`}
        </span>
      </div>
      <div className="flex items-center gap-2">
        <div className="w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
          <div
            className={cn(
              'h-full',
              allPassed
                ? 'bg-green-500 dark:bg-green-600'
                : percentage >= 50
                  ? 'bg-amber-500 dark:bg-amber-600'
                  : 'bg-red-500 dark:bg-red-600',
            )}
            style={{ width: `${percentage}%` }}
          />
        </div>
        <span
          className={cn(
            'text-xs font-medium px-2 py-1 rounded-full',
            allPassed
              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
              : percentage >= 50
                ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300'
                : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
          )}
        >
          {percentage}%
        </span>
      </div>
    </motion.div>
  );
}
