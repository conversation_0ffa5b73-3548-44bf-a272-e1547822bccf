'use client';

import { ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { TestSuiteResult } from './types';
import { TestResultItem } from './test-result-item';
import { motion, AnimatePresence } from 'motion/react';

interface TestSuiteResultGroupProps {
  suiteResult: TestSuiteResult;
  toggleExpand: (suiteId: string) => void;
}

export function TestSuiteResultGroup({ suiteResult, toggleExpand }: TestSuiteResultGroupProps) {
  const passedTests = suiteResult.results.filter((r) => r.success).length;
  const totalTests = suiteResult.results.length;
  const allPassed = passedTests === totalTests && totalTests > 0;
  const partiallyPassed = passedTests > 0 && passedTests < totalTests;

  return (
    <div className="rounded-md overflow-hidden border border-border">
      <button
        className={cn(
          'w-full flex items-center justify-between p-3',
          allPassed
            ? 'bg-green-50/50 dark:bg-green-950/20'
            : partiallyPassed
              ? 'bg-amber-50/50 dark:bg-amber-950/20'
              : 'bg-red-50/50 dark:bg-red-950/20',
        )}
        onClick={() => toggleExpand(suiteResult.id)}
        aria-expanded={suiteResult.isExpanded}
        aria-controls={`suite-content-${suiteResult.id}`}
      >
        <div className="flex items-center gap-2">
          <motion.div
            initial={false}
            animate={{ rotate: suiteResult.isExpanded ? 90 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronRight className="w-4 h-4 text-muted-foreground" aria-hidden="true" />
          </motion.div>
          <h3 className="font-medium text-left">{suiteResult.name || 'Test Results'}</h3>
        </div>
        <div className="flex items-center gap-2">
          <span
            className={cn(
              'text-xs font-medium px-2 py-1 rounded-full',
              allPassed
                ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                : partiallyPassed
                  ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300'
                  : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
            )}
          >
            {passedTests}/{totalTests} passing
          </span>
        </div>
      </button>

      <AnimatePresence>
        {suiteResult.isExpanded && (
          <motion.div
            id={`suite-content-${suiteResult.id}`}
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="p-3 space-y-2 border-t">
              {suiteResult.results.length > 0 ? (
                suiteResult.results.map((result, idx) => (
                  <TestResultItem key={result.id || idx} result={result} index={idx + 1} />
                ))
              ) : (
                <p className="text-sm text-muted-foreground italic">No test results available</p>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
