'use client';

import { useEffect, useState } from 'react';
import type { TestSuiteResult, TestResultFilter } from './types';
import { TestResultsSummary } from './test-results-summary';
import { TestSuiteResultGroup } from './test-suite-group';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, Filter, CheckCircle, XCircle } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface TestResultsProps {
  initialTestSuites: Omit<TestSuiteResult, 'isExpanded'>[];
}

export function TestResults({ initialTestSuites }: TestResultsProps) {
  // Initialize with all suites expanded
  const [testSuites, setTestSuites] = useState<TestSuiteResult[]>(
    initialTestSuites.map((suite) => ({ ...suite, isExpanded: true })),
  );
  const [searchQuery, setSearchQuery] = useState('');
  const [filter, setFilter] = useState<TestResultFilter>('all');

  const toggleExpand = (suiteId: string) => {
    setTestSuites((prevSuites) =>
      prevSuites.map((suite) =>
        suite.id === suiteId ? { ...suite, isExpanded: !suite.isExpanded } : suite,
      ),
    );
  };

  const expandAll = () => {
    setTestSuites((prevSuites) => prevSuites.map((suite) => ({ ...suite, isExpanded: true })));
  };

  const collapseAll = () => {
    setTestSuites((prevSuites) => prevSuites.map((suite) => ({ ...suite, isExpanded: false })));
  };

  // Filter test suites based on search query and filter type
  const filteredTestSuites = testSuites
    .map((suite) => {
      // Filter results based on search and filter type
      const filteredResults = suite.results.filter((result) => {
        const matchesSearch =
          searchQuery === '' || result.title.toLowerCase().includes(searchQuery.toLowerCase());

        const matchesFilter =
          filter === 'all' ||
          (filter === 'passed' && result.success) ||
          (filter === 'failed' && !result.success);

        return matchesSearch && matchesFilter;
      });

      // Return suite with filtered results
      return {
        ...suite,
        results: filteredResults,
      };
    })
    .filter((suite) => suite.results.length > 0); // Only show suites with matching results

  useEffect(() => {
    setTestSuites(initialTestSuites.map((suite) => ({ ...suite, isExpanded: true })));
  }, [initialTestSuites]);

  return (
    <div className="space-y-4">
      <TestResultsSummary testSuiteResults={testSuites} />

      <div className="flex flex-col sm:flex-row gap-2 mb-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search tests..."
            className="pl-9"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="flex gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex gap-2">
                <Filter className="h-4 w-4" />
                {filter === 'all' ? 'All Tests' : filter === 'passed' ? 'Passed' : 'Failed'}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setFilter('all')}>All Tests</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilter('passed')}>
                <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                Passed
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilter('failed')}>
                <XCircle className="mr-2 h-4 w-4 text-red-500" />
                Failed
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button variant="outline" size="icon" onClick={expandAll} title="Expand all">
            <span className="sr-only">Expand all</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="m18 15-6-6-6 6" />
            </svg>
          </Button>

          <Button variant="outline" size="icon" onClick={collapseAll} title="Collapse all">
            <span className="sr-only">Collapse all</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="m6 9 6 6 6-6" />
            </svg>
          </Button>
        </div>
      </div>

      {filteredTestSuites.length > 0 ? (
        <div className="space-y-3">
          {filteredTestSuites.map((suite) => (
            <TestSuiteResultGroup key={suite.id} suiteResult={suite} toggleExpand={toggleExpand} />
          ))}
        </div>
      ) : (
        <div className="p-8 text-center border rounded-md bg-muted/10">
          <p className="text-muted-foreground">No test results match your filters</p>
        </div>
      )}
    </div>
  );
}
