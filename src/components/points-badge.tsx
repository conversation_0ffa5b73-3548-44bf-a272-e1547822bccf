import { TrophyIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import type React from 'react';

interface PointsBadgeProps {
  points: number | null | undefined;
  className?: string;
  variant?: 'minimal';
}

export const PointsBadge: React.FC<PointsBadgeProps> = ({ points, className, variant }) => {
  const difficultyBadgeClasses = cn(
    'capitalize rounded-full border shadow-inner backdrop-blur-sm transition flex items-center gap-1.5',
    variant !== 'minimal' &&
      'border-amber-600 dark:text-amber-400 text-amber-800 dark:bg-amber-600/10 bg-amber-200',
    variant !== 'minimal' && 'px-3 py-1',
    className,
  );

  return (
    <Badge variant="secondary" className={difficultyBadgeClasses}>
      {variant !== 'minimal' && (
        <TrophyIcon className={cn('size-4 text-amber-500 dark:text-amber-400')} />
      )}
      <span>{points}</span>
    </Badge>
  );
};
