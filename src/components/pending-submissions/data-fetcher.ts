import type { Payload } from 'payload';

export interface Student {
  id: number;
  username: string;
  fullName: string;
  email: string;
  division?: string;
}

export interface Assignment {
  id: number;
  title: string;
  dueDate: string;
  createdAt: string;
  points: number;
  difficulty: string;
  language: string;
}

export interface PendingSubmissionData {
  assignment: Assignment;
  pendingStudents: Student[];
}

export async function getPendingSubmissionsData(
  payload: Payload,
): Promise<PendingSubmissionData[]> {
  try {
    // Use Promise.all for parallel execution to improve performance
    const [assignmentsResponse, studentsResponse, submissionsResponse] = await Promise.all([
      // Fetch all assignments with only required fields
      payload.find({
        collection: 'assignments',
        limit: 1000,
        sort: '-dueDate',
        depth: 0,
        select: {
          id: true,
          title: true,
          dueDate: true,
          createdAt: true,
          points: true,
          difficulty: true,
          language: true,
        },
        where: {
          _status: {
            equals: 'published',
          },
        },
      }),

      // Fetch all students with student role in tenants - only required fields
      payload.find({
        collection: 'users',
        limit: 1000,
        depth: 0,
        select: {
          id: true,
          username: true,
          fullName: true,
          email: true,
          division: true,
        },
        where: {
          'tenants.roles': {
            contains: 'student',
          },
        },
      }),

      // Fetch all submissions with only assignment and student IDs
      payload.find({
        collection: 'submissions',
        limit: 10000,
        depth: 0,
        select: {
          assignment: true,
          student: true,
        },
      }),
    ]);

    const assignments = assignmentsResponse.docs;
    const students = studentsResponse.docs;
    const submissions = submissionsResponse.docs;

    // Create a set of submissions by assignment and student for O(1) lookup
    // Since we're using depth: 0, assignment and student will be IDs directly
    const submissionSet = new Set<string>();
    submissions.forEach((submission) => {
      const key = `${submission.assignment}-${submission.student}`;
      submissionSet.add(key);
    });

    // Find pending submissions for each assignment
    const pendingSubmissionsData: PendingSubmissionData[] = [];

    assignments.forEach((assignment) => {
      const pendingStudents = students.filter((student) => {
        const key = `${assignment.id}-${student.id}`;
        return !submissionSet.has(key);
      });

      if (pendingStudents.length > 0) {
        pendingSubmissionsData.push({
          assignment: {
            id: assignment.id,
            title: assignment.title,
            dueDate: assignment.dueDate,
            createdAt: assignment.createdAt,
            points: assignment.points,
            difficulty: assignment.difficulty,
            language: assignment.language,
          },
          pendingStudents: pendingStudents.map((student) => ({
            id: student.id,
            username: student.username || '',
            fullName: student.fullName || '',
            email: student.email,
            division: student.division || '',
          })),
        });
      }
    });

    return pendingSubmissionsData;
  } catch (error) {
    console.error('Error fetching pending submissions:', error);
    return [];
  }
}
