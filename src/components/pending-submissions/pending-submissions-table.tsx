'use client';

import { Button } from '@payloadcms/ui';
import { ChevronDown, ChevronRight, Mail, Search } from 'lucide-react';
import type React from 'react';
import { useState } from 'react';
import type { PendingSubmissionData } from './data-fetcher';

interface PendingSubmissionsTableProps {
  initialData: PendingSubmissionData[];
}

export const PendingSubmissionsTable: React.FC<PendingSubmissionsTableProps> = ({
  initialData,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedAssignments, setExpandedAssignments] = useState<Set<number>>(new Set());

  const toggleAssignment = (assignmentId: number) => {
    const newExpanded = new Set(expandedAssignments);
    if (newExpanded.has(assignmentId)) {
      newExpanded.delete(assignmentId);
    } else {
      newExpanded.add(assignmentId);
    }
    setExpandedAssignments(newExpanded);
  };

  const filteredData = initialData.filter(
    (item) =>
      item.assignment.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.pendingStudents.some(
        (student) =>
          student.fullName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          student.email.toLowerCase().includes(searchQuery.toLowerCase()),
      ),
  );

  const totalPendingStudents = initialData.reduce(
    (total, item) => total + item.pendingStudents.length,
    0,
  );

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem', marginBottom: '2rem' }}>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          <input
            type="text"
            placeholder="Search assignments..."
            value={searchQuery}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
            style={{
              maxWidth: '32rem',
              padding: '0.5rem 0.75rem',
              border: '1px solid #d1d5db',
              borderRadius: '0.375rem',
              fontSize: '0.875rem',
              outline: 'none',
              transition: 'border-color 0.2s',
            }}
            onFocus={(e) => {
              e.target.style.borderColor = '#3b82f6';
              e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
            }}
            onBlur={(e) => {
              e.target.style.borderColor = '#d1d5db';
              e.target.style.boxShadow = 'none';
            }}
          />
          <Button className="btn--size-medium">
            <Search style={{ height: '1rem', width: '1rem' }} />
          </Button>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <span
            style={{
              fontSize: '1.4rem',
              padding: '0.25rem 0.75rem',
              backgroundColor: '#f3f4f6',
              color: '#374151',
              borderRadius: '9999px',
              fontWeight: '500',
              display: 'inline-flex',
              alignItems: 'center',
            }}
          >
            {totalPendingStudents} pending submissions
          </span>
          {/* <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button> */}
        </div>
      </div>

      {filteredData.length === 0 ? (
        <div
          style={{
            backgroundColor: 'white',
            border: '1px solid #e5e7eb',
            borderRadius: '0.5rem',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
          }}
        >
          <div style={{ padding: '1.5rem' }}>
            <h3
              style={{
                fontSize: '1.125rem',
                fontWeight: '600',
                color: '#111827',
                marginBottom: '0.5rem',
              }}
            >
              No Pending Submissions
            </h3>
            <p
              style={{
                fontSize: '0.875rem',
                color: '#6b7280',
                margin: 0,
              }}
            >
              All students have submitted their assignments or no assignments found.
            </p>
          </div>
        </div>
      ) : (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          {filteredData.map((item) => (
            <div
              key={item.assignment.id}
              style={{
                backgroundColor: 'white',
                border: '1px solid #e5e7eb',
                borderRadius: '0.5rem',
                boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
              }}
            >
              <div>
                <div
                  onClick={() => toggleAssignment(item.assignment.id)}
                  style={{
                    cursor: 'pointer',
                    transition: 'background-color 0.2s',
                    padding: '1.5rem',
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f9fafb';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.75rem',
                      }}
                    >
                      {expandedAssignments.has(item.assignment.id) ? (
                        <ChevronDown style={{ height: '1rem', width: '1rem' }} />
                      ) : (
                        <ChevronRight style={{ height: '1rem', width: '1rem' }} />
                      )}
                      <div style={{ display: 'flex', gap: '0.4rem', flexDirection: 'column' }}>
                        <h3
                          style={{
                            fontSize: '1.125rem',
                            fontWeight: '600',
                            color: '#111827',
                            margin: 0,
                          }}
                        >
                          {item.assignment.title}
                        </h3>
                        <div
                          style={{
                            display: 'flex',
                            gap: '.4rem',
                            fontSize: '0.875rem',
                            color: '#6b7280',
                          }}
                        >
                          <span>
                            Created on:{' '}
                            {new Date(item.assignment.createdAt).toLocaleDateString('en-IN', {
                              timeZone: 'Asia/Kolkata',
                            })}
                          </span>
                          <span>•</span>
                          <span>
                            Due:{' '}
                            {new Date(item.assignment.dueDate).toLocaleDateString('en-IN', {
                              timeZone: 'Asia/Kolkata',
                            })}
                          </span>
                          <span>•</span>
                          <span>{item.assignment.points} points</span>
                          <span>•</span>
                          <span className="capitalize">{item.assignment.difficulty}</span>
                        </div>
                      </div>
                    </div>
                    <span
                      style={{
                        fontSize: '1.2rem',
                        padding: '0.25rem 0.75rem',
                        backgroundColor: '#fef2f2',
                        color: '#dc2626',
                        borderRadius: '9999px',
                        fontWeight: '500',
                        display: 'inline-flex',
                        alignItems: 'center',
                      }}
                    >
                      {item.pendingStudents.length} pending
                    </span>
                  </div>
                </div>
                {expandedAssignments.has(item.assignment.id) && (
                  <div style={{ padding: '0 1.5rem 1.5rem 1.5rem' }}>
                    <table
                      style={{
                        width: '100%',
                        borderCollapse: 'collapse',
                        fontSize: '0.875rem',
                      }}
                    >
                      <thead>
                        <tr
                          style={{
                            borderBottom: '1px solid #e5e7eb',
                          }}
                        >
                          <th
                            style={{
                              textAlign: 'left',
                              padding: '0.75rem 0.5rem',
                              fontWeight: '500',
                              color: '#374151',
                            }}
                          >
                            Roll No.
                          </th>
                          <th
                            style={{
                              textAlign: 'left',
                              padding: '0.75rem 0.5rem',
                              fontWeight: '500',
                              color: '#374151',
                            }}
                          >
                            Student Name
                          </th>
                          <th
                            style={{
                              textAlign: 'left',
                              padding: '0.75rem 0.5rem',
                              fontWeight: '500',
                              color: '#374151',
                            }}
                          >
                            Email
                          </th>
                          <th
                            style={{
                              textAlign: 'left',
                              padding: '0.75rem 0.5rem',
                              fontWeight: '500',
                              color: '#374151',
                            }}
                          >
                            Division
                          </th>
                          <th
                            style={{
                              textAlign: 'right',
                              padding: '0.75rem 0.5rem',
                              fontWeight: '500',
                              color: '#374151',
                            }}
                          >
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {item.pendingStudents.map((student) => (
                          <tr
                            key={student.id}
                            style={{
                              borderBottom: '1px solid #f3f4f6',
                            }}
                          >
                            <td
                              style={{
                                padding: '0.75rem 0.5rem',
                                verticalAlign: 'top',
                                fontWeight: '500',
                              }}
                            >
                              {student.username}
                            </td>
                            <td
                              style={{
                                padding: '0.75rem 0.5rem',
                                verticalAlign: 'top',
                              }}
                            >
                              {student.fullName}
                            </td>
                            <td
                              style={{
                                padding: '0.75rem 0.5rem',
                                verticalAlign: 'top',
                              }}
                            >
                              {student.email}
                            </td>
                            <td
                              style={{
                                padding: '0.75rem 0.5rem',
                                verticalAlign: 'top',
                              }}
                            >
                              {student.division || 'N/A'}
                            </td>
                            <td
                              style={{
                                padding: '0.75rem 0.5rem',
                                textAlign: 'right',
                                verticalAlign: 'top',
                              }}
                            >
                              <a
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  color: '#374151',
                                  textDecoration: 'none',
                                  justifyContent: 'end',
                                  cursor: 'pointer',
                                }}
                                href={`mailto:${student.email}?subject=Reminder: ${item.assignment.title} Assignment Due&body=Dear ${student.fullName},%0D%0A%0D%0AThis is a friendly reminder that your assignment "${item.assignment.title}" is due on ${new Date(item.assignment.dueDate).toLocaleDateString()}.%0D%0A%0D%0APlease submit your assignment as soon as possible.%0D%0A%0D%0ABest regards,%0D%0AYour Instructor`}
                              >
                                <Mail
                                  style={{
                                    height: '1rem',
                                    width: '1rem',
                                    marginRight: '0.5rem',
                                  }}
                                />
                                Remind
                              </a>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
