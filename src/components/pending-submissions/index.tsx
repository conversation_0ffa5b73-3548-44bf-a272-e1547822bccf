import { DefaultTemplate } from '@payloadcms/next/templates';
import { Gutter } from '@payloadcms/ui';
import type { AdminViewServerProps } from 'payload';
import React from 'react';
import { getPendingSubmissionsData } from './data-fetcher';
import { PendingSubmissionsTable } from './pending-submissions-table';

export default async function PendingSubmissionsView({
  initPageResult,
  params,
  searchParams,
}: AdminViewServerProps) {
  // Fetch data server-side
  const pendingSubmissionsData = await getPendingSubmissionsData(initPageResult.req.payload);

  return (
    <DefaultTemplate
      i18n={initPageResult.req.i18n}
      locale={initPageResult.locale}
      params={params}
      payload={initPageResult.req.payload}
      permissions={initPageResult.permissions}
      searchParams={searchParams}
      user={initPageResult.req.user || undefined}
      visibleEntities={initPageResult.visibleEntities}
    >
      <Gutter>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
          <div>
            <h1
              style={{
                fontSize: '1.5rem',
                fontWeight: 'bold',
                letterSpacing: '-0.025em',
                marginBottom: '0.5rem',
              }}
            >
              Pending Submissions
            </h1>
            <p
              style={{
                color: '#6b7280',
                fontSize: '0.875rem',
              }}
            >
              Students who haven&apos;t submitted their assignments yet, organized by assignment
            </p>
          </div>
          <PendingSubmissionsTable initialData={pendingSubmissionsData} />
        </div>
      </Gutter>
    </DefaultTemplate>
  );
}
