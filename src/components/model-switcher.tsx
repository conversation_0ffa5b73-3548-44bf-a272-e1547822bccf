'use client';

import { <PERSON>ader2, <PERSON>f<PERSON><PERSON><PERSON>, Send } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface ModelInfo {
  currentModel: string;
  availableModels: string[];
}

export function ModelSwitcher() {
  const [modelInfo, setModelInfo] = useState<ModelInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSwitching, setIsSwitching] = useState(false);
  const [customModel, setCustomModel] = useState('');

  const fetchModelInfo = async () => {
    try {
      const response = await fetch('/api/model');
      const result = await response.json();

      if (result.success) {
        setModelInfo(result.data);
      } else {
        toast.error('Failed to fetch model information');
      }
    } catch (error) {
      console.error('Error fetching model info:', error);
      toast.error('Failed to fetch model information');
    } finally {
      setIsLoading(false);
    }
  };

  const switchModel = async (modelName: string) => {
    if (modelName === modelInfo?.currentModel) return;

    setIsSwitching(true);
    try {
      const response = await fetch('/api/model', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ modelName }),
      });

      const result = await response.json();

      if (result.success) {
        setModelInfo((prev) => (prev ? { ...prev, currentModel: modelName } : null));
        toast.success(result.data.message);
        setCustomModel(''); // Clear custom input after successful switch
      } else {
        toast.error(result.error || 'Failed to switch model');
      }
    } catch (error) {
      console.error('Error switching model:', error);
      toast.error('Failed to switch model');
    } finally {
      setIsSwitching(false);
    }
  };

  const handleCustomModelSubmit = () => {
    const trimmedModel = customModel.trim();
    if (!trimmedModel) {
      toast.error('Please enter a model name');
      return;
    }
    switchModel(trimmedModel);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleCustomModelSubmit();
    }
  };

  useEffect(() => {
    fetchModelInfo();
  }, []);

  if (isLoading) {
    return (
      <Card className="w-full max-w-md">
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading model information...</span>
        </CardContent>
      </Card>
    );
  }

  if (!modelInfo) {
    return (
      <Card className="w-full max-w-md">
        <CardContent className="p-6">
          <p className="text-center text-muted-foreground">Failed to load model information</p>
          <Button onClick={fetchModelInfo} className="w-full mt-4" variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          AI Model Switcher
          <Badge variant="secondary" className="text-xs">
            Runtime
          </Badge>
        </CardTitle>
        <CardDescription>
          Switch between different AI models at runtime using dropdown or custom input
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="text-sm font-medium mb-2 block">Current Model</label>
          <Badge variant="default" className="font-mono">
            {modelInfo.currentModel}
          </Badge>
        </div>

        <div>
          <label className="text-sm font-medium mb-2 block">Switch Model</label>
          <Select value={modelInfo.currentModel} onValueChange={switchModel} disabled={isSwitching}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a model" />
            </SelectTrigger>
            <SelectContent>
              {modelInfo.availableModels.map((model) => (
                <SelectItem key={model} value={model}>
                  <span className="font-mono text-sm">{model}</span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="text-sm font-medium mb-2 block">Or Enter Custom Model</label>
          <div className="flex gap-2">
            <Input
              placeholder="e.g., openai/gpt-4o, anthropic/claude-3.5-sonnet"
              value={customModel}
              onChange={(e) => setCustomModel(e.target.value)}
              onKeyDown={handleKeyDown}
              disabled={isSwitching}
              className="font-mono text-sm"
            />
            <Button
              onClick={handleCustomModelSubmit}
              disabled={isSwitching || !customModel.trim()}
              size="sm"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Enter any OpenRouter model name (e.g., provider/model-name)
          </p>
        </div>

        {isSwitching && (
          <div className="flex items-center justify-center p-2 text-sm text-muted-foreground">
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
            Switching model...
          </div>
        )}

        <Button
          onClick={fetchModelInfo}
          variant="outline"
          size="sm"
          className="w-full"
          disabled={isSwitching}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </CardContent>
    </Card>
  );
}
