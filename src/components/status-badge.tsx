import type React from 'react';
import { cn } from '@/lib/utils';
import { LeafIcon, GaugeIcon } from 'lucide-react';
import { Badge } from './ui/badge';

interface StatusBadgeProps {
  status: 'upcoming' | 'overdue' | null;
  variant?: 'minimal';
}

const STATUS_CONFIG = {
  upcoming: {
    icon: LeafIcon,
    colors: {
      text: 'dark:text-blue-300 text-blue-700',
      background: 'dark:bg-blue-500/10 bg-blue-100',
      border: 'border-blue-500',
    },
  },
  overdue: {
    icon: GaugeIcon,
    colors: {
      text: 'dark:text-red-400 text-red-600',
      background: 'dark:bg-red-500/10 bg-red-100',
      border: 'border-red-500',
    },
  },
} as const;

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status, variant }) => {
  if (!status) return null;

  const config = STATUS_CONFIG[status];
  const IconComponent = config.icon;
  const { text, background, border } = config.colors;

  const badgeClasses = cn(
    'capitalize rounded-full shadow-inner backdrop-blur-sm transition flex items-center gap-1.5',
    text,
    background,
    variant !== 'minimal' ? `px-3 py-1 border ${border}` : 'border-transparent',
  );

  return (
    <Badge variant="secondary" className={badgeClasses}>
      {variant !== 'minimal' && <IconComponent className="w-4 h-4" />}
      <span>{status}</span>
    </Badge>
  );
};
