// 'use client';

// import { DownloadIcon } from 'lucide-react';

// // import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
// // import {
// //   Select,
// //   SelectContent,
// //   SelectItem,
// //   SelectTrigger,
// //   SelectValue,
// // } from '@/components/ui/select';

// import { StatsCards } from '../dashboard/stats-cards';
// import { SubmissionChart } from '../submission-chart';
// import { SubmissionRateChart } from '../submission-rate-chart';
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
// import { Button } from '@payloadcms/ui';

// const mockSubmissionData = {
//   totalStudents: 120,
//   submitted: 98,
//   notSubmitted: 22,
//   onTime: 85,
//   late: 13,
//   averageScore: 78,
//   highestScore: 98,
//   lowestScore: 45,
// };

// export function SubmissionsDashboard() {
//   // const [searchQuery, setSearchQuery] = useState('');

//   // Filter students who haven't submitted based on search query
//   // const filteredNotSubmitted = mockStudentsNotSubmitted.filter(
//   //   (student) =>
//   //     student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
//   //     student.email.toLowerCase().includes(searchQuery.toLowerCase()),
//   // );

//   // // Filter submissions based on search query
//   // const filteredSubmissions = mockSubmissions.filter((submission) =>
//   //   submission.studentName.toLowerCase().includes(searchQuery.toLowerCase()),
//   // );

//   return (
//     <div className="flex flex-col min-h-screen container max-w-full mx-auto">
//       <div className="flex-1 space-y-6 p-6 md:p-8">
//         <div className="flex items-center justify-between">
//           <h1 className="text-3xl font-bold tracking-tight">Assignment Submissions</h1>
//           <div className="flex items-center gap-2">
//             {/* <Select defaultValue="current">
//               <SelectTrigger className="w-[180px]">
//                 <SelectValue placeholder="Select Assignment" />
//               </SelectTrigger>
//               <SelectContent>
//                 <SelectItem value="current">Final Project</SelectItem>
//                 <SelectItem value="midterm">Midterm Project</SelectItem>
//                 <SelectItem value="assignment1">Assignment 1</SelectItem>
//                 <SelectItem value="assignment2">Assignment 2</SelectItem>
//               </SelectContent>
//             </Select> */}
//             <Button buttonStyle="pill">
//               <DownloadIcon className="h-4 w-4" />
//               <span className="sr-only">Download report</span>
//             </Button>
//           </div>
//         </div>

//         <StatsCards data={mockSubmissionData} />

//         <div className="grid gap-6 md:grid-cols-2">
//           <Card className="col-span-1">
//             <CardHeader>
//               <CardTitle>Submission Timeline</CardTitle>
//               <CardDescription>Number of submissions over time</CardDescription>
//             </CardHeader>
//             <CardContent className="">
//               <SubmissionChart />
//             </CardContent>
//           </Card>
//           <Card className="col-span-1">
//             <CardHeader>
//               <CardTitle>Submission Rate</CardTitle>
//               <CardDescription>Percentage of students who submitted</CardDescription>
//             </CardHeader>
//             <CardContent className="">
//               <SubmissionRateChart
//                 submitted={mockSubmissionData.submitted}
//                 notSubmitted={mockSubmissionData.notSubmitted}
//               />
//             </CardContent>
//           </Card>
//         </div>

//         <div className="space-y-4">
//           {/* <Tabs defaultValue="submitted">
//             <div className="flex flex-wrap max-sm:gap-2 justify-between">
//               <TabsList className="">
//                 <TabsTrigger value="submitted">
//                   Submitted ({mockSubmissionData.submitted})
//                 </TabsTrigger>
//                 <TabsTrigger value="not-submitted">
//                   Not Submitted ({mockSubmissionData.notSubmitted})
//                 </TabsTrigger>
//               </TabsList>
//               <SearchControls searchQuery={searchQuery} onSearchChange={setSearchQuery} />
//             </div>
//             <TabsContent value="submitted" className="space-y-4">
//               <SubmissionsTable submissions={filteredSubmissions} />
//             </TabsContent>
//             <TabsContent value="not-submitted" className="space-y-4">
//               <NotSubmittedTable students={filteredNotSubmitted} />
//             </TabsContent>
//           </Tabs> */}
//         </div>
//       </div>
//     </div>
//   );
// }

// // export default function SubmissionAnalytics() {
// //   const [isClient, setIsClient] = useState(false);

// //   useEffect(() => {
// //     setIsClient(true);
// //   }, []);

// //   return isClient ? <SubmissionsDashboard /> : <Loading />;
// // }
