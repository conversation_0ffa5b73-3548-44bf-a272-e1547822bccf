import { ThemeProvider } from '@/components/theme-provider';
import { Toaster } from '@/components/ui/sonner';
import { NuqsAdapter } from 'nuqs/adapters/next/app';
import { Geist, <PERSON>eist_Mono } from 'next/font/google';
import React from 'react';
import { PostHogProvider, TanstackQueryProvider } from '@/providers';

const geist = Geist({
  weight: ['100', '200', '300', '400', '500', '600'],
  subsets: ['latin'],
  display: 'swap',
});

const geistMono = Geist_Mono({
  weight: ['100', '200', '300', '400', '500', '600'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-mono',
});

interface SharedLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export const SharedLayout = ({ children, className }: SharedLayoutProps) => {
  return (
    <PostHogProvider>
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
        <main className={`${geist.className} ${className}`}>
          <NuqsAdapter>
            <TanstackQueryProvider>{children}</TanstackQueryProvider>
          </NuqsAdapter>
        </main>
        <Toaster richColors />
      </ThemeProvider>
    </PostHogProvider>
  );
};

export { geist, geistMono };
