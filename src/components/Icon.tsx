import { cn } from '@/lib/utils';
import { HomeIcon } from 'lucide-react';
import React from 'react';

interface IconProps {
  className?: string;
  showHomeIcon?: boolean;
}

const Icon = ({ className, showHomeIcon = true }: IconProps) => {
  return showHomeIcon ? (
    <HomeIcon size={18} />
  ) : (
    <svg
      className={cn('w-full h-full rounded-sm', className)}
      viewBox="0 0 250 250"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width={250} height={250} fill="currentColor" />
      <path
        d="M66.5 150L146.5 95L66.5 40"
        className="[filter:invert(1)] dark:stroke-[18] stroke-[16]"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M183.5 210L126.5 167L183.5 124"
        className="[filter:invert(1)] dark:stroke-[18] stroke-[16]"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default Icon;
