'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Moon, Sun } from 'lucide-react';
import { useTheme } from '@/hooks/use-theme';
import { RadioGroup, RadioGroupItem } from './ui/radio-group';
import { useId } from 'react';
import Image from 'next/image';

export function ThemeToggle({ varient = 'button' }: { varient?: 'button' | 'large' }) {
  const { setTheme, resolvedTheme } = useTheme();
  const id = useId();

  if (varient === 'large') {
    return (
      <fieldset className="space-y-4">
        <legend className="text-sm font-medium leading-none text-foreground">Choose a theme</legend>
        <RadioGroup className="flex gap-3" defaultValue="1">
          {[
            { value: '1', label: 'Light', image: '/ui-light.png' },
            { value: '2', label: 'Dark', image: '/ui-dark.png' },
            { value: '3', label: 'System', image: '/ui-system.png' },
          ].map((item) => (
            <label key={`${id}-${item.value}`}>
              <RadioGroupItem
                id={`${id}-${item.value}`}
                value={item.value}
                className="peer sr-only after:absolute after:inset-0"
                onClick={() => {
                  if (item.value === '1') {
                    setTheme('light');
                  } else if (item.value === '2') {
                    setTheme('dark');
                  } else {
                    setTheme('system');
                  }
                }}
              />
              <Image
                src={item.image}
                alt={item.label}
                width={88}
                height={70}
                className="relative cursor-pointer overflow-hidden rounded-lg border border-input shadow-sm shadow-black/5 outline-offset-2 transition-colors peer-[:focus-visible]:outline-2 peer-[:focus-visible]:outline-ring/70 peer-data-[disabled]:cursor-not-allowed peer-data-[state=checked]:border-ring peer-data-[state=checked]:bg-accent peer-data-[disabled]:opacity-50"
              />
              <span className="group mt-2 flex items-center gap-1 peer-data-[state=unchecked]:text-muted-foreground/70">
                <span className="text-xs font-medium">{item.label}</span>
              </span>
            </label>
          ))}
        </RadioGroup>
      </fieldset>
    );
  }

  return (
    <Button
      size="sm"
      className="h-8 w-8"
      variant="outline"
      onClick={() => setTheme(resolvedTheme === 'dark' ? 'light' : 'dark')}
    >
      <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
      <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
      <span className="sr-only">Toggle theme</span>
    </Button>
  );
}
