import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "submissions" ADD COLUMN "html" varchar;
  ALTER TABLE "submissions" ADD COLUMN "css" varchar;
  ALTER TABLE "submissions" ADD COLUMN "js" varchar;
  ALTER TABLE "submissions" ADD COLUMN "java" varchar;
  ALTER TABLE "submissions" ADD COLUMN "c" varchar;
  ALTER TABLE "_submissions_v" ADD COLUMN "version_html" varchar;
  ALTER TABLE "_submissions_v" ADD COLUMN "version_css" varchar;
  ALTER TABLE "_submissions_v" ADD COLUMN "version_js" varchar;
  ALTER TABLE "_submissions_v" ADD COLUMN "version_java" varchar;
  ALTER TABLE "_submissions_v" ADD COLUMN "version_c" varchar;
  ALTER TABLE "submissions" DROP COLUMN "solution_code_html";
  ALTER TABLE "submissions" DROP COLUMN "solution_code_css";
  ALTER TABLE "submissions" DROP COLUMN "solution_code_js";
  ALTER TABLE "submissions" DROP COLUMN "solution_code_java";
  ALTER TABLE "submissions" DROP COLUMN "solution_code_c";
  ALTER TABLE "_submissions_v" DROP COLUMN "version_solution_code_html";
  ALTER TABLE "_submissions_v" DROP COLUMN "version_solution_code_css";
  ALTER TABLE "_submissions_v" DROP COLUMN "version_solution_code_js";
  ALTER TABLE "_submissions_v" DROP COLUMN "version_solution_code_java";
  ALTER TABLE "_submissions_v" DROP COLUMN "version_solution_code_c";`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "submissions" ADD COLUMN "solution_code_html" varchar;
  ALTER TABLE "submissions" ADD COLUMN "solution_code_css" varchar;
  ALTER TABLE "submissions" ADD COLUMN "solution_code_js" varchar;
  ALTER TABLE "submissions" ADD COLUMN "solution_code_java" varchar;
  ALTER TABLE "submissions" ADD COLUMN "solution_code_c" varchar;
  ALTER TABLE "_submissions_v" ADD COLUMN "version_solution_code_html" varchar;
  ALTER TABLE "_submissions_v" ADD COLUMN "version_solution_code_css" varchar;
  ALTER TABLE "_submissions_v" ADD COLUMN "version_solution_code_js" varchar;
  ALTER TABLE "_submissions_v" ADD COLUMN "version_solution_code_java" varchar;
  ALTER TABLE "_submissions_v" ADD COLUMN "version_solution_code_c" varchar;
  ALTER TABLE "submissions" DROP COLUMN "html";
  ALTER TABLE "submissions" DROP COLUMN "css";
  ALTER TABLE "submissions" DROP COLUMN "js";
  ALTER TABLE "submissions" DROP COLUMN "java";
  ALTER TABLE "submissions" DROP COLUMN "c";
  ALTER TABLE "_submissions_v" DROP COLUMN "version_html";
  ALTER TABLE "_submissions_v" DROP COLUMN "version_css";
  ALTER TABLE "_submissions_v" DROP COLUMN "version_js";
  ALTER TABLE "_submissions_v" DROP COLUMN "version_java";
  ALTER TABLE "_submissions_v" DROP COLUMN "version_c";`)
}
