import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   CREATE TYPE "public"."enum_users_roles" AS ENUM('super-admin', 'user');
  CREATE TYPE "public"."enum_users_tenants_roles" AS ENUM('faculty', 'student');
  CREATE TYPE "public"."enum_assignments_blocks_action_action_type" AS ENUM('click', 'input', 'hover');
  CREATE TYPE "public"."enum_assignments_blocks_assertion_assertion_type" AS ENUM('textContent', 'exists', 'css', 'notExists', 'hasClass', 'ariaLabel', 'value', 'alert');
  CREATE TYPE "public"."enum_assignments_test_suites_visibility" AS ENUM('visible', 'hidden');
  CREATE TYPE "public"."enum_assignments_language" AS ENUM('web', 'java', 'c');
  CREATE TYPE "public"."enum_assignments_difficulty" AS ENUM('easy', 'medium', 'hard');
  CREATE TYPE "public"."enum_assignments_status" AS ENUM('draft', 'published');
  CREATE TYPE "public"."enum__assignments_v_blocks_action_action_type" AS ENUM('click', 'input', 'hover');
  CREATE TYPE "public"."enum__assignments_v_blocks_assertion_assertion_type" AS ENUM('textContent', 'exists', 'css', 'notExists', 'hasClass', 'ariaLabel', 'value', 'alert');
  CREATE TYPE "public"."enum__assignments_v_version_test_suites_visibility" AS ENUM('visible', 'hidden');
  CREATE TYPE "public"."enum__assignments_v_version_language" AS ENUM('web', 'java', 'c');
  CREATE TYPE "public"."enum__assignments_v_version_difficulty" AS ENUM('easy', 'medium', 'hard');
  CREATE TYPE "public"."enum__assignments_v_version_status" AS ENUM('draft', 'published');
  CREATE TYPE "public"."enum_submissions_status" AS ENUM('review', 'graded', 'resubmit');
  CREATE TYPE "public"."enum__submissions_v_version_status" AS ENUM('review', 'graded', 'resubmit');
  CREATE TYPE "public"."enum_payload_jobs_log_task_slug" AS ENUM('inline', 'submission-worker');
  CREATE TYPE "public"."enum_payload_jobs_log_state" AS ENUM('failed', 'succeeded');
  CREATE TYPE "public"."enum_payload_jobs_task_slug" AS ENUM('inline', 'submission-worker');
  CREATE TABLE "users_roles" (
  	"order" integer NOT NULL,
  	"parent_id" integer NOT NULL,
  	"value" "enum_users_roles",
  	"id" serial PRIMARY KEY NOT NULL
  );
  
  CREATE TABLE "users_tenants_roles" (
  	"order" integer NOT NULL,
  	"parent_id" varchar NOT NULL,
  	"value" "enum_users_tenants_roles",
  	"id" serial PRIMARY KEY NOT NULL
  );
  
  CREATE TABLE "users_tenants" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"tenant_id" integer NOT NULL
  );
  
  CREATE TABLE "users" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"username" varchar,
  	"full_name" varchar,
  	"division" varchar,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"email" varchar NOT NULL,
  	"reset_password_token" varchar,
  	"reset_password_expiration" timestamp(3) with time zone,
  	"salt" varchar,
  	"hash" varchar,
  	"login_attempts" numeric DEFAULT 0,
  	"lock_until" timestamp(3) with time zone
  );
  
  CREATE TABLE "tenants" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"name" varchar NOT NULL,
  	"domain" varchar,
  	"slug" varchar NOT NULL,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  CREATE TABLE "batches" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"batch_id" varchar NOT NULL,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  CREATE TABLE "batches_rels" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"order" integer,
  	"parent_id" integer NOT NULL,
  	"path" varchar NOT NULL,
  	"users_id" integer
  );
  
  CREATE TABLE "enrollments" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"module_id" integer NOT NULL,
  	"batch_id" integer NOT NULL,
  	"access_start" timestamp(3) with time zone NOT NULL,
  	"access_end" timestamp(3) with time zone NOT NULL,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  CREATE TABLE "subjects" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"tenant_id" integer,
  	"name" varchar NOT NULL,
  	"description" varchar,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  CREATE TABLE "modules" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"title" varchar NOT NULL,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  CREATE TABLE "modules_rels" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"order" integer,
  	"parent_id" integer NOT NULL,
  	"path" varchar NOT NULL,
  	"assignments_id" integer
  );
  
  CREATE TABLE "assignments_hints" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"question" varchar,
  	"answer" varchar
  );
  
  CREATE TABLE "assignments_resources" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"title" varchar,
  	"url" varchar
  );
  
  CREATE TABLE "assignments_blocks_action" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"_path" text NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"action_type" "enum_assignments_blocks_action_action_type",
  	"action_selector" varchar,
  	"action_value" varchar,
  	"block_name" varchar
  );
  
  CREATE TABLE "assignments_blocks_assertion" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"_path" text NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"assertion_type" "enum_assignments_blocks_assertion_assertion_type",
  	"assertion_selector" varchar,
  	"expected_value" varchar,
  	"expected_class" varchar,
  	"css_property" varchar,
  	"expected_css_value" varchar,
  	"expected_alert_text" varchar,
  	"block_name" varchar
  );
  
  CREATE TABLE "assignments_test_suites" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"points" numeric DEFAULT 10,
  	"visibility" "enum_assignments_test_suites_visibility" DEFAULT 'visible'
  );
  
  CREATE TABLE "assignments_java_test_cases" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"title" varchar,
  	"input" varchar,
  	"expected_output" varchar,
  	"tolerance" numeric,
  	"is_hidden" boolean DEFAULT false
  );
  
  CREATE TABLE "assignments_c_test_cases" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"title" varchar,
  	"input" varchar,
  	"expected_output" varchar,
  	"tolerance" numeric,
  	"is_hidden" boolean DEFAULT false
  );
  
  CREATE TABLE "assignments" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"tenant_id" integer,
  	"title" varchar,
  	"subject_id" integer,
  	"module_id" integer,
  	"description" varchar,
  	"language" "enum_assignments_language" DEFAULT 'web',
  	"difficulty" "enum_assignments_difficulty" DEFAULT 'easy',
  	"points" numeric,
  	"due_date" timestamp(3) with time zone,
  	"instructions" varchar,
  	"requires_command_line_args" boolean,
  	"starter_code_html" varchar,
  	"starter_code_css" varchar,
  	"starter_code_js" varchar,
  	"starter_code_java" varchar,
  	"starter_code_c" varchar,
  	"solution_code_html" varchar,
  	"solution_code_css" varchar,
  	"solution_code_js" varchar,
  	"solution_code_java" varchar,
  	"solution_code_c" varchar,
  	"solution_notes" varchar,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"_status" "enum_assignments_status" DEFAULT 'draft'
  );
  
  CREATE TABLE "_assignments_v_version_hints" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" serial PRIMARY KEY NOT NULL,
  	"question" varchar,
  	"answer" varchar,
  	"_uuid" varchar
  );
  
  CREATE TABLE "_assignments_v_version_resources" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" serial PRIMARY KEY NOT NULL,
  	"title" varchar,
  	"url" varchar,
  	"_uuid" varchar
  );
  
  CREATE TABLE "_assignments_v_blocks_action" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"_path" text NOT NULL,
  	"id" serial PRIMARY KEY NOT NULL,
  	"action_type" "enum__assignments_v_blocks_action_action_type",
  	"action_selector" varchar,
  	"action_value" varchar,
  	"_uuid" varchar,
  	"block_name" varchar
  );
  
  CREATE TABLE "_assignments_v_blocks_assertion" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"_path" text NOT NULL,
  	"id" serial PRIMARY KEY NOT NULL,
  	"assertion_type" "enum__assignments_v_blocks_assertion_assertion_type",
  	"assertion_selector" varchar,
  	"expected_value" varchar,
  	"expected_class" varchar,
  	"css_property" varchar,
  	"expected_css_value" varchar,
  	"expected_alert_text" varchar,
  	"_uuid" varchar,
  	"block_name" varchar
  );
  
  CREATE TABLE "_assignments_v_version_test_suites" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" serial PRIMARY KEY NOT NULL,
  	"points" numeric DEFAULT 10,
  	"visibility" "enum__assignments_v_version_test_suites_visibility" DEFAULT 'visible',
  	"_uuid" varchar
  );
  
  CREATE TABLE "_assignments_v_version_java_test_cases" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" serial PRIMARY KEY NOT NULL,
  	"title" varchar,
  	"input" varchar,
  	"expected_output" varchar,
  	"tolerance" numeric,
  	"is_hidden" boolean DEFAULT false,
  	"_uuid" varchar
  );
  
  CREATE TABLE "_assignments_v_version_c_test_cases" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" serial PRIMARY KEY NOT NULL,
  	"title" varchar,
  	"input" varchar,
  	"expected_output" varchar,
  	"tolerance" numeric,
  	"is_hidden" boolean DEFAULT false,
  	"_uuid" varchar
  );
  
  CREATE TABLE "_assignments_v" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"parent_id" integer,
  	"version_tenant_id" integer,
  	"version_title" varchar,
  	"version_subject_id" integer,
  	"version_module_id" integer,
  	"version_description" varchar,
  	"version_language" "enum__assignments_v_version_language" DEFAULT 'web',
  	"version_difficulty" "enum__assignments_v_version_difficulty" DEFAULT 'easy',
  	"version_points" numeric,
  	"version_due_date" timestamp(3) with time zone,
  	"version_instructions" varchar,
  	"version_requires_command_line_args" boolean,
  	"version_starter_code_html" varchar,
  	"version_starter_code_css" varchar,
  	"version_starter_code_js" varchar,
  	"version_starter_code_java" varchar,
  	"version_starter_code_c" varchar,
  	"version_solution_code_html" varchar,
  	"version_solution_code_css" varchar,
  	"version_solution_code_js" varchar,
  	"version_solution_code_java" varchar,
  	"version_solution_code_c" varchar,
  	"version_solution_notes" varchar,
  	"version_updated_at" timestamp(3) with time zone,
  	"version_created_at" timestamp(3) with time zone,
  	"version__status" "enum__assignments_v_version_status" DEFAULT 'draft',
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"latest" boolean
  );
  
  CREATE TABLE "submissions" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"tenant_id" integer,
  	"student_id" integer NOT NULL,
  	"assignment_id" integer NOT NULL,
  	"status" "enum_submissions_status" DEFAULT 'review',
  	"is_locked" boolean DEFAULT false,
  	"solution_code_html" varchar,
  	"solution_code_css" varchar,
  	"solution_code_js" varchar,
  	"solution_code_java" varchar,
  	"solution_code_c" varchar,
  	"score" numeric NOT NULL,
  	"passed_test_cases" numeric NOT NULL,
  	"failed_test_cases" numeric NOT NULL,
  	"feedback" varchar,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  CREATE TABLE "_submissions_v" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"parent_id" integer,
  	"version_tenant_id" integer,
  	"version_student_id" integer NOT NULL,
  	"version_assignment_id" integer NOT NULL,
  	"version_status" "enum__submissions_v_version_status" DEFAULT 'review',
  	"version_is_locked" boolean DEFAULT false,
  	"version_solution_code_html" varchar,
  	"version_solution_code_css" varchar,
  	"version_solution_code_js" varchar,
  	"version_solution_code_java" varchar,
  	"version_solution_code_c" varchar,
  	"version_score" numeric NOT NULL,
  	"version_passed_test_cases" numeric NOT NULL,
  	"version_failed_test_cases" numeric NOT NULL,
  	"version_feedback" varchar,
  	"version_updated_at" timestamp(3) with time zone,
  	"version_created_at" timestamp(3) with time zone,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  CREATE TABLE "payload_jobs_log" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"executed_at" timestamp(3) with time zone NOT NULL,
  	"completed_at" timestamp(3) with time zone NOT NULL,
  	"task_slug" "enum_payload_jobs_log_task_slug" NOT NULL,
  	"task_i_d" varchar NOT NULL,
  	"input" jsonb,
  	"output" jsonb,
  	"state" "enum_payload_jobs_log_state" NOT NULL,
  	"error" jsonb
  );
  
  CREATE TABLE "payload_jobs" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"input" jsonb,
  	"completed_at" timestamp(3) with time zone,
  	"total_tried" numeric DEFAULT 0,
  	"has_error" boolean DEFAULT false,
  	"error" jsonb,
  	"task_slug" "enum_payload_jobs_task_slug",
  	"queue" varchar DEFAULT 'default',
  	"wait_until" timestamp(3) with time zone,
  	"processing" boolean DEFAULT false,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  CREATE TABLE "payload_locked_documents" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"global_slug" varchar,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  CREATE TABLE "payload_locked_documents_rels" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"order" integer,
  	"parent_id" integer NOT NULL,
  	"path" varchar NOT NULL,
  	"users_id" integer,
  	"tenants_id" integer,
  	"batches_id" integer,
  	"enrollments_id" integer,
  	"subjects_id" integer,
  	"modules_id" integer,
  	"assignments_id" integer,
  	"submissions_id" integer,
  	"payload_jobs_id" integer
  );
  
  CREATE TABLE "payload_preferences" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"key" varchar,
  	"value" jsonb,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  CREATE TABLE "payload_preferences_rels" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"order" integer,
  	"parent_id" integer NOT NULL,
  	"path" varchar NOT NULL,
  	"users_id" integer
  );
  
  CREATE TABLE "payload_migrations" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"name" varchar,
  	"batch" numeric,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  ALTER TABLE "users_roles" ADD CONSTRAINT "users_roles_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "users_tenants_roles" ADD CONSTRAINT "users_tenants_roles_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."users_tenants"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "users_tenants" ADD CONSTRAINT "users_tenants_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "users_tenants" ADD CONSTRAINT "users_tenants_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "batches_rels" ADD CONSTRAINT "batches_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."batches"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "batches_rels" ADD CONSTRAINT "batches_rels_users_fk" FOREIGN KEY ("users_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "enrollments" ADD CONSTRAINT "enrollments_module_id_modules_id_fk" FOREIGN KEY ("module_id") REFERENCES "public"."modules"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "enrollments" ADD CONSTRAINT "enrollments_batch_id_batches_id_fk" FOREIGN KEY ("batch_id") REFERENCES "public"."batches"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "subjects" ADD CONSTRAINT "subjects_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "modules_rels" ADD CONSTRAINT "modules_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."modules"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "modules_rels" ADD CONSTRAINT "modules_rels_assignments_fk" FOREIGN KEY ("assignments_id") REFERENCES "public"."assignments"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "assignments_hints" ADD CONSTRAINT "assignments_hints_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."assignments"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "assignments_resources" ADD CONSTRAINT "assignments_resources_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."assignments"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "assignments_blocks_action" ADD CONSTRAINT "assignments_blocks_action_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."assignments"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "assignments_blocks_assertion" ADD CONSTRAINT "assignments_blocks_assertion_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."assignments"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "assignments_test_suites" ADD CONSTRAINT "assignments_test_suites_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."assignments"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "assignments_java_test_cases" ADD CONSTRAINT "assignments_java_test_cases_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."assignments"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "assignments_c_test_cases" ADD CONSTRAINT "assignments_c_test_cases_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."assignments"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "assignments" ADD CONSTRAINT "assignments_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "assignments" ADD CONSTRAINT "assignments_subject_id_subjects_id_fk" FOREIGN KEY ("subject_id") REFERENCES "public"."subjects"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "assignments" ADD CONSTRAINT "assignments_module_id_modules_id_fk" FOREIGN KEY ("module_id") REFERENCES "public"."modules"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "_assignments_v_version_hints" ADD CONSTRAINT "_assignments_v_version_hints_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_assignments_v"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_assignments_v_version_resources" ADD CONSTRAINT "_assignments_v_version_resources_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_assignments_v"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_assignments_v_blocks_action" ADD CONSTRAINT "_assignments_v_blocks_action_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_assignments_v"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_assignments_v_blocks_assertion" ADD CONSTRAINT "_assignments_v_blocks_assertion_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_assignments_v"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_assignments_v_version_test_suites" ADD CONSTRAINT "_assignments_v_version_test_suites_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_assignments_v"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_assignments_v_version_java_test_cases" ADD CONSTRAINT "_assignments_v_version_java_test_cases_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_assignments_v"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_assignments_v_version_c_test_cases" ADD CONSTRAINT "_assignments_v_version_c_test_cases_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_assignments_v"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_assignments_v" ADD CONSTRAINT "_assignments_v_parent_id_assignments_id_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."assignments"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "_assignments_v" ADD CONSTRAINT "_assignments_v_version_tenant_id_tenants_id_fk" FOREIGN KEY ("version_tenant_id") REFERENCES "public"."tenants"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "_assignments_v" ADD CONSTRAINT "_assignments_v_version_subject_id_subjects_id_fk" FOREIGN KEY ("version_subject_id") REFERENCES "public"."subjects"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "_assignments_v" ADD CONSTRAINT "_assignments_v_version_module_id_modules_id_fk" FOREIGN KEY ("version_module_id") REFERENCES "public"."modules"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "submissions" ADD CONSTRAINT "submissions_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "submissions" ADD CONSTRAINT "submissions_student_id_users_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "submissions" ADD CONSTRAINT "submissions_assignment_id_assignments_id_fk" FOREIGN KEY ("assignment_id") REFERENCES "public"."assignments"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "_submissions_v" ADD CONSTRAINT "_submissions_v_parent_id_submissions_id_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."submissions"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "_submissions_v" ADD CONSTRAINT "_submissions_v_version_tenant_id_tenants_id_fk" FOREIGN KEY ("version_tenant_id") REFERENCES "public"."tenants"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "_submissions_v" ADD CONSTRAINT "_submissions_v_version_student_id_users_id_fk" FOREIGN KEY ("version_student_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "_submissions_v" ADD CONSTRAINT "_submissions_v_version_assignment_id_assignments_id_fk" FOREIGN KEY ("version_assignment_id") REFERENCES "public"."assignments"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "payload_jobs_log" ADD CONSTRAINT "payload_jobs_log_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."payload_jobs"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."payload_locked_documents"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_users_fk" FOREIGN KEY ("users_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_tenants_fk" FOREIGN KEY ("tenants_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_batches_fk" FOREIGN KEY ("batches_id") REFERENCES "public"."batches"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_enrollments_fk" FOREIGN KEY ("enrollments_id") REFERENCES "public"."enrollments"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_subjects_fk" FOREIGN KEY ("subjects_id") REFERENCES "public"."subjects"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_modules_fk" FOREIGN KEY ("modules_id") REFERENCES "public"."modules"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_assignments_fk" FOREIGN KEY ("assignments_id") REFERENCES "public"."assignments"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_submissions_fk" FOREIGN KEY ("submissions_id") REFERENCES "public"."submissions"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_payload_jobs_fk" FOREIGN KEY ("payload_jobs_id") REFERENCES "public"."payload_jobs"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_preferences_rels" ADD CONSTRAINT "payload_preferences_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."payload_preferences"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "payload_preferences_rels" ADD CONSTRAINT "payload_preferences_rels_users_fk" FOREIGN KEY ("users_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;
  CREATE INDEX "users_roles_order_idx" ON "users_roles" USING btree ("order");
  CREATE INDEX "users_roles_parent_idx" ON "users_roles" USING btree ("parent_id");
  CREATE INDEX "users_tenants_roles_order_idx" ON "users_tenants_roles" USING btree ("order");
  CREATE INDEX "users_tenants_roles_parent_idx" ON "users_tenants_roles" USING btree ("parent_id");
  CREATE INDEX "users_tenants_order_idx" ON "users_tenants" USING btree ("_order");
  CREATE INDEX "users_tenants_parent_id_idx" ON "users_tenants" USING btree ("_parent_id");
  CREATE INDEX "users_tenants_tenant_idx" ON "users_tenants" USING btree ("tenant_id");
  CREATE INDEX "users_username_idx" ON "users" USING btree ("username");
  CREATE INDEX "users_updated_at_idx" ON "users" USING btree ("updated_at");
  CREATE INDEX "users_created_at_idx" ON "users" USING btree ("created_at");
  CREATE UNIQUE INDEX "users_email_idx" ON "users" USING btree ("email");
  CREATE UNIQUE INDEX "tenants_slug_idx" ON "tenants" USING btree ("slug");
  CREATE INDEX "tenants_updated_at_idx" ON "tenants" USING btree ("updated_at");
  CREATE INDEX "tenants_created_at_idx" ON "tenants" USING btree ("created_at");
  CREATE UNIQUE INDEX "batches_batch_id_idx" ON "batches" USING btree ("batch_id");
  CREATE INDEX "batches_updated_at_idx" ON "batches" USING btree ("updated_at");
  CREATE INDEX "batches_created_at_idx" ON "batches" USING btree ("created_at");
  CREATE INDEX "batches_rels_order_idx" ON "batches_rels" USING btree ("order");
  CREATE INDEX "batches_rels_parent_idx" ON "batches_rels" USING btree ("parent_id");
  CREATE INDEX "batches_rels_path_idx" ON "batches_rels" USING btree ("path");
  CREATE INDEX "batches_rels_users_id_idx" ON "batches_rels" USING btree ("users_id");
  CREATE INDEX "enrollments_module_idx" ON "enrollments" USING btree ("module_id");
  CREATE INDEX "enrollments_batch_idx" ON "enrollments" USING btree ("batch_id");
  CREATE INDEX "enrollments_updated_at_idx" ON "enrollments" USING btree ("updated_at");
  CREATE INDEX "enrollments_created_at_idx" ON "enrollments" USING btree ("created_at");
  CREATE INDEX "batch_accessStart_accessEnd_idx" ON "enrollments" USING btree ("batch_id","access_start","access_end");
  CREATE INDEX "subjects_tenant_idx" ON "subjects" USING btree ("tenant_id");
  CREATE INDEX "subjects_updated_at_idx" ON "subjects" USING btree ("updated_at");
  CREATE INDEX "subjects_created_at_idx" ON "subjects" USING btree ("created_at");
  CREATE INDEX "modules_updated_at_idx" ON "modules" USING btree ("updated_at");
  CREATE INDEX "modules_created_at_idx" ON "modules" USING btree ("created_at");
  CREATE INDEX "modules_rels_order_idx" ON "modules_rels" USING btree ("order");
  CREATE INDEX "modules_rels_parent_idx" ON "modules_rels" USING btree ("parent_id");
  CREATE INDEX "modules_rels_path_idx" ON "modules_rels" USING btree ("path");
  CREATE INDEX "modules_rels_assignments_id_idx" ON "modules_rels" USING btree ("assignments_id");
  CREATE INDEX "assignments_hints_order_idx" ON "assignments_hints" USING btree ("_order");
  CREATE INDEX "assignments_hints_parent_id_idx" ON "assignments_hints" USING btree ("_parent_id");
  CREATE INDEX "assignments_resources_order_idx" ON "assignments_resources" USING btree ("_order");
  CREATE INDEX "assignments_resources_parent_id_idx" ON "assignments_resources" USING btree ("_parent_id");
  CREATE INDEX "assignments_blocks_action_order_idx" ON "assignments_blocks_action" USING btree ("_order");
  CREATE INDEX "assignments_blocks_action_parent_id_idx" ON "assignments_blocks_action" USING btree ("_parent_id");
  CREATE INDEX "assignments_blocks_action_path_idx" ON "assignments_blocks_action" USING btree ("_path");
  CREATE INDEX "assignments_blocks_assertion_order_idx" ON "assignments_blocks_assertion" USING btree ("_order");
  CREATE INDEX "assignments_blocks_assertion_parent_id_idx" ON "assignments_blocks_assertion" USING btree ("_parent_id");
  CREATE INDEX "assignments_blocks_assertion_path_idx" ON "assignments_blocks_assertion" USING btree ("_path");
  CREATE INDEX "assignments_test_suites_order_idx" ON "assignments_test_suites" USING btree ("_order");
  CREATE INDEX "assignments_test_suites_parent_id_idx" ON "assignments_test_suites" USING btree ("_parent_id");
  CREATE INDEX "assignments_java_test_cases_order_idx" ON "assignments_java_test_cases" USING btree ("_order");
  CREATE INDEX "assignments_java_test_cases_parent_id_idx" ON "assignments_java_test_cases" USING btree ("_parent_id");
  CREATE INDEX "assignments_c_test_cases_order_idx" ON "assignments_c_test_cases" USING btree ("_order");
  CREATE INDEX "assignments_c_test_cases_parent_id_idx" ON "assignments_c_test_cases" USING btree ("_parent_id");
  CREATE INDEX "assignments_tenant_idx" ON "assignments" USING btree ("tenant_id");
  CREATE INDEX "assignments_subject_idx" ON "assignments" USING btree ("subject_id");
  CREATE INDEX "assignments_module_idx" ON "assignments" USING btree ("module_id");
  CREATE INDEX "assignments_language_idx" ON "assignments" USING btree ("language");
  CREATE INDEX "assignments_difficulty_idx" ON "assignments" USING btree ("difficulty");
  CREATE INDEX "assignments_due_date_idx" ON "assignments" USING btree ("due_date");
  CREATE INDEX "assignments_updated_at_idx" ON "assignments" USING btree ("updated_at");
  CREATE INDEX "assignments_created_at_idx" ON "assignments" USING btree ("created_at");
  CREATE INDEX "assignments__status_idx" ON "assignments" USING btree ("_status");
  CREATE INDEX "_assignments_v_version_hints_order_idx" ON "_assignments_v_version_hints" USING btree ("_order");
  CREATE INDEX "_assignments_v_version_hints_parent_id_idx" ON "_assignments_v_version_hints" USING btree ("_parent_id");
  CREATE INDEX "_assignments_v_version_resources_order_idx" ON "_assignments_v_version_resources" USING btree ("_order");
  CREATE INDEX "_assignments_v_version_resources_parent_id_idx" ON "_assignments_v_version_resources" USING btree ("_parent_id");
  CREATE INDEX "_assignments_v_blocks_action_order_idx" ON "_assignments_v_blocks_action" USING btree ("_order");
  CREATE INDEX "_assignments_v_blocks_action_parent_id_idx" ON "_assignments_v_blocks_action" USING btree ("_parent_id");
  CREATE INDEX "_assignments_v_blocks_action_path_idx" ON "_assignments_v_blocks_action" USING btree ("_path");
  CREATE INDEX "_assignments_v_blocks_assertion_order_idx" ON "_assignments_v_blocks_assertion" USING btree ("_order");
  CREATE INDEX "_assignments_v_blocks_assertion_parent_id_idx" ON "_assignments_v_blocks_assertion" USING btree ("_parent_id");
  CREATE INDEX "_assignments_v_blocks_assertion_path_idx" ON "_assignments_v_blocks_assertion" USING btree ("_path");
  CREATE INDEX "_assignments_v_version_test_suites_order_idx" ON "_assignments_v_version_test_suites" USING btree ("_order");
  CREATE INDEX "_assignments_v_version_test_suites_parent_id_idx" ON "_assignments_v_version_test_suites" USING btree ("_parent_id");
  CREATE INDEX "_assignments_v_version_java_test_cases_order_idx" ON "_assignments_v_version_java_test_cases" USING btree ("_order");
  CREATE INDEX "_assignments_v_version_java_test_cases_parent_id_idx" ON "_assignments_v_version_java_test_cases" USING btree ("_parent_id");
  CREATE INDEX "_assignments_v_version_c_test_cases_order_idx" ON "_assignments_v_version_c_test_cases" USING btree ("_order");
  CREATE INDEX "_assignments_v_version_c_test_cases_parent_id_idx" ON "_assignments_v_version_c_test_cases" USING btree ("_parent_id");
  CREATE INDEX "_assignments_v_parent_idx" ON "_assignments_v" USING btree ("parent_id");
  CREATE INDEX "_assignments_v_version_version_tenant_idx" ON "_assignments_v" USING btree ("version_tenant_id");
  CREATE INDEX "_assignments_v_version_version_subject_idx" ON "_assignments_v" USING btree ("version_subject_id");
  CREATE INDEX "_assignments_v_version_version_module_idx" ON "_assignments_v" USING btree ("version_module_id");
  CREATE INDEX "_assignments_v_version_version_language_idx" ON "_assignments_v" USING btree ("version_language");
  CREATE INDEX "_assignments_v_version_version_difficulty_idx" ON "_assignments_v" USING btree ("version_difficulty");
  CREATE INDEX "_assignments_v_version_version_due_date_idx" ON "_assignments_v" USING btree ("version_due_date");
  CREATE INDEX "_assignments_v_version_version_updated_at_idx" ON "_assignments_v" USING btree ("version_updated_at");
  CREATE INDEX "_assignments_v_version_version_created_at_idx" ON "_assignments_v" USING btree ("version_created_at");
  CREATE INDEX "_assignments_v_version_version__status_idx" ON "_assignments_v" USING btree ("version__status");
  CREATE INDEX "_assignments_v_created_at_idx" ON "_assignments_v" USING btree ("created_at");
  CREATE INDEX "_assignments_v_updated_at_idx" ON "_assignments_v" USING btree ("updated_at");
  CREATE INDEX "_assignments_v_latest_idx" ON "_assignments_v" USING btree ("latest");
  CREATE INDEX "submissions_tenant_idx" ON "submissions" USING btree ("tenant_id");
  CREATE INDEX "submissions_student_idx" ON "submissions" USING btree ("student_id");
  CREATE INDEX "submissions_assignment_idx" ON "submissions" USING btree ("assignment_id");
  CREATE INDEX "submissions_updated_at_idx" ON "submissions" USING btree ("updated_at");
  CREATE INDEX "submissions_created_at_idx" ON "submissions" USING btree ("created_at");
  CREATE UNIQUE INDEX "student_assignment_idx" ON "submissions" USING btree ("student_id","assignment_id");
  CREATE INDEX "_submissions_v_parent_idx" ON "_submissions_v" USING btree ("parent_id");
  CREATE INDEX "_submissions_v_version_version_tenant_idx" ON "_submissions_v" USING btree ("version_tenant_id");
  CREATE INDEX "_submissions_v_version_version_student_idx" ON "_submissions_v" USING btree ("version_student_id");
  CREATE INDEX "_submissions_v_version_version_assignment_idx" ON "_submissions_v" USING btree ("version_assignment_id");
  CREATE INDEX "_submissions_v_version_version_updated_at_idx" ON "_submissions_v" USING btree ("version_updated_at");
  CREATE INDEX "_submissions_v_version_version_created_at_idx" ON "_submissions_v" USING btree ("version_created_at");
  CREATE INDEX "_submissions_v_created_at_idx" ON "_submissions_v" USING btree ("created_at");
  CREATE INDEX "_submissions_v_updated_at_idx" ON "_submissions_v" USING btree ("updated_at");
  CREATE INDEX "version_student_version_assignment_idx" ON "_submissions_v" USING btree ("version_student_id","version_assignment_id");
  CREATE INDEX "payload_jobs_log_order_idx" ON "payload_jobs_log" USING btree ("_order");
  CREATE INDEX "payload_jobs_log_parent_id_idx" ON "payload_jobs_log" USING btree ("_parent_id");
  CREATE INDEX "payload_jobs_completed_at_idx" ON "payload_jobs" USING btree ("completed_at");
  CREATE INDEX "payload_jobs_total_tried_idx" ON "payload_jobs" USING btree ("total_tried");
  CREATE INDEX "payload_jobs_has_error_idx" ON "payload_jobs" USING btree ("has_error");
  CREATE INDEX "payload_jobs_task_slug_idx" ON "payload_jobs" USING btree ("task_slug");
  CREATE INDEX "payload_jobs_queue_idx" ON "payload_jobs" USING btree ("queue");
  CREATE INDEX "payload_jobs_wait_until_idx" ON "payload_jobs" USING btree ("wait_until");
  CREATE INDEX "payload_jobs_processing_idx" ON "payload_jobs" USING btree ("processing");
  CREATE INDEX "payload_jobs_updated_at_idx" ON "payload_jobs" USING btree ("updated_at");
  CREATE INDEX "payload_jobs_created_at_idx" ON "payload_jobs" USING btree ("created_at");
  CREATE INDEX "payload_locked_documents_global_slug_idx" ON "payload_locked_documents" USING btree ("global_slug");
  CREATE INDEX "payload_locked_documents_updated_at_idx" ON "payload_locked_documents" USING btree ("updated_at");
  CREATE INDEX "payload_locked_documents_created_at_idx" ON "payload_locked_documents" USING btree ("created_at");
  CREATE INDEX "payload_locked_documents_rels_order_idx" ON "payload_locked_documents_rels" USING btree ("order");
  CREATE INDEX "payload_locked_documents_rels_parent_idx" ON "payload_locked_documents_rels" USING btree ("parent_id");
  CREATE INDEX "payload_locked_documents_rels_path_idx" ON "payload_locked_documents_rels" USING btree ("path");
  CREATE INDEX "payload_locked_documents_rels_users_id_idx" ON "payload_locked_documents_rels" USING btree ("users_id");
  CREATE INDEX "payload_locked_documents_rels_tenants_id_idx" ON "payload_locked_documents_rels" USING btree ("tenants_id");
  CREATE INDEX "payload_locked_documents_rels_batches_id_idx" ON "payload_locked_documents_rels" USING btree ("batches_id");
  CREATE INDEX "payload_locked_documents_rels_enrollments_id_idx" ON "payload_locked_documents_rels" USING btree ("enrollments_id");
  CREATE INDEX "payload_locked_documents_rels_subjects_id_idx" ON "payload_locked_documents_rels" USING btree ("subjects_id");
  CREATE INDEX "payload_locked_documents_rels_modules_id_idx" ON "payload_locked_documents_rels" USING btree ("modules_id");
  CREATE INDEX "payload_locked_documents_rels_assignments_id_idx" ON "payload_locked_documents_rels" USING btree ("assignments_id");
  CREATE INDEX "payload_locked_documents_rels_submissions_id_idx" ON "payload_locked_documents_rels" USING btree ("submissions_id");
  CREATE INDEX "payload_locked_documents_rels_payload_jobs_id_idx" ON "payload_locked_documents_rels" USING btree ("payload_jobs_id");
  CREATE INDEX "payload_preferences_key_idx" ON "payload_preferences" USING btree ("key");
  CREATE INDEX "payload_preferences_updated_at_idx" ON "payload_preferences" USING btree ("updated_at");
  CREATE INDEX "payload_preferences_created_at_idx" ON "payload_preferences" USING btree ("created_at");
  CREATE INDEX "payload_preferences_rels_order_idx" ON "payload_preferences_rels" USING btree ("order");
  CREATE INDEX "payload_preferences_rels_parent_idx" ON "payload_preferences_rels" USING btree ("parent_id");
  CREATE INDEX "payload_preferences_rels_path_idx" ON "payload_preferences_rels" USING btree ("path");
  CREATE INDEX "payload_preferences_rels_users_id_idx" ON "payload_preferences_rels" USING btree ("users_id");
  CREATE INDEX "payload_migrations_updated_at_idx" ON "payload_migrations" USING btree ("updated_at");
  CREATE INDEX "payload_migrations_created_at_idx" ON "payload_migrations" USING btree ("created_at");`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   DROP TABLE "users_roles" CASCADE;
  DROP TABLE "users_tenants_roles" CASCADE;
  DROP TABLE "users_tenants" CASCADE;
  DROP TABLE "users" CASCADE;
  DROP TABLE "tenants" CASCADE;
  DROP TABLE "batches" CASCADE;
  DROP TABLE "batches_rels" CASCADE;
  DROP TABLE "enrollments" CASCADE;
  DROP TABLE "subjects" CASCADE;
  DROP TABLE "modules" CASCADE;
  DROP TABLE "modules_rels" CASCADE;
  DROP TABLE "assignments_hints" CASCADE;
  DROP TABLE "assignments_resources" CASCADE;
  DROP TABLE "assignments_blocks_action" CASCADE;
  DROP TABLE "assignments_blocks_assertion" CASCADE;
  DROP TABLE "assignments_test_suites" CASCADE;
  DROP TABLE "assignments_java_test_cases" CASCADE;
  DROP TABLE "assignments_c_test_cases" CASCADE;
  DROP TABLE "assignments" CASCADE;
  DROP TABLE "_assignments_v_version_hints" CASCADE;
  DROP TABLE "_assignments_v_version_resources" CASCADE;
  DROP TABLE "_assignments_v_blocks_action" CASCADE;
  DROP TABLE "_assignments_v_blocks_assertion" CASCADE;
  DROP TABLE "_assignments_v_version_test_suites" CASCADE;
  DROP TABLE "_assignments_v_version_java_test_cases" CASCADE;
  DROP TABLE "_assignments_v_version_c_test_cases" CASCADE;
  DROP TABLE "_assignments_v" CASCADE;
  DROP TABLE "submissions" CASCADE;
  DROP TABLE "_submissions_v" CASCADE;
  DROP TABLE "payload_jobs_log" CASCADE;
  DROP TABLE "payload_jobs" CASCADE;
  DROP TABLE "payload_locked_documents" CASCADE;
  DROP TABLE "payload_locked_documents_rels" CASCADE;
  DROP TABLE "payload_preferences" CASCADE;
  DROP TABLE "payload_preferences_rels" CASCADE;
  DROP TABLE "payload_migrations" CASCADE;
  DROP TYPE "public"."enum_users_roles";
  DROP TYPE "public"."enum_users_tenants_roles";
  DROP TYPE "public"."enum_assignments_blocks_action_action_type";
  DROP TYPE "public"."enum_assignments_blocks_assertion_assertion_type";
  DROP TYPE "public"."enum_assignments_test_suites_visibility";
  DROP TYPE "public"."enum_assignments_language";
  DROP TYPE "public"."enum_assignments_difficulty";
  DROP TYPE "public"."enum_assignments_status";
  DROP TYPE "public"."enum__assignments_v_blocks_action_action_type";
  DROP TYPE "public"."enum__assignments_v_blocks_assertion_assertion_type";
  DROP TYPE "public"."enum__assignments_v_version_test_suites_visibility";
  DROP TYPE "public"."enum__assignments_v_version_language";
  DROP TYPE "public"."enum__assignments_v_version_difficulty";
  DROP TYPE "public"."enum__assignments_v_version_status";
  DROP TYPE "public"."enum_submissions_status";
  DROP TYPE "public"."enum__submissions_v_version_status";
  DROP TYPE "public"."enum_payload_jobs_log_task_slug";
  DROP TYPE "public"."enum_payload_jobs_log_state";
  DROP TYPE "public"."enum_payload_jobs_task_slug";`)
}
