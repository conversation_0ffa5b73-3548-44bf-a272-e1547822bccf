{"id": "e1e49617-2570-45a7-bfae-1e507ba53fc3", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.users_roles": {"name": "users_roles", "schema": "", "columns": {"order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "enum_users_roles", "typeSchema": "public", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}}, "indexes": {"users_roles_order_idx": {"name": "users_roles_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_roles_parent_idx": {"name": "users_roles_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"users_roles_parent_fk": {"name": "users_roles_parent_fk", "tableFrom": "users_roles", "tableTo": "users", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users_tenants_roles": {"name": "users_tenants_roles", "schema": "", "columns": {"order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "enum_users_tenants_roles", "typeSchema": "public", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}}, "indexes": {"users_tenants_roles_order_idx": {"name": "users_tenants_roles_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_tenants_roles_parent_idx": {"name": "users_tenants_roles_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"users_tenants_roles_parent_fk": {"name": "users_tenants_roles_parent_fk", "tableFrom": "users_tenants_roles", "tableTo": "users_tenants", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users_tenants": {"name": "users_tenants", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {"users_tenants_order_idx": {"name": "users_tenants_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_tenants_parent_id_idx": {"name": "users_tenants_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_tenants_tenant_idx": {"name": "users_tenants_tenant_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"users_tenants_tenant_id_tenants_id_fk": {"name": "users_tenants_tenant_id_tenants_id_fk", "tableFrom": "users_tenants", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "users_tenants_parent_id_fk": {"name": "users_tenants_parent_id_fk", "tableFrom": "users_tenants", "tableTo": "users", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "full_name": {"name": "full_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "division": {"name": "division", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "reset_password_token": {"name": "reset_password_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "reset_password_expiration": {"name": "reset_password_expiration", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "salt": {"name": "salt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "hash": {"name": "hash", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "login_attempts": {"name": "login_attempts", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "lock_until": {"name": "lock_until", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"users_username_idx": {"name": "users_username_idx", "columns": [{"expression": "username", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_updated_at_idx": {"name": "users_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_created_at_idx": {"name": "users_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tenants": {"name": "tenants", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "domain": {"name": "domain", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"tenants_slug_idx": {"name": "tenants_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "tenants_updated_at_idx": {"name": "tenants_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "tenants_created_at_idx": {"name": "tenants_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.batches": {"name": "batches", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "batch_id": {"name": "batch_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"batches_batch_id_idx": {"name": "batches_batch_id_idx", "columns": [{"expression": "batch_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "batches_updated_at_idx": {"name": "batches_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "batches_created_at_idx": {"name": "batches_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.batches_rels": {"name": "batches_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"batches_rels_order_idx": {"name": "batches_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "batches_rels_parent_idx": {"name": "batches_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "batches_rels_path_idx": {"name": "batches_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "batches_rels_users_id_idx": {"name": "batches_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"batches_rels_parent_fk": {"name": "batches_rels_parent_fk", "tableFrom": "batches_rels", "tableTo": "batches", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "batches_rels_users_fk": {"name": "batches_rels_users_fk", "tableFrom": "batches_rels", "tableTo": "users", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.enrollments": {"name": "enrollments", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "module_id": {"name": "module_id", "type": "integer", "primaryKey": false, "notNull": true}, "batch_id": {"name": "batch_id", "type": "integer", "primaryKey": false, "notNull": true}, "access_start": {"name": "access_start", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true}, "access_end": {"name": "access_end", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"enrollments_module_idx": {"name": "enrollments_module_idx", "columns": [{"expression": "module_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "enrollments_batch_idx": {"name": "enrollments_batch_idx", "columns": [{"expression": "batch_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "enrollments_updated_at_idx": {"name": "enrollments_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "enrollments_created_at_idx": {"name": "enrollments_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "batch_accessStart_accessEnd_idx": {"name": "batch_accessStart_accessEnd_idx", "columns": [{"expression": "batch_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "access_start", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "access_end", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"enrollments_module_id_modules_id_fk": {"name": "enrollments_module_id_modules_id_fk", "tableFrom": "enrollments", "tableTo": "modules", "columnsFrom": ["module_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "enrollments_batch_id_batches_id_fk": {"name": "enrollments_batch_id_batches_id_fk", "tableFrom": "enrollments", "tableTo": "batches", "columnsFrom": ["batch_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subjects": {"name": "subjects", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"subjects_tenant_idx": {"name": "subjects_tenant_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subjects_updated_at_idx": {"name": "subjects_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subjects_created_at_idx": {"name": "subjects_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"subjects_tenant_id_tenants_id_fk": {"name": "subjects_tenant_id_tenants_id_fk", "tableFrom": "subjects", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.modules": {"name": "modules", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"modules_updated_at_idx": {"name": "modules_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "modules_created_at_idx": {"name": "modules_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.modules_rels": {"name": "modules_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "assignments_id": {"name": "assignments_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"modules_rels_order_idx": {"name": "modules_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "modules_rels_parent_idx": {"name": "modules_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "modules_rels_path_idx": {"name": "modules_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "modules_rels_assignments_id_idx": {"name": "modules_rels_assignments_id_idx", "columns": [{"expression": "assignments_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"modules_rels_parent_fk": {"name": "modules_rels_parent_fk", "tableFrom": "modules_rels", "tableTo": "modules", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "modules_rels_assignments_fk": {"name": "modules_rels_assignments_fk", "tableFrom": "modules_rels", "tableTo": "assignments", "columnsFrom": ["assignments_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.assignments_hints": {"name": "assignments_hints", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "question": {"name": "question", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "answer": {"name": "answer", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"assignments_hints_order_idx": {"name": "assignments_hints_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "assignments_hints_parent_id_idx": {"name": "assignments_hints_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"assignments_hints_parent_id_fk": {"name": "assignments_hints_parent_id_fk", "tableFrom": "assignments_hints", "tableTo": "assignments", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.assignments_resources": {"name": "assignments_resources", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"assignments_resources_order_idx": {"name": "assignments_resources_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "assignments_resources_parent_id_idx": {"name": "assignments_resources_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"assignments_resources_parent_id_fk": {"name": "assignments_resources_parent_id_fk", "tableFrom": "assignments_resources", "tableTo": "assignments", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.assignments": {"name": "assignments", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "subject_id": {"name": "subject_id", "type": "integer", "primaryKey": false, "notNull": false}, "module_id": {"name": "module_id", "type": "integer", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "language": {"name": "language", "type": "enum_assignments_language", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'web'"}, "difficulty": {"name": "difficulty", "type": "enum_assignments_difficulty", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'easy'"}, "points": {"name": "points", "type": "numeric", "primaryKey": false, "notNull": false}, "due_date": {"name": "due_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "instructions": {"name": "instructions", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "tests_requirement": {"name": "tests_requirement", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "starter_code_html": {"name": "starter_code_html", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "starter_code_css": {"name": "starter_code_css", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "starter_code_js": {"name": "starter_code_js", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "starter_code_java": {"name": "starter_code_java", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "starter_code_c": {"name": "starter_code_c", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "solution_code_html": {"name": "solution_code_html", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "solution_code_css": {"name": "solution_code_css", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "solution_code_js": {"name": "solution_code_js", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "solution_code_java": {"name": "solution_code_java", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "solution_code_c": {"name": "solution_code_c", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "solution_notes": {"name": "solution_notes", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "_status": {"name": "_status", "type": "enum_assignments_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'draft'"}}, "indexes": {"assignments_tenant_idx": {"name": "assignments_tenant_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "assignments_subject_idx": {"name": "assignments_subject_idx", "columns": [{"expression": "subject_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "assignments_module_idx": {"name": "assignments_module_idx", "columns": [{"expression": "module_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "assignments_language_idx": {"name": "assignments_language_idx", "columns": [{"expression": "language", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "assignments_difficulty_idx": {"name": "assignments_difficulty_idx", "columns": [{"expression": "difficulty", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "assignments_due_date_idx": {"name": "assignments_due_date_idx", "columns": [{"expression": "due_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "assignments_updated_at_idx": {"name": "assignments_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "assignments_created_at_idx": {"name": "assignments_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "assignments__status_idx": {"name": "assignments__status_idx", "columns": [{"expression": "_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"assignments_tenant_id_tenants_id_fk": {"name": "assignments_tenant_id_tenants_id_fk", "tableFrom": "assignments", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "assignments_subject_id_subjects_id_fk": {"name": "assignments_subject_id_subjects_id_fk", "tableFrom": "assignments", "tableTo": "subjects", "columnsFrom": ["subject_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "assignments_module_id_modules_id_fk": {"name": "assignments_module_id_modules_id_fk", "tableFrom": "assignments", "tableTo": "modules", "columnsFrom": ["module_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._assignments_v_version_hints": {"name": "_assignments_v_version_hints", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "question": {"name": "question", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "answer": {"name": "answer", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_assignments_v_version_hints_order_idx": {"name": "_assignments_v_version_hints_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_assignments_v_version_hints_parent_id_idx": {"name": "_assignments_v_version_hints_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_assignments_v_version_hints_parent_id_fk": {"name": "_assignments_v_version_hints_parent_id_fk", "tableFrom": "_assignments_v_version_hints", "tableTo": "_assignments_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._assignments_v_version_resources": {"name": "_assignments_v_version_resources", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_assignments_v_version_resources_order_idx": {"name": "_assignments_v_version_resources_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_assignments_v_version_resources_parent_id_idx": {"name": "_assignments_v_version_resources_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_assignments_v_version_resources_parent_id_fk": {"name": "_assignments_v_version_resources_parent_id_fk", "tableFrom": "_assignments_v_version_resources", "tableTo": "_assignments_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._assignments_v": {"name": "_assignments_v", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_tenant_id": {"name": "version_tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_title": {"name": "version_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_subject_id": {"name": "version_subject_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_module_id": {"name": "version_module_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_description": {"name": "version_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_language": {"name": "version_language", "type": "enum__assignments_v_version_language", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'web'"}, "version_difficulty": {"name": "version_difficulty", "type": "enum__assignments_v_version_difficulty", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'easy'"}, "version_points": {"name": "version_points", "type": "numeric", "primaryKey": false, "notNull": false}, "version_due_date": {"name": "version_due_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_instructions": {"name": "version_instructions", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_tests_requirement": {"name": "version_tests_requirement", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_starter_code_html": {"name": "version_starter_code_html", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_starter_code_css": {"name": "version_starter_code_css", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_starter_code_js": {"name": "version_starter_code_js", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_starter_code_java": {"name": "version_starter_code_java", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_starter_code_c": {"name": "version_starter_code_c", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_solution_code_html": {"name": "version_solution_code_html", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_solution_code_css": {"name": "version_solution_code_css", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_solution_code_js": {"name": "version_solution_code_js", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_solution_code_java": {"name": "version_solution_code_java", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_solution_code_c": {"name": "version_solution_code_c", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_solution_notes": {"name": "version_solution_notes", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_updated_at": {"name": "version_updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_created_at": {"name": "version_created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version__status": {"name": "version__status", "type": "enum__assignments_v_version_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "latest": {"name": "latest", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"_assignments_v_parent_idx": {"name": "_assignments_v_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_assignments_v_version_version_tenant_idx": {"name": "_assignments_v_version_version_tenant_idx", "columns": [{"expression": "version_tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_assignments_v_version_version_subject_idx": {"name": "_assignments_v_version_version_subject_idx", "columns": [{"expression": "version_subject_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_assignments_v_version_version_module_idx": {"name": "_assignments_v_version_version_module_idx", "columns": [{"expression": "version_module_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_assignments_v_version_version_language_idx": {"name": "_assignments_v_version_version_language_idx", "columns": [{"expression": "version_language", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_assignments_v_version_version_difficulty_idx": {"name": "_assignments_v_version_version_difficulty_idx", "columns": [{"expression": "version_difficulty", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_assignments_v_version_version_due_date_idx": {"name": "_assignments_v_version_version_due_date_idx", "columns": [{"expression": "version_due_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_assignments_v_version_version_updated_at_idx": {"name": "_assignments_v_version_version_updated_at_idx", "columns": [{"expression": "version_updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_assignments_v_version_version_created_at_idx": {"name": "_assignments_v_version_version_created_at_idx", "columns": [{"expression": "version_created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_assignments_v_version_version__status_idx": {"name": "_assignments_v_version_version__status_idx", "columns": [{"expression": "version__status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_assignments_v_created_at_idx": {"name": "_assignments_v_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_assignments_v_updated_at_idx": {"name": "_assignments_v_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_assignments_v_latest_idx": {"name": "_assignments_v_latest_idx", "columns": [{"expression": "latest", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_assignments_v_parent_id_assignments_id_fk": {"name": "_assignments_v_parent_id_assignments_id_fk", "tableFrom": "_assignments_v", "tableTo": "assignments", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_assignments_v_version_tenant_id_tenants_id_fk": {"name": "_assignments_v_version_tenant_id_tenants_id_fk", "tableFrom": "_assignments_v", "tableTo": "tenants", "columnsFrom": ["version_tenant_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_assignments_v_version_subject_id_subjects_id_fk": {"name": "_assignments_v_version_subject_id_subjects_id_fk", "tableFrom": "_assignments_v", "tableTo": "subjects", "columnsFrom": ["version_subject_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_assignments_v_version_module_id_modules_id_fk": {"name": "_assignments_v_version_module_id_modules_id_fk", "tableFrom": "_assignments_v", "tableTo": "modules", "columnsFrom": ["version_module_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.submissions_tests_result": {"name": "submissions_tests_result", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "input": {"name": "input", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "expected": {"name": "expected", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "actual": {"name": "actual", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "enum_submissions_tests_result_status", "typeSchema": "public", "primaryKey": false, "notNull": false}}, "indexes": {"submissions_tests_result_order_idx": {"name": "submissions_tests_result_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "submissions_tests_result_parent_id_idx": {"name": "submissions_tests_result_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"submissions_tests_result_parent_id_fk": {"name": "submissions_tests_result_parent_id_fk", "tableFrom": "submissions_tests_result", "tableTo": "submissions", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.submissions": {"name": "submissions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "student_id": {"name": "student_id", "type": "integer", "primaryKey": false, "notNull": true}, "assignment_id": {"name": "assignment_id", "type": "integer", "primaryKey": false, "notNull": true}, "html": {"name": "html", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "css": {"name": "css", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "js": {"name": "js", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "java": {"name": "java", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "c": {"name": "c", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "summary": {"name": "summary", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "enum_submissions_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'review'"}, "score": {"name": "score", "type": "numeric", "primaryKey": false, "notNull": false}, "feedback": {"name": "feedback", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "is_locked": {"name": "is_locked", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"submissions_tenant_idx": {"name": "submissions_tenant_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "submissions_student_idx": {"name": "submissions_student_idx", "columns": [{"expression": "student_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "submissions_assignment_idx": {"name": "submissions_assignment_idx", "columns": [{"expression": "assignment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "submissions_updated_at_idx": {"name": "submissions_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "submissions_created_at_idx": {"name": "submissions_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "student_assignment_idx": {"name": "student_assignment_idx", "columns": [{"expression": "student_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "assignment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"submissions_tenant_id_tenants_id_fk": {"name": "submissions_tenant_id_tenants_id_fk", "tableFrom": "submissions", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "submissions_student_id_users_id_fk": {"name": "submissions_student_id_users_id_fk", "tableFrom": "submissions", "tableTo": "users", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "submissions_assignment_id_assignments_id_fk": {"name": "submissions_assignment_id_assignments_id_fk", "tableFrom": "submissions", "tableTo": "assignments", "columnsFrom": ["assignment_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._submissions_v_version_tests_result": {"name": "_submissions_v_version_tests_result", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "input": {"name": "input", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "expected": {"name": "expected", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "actual": {"name": "actual", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "enum__submissions_v_version_tests_result_status", "typeSchema": "public", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_submissions_v_version_tests_result_order_idx": {"name": "_submissions_v_version_tests_result_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_submissions_v_version_tests_result_parent_id_idx": {"name": "_submissions_v_version_tests_result_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_submissions_v_version_tests_result_parent_id_fk": {"name": "_submissions_v_version_tests_result_parent_id_fk", "tableFrom": "_submissions_v_version_tests_result", "tableTo": "_submissions_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._submissions_v": {"name": "_submissions_v", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_tenant_id": {"name": "version_tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_student_id": {"name": "version_student_id", "type": "integer", "primaryKey": false, "notNull": true}, "version_assignment_id": {"name": "version_assignment_id", "type": "integer", "primaryKey": false, "notNull": true}, "version_html": {"name": "version_html", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_css": {"name": "version_css", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_js": {"name": "version_js", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_java": {"name": "version_java", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_c": {"name": "version_c", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_summary": {"name": "version_summary", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_status": {"name": "version_status", "type": "enum__submissions_v_version_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'review'"}, "version_score": {"name": "version_score", "type": "numeric", "primaryKey": false, "notNull": false}, "version_feedback": {"name": "version_feedback", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_is_locked": {"name": "version_is_locked", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "version_updated_at": {"name": "version_updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_created_at": {"name": "version_created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"_submissions_v_parent_idx": {"name": "_submissions_v_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_submissions_v_version_version_tenant_idx": {"name": "_submissions_v_version_version_tenant_idx", "columns": [{"expression": "version_tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_submissions_v_version_version_student_idx": {"name": "_submissions_v_version_version_student_idx", "columns": [{"expression": "version_student_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_submissions_v_version_version_assignment_idx": {"name": "_submissions_v_version_version_assignment_idx", "columns": [{"expression": "version_assignment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_submissions_v_version_version_updated_at_idx": {"name": "_submissions_v_version_version_updated_at_idx", "columns": [{"expression": "version_updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_submissions_v_version_version_created_at_idx": {"name": "_submissions_v_version_version_created_at_idx", "columns": [{"expression": "version_created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_submissions_v_created_at_idx": {"name": "_submissions_v_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_submissions_v_updated_at_idx": {"name": "_submissions_v_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "version_student_version_assignment_idx": {"name": "version_student_version_assignment_idx", "columns": [{"expression": "version_student_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "version_assignment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_submissions_v_parent_id_submissions_id_fk": {"name": "_submissions_v_parent_id_submissions_id_fk", "tableFrom": "_submissions_v", "tableTo": "submissions", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_submissions_v_version_tenant_id_tenants_id_fk": {"name": "_submissions_v_version_tenant_id_tenants_id_fk", "tableFrom": "_submissions_v", "tableTo": "tenants", "columnsFrom": ["version_tenant_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_submissions_v_version_student_id_users_id_fk": {"name": "_submissions_v_version_student_id_users_id_fk", "tableFrom": "_submissions_v", "tableTo": "users", "columnsFrom": ["version_student_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_submissions_v_version_assignment_id_assignments_id_fk": {"name": "_submissions_v_version_assignment_id_assignments_id_fk", "tableFrom": "_submissions_v", "tableTo": "assignments", "columnsFrom": ["version_assignment_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_jobs_log": {"name": "payload_jobs_log", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "executed_at": {"name": "executed_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true}, "completed_at": {"name": "completed_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true}, "task_slug": {"name": "task_slug", "type": "enum_payload_jobs_log_task_slug", "typeSchema": "public", "primaryKey": false, "notNull": true}, "task_i_d": {"name": "task_i_d", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "input": {"name": "input", "type": "jsonb", "primaryKey": false, "notNull": false}, "output": {"name": "output", "type": "jsonb", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "enum_payload_jobs_log_state", "typeSchema": "public", "primaryKey": false, "notNull": true}, "error": {"name": "error", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"payload_jobs_log_order_idx": {"name": "payload_jobs_log_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_log_parent_id_idx": {"name": "payload_jobs_log_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_jobs_log_parent_id_fk": {"name": "payload_jobs_log_parent_id_fk", "tableFrom": "payload_jobs_log", "tableTo": "payload_jobs", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_jobs": {"name": "payload_jobs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "input": {"name": "input", "type": "jsonb", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "total_tried": {"name": "total_tried", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "has_error": {"name": "has_error", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "error": {"name": "error", "type": "jsonb", "primaryKey": false, "notNull": false}, "task_slug": {"name": "task_slug", "type": "enum_payload_jobs_task_slug", "typeSchema": "public", "primaryKey": false, "notNull": false}, "queue": {"name": "queue", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'default'"}, "wait_until": {"name": "wait_until", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "processing": {"name": "processing", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_jobs_completed_at_idx": {"name": "payload_jobs_completed_at_idx", "columns": [{"expression": "completed_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_total_tried_idx": {"name": "payload_jobs_total_tried_idx", "columns": [{"expression": "total_tried", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_has_error_idx": {"name": "payload_jobs_has_error_idx", "columns": [{"expression": "has_error", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_task_slug_idx": {"name": "payload_jobs_task_slug_idx", "columns": [{"expression": "task_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_queue_idx": {"name": "payload_jobs_queue_idx", "columns": [{"expression": "queue", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_wait_until_idx": {"name": "payload_jobs_wait_until_idx", "columns": [{"expression": "wait_until", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_processing_idx": {"name": "payload_jobs_processing_idx", "columns": [{"expression": "processing", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_updated_at_idx": {"name": "payload_jobs_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_created_at_idx": {"name": "payload_jobs_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_locked_documents": {"name": "payload_locked_documents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "global_slug": {"name": "global_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_locked_documents_global_slug_idx": {"name": "payload_locked_documents_global_slug_idx", "columns": [{"expression": "global_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_updated_at_idx": {"name": "payload_locked_documents_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_created_at_idx": {"name": "payload_locked_documents_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_locked_documents_rels": {"name": "payload_locked_documents_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}, "tenants_id": {"name": "tenants_id", "type": "integer", "primaryKey": false, "notNull": false}, "batches_id": {"name": "batches_id", "type": "integer", "primaryKey": false, "notNull": false}, "enrollments_id": {"name": "enrollments_id", "type": "integer", "primaryKey": false, "notNull": false}, "subjects_id": {"name": "subjects_id", "type": "integer", "primaryKey": false, "notNull": false}, "modules_id": {"name": "modules_id", "type": "integer", "primaryKey": false, "notNull": false}, "assignments_id": {"name": "assignments_id", "type": "integer", "primaryKey": false, "notNull": false}, "submissions_id": {"name": "submissions_id", "type": "integer", "primaryKey": false, "notNull": false}, "payload_jobs_id": {"name": "payload_jobs_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"payload_locked_documents_rels_order_idx": {"name": "payload_locked_documents_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_parent_idx": {"name": "payload_locked_documents_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_path_idx": {"name": "payload_locked_documents_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_users_id_idx": {"name": "payload_locked_documents_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_tenants_id_idx": {"name": "payload_locked_documents_rels_tenants_id_idx", "columns": [{"expression": "tenants_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_batches_id_idx": {"name": "payload_locked_documents_rels_batches_id_idx", "columns": [{"expression": "batches_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_enrollments_id_idx": {"name": "payload_locked_documents_rels_enrollments_id_idx", "columns": [{"expression": "enrollments_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_subjects_id_idx": {"name": "payload_locked_documents_rels_subjects_id_idx", "columns": [{"expression": "subjects_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_modules_id_idx": {"name": "payload_locked_documents_rels_modules_id_idx", "columns": [{"expression": "modules_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_assignments_id_idx": {"name": "payload_locked_documents_rels_assignments_id_idx", "columns": [{"expression": "assignments_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_submissions_id_idx": {"name": "payload_locked_documents_rels_submissions_id_idx", "columns": [{"expression": "submissions_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_payload_jobs_id_idx": {"name": "payload_locked_documents_rels_payload_jobs_id_idx", "columns": [{"expression": "payload_jobs_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_locked_documents_rels_parent_fk": {"name": "payload_locked_documents_rels_parent_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "payload_locked_documents", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_users_fk": {"name": "payload_locked_documents_rels_users_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "users", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_tenants_fk": {"name": "payload_locked_documents_rels_tenants_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "tenants", "columnsFrom": ["tenants_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_batches_fk": {"name": "payload_locked_documents_rels_batches_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "batches", "columnsFrom": ["batches_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_enrollments_fk": {"name": "payload_locked_documents_rels_enrollments_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "enrollments", "columnsFrom": ["enrollments_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_subjects_fk": {"name": "payload_locked_documents_rels_subjects_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "subjects", "columnsFrom": ["subjects_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_modules_fk": {"name": "payload_locked_documents_rels_modules_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "modules", "columnsFrom": ["modules_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_assignments_fk": {"name": "payload_locked_documents_rels_assignments_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "assignments", "columnsFrom": ["assignments_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_submissions_fk": {"name": "payload_locked_documents_rels_submissions_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "submissions", "columnsFrom": ["submissions_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_payload_jobs_fk": {"name": "payload_locked_documents_rels_payload_jobs_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "payload_jobs", "columnsFrom": ["payload_jobs_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_preferences": {"name": "payload_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_preferences_key_idx": {"name": "payload_preferences_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_updated_at_idx": {"name": "payload_preferences_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_created_at_idx": {"name": "payload_preferences_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_preferences_rels": {"name": "payload_preferences_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"payload_preferences_rels_order_idx": {"name": "payload_preferences_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_parent_idx": {"name": "payload_preferences_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_path_idx": {"name": "payload_preferences_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_users_id_idx": {"name": "payload_preferences_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_preferences_rels_parent_fk": {"name": "payload_preferences_rels_parent_fk", "tableFrom": "payload_preferences_rels", "tableTo": "payload_preferences", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_preferences_rels_users_fk": {"name": "payload_preferences_rels_users_fk", "tableFrom": "payload_preferences_rels", "tableTo": "users", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_migrations": {"name": "payload_migrations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "batch": {"name": "batch", "type": "numeric", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_migrations_updated_at_idx": {"name": "payload_migrations_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_migrations_created_at_idx": {"name": "payload_migrations_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.enum_users_roles": {"name": "enum_users_roles", "schema": "public", "values": ["super-admin", "user"]}, "public.enum_users_tenants_roles": {"name": "enum_users_tenants_roles", "schema": "public", "values": ["faculty", "student"]}, "public.enum_assignments_language": {"name": "enum_assignments_language", "schema": "public", "values": ["web", "java", "c"]}, "public.enum_assignments_difficulty": {"name": "enum_assignments_difficulty", "schema": "public", "values": ["easy", "medium", "hard"]}, "public.enum_assignments_status": {"name": "enum_assignments_status", "schema": "public", "values": ["draft", "published"]}, "public.enum__assignments_v_version_language": {"name": "enum__assignments_v_version_language", "schema": "public", "values": ["web", "java", "c"]}, "public.enum__assignments_v_version_difficulty": {"name": "enum__assignments_v_version_difficulty", "schema": "public", "values": ["easy", "medium", "hard"]}, "public.enum__assignments_v_version_status": {"name": "enum__assignments_v_version_status", "schema": "public", "values": ["draft", "published"]}, "public.enum_submissions_tests_result_status": {"name": "enum_submissions_tests_result_status", "schema": "public", "values": ["PASS", "FAIL"]}, "public.enum_submissions_status": {"name": "enum_submissions_status", "schema": "public", "values": ["review", "graded", "resubmit"]}, "public.enum__submissions_v_version_tests_result_status": {"name": "enum__submissions_v_version_tests_result_status", "schema": "public", "values": ["PASS", "FAIL"]}, "public.enum__submissions_v_version_status": {"name": "enum__submissions_v_version_status", "schema": "public", "values": ["review", "graded", "resubmit"]}, "public.enum_payload_jobs_log_task_slug": {"name": "enum_payload_jobs_log_task_slug", "schema": "public", "values": ["inline", "submission-worker"]}, "public.enum_payload_jobs_log_state": {"name": "enum_payload_jobs_log_state", "schema": "public", "values": ["failed", "succeeded"]}, "public.enum_payload_jobs_task_slug": {"name": "enum_payload_jobs_task_slug", "schema": "public", "values": ["inline", "submission-worker"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}