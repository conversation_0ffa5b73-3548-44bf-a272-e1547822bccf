import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   CREATE TYPE "public"."enum_submissions_tests_result_status" AS ENUM('PASS', 'FAIL');
  CREATE TYPE "public"."enum__submissions_v_version_tests_result_status" AS ENUM('PASS', 'FAIL');
  CREATE TABLE "submissions_tests_result" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"input" varchar,
  	"expected" varchar,
  	"actual" varchar,
  	"status" "enum_submissions_tests_result_status"
  );
  
  CREATE TABLE "_submissions_v_version_tests_result" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" serial PRIMARY KEY NOT NULL,
  	"input" varchar,
  	"expected" varchar,
  	"actual" varchar,
  	"status" "enum__submissions_v_version_tests_result_status",
  	"_uuid" varchar
  );
  
  ALTER TABLE "assignments_blocks_action" DISABLE ROW LEVEL SECURITY;
  ALTER TABLE "assignments_blocks_assertion" DISABLE ROW LEVEL SECURITY;
  ALTER TABLE "assignments_test_suites" DISABLE ROW LEVEL SECURITY;
  ALTER TABLE "assignments_java_test_cases" DISABLE ROW LEVEL SECURITY;
  ALTER TABLE "assignments_c_test_cases" DISABLE ROW LEVEL SECURITY;
  ALTER TABLE "_assignments_v_blocks_action" DISABLE ROW LEVEL SECURITY;
  ALTER TABLE "_assignments_v_blocks_assertion" DISABLE ROW LEVEL SECURITY;
  ALTER TABLE "_assignments_v_version_test_suites" DISABLE ROW LEVEL SECURITY;
  ALTER TABLE "_assignments_v_version_java_test_cases" DISABLE ROW LEVEL SECURITY;
  ALTER TABLE "_assignments_v_version_c_test_cases" DISABLE ROW LEVEL SECURITY;
  DROP TABLE "assignments_blocks_action" CASCADE;
  DROP TABLE "assignments_blocks_assertion" CASCADE;
  DROP TABLE "assignments_test_suites" CASCADE;
  DROP TABLE "assignments_java_test_cases" CASCADE;
  DROP TABLE "assignments_c_test_cases" CASCADE;
  DROP TABLE "_assignments_v_blocks_action" CASCADE;
  DROP TABLE "_assignments_v_blocks_assertion" CASCADE;
  DROP TABLE "_assignments_v_version_test_suites" CASCADE;
  DROP TABLE "_assignments_v_version_java_test_cases" CASCADE;
  DROP TABLE "_assignments_v_version_c_test_cases" CASCADE;
  ALTER TABLE "submissions" ALTER COLUMN "score" DROP NOT NULL;
  ALTER TABLE "submissions" ALTER COLUMN "passed_test_cases" DROP NOT NULL;
  ALTER TABLE "submissions" ALTER COLUMN "failed_test_cases" DROP NOT NULL;
  ALTER TABLE "_submissions_v" ALTER COLUMN "version_score" DROP NOT NULL;
  ALTER TABLE "_submissions_v" ALTER COLUMN "version_passed_test_cases" DROP NOT NULL;
  ALTER TABLE "_submissions_v" ALTER COLUMN "version_failed_test_cases" DROP NOT NULL;
  ALTER TABLE "assignments" ADD COLUMN "tests_requirement" varchar;
  ALTER TABLE "_assignments_v" ADD COLUMN "version_tests_requirement" varchar;
  ALTER TABLE "submissions_tests_result" ADD CONSTRAINT "submissions_tests_result_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."submissions"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_submissions_v_version_tests_result" ADD CONSTRAINT "_submissions_v_version_tests_result_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_submissions_v"("id") ON DELETE cascade ON UPDATE no action;
  CREATE INDEX "submissions_tests_result_order_idx" ON "submissions_tests_result" USING btree ("_order");
  CREATE INDEX "submissions_tests_result_parent_id_idx" ON "submissions_tests_result" USING btree ("_parent_id");
  CREATE INDEX "_submissions_v_version_tests_result_order_idx" ON "_submissions_v_version_tests_result" USING btree ("_order");
  CREATE INDEX "_submissions_v_version_tests_result_parent_id_idx" ON "_submissions_v_version_tests_result" USING btree ("_parent_id");
  ALTER TABLE "assignments" DROP COLUMN "requires_command_line_args";
  ALTER TABLE "_assignments_v" DROP COLUMN "version_requires_command_line_args";
  DROP TYPE "public"."enum_assignments_blocks_action_action_type";
  DROP TYPE "public"."enum_assignments_blocks_assertion_assertion_type";
  DROP TYPE "public"."enum_assignments_test_suites_visibility";
  DROP TYPE "public"."enum__assignments_v_blocks_action_action_type";
  DROP TYPE "public"."enum__assignments_v_blocks_assertion_assertion_type";
  DROP TYPE "public"."enum__assignments_v_version_test_suites_visibility";`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   CREATE TYPE "public"."enum_assignments_blocks_action_action_type" AS ENUM('click', 'input', 'hover');
  CREATE TYPE "public"."enum_assignments_blocks_assertion_assertion_type" AS ENUM('textContent', 'exists', 'css', 'notExists', 'hasClass', 'ariaLabel', 'value', 'alert');
  CREATE TYPE "public"."enum_assignments_test_suites_visibility" AS ENUM('visible', 'hidden');
  CREATE TYPE "public"."enum__assignments_v_blocks_action_action_type" AS ENUM('click', 'input', 'hover');
  CREATE TYPE "public"."enum__assignments_v_blocks_assertion_assertion_type" AS ENUM('textContent', 'exists', 'css', 'notExists', 'hasClass', 'ariaLabel', 'value', 'alert');
  CREATE TYPE "public"."enum__assignments_v_version_test_suites_visibility" AS ENUM('visible', 'hidden');
  CREATE TABLE "assignments_blocks_action" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"_path" text NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"action_type" "enum_assignments_blocks_action_action_type",
  	"action_selector" varchar,
  	"action_value" varchar,
  	"block_name" varchar
  );
  
  CREATE TABLE "assignments_blocks_assertion" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"_path" text NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"assertion_type" "enum_assignments_blocks_assertion_assertion_type",
  	"assertion_selector" varchar,
  	"expected_value" varchar,
  	"expected_class" varchar,
  	"css_property" varchar,
  	"expected_css_value" varchar,
  	"expected_alert_text" varchar,
  	"block_name" varchar
  );
  
  CREATE TABLE "assignments_test_suites" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"points" numeric DEFAULT 10,
  	"visibility" "enum_assignments_test_suites_visibility" DEFAULT 'visible'
  );
  
  CREATE TABLE "assignments_java_test_cases" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"title" varchar,
  	"input" varchar,
  	"expected_output" varchar,
  	"tolerance" numeric,
  	"is_hidden" boolean DEFAULT false
  );
  
  CREATE TABLE "assignments_c_test_cases" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"title" varchar,
  	"input" varchar,
  	"expected_output" varchar,
  	"tolerance" numeric,
  	"is_hidden" boolean DEFAULT false
  );
  
  CREATE TABLE "_assignments_v_blocks_action" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"_path" text NOT NULL,
  	"id" serial PRIMARY KEY NOT NULL,
  	"action_type" "enum__assignments_v_blocks_action_action_type",
  	"action_selector" varchar,
  	"action_value" varchar,
  	"_uuid" varchar,
  	"block_name" varchar
  );
  
  CREATE TABLE "_assignments_v_blocks_assertion" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"_path" text NOT NULL,
  	"id" serial PRIMARY KEY NOT NULL,
  	"assertion_type" "enum__assignments_v_blocks_assertion_assertion_type",
  	"assertion_selector" varchar,
  	"expected_value" varchar,
  	"expected_class" varchar,
  	"css_property" varchar,
  	"expected_css_value" varchar,
  	"expected_alert_text" varchar,
  	"_uuid" varchar,
  	"block_name" varchar
  );
  
  CREATE TABLE "_assignments_v_version_test_suites" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" serial PRIMARY KEY NOT NULL,
  	"points" numeric DEFAULT 10,
  	"visibility" "enum__assignments_v_version_test_suites_visibility" DEFAULT 'visible',
  	"_uuid" varchar
  );
  
  CREATE TABLE "_assignments_v_version_java_test_cases" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" serial PRIMARY KEY NOT NULL,
  	"title" varchar,
  	"input" varchar,
  	"expected_output" varchar,
  	"tolerance" numeric,
  	"is_hidden" boolean DEFAULT false,
  	"_uuid" varchar
  );
  
  CREATE TABLE "_assignments_v_version_c_test_cases" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" serial PRIMARY KEY NOT NULL,
  	"title" varchar,
  	"input" varchar,
  	"expected_output" varchar,
  	"tolerance" numeric,
  	"is_hidden" boolean DEFAULT false,
  	"_uuid" varchar
  );
  
  ALTER TABLE "submissions_tests_result" DISABLE ROW LEVEL SECURITY;
  ALTER TABLE "_submissions_v_version_tests_result" DISABLE ROW LEVEL SECURITY;
  DROP TABLE "submissions_tests_result" CASCADE;
  DROP TABLE "_submissions_v_version_tests_result" CASCADE;
  ALTER TABLE "submissions" ALTER COLUMN "score" SET NOT NULL;
  ALTER TABLE "submissions" ALTER COLUMN "passed_test_cases" SET NOT NULL;
  ALTER TABLE "submissions" ALTER COLUMN "failed_test_cases" SET NOT NULL;
  ALTER TABLE "_submissions_v" ALTER COLUMN "version_score" SET NOT NULL;
  ALTER TABLE "_submissions_v" ALTER COLUMN "version_passed_test_cases" SET NOT NULL;
  ALTER TABLE "_submissions_v" ALTER COLUMN "version_failed_test_cases" SET NOT NULL;
  ALTER TABLE "assignments" ADD COLUMN "requires_command_line_args" boolean;
  ALTER TABLE "_assignments_v" ADD COLUMN "version_requires_command_line_args" boolean;
  ALTER TABLE "assignments_blocks_action" ADD CONSTRAINT "assignments_blocks_action_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."assignments"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "assignments_blocks_assertion" ADD CONSTRAINT "assignments_blocks_assertion_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."assignments"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "assignments_test_suites" ADD CONSTRAINT "assignments_test_suites_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."assignments"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "assignments_java_test_cases" ADD CONSTRAINT "assignments_java_test_cases_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."assignments"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "assignments_c_test_cases" ADD CONSTRAINT "assignments_c_test_cases_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."assignments"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_assignments_v_blocks_action" ADD CONSTRAINT "_assignments_v_blocks_action_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_assignments_v"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_assignments_v_blocks_assertion" ADD CONSTRAINT "_assignments_v_blocks_assertion_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_assignments_v"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_assignments_v_version_test_suites" ADD CONSTRAINT "_assignments_v_version_test_suites_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_assignments_v"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_assignments_v_version_java_test_cases" ADD CONSTRAINT "_assignments_v_version_java_test_cases_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_assignments_v"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_assignments_v_version_c_test_cases" ADD CONSTRAINT "_assignments_v_version_c_test_cases_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_assignments_v"("id") ON DELETE cascade ON UPDATE no action;
  CREATE INDEX "assignments_blocks_action_order_idx" ON "assignments_blocks_action" USING btree ("_order");
  CREATE INDEX "assignments_blocks_action_parent_id_idx" ON "assignments_blocks_action" USING btree ("_parent_id");
  CREATE INDEX "assignments_blocks_action_path_idx" ON "assignments_blocks_action" USING btree ("_path");
  CREATE INDEX "assignments_blocks_assertion_order_idx" ON "assignments_blocks_assertion" USING btree ("_order");
  CREATE INDEX "assignments_blocks_assertion_parent_id_idx" ON "assignments_blocks_assertion" USING btree ("_parent_id");
  CREATE INDEX "assignments_blocks_assertion_path_idx" ON "assignments_blocks_assertion" USING btree ("_path");
  CREATE INDEX "assignments_test_suites_order_idx" ON "assignments_test_suites" USING btree ("_order");
  CREATE INDEX "assignments_test_suites_parent_id_idx" ON "assignments_test_suites" USING btree ("_parent_id");
  CREATE INDEX "assignments_java_test_cases_order_idx" ON "assignments_java_test_cases" USING btree ("_order");
  CREATE INDEX "assignments_java_test_cases_parent_id_idx" ON "assignments_java_test_cases" USING btree ("_parent_id");
  CREATE INDEX "assignments_c_test_cases_order_idx" ON "assignments_c_test_cases" USING btree ("_order");
  CREATE INDEX "assignments_c_test_cases_parent_id_idx" ON "assignments_c_test_cases" USING btree ("_parent_id");
  CREATE INDEX "_assignments_v_blocks_action_order_idx" ON "_assignments_v_blocks_action" USING btree ("_order");
  CREATE INDEX "_assignments_v_blocks_action_parent_id_idx" ON "_assignments_v_blocks_action" USING btree ("_parent_id");
  CREATE INDEX "_assignments_v_blocks_action_path_idx" ON "_assignments_v_blocks_action" USING btree ("_path");
  CREATE INDEX "_assignments_v_blocks_assertion_order_idx" ON "_assignments_v_blocks_assertion" USING btree ("_order");
  CREATE INDEX "_assignments_v_blocks_assertion_parent_id_idx" ON "_assignments_v_blocks_assertion" USING btree ("_parent_id");
  CREATE INDEX "_assignments_v_blocks_assertion_path_idx" ON "_assignments_v_blocks_assertion" USING btree ("_path");
  CREATE INDEX "_assignments_v_version_test_suites_order_idx" ON "_assignments_v_version_test_suites" USING btree ("_order");
  CREATE INDEX "_assignments_v_version_test_suites_parent_id_idx" ON "_assignments_v_version_test_suites" USING btree ("_parent_id");
  CREATE INDEX "_assignments_v_version_java_test_cases_order_idx" ON "_assignments_v_version_java_test_cases" USING btree ("_order");
  CREATE INDEX "_assignments_v_version_java_test_cases_parent_id_idx" ON "_assignments_v_version_java_test_cases" USING btree ("_parent_id");
  CREATE INDEX "_assignments_v_version_c_test_cases_order_idx" ON "_assignments_v_version_c_test_cases" USING btree ("_order");
  CREATE INDEX "_assignments_v_version_c_test_cases_parent_id_idx" ON "_assignments_v_version_c_test_cases" USING btree ("_parent_id");
  ALTER TABLE "assignments" DROP COLUMN "tests_requirement";
  ALTER TABLE "_assignments_v" DROP COLUMN "version_tests_requirement";
  DROP TYPE "public"."enum_submissions_tests_result_status";
  DROP TYPE "public"."enum__submissions_v_version_tests_result_status";`)
}
