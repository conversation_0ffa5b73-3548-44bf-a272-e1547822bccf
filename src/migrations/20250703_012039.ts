import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "submissions" ADD COLUMN "summary" varchar;
  ALTER TABLE "_submissions_v" ADD COLUMN "version_summary" varchar;
  ALTER TABLE "submissions" DROP COLUMN "passed_test_cases";
  ALTER TABLE "submissions" DROP COLUMN "failed_test_cases";
  ALTER TABLE "_submissions_v" DROP COLUMN "version_passed_test_cases";
  ALTER TABLE "_submissions_v" DROP COLUMN "version_failed_test_cases";`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "submissions" ADD COLUMN "passed_test_cases" numeric;
  ALTER TABLE "submissions" ADD COLUMN "failed_test_cases" numeric;
  ALTER TABLE "_submissions_v" ADD COLUMN "version_passed_test_cases" numeric;
  ALTER TABLE "_submissions_v" ADD COLUMN "version_failed_test_cases" numeric;
  ALTER TABLE "submissions" DROP COLUMN "summary";
  ALTER TABLE "_submissions_v" DROP COLUMN "version_summary";`)
}
