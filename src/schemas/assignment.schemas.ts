import { z } from 'zod';

export const SubmissionDataSchema = z.object({
  // passedTestCases: z.number().min(0).default(0),
  // failedTestCases: z.number().min(0).default(0),
  html: z.string().optional(),
  css: z.string().optional(),
  js: z.string().optional(),
  c: z.string().optional(),
  java: z.string().optional(),
});

export const GenerationOptions = z.object({
  includeInstructions: z.boolean().default(true),
  includeTests: z.boolean().default(true),
  includeSolution: z.boolean().default(true),
  includeStarterCode: z.boolean().default(true),
  includeHints: z.boolean().default(false),
});

export const GenerateAssignmentSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  difficulty: z.enum(['easy', 'medium', 'hard']),
  points: z.number().min(1).max(1000),
  subjectId: z.coerce.number().min(1),
  language: z.enum(['java', 'c']),
  additionalRequirements: z.string().default(''),
  generationOptions: GenerationOptions,
});
