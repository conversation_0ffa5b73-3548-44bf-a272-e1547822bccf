import { Assignment, Tenant } from '@/payload-types';

// const WEB_DEV_SUBJECT_ID = 1
// const OOPJ_SUBJECT_ID = 2
// const C_SUBJECT_ID = 3

// Helper function to generate ISO date strings for consistency
const daysFromNow = (days: number): string => {
  return new Date(Date.now() + days * 24 * 60 * 60 * 1000).toISOString();
};

// Function signature matching the requested format
export const getAssignments = ({ tenant }: { tenant: Tenant }): Assignment[] => {
  // Ensure tenant ID is used where necessary (if it's not just a placeholder type)
  const tenantId = typeof tenant === 'object' && tenant !== null ? tenant.id : tenant;

  // --- Helper for code blocks: Removes leading/trailing newlines and common indent ---
  // (Assumes indent is spaces, finds the minimum indent of non-empty lines)
  const cleanCode = (code: string): string => {
    if (!code) return '';
    const lines = code.split('\n');
    // Remove leading/trailing blank lines
    while (lines.length > 0 && lines[0].trim() === '') lines.shift();
    while (lines.length > 0 && lines[lines.length - 1].trim() === '') lines.pop();
    if (lines.length === 0) return '';

    // Find minimum indent
    let minIndent = Infinity;
    lines.forEach((line) => {
      const match = line.match(/^\s*/);
      if (line.trim() !== '' && match) {
        minIndent = Math.min(minIndent, match[0].length);
      }
    });

    // Remove common indent
    if (minIndent > 0 && minIndent !== Infinity) {
      return lines.map((line) => line.substring(minIndent)).join('\n');
    }
    return lines.join('\n');
  };

  return [
    // ==========================================================
    // --- Assignment 1: HTML Fundamentals ---
    // ==========================================================
    {
      id: 101,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      language: 'web',

      tenant: tenantId,
      _status: 'published',
      title: 'Basic Structure and Semantics',
      difficulty: 'easy',
      description: 'Create a basic HTML page using semantic elements and fundamental tags.',
      points: 20,
      instructions: `Create a simple HTML page with the following:

-   A \`header\` containing a title (\`h1\`) and a navigation (\`nav\`) with two links.
-   A \`main\` section with a heading (\`h2\`) and a paragraph of text.
-   A \`footer\` with copyright information.

Ensure you use semantic HTML5 elements (\`header\`, \`nav\`, \`main\`, \`footer\`) appropriately.`,
      dueDate: daysFromNow(7),
      hints: [
        {
          question: 'What are the main semantic elements in HTML5?',
          answer:
            'The main semantic elements are `<header>`, `<nav>`, `<main>`, `<article>`, `<aside>`, and `<footer>`.',
        },
        {
          question: 'How do you create a link in HTML?',
          answer: 'Use the `<a>` tag with the `href` attribute.',
        },
      ],
      resources: [
        {
          title: 'MDN: HTML elements reference',
          url: 'https://developer.mozilla.org/en-US/docs/Web/HTML/Element',
        },
      ],
      starterCode: {
        html: cleanCode(`
            <!-- Your HTML code here -->
        `),
        css: '',
        js: '',
      },
      solutionCode: {
        html: cleanCode(`
            <header>
                <h1>My Website</h1>
                <nav>
                    <a href="#">Home</a>
                    <a href="#">About</a>
                </nav>
            </header>
            <main>
                <h2>Welcome</h2>
                <p>This is a sample paragraph.</p>
            </main>
            <footer>
                <p>© 2025 My Website</p>
            </footer>
        `),
        css: '',
        js: '',
      },
      // testSuites: [
      //   {
      //     visibility: 'visible',
      //     points: 20,
      //     tests: [
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'exists',
      //         assertionSelector: 'header',
      //         blockName: 'Header element exists',
      //       },
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'exists',
      //         assertionSelector: 'nav',
      //         blockName: 'Nav element exists',
      //       },
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'exists',
      //         assertionSelector: 'main',
      //         blockName: 'Main element exists',
      //       },
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'exists',
      //         assertionSelector: 'footer',
      //         blockName: 'Footer element exists',
      //       },
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'exists',
      //         assertionSelector: 'h1',
      //         blockName: 'h1 exists',
      //       },
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'exists',
      //         assertionSelector: 'nav > a',
      //         blockName: 'Navigation contains at least one link',
      //       },
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'exists',
      //         assertionSelector: 'main > h2',
      //         blockName: 'Main section contains a heading (h2)',
      //       },
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'exists',
      //         assertionSelector: 'footer > p',
      //         blockName: 'Footer contains a paragraph',
      //       },
      //     ],
      //   },
      // ],
    },

    // ==========================================================
    // --- Assignment 2: CSS Styling ---
    // ==========================================================
    {
      id: 102,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      tenant: tenantId,

      _status: 'published',
      language: 'web',
      title: 'Selectors and Basic Styling',
      difficulty: 'medium',
      description: 'Apply CSS styles to HTML elements using different selectors.',
      points: 30,
      instructions: `Given the following HTML structure, apply CSS styles to achieve the following:

-   Set the background color of the \`body\` to \`#f0f0f0\`.
-   Style the \`h1\` element to have a \`color\` of \`#333\` and a \`text-align\` of \`center\`.
-   Style all paragraphs (\`p\`) to have a \`font-size\` of \`16px\` and a \`line-height\` of \`1.5\`.
-   Style the element with the class \`highlight\` to have a \`background-color\` of \`yellow\`.`,
      dueDate: daysFromNow(7),
      hints: [
        {
          question: 'How do you select an element by tag name in CSS?',
          answer: 'Use the tag name directly (e.g., `body`).',
        },
        {
          question: 'How do you select an element by class in CSS?',
          answer: 'Use a period followed by the class name (e.g., `.highlight`).',
        },
      ],
      resources: [
        {
          title: 'MDN: CSS Selectors',
          url: 'https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Selectors',
        },
      ],
      starterCode: {
        html: cleanCode(`
            <h1>Welcome</h1>
            <p>This is a paragraph of text.</p>
            <p class="highlight">This paragraph should be highlighted.</p>
        `),
        css: cleanCode(`
            /* Your CSS code here */
        `),
        js: '',
      },
      solutionCode: {
        html: cleanCode(`
            <h1>Welcome</h1>
            <p>This is a paragraph of text.</p>
            <p class="highlight">This paragraph should be highlighted.</p>
        `),
        css: cleanCode(`
            body {
                background-color: #f0f0f0;
            }

            h1 {
                color: #333;
                text-align: center;
            }

            p {
                font-size: 16px;
                line-height: 1.5;
            }

            .highlight {
                background-color: yellow;
            }
        `),
        js: '',
      },
      // testSuites: [
      //   {
      //     visibility: 'visible',
      //     points: 30,
      //     tests: [
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'css',
      //         assertionSelector: 'body',
      //         cssProperty: 'background-color',
      //         expectedCssValue: 'rgb(240, 240, 240)',
      //         blockName: 'Body background color is #f0f0f0',
      //       },
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'css',
      //         assertionSelector: 'h1',
      //         cssProperty: 'color',
      //         expectedCssValue: 'rgb(51, 51, 51)',
      //         blockName: 'H1 color is #333',
      //       },
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'css',
      //         assertionSelector: 'h1',
      //         cssProperty: 'text-align',
      //         expectedCssValue: 'center',
      //         blockName: 'H1 text is centered',
      //       },
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'css',
      //         assertionSelector: 'p',
      //         cssProperty: 'font-size',
      //         expectedCssValue: '16px',
      //         blockName: 'P font size is 16px',
      //       },
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'css',
      //         assertionSelector: '.highlight',
      //         cssProperty: 'background-color',
      //         expectedCssValue: 'yellow',
      //         blockName: 'Highlight background is yellow',
      //       },
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'css',
      //         assertionSelector: 'body',
      //         cssProperty: 'font-family',
      //         expectedCssValue: 'sans-serif',
      //         blockName: 'body font family defaults to sans-serif',
      //       },
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'css',
      //         assertionSelector: 'h1',
      //         cssProperty: 'margin-top',
      //         expectedCssValue: '0px',
      //         blockName: 'h1 margin defaults to 0',
      //       },
      //     ],
      //   },
      // ],
    },

    // ==========================================================
    // --- Assignment 3: Simple Counter App ---
    // ==========================================================
    {
      id: 103,
      language: 'web',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      _status: 'published',

      tenant: tenantId,
      title: 'Simple Counter Application',
      difficulty: 'hard',
      description: 'Create a simple counter application with increment and decrement buttons.',
      points: 50,
      instructions: `Create a counter application with the following functionality:

-   **HTML:**
    -   A display area (e.g., a \`span\` or \`div\`) to show the current count, initialized to 0. Give it an ID (\`counter\`).
    -   An increment button with the text "+". Give it an ID (\`incrementBtn\`).
    -   A decrement button with the text "-". Give it an ID (\`decrementBtn\`).
-   **CSS:** Style the counter, buttons, and container for basic readability (centering, padding, etc.).
-   **JavaScript:**
    -   Get references to the display area and the two buttons.
    -   Add click event listeners to the increment and decrement buttons.
    -   When the increment button is clicked, increase the counter value by 1 and update the display.
    -   When the decrement button is clicked, decrease the counter value by 1 and update the display. Ensure the counter doesn't go below 0.`,
      dueDate: daysFromNow(7),
      hints: [
        {
          question: 'How do you get an element by its ID?',
          answer: 'Use `document.getElementById("yourId")`.',
        },
        {
          question: 'How do you attach a click event listener?',
          answer: 'Use `element.addEventListener("click", function() { ... });`.',
        },
        {
          question: 'How do you update the text content of an element?',
          answer: 'Use `element.textContent = newValue`.',
        },
        {
          question: 'How do you convert string content to integer?',
          answer: 'use `parseInt(content)`',
        },
      ],
      resources: [
        {
          title: 'MDN: getElementById',
          url: 'https://developer.mozilla.org/en-US/docs/Web/API/Document/getElementById',
        },
        {
          title: 'MDN: addEventListener',
          url: 'https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener',
        },
      ],
      starterCode: {
        html: cleanCode(`
            <div id="container">
                <button id="decrementBtn">-</button>
                <span id="counter">0</span>
                <button id="incrementBtn">+</button>
            </div>
        `),
        css: cleanCode(`
            #container {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10px;
                padding: 20px;
                text-align: center;
            }

            button {
                padding: 5px 10px;
                font-size: 16px;
                cursor: pointer;
            }
        `),
        js: cleanCode(`
            // Your JavaScript code here
        `),
      },
      solutionCode: {
        html: cleanCode(`
            <div id="container">
                <button id="decrementBtn">-</button>
                <span id="counter">0</span>
                <button id="incrementBtn">+</button>
            </div>
        `),
        css: cleanCode(`
            #container {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10px;
                padding: 20px;
                text-align: center;
            }

            button {
                padding: 5px 10px;
                font-size: 16px;
                cursor: pointer;
            }
        `),
        js: cleanCode(`
            const counterDisplay = document.getElementById('counter');
            const incrementBtn = document.getElementById('incrementBtn');
            const decrementBtn = document.getElementById('decrementBtn');

            let counterValue = 0;

            incrementBtn.addEventListener('click', () => {
                counterValue++;
                counterDisplay.textContent = counterValue;
            });

            decrementBtn.addEventListener('click', () => {
                if (counterValue > 0) {
                    counterValue--;
                    counterDisplay.textContent = counterValue;
                }
            });
        `),
      },
      // testSuites: [
      //   {
      //     visibility: 'visible',
      //     points: 20,
      //     tests: [
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'exists',
      //         assertionSelector: '#counter',
      //         blockName: 'Counter display element exists',
      //       },
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'exists',
      //         assertionSelector: '#incrementBtn',
      //         blockName: 'Increment button exists',
      //       },
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'exists',
      //         assertionSelector: '#decrementBtn',
      //         blockName: 'Decrement button exists',
      //       },
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'textContent',
      //         assertionSelector: '#counter',
      //         expectedValue: '0',
      //         blockName: 'Initial counter value is 0',
      //       },
      //     ],
      //   },
      //   {
      //     visibility: 'visible',
      //     points: 15,
      //     tests: [
      //       {
      //         blockType: 'action',
      //         actionType: 'click',
      //         actionSelector: '#incrementBtn',
      //         blockName: 'Click increment button',
      //       },
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'textContent',
      //         assertionSelector: '#counter',
      //         expectedValue: '1',
      //         blockName: 'Counter value is 1 after one increment',
      //       },
      //       {
      //         blockType: 'action',
      //         actionType: 'click',
      //         actionSelector: '#incrementBtn',
      //         blockName: 'Click increment button again',
      //       },
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'textContent',
      //         assertionSelector: '#counter',
      //         expectedValue: '2',
      //         blockName: 'Counter value is 2 after two increments',
      //       },
      //     ],
      //   },
      //   {
      //     visibility: 'visible',
      //     points: 15,
      //     tests: [
      //       {
      //         blockType: 'action',
      //         actionType: 'click',
      //         actionSelector: '#incrementBtn',
      //         blockName: 'Click increment to 1',
      //       },
      //       {
      //         blockType: 'action',
      //         actionType: 'click',
      //         actionSelector: '#decrementBtn',
      //         blockName: 'Click decrement button once',
      //       },
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'textContent',
      //         assertionSelector: '#counter',
      //         expectedValue: '0',
      //         blockName: 'Counter value is 0 after one decrement from 1',
      //       },
      //       {
      //         blockType: 'action',
      //         actionType: 'click',
      //         actionSelector: '#decrementBtn',
      //         blockName: 'Click decrement button when at 0',
      //       },
      //       {
      //         blockType: 'assertion',
      //         assertionType: 'textContent',
      //         assertionSelector: '#counter',
      //         expectedValue: '0',
      //         blockName: 'Counter value remains 0 when decrementing from 0',
      //       },
      //     ],
      //   },
      // ],
    },

    // OOPJ Assignments
    // Lab 1
    {
      id: 201,
      createdAt: '2025-05-19T01:35:15.377Z',
      updatedAt: '2025-05-19T01:35:15.377Z',
      tenant: tenantId,

      _status: 'published',
      language: 'java',
      difficulty: 'easy',
      points: 10,
      dueDate: daysFromNow(7),
      title: "Hello World - Print 'Welcome to Java'",
      description:
        "Write a simple Java program to display the message 'Welcome to Java' in the console.",
      instructions: `Welcome to your first Java programming assignment! This exercise will introduce you to the basic structure of a Java program and how to display output to the console.

**What is a Java Program?**
A Java program is a set of instructions written in the Java programming language. Every Java program must be contained within a class, and execution begins with a special method called \`main\`.

**Objective:**
Create a Java program that displays the exact message "Welcome to Java" in the console.

**Understanding the Structure:**

1. **Class Declaration**: Every Java program starts with a class. Think of a class as a container for your code.
   \`\`\`java
   public class Main {
       // Your code goes here
   }
   \`\`\`

2. **Main Method**: This is the entry point of your program - where execution begins.
   \`\`\`java
   public static void main(String[] args) {
       // Your instructions go here
   }
   \`\`\`

3. **Printing to Console**: To display text, we use \`System.out.println()\`

**Step-by-Step Instructions:**

1. **Create the class**: Start by declaring a public class named \`Main\`

2. **Add the main method**: Inside the \`Main\` class, create the main method with the exact signature: \`public static void main(String[] args)\`

3. **Print the message**: Inside the main method, use \`System.out.println("Welcome to Java");\` to display the message

4. **Verify exact output**: The program must print exactly "Welcome to Java" (without quotes)

**Important Notes:**
- Java is case-sensitive: \`System\` must be capitalized
- Every statement must end with a semicolon (;)
- Text to be printed must be enclosed in double quotes (" ")
- The message must match exactly: "Welcome to Java"

**What Each Part Means:**
- \`public\`: This keyword makes the class accessible from anywhere
- \`static\`: This means the method belongs to the class itself, not to instances of the class
- \`void\`: This means the method doesn't return any value
- \`String[] args\`: This allows the program to accept command-line arguments (we won't use this in this assignment)`,
      hints: [
        {
          question: 'What is the basic structure of a Java program?',
          answer:
            'Every Java program needs a class and a main method: `public class Main { public static void main(String[] args) { // code here } }`',
        },
        {
          question: 'How do you print text to the console in Java?',
          answer:
            'Use `System.out.println("text");` to display text. The text must be enclosed in double quotes.',
        },
        {
          question: 'Why do I need semicolons?',
          answer:
            'In Java, semicolons (;) mark the end of a statement. Every complete instruction must end with one.',
        },
        {
          question: "What if my program doesn't compile?",
          answer:
            'Check for common issues: missing semicolons, incorrect capitalization (System vs system), or missing quotes around text.',
        },
      ],
      resources: [
        {
          title: 'Oracle Java Tutorial - Getting Started',
          url: 'https://docs.oracle.com/javase/tutorial/getStarted/',
        },
        {
          title: 'Java Hello World Program',
          url: 'https://www.programiz.com/java-programming/hello-world',
        },
        {
          title: 'Understanding Java Program Structure',
          url: 'https://www.w3schools.com/java/java_syntax.asp',
        },
      ],
      starterCode: {
        java: `public class Main {
    public static void main(String[] args) {
        // Write your code here to print "Welcome to Java"
        // Use System.out.println() to display the message

    }
}`,
      },
      solutionCode: {
        java: `public class Main {
    public static void main(String[] args) {
        System.out.println("Welcome to Java");
    }
}`,
      },
      solutionNotes:
        'This is the most basic Java program structure. It demonstrates the essential components: class declaration, main method, and console output.',
      // javaTestCases: [
      //   {
      //     title: 'Verify Exact Output',
      //     expectedOutput: 'Welcome to Java',
      //     isHidden: false,
      //   },
      // ],
    },
    {
      id: 202,
      createdAt: '2025-05-19T01:35:15.377Z',
      updatedAt: '2025-05-19T01:35:15.377Z',
      tenant: tenantId,
      _status: 'published',
      language: 'java',
      difficulty: 'easy',
      points: 15,
      dueDate: daysFromNow(7),
      title: 'Print Your Name and Age',
      description: 'Write a Java program to print your name and age on separate lines.',
      instructions: `In this assignment, you will learn how to display multiple lines of output and work with different data types in Java.

**Objective:**
Create a program that displays your name and age on separate lines using two different print statements.

**Key Concepts:**
1. **Multiple Output Statements**: You can use multiple \`System.out.println()\` statements to print different lines
2. **Text vs Numbers**: Text must be in quotes, but numbers can be written directly
3. **Line Separation**: Each \`println\` statement creates a new line

**Step-by-Step Instructions:**

1. **Set up the basic structure**: Create a \`Main\` class with the \`main\` method (just like in the previous assignment)

2. **Print your name**:
   - Use \`System.out.println("Your Name Here");\` to display your name
   - Replace "Your Name Here" with your actual name
   - Keep the quotes around your name since it's text

3. **Print your age**:
   - Use another \`System.out.println()\` statement for your age
   - You can print your age as a number (without quotes) or as text (with quotes)
   - Example: \`System.out.println(20);\` or \`System.out.println("20");\`

**Understanding the Difference:**
- **Text (String)**: Must be enclosed in double quotes. Example: \`"John Doe"\`
- **Numbers**: Can be written directly without quotes. Example: \`20\`
- **New Lines**: Each \`println\` statement automatically moves to the next line

**Example Output:**
\`\`\`
John Doe
20
\`\`\`

**Important Notes:**
- Use separate \`System.out.println()\` statements for the name and age
- The name should be on the first line, age on the second line
- Make sure both statements are inside the main method
- Don't forget semicolons after each statement

**What You're Learning:**
- How to use multiple output statements
- The difference between printing text and numbers
- Understanding that each \`println\` creates a new line automatically`,
      hints: [
        {
          question: 'How do I print on multiple lines?',
          answer:
            'Use multiple `System.out.println()` statements. Each `println` automatically moves to the next line.',
        },
        {
          question: 'Do I need quotes around my age?',
          answer:
            'Numbers can be printed with or without quotes. `System.out.println(20);` and `System.out.println("20");` both work.',
        },
        {
          question: 'What if I want to print on the same line?',
          answer:
            "Use `System.out.print()` instead of `System.out.println()` if you don't want a new line, but this assignment requires separate lines.",
        },
        {
          question: 'Can I use variables for my name and age?',
          answer:
            "For this assignment, you can directly print the values. We'll learn about variables in later assignments.",
        },
      ],
      resources: [
        {
          title: 'Java Output Methods',
          url: 'https://www.w3schools.com/java/java_output.asp',
        },
        {
          title: 'Understanding Strings vs Numbers',
          url: 'https://www.programiz.com/java-programming/strings',
        },
      ],
      starterCode: {
        java: `public class Main {
    public static void main(String[] args) {
        // Print your name on the first line
        // Use: System.out.println("Your Name");

        // Print your age on the second line
        // Use: System.out.println(Your Age);

    }
}`,
      },
      solutionCode: {
        java: `public class Main {
    public static void main(String[] args) {
        System.out.println("John Doe"); // Replace with your name
        System.out.println(20);         // Replace with your age
    }
}`,
      },
      solutionNotes:
        'This program demonstrates multiple output statements and shows how to print both text (strings) and numbers.',
      // javaTestCases: [
      //   {
      //     title: 'Verify Two-Line Output',
      //     expectedOutput: 'John Doe\n20',
      //     isHidden: false,
      //   },
      // ],
    },
    {
      id: 203,
      createdAt: '2025-05-19T01:35:15.377Z',
      updatedAt: '2025-05-19T01:35:15.377Z',
      tenant: tenantId,
      _status: 'published',
      language: 'java',
      difficulty: 'easy',
      points: 15,
      dueDate: daysFromNow(7),
      title: 'Simple Addition',
      description: 'Write a Java program that adds two numbers and prints the result.',
      instructions: `In this assignment, you will learn about variables, data types, and arithmetic operations in Java.

**Objective:**
Create a program that declares two integer variables, assigns them values, adds them together, and prints the sum.

**New Concepts:**
1. **Variables**: Named containers that store values
2. **Data Types**: Different kinds of data (int for integers, double for decimals, etc.)
3. **Arithmetic Operations**: Mathematical calculations using operators like +, -, *, /

**Understanding Variables:**
- A variable is like a labeled box that holds a value
- In Java, you must declare the type of data a variable will hold
- For whole numbers (integers), we use the \`int\` data type

**Step-by-Step Instructions:**

1. **Set up the program structure**: Create the \`Main\` class and \`main\` method

2. **Declare and initialize variables**:
   - Create first variable: \`int num1 = 5;\`
   - Create second variable: \`int num2 = 10;\`
   - You can use any values you want, but the example uses 5 and 10

3. **Perform the calculation**:
   - Create a third variable to store the result: \`int sum = num1 + num2;\`
   - The \`+\` operator adds the two numbers together

4. **Display the result**:
   - Use \`System.out.println(sum);\` to print the calculated sum
   - Note: We're printing the variable name (sum), not text, so no quotes needed

**Detailed Breakdown:**

\`\`\`java
int num1 = 5;     // Declares an integer variable named num1 and assigns it the value 5
int num2 = 10;    // Declares an integer variable named num2 and assigns it the value 10
int sum = num1 + num2;  // Declares sum and assigns it the result of num1 + num2
System.out.println(sum); // Prints the value stored in the sum variable
\`\`\`

**Important Notes:**
- Variable names should be meaningful (like \`num1\`, \`num2\`, \`sum\`)
- Each variable declaration must specify the data type (\`int\` for integers)
- The \`=\` operator assigns a value to a variable
- Arithmetic operations follow standard mathematical rules

**Expected Output:**
If you use num1 = 5 and num2 = 10, the output should be:
\`\`\`
15
\`\`\`

**What You're Learning:**
- How to declare and initialize variables
- Basic arithmetic operations in Java
- How variables store and manipulate data
- The difference between printing variables and printing text`,
      hints: [
        {
          question: 'How do you declare an integer variable in Java?',
          answer: 'Use the syntax: `int variableName = value;` For example: `int num1 = 5;`',
        },
        {
          question: 'How do you add two numbers in Java?',
          answer: 'Use the + operator between the numbers or variables: `int sum = num1 + num2;`',
        },
        {
          question: "Why don't I need quotes around the variable name when printing?",
          answer:
            'Quotes are for text (strings). When printing a variable, you want to print its value, not its name as text.',
        },
        {
          question: 'Can I use different numbers?',
          answer:
            'Yes! You can use any integers you want. The test case expects 5 + 10 = 15, so use those values to pass the test.',
        },
      ],
      resources: [
        {
          title: 'Java Variables and Data Types',
          url: 'https://www.w3schools.com/java/java_variables.asp',
        },
        {
          title: 'Java Arithmetic Operators',
          url: 'https://www.w3schools.com/java/java_operators.asp',
        },
        {
          title: 'Understanding Integer Data Type',
          url: 'https://www.programiz.com/java-programming/variables-literals',
        },
      ],
      starterCode: {
        java: `public class Main {
    public static void main(String[] args) {
        // Declare first integer variable and assign it a value
        // Example: int num1 = 5;

        // Declare second integer variable and assign it a value
        // Example: int num2 = 10;

        // Calculate the sum of the two numbers
        // Example: int sum = num1 + num2;

        // Print the sum
        // Example: System.out.println(sum);

    }
}`,
      },
      solutionCode: {
        java: `public class Main {
    public static void main(String[] args) {
        int num1 = 5;
        int num2 = 10;
        int sum = num1 + num2;
        System.out.println(sum);
    }
}`,
      },
      solutionNotes:
        'This program introduces variables, data types, and basic arithmetic operations - fundamental concepts in programming.',
      // javaTestCases: [
      //   {
      //     title: 'Verify Addition Result',
      //     expectedOutput: '15',
      //     isHidden: false,
      //   },
      // ],
    },
    {
      id: 204,
      createdAt: '2025-05-19T01:35:15.377Z',
      updatedAt: '2025-05-19T01:35:15.377Z',
      tenant: tenantId,
      _status: 'published',
      language: 'java',
      difficulty: 'medium',
      points: 30,
      dueDate: daysFromNow(7),
      title: 'Interactive Addition',
      description:
        'Write a Java program that takes two numbers as input from the user, adds them, and prints the result.',
      instructions: `In this assignment, you will learn how to make your programs interactive by reading input from the user using the Scanner class.

**Objective:**
Create a program that prompts the user to enter two numbers, reads those numbers, adds them together, and displays the sum.

**New Concepts:**
1. **User Input**: Reading data that the user types
2. **Scanner Class**: A Java class specifically designed for reading input
3. **Interactive Programs**: Programs that respond to user input
4. **Import Statements**: Bringing external classes into your program

**Understanding the Scanner Class:**
- Scanner is like a tool that can read different types of input
- It can read integers, floating-point numbers, strings, etc.
- Before using Scanner, you must import it and create a Scanner object

**Step-by-Step Instructions:**

1. **Import the Scanner class**:
   - Add \`import java.util.Scanner;\` at the very top of your file (before the class declaration)
   - This tells Java you want to use the Scanner class

2. **Set up the program structure**: Create your \`Main\` class and \`main\` method

3. **Create a Scanner object**:
   - Inside the main method: \`Scanner scanner = new Scanner(System.in);\`
   - This creates a Scanner that reads from the keyboard (System.in)

4. **Prompt and read the first number**:
   - Display a prompt: \`System.out.print("Enter the first number: ");\`
   - Read the input: \`int num1 = scanner.nextInt();\`
   - Note: \`print\` doesn't add a new line, so the user types on the same line

5. **Prompt and read the second number**:
   - Display another prompt: \`System.out.print("Enter the second number: ");\`
   - Read the second input: \`int num2 = scanner.nextInt();\`

6. **Calculate and display the result**:
   - Add the numbers: \`int sum = num1 + num2;\`
   - Print the result: \`System.out.println(sum);\`

7. **Clean up resources**:
   - Close the scanner: \`scanner.close();\`
   - This is good practice to free up system resources

**Complete Program Flow:**
1. Program prompts: "Enter the first number: "
2. User types a number and presses Enter
3. Program prompts: "Enter the second number: "
4. User types another number and presses Enter
5. Program calculates the sum and displays it

**Important Notes:**
- \`System.out.print()\` displays text without moving to a new line
- \`System.out.println()\` displays text and then moves to a new line
- \`scanner.nextInt()\` waits for the user to type an integer and press Enter
- Always close the Scanner when you're done with it
- The import statement must be at the top of the file

**Example Interaction:**
\`\`\`
Enter the first number: 5
Enter the second number: 10
15
\`\`\`

**What You're Learning:**
- How to make programs interactive with user input
- Using import statements to access additional Java classes
- Working with Scanner objects and their methods
- The difference between \`print\` and \`println\`
- Proper resource management (closing the Scanner)`,
      hints: [
        {
          question: 'Where should I put the import statement?',
          answer:
            'Import statements go at the very top of the file, before the class declaration: `import java.util.Scanner;`',
        },
        {
          question: 'How do I create a Scanner object?',
          answer: 'Use `Scanner scanner = new Scanner(System.in);` inside your main method.',
        },
        {
          question: 'How do I read an integer from the user?',
          answer:
            'Use `int number = scanner.nextInt();` - this waits for the user to enter a number.',
        },
        {
          question: "What's the difference between print and println?",
          answer:
            '`print` keeps the cursor on the same line, while `println` moves to a new line after printing.',
        },
        {
          question: 'Why should I close the Scanner?',
          answer:
            'Closing the Scanner frees up system resources and is considered good programming practice.',
        },
      ],
      resources: [
        {
          title: 'Java Scanner Class Documentation',
          url: 'https://docs.oracle.com/javase/8/docs/api/java/util/Scanner.html',
        },
        {
          title: 'Reading User Input in Java',
          url: 'https://www.w3schools.com/java/java_user_input.asp',
        },
        {
          title: 'Java Import Statements',
          url: 'https://www.programiz.com/java-programming/packages-import',
        },
      ],
      starterCode: {
        java: `import java.util.Scanner;

public class Main {
    public static void main(String[] args) {
        // Create a Scanner object to read input

        // Prompt the user for the first number
        // Read the first number

        // Prompt the user for the second number
        // Read the second number

        // Calculate the sum

        // Print the result

        // Close the Scanner

    }
}`,
      },
      solutionCode: {
        java: `import java.util.Scanner;

public class Main {
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        System.out.print("Enter the first number: ");
        int num1 = scanner.nextInt();

        System.out.print("Enter the second number: ");
        int num2 = scanner.nextInt();

        int sum = num1 + num2;
        System.out.println(sum);

        scanner.close();
    }
}`,
      },
      solutionNotes:
        'This program demonstrates interactive programming using Scanner for user input, combining previously learned concepts with new input/output techniques.',
      // javaTestCases: [
      //   {
      //     title: 'Interactive Addition Test',
      //     expectedOutput: 'Enter the first number: Enter the second number: 15',
      //     isHidden: false,
      //     input: '5\n10',
      //   },
      // ],
    },

    // Lab 2
    {
      id: 205,
      createdAt: '2025-05-19T10:00:00.000Z',
      updatedAt: '2025-05-19T10:00:00.000Z',
      tenant: tenantId,

      _status: 'published',
      language: 'java',
      difficulty: 'easy',
      points: 10,
      dueDate: daysFromNow(7),
      title: 'Add Three Numbers',
      description:
        'Write a Java program that reads three integers from the console and prints their sum.',
      instructions: `In this assignment, you will learn how to read multiple inputs from the user and perform arithmetic operations.

**Objective:**
Your program should read three integer numbers from the user and display their sum in a specific format.

**Steps:**
1. **Import the Scanner class**: Add \`import java.util.Scanner;\` at the top of your file to use the Scanner for reading input.

2. **Create a Scanner object**: Inside your main method, create a Scanner object to read from System.in:
   \`Scanner scanner = new Scanner(System.in);\`

3. **Read three integers**: Use \`scanner.nextInt()\` three times to read three separate integers. Store them in variables (e.g., \`num1\`, \`num2\`, \`num3\`).

4. **Calculate the sum**: Add the three numbers together and store the result in a variable called \`sum\`.

5. **Display the result**: Print the sum using the exact format: \`Sum: <value>\`
   Example: If the sum is 6, print "Sum: 6"

6. **Close the Scanner**: Always close the Scanner when done: \`scanner.close();\`

**Important Notes:**
- The program should read the three numbers separated by spaces or on separate lines
- The output format must match exactly: "Sum: " followed by the calculated sum
- Make sure to handle integer arithmetic correctly

**Example:**
Input: 1 2 3
Output: Sum: 6`,
      hints: [
        {
          question: 'How do I read multiple integers in Java?',
          answer:
            'Create a Scanner object and call nextInt() multiple times: `int a = scanner.nextInt(); int b = scanner.nextInt();`',
        },
        {
          question: 'What if the numbers are on separate lines?',
          answer:
            'Scanner.nextInt() automatically handles whitespace and newlines, so it works for both space-separated and line-separated input.',
        },
        {
          question: 'How do I format the output correctly?',
          answer: 'Use System.out.println("Sum: " + sum); to match the required format exactly.',
        },
      ],
      resources: [
        {
          title: 'Reading Input with Scanner',
          url: 'https://docs.oracle.com/javase/tutorial/essential/io/scanning.html',
        },
        {
          title: 'Java Scanner Class Documentation',
          url: 'https://docs.oracle.com/javase/8/docs/api/java/util/Scanner.html',
        },
      ],
      starterCode: {
        java: `import java.util.Scanner;

public class Main {
    public static void main(String[] args) {
        // Create Scanner object to read input

        // Read three integers from user

        // Calculate the sum

        // Print the result in format "Sum: <value>"

        // Close the Scanner
    }
}`,
      },
      solutionCode: {
        java: `import java.util.Scanner;

public class Main {
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        int num1 = scanner.nextInt();
        int num2 = scanner.nextInt();
        int num3 = scanner.nextInt();

        int sum = num1 + num2 + num3;
        System.out.println("Sum: " + sum);

        scanner.close();
    }
}`,
      },
      solutionNotes:
        'This solution demonstrates reading multiple integers using Scanner and formatting output correctly.',
      // javaTestCases: [
      //   {
      //     title: 'Correct Sum Calculation',
      //     input: '1 2 3',
      //     expectedOutput: 'Sum: 6',
      //     isHidden: false,
      //   },
      // ],
    },
    {
      id: 206,
      createdAt: '2025-05-19T10:01:00.000Z',
      updatedAt: '2025-05-19T10:01:00.000Z',
      tenant: tenantId,

      _status: 'published',
      language: 'java',
      difficulty: 'easy',
      points: 10,
      dueDate: daysFromNow(7),
      title: 'Diameter from Circle Area',
      description: 'Given the area of a circle, compute and print its diameter.',
      instructions:
        "In this assignment, you will work with mathematical formulas and the Math class in Java to solve a geometry problem.\n\n**Mathematical Background:**\n- Area of a circle: A = π * r²\n- From area, we can find radius: r = √(A/π)\n- Diameter: d = 2 * r\n\n**Objective:**\nWrite a program that reads the area of a circle and calculates its diameter.\n\n**Steps:**\n1. **Import Scanner**: Import `java.util.Scanner` to read input from the user.\n\n2. **Read the area**: Use Scanner to read a double value representing the circle's area.\n\n3. **Calculate the radius**:\n   - Use the formula: radius = √(area/π)\n   - Use `Math.sqrt()` for square root calculation\n   - Use `Math.PI` for the value of π (approximately 3.14159...)\n\n4. **Calculate the diameter**: diameter = 2 * radius\n\n5. **Display the result**: Print only the diameter (no prefix text).\n\n6. **Handle precision**: The result may have many decimal places - that's expected and correct.\n\n**Important Notes:**\n- Use `double` data type for floating-point calculations\n- Java's Math.PI provides high precision for π\n- The output should be just the numeric value of the diameter\n\n**Example:**\nIf area = 78.5398, the output should be approximately: `9.999974648401234`",
      hints: [
        {
          question: 'How do I access π in Java?',
          answer: 'Use Math.PI which provides the value of π with high precision.',
        },
        {
          question: 'How do I calculate square root?',
          answer: 'Use Math.sqrt(value) to get the square root of a number.',
        },
        {
          question: 'What data type should I use for the area?',
          answer: 'Use double for floating-point numbers to handle decimal values accurately.',
        },
      ],
      resources: [
        {
          title: 'Java Math Class Documentation',
          url: 'https://docs.oracle.com/javase/8/docs/api/java/lang/Math.html',
        },
        {
          title: 'Circle Area and Circumference',
          url: 'https://www.mathsisfun.com/geometry/circle.html',
        },
      ],
      starterCode: {
        java: 'import java.util.Scanner;\n\npublic class Main {\n    public static void main(String[] args) {\n        // Create Scanner to read input\n\n        // Read the area as a double\n\n        // Calculate radius using: radius = sqrt(area / π)\n\n        // Calculate diameter: diameter = 2 * radius\n\n        // Print only the diameter value\n    }\n}',
      },
      solutionCode: {
        java: 'import java.util.Scanner;\n\npublic class Main {\n    public static void main(String[] args) {\n        Scanner scanner = new Scanner(System.in);\n        double area = scanner.nextDouble();\n\n        double radius = Math.sqrt(area / Math.PI);\n        double diameter = 2 * radius;\n\n        System.out.println(diameter);\n\n        scanner.close();\n    }\n}',
      },
      solutionNotes:
        'This solution demonstrates using Math class methods and working with geometric formulas.',
      // javaTestCases: [
      //   {
      //     title: 'Area 78.5398',
      //     input: '78.5398',
      //     expectedOutput: '10.00',
      //     tolerance: 0.01,
      //     isHidden: false,
      //   },
      //   {
      //     title: 'Area 50.2655',
      //     input: '50.2655',
      //     expectedOutput: '8.00',
      //     tolerance: 0.01,
      //     isHidden: false,
      //   },
      //   {
      //     title: 'Area 201.062',
      //     input: '201.062',
      //     expectedOutput: '16.00',
      //     tolerance: 0.01,
      //     isHidden: false,
      //   },
      //   {
      //     title: 'Area 0',
      //     input: '0',
      //     expectedOutput: '0.00',
      //     tolerance: 0.01,
      //     isHidden: false,
      //   },
      //   {
      //     title: 'Area 3.14159',
      //     input: '3.14159',
      //     expectedOutput: '2.00',
      //     tolerance: 0.01,
      //     isHidden: false,
      //   },
      // ],
    },
    {
      id: 207,
      createdAt: '2025-05-19T10:02:00.000Z',
      updatedAt: '2025-05-19T10:02:00.000Z',
      tenant: tenantId,

      _status: 'published',
      language: 'java',
      difficulty: 'medium',
      points: 15,
      dueDate: daysFromNow(7),
      title: 'Fahrenheit to Celsius Converter',
      description: 'Convert a temperature from Fahrenheit to Celsius.',
      instructions: `In this assignment, you will create a temperature converter that transforms Fahrenheit to Celsius using a mathematical formula.

**Temperature Conversion Background:**
The relationship between Fahrenheit and Celsius scales is:
- Celsius = (Fahrenheit - 32) × 5/9
- This formula accounts for the different starting points (32°F = 0°C) and different scale sizes

**Objective:**
Write a program that reads a temperature in Fahrenheit and converts it to Celsius.

**Steps:**
1. **Set up input reading**: Import Scanner and create a Scanner object to read from System.in.

2. **Read temperature**: Prompt and read a double value representing temperature in Fahrenheit.

3. **Apply conversion formula**:
   - Subtract 32 from the Fahrenheit temperature
   - Multiply the result by 5/9
   - Store the result in a variable for Celsius temperature

4. **Display the result**: Print the Celsius temperature using the format: \`Celsius: <value>\`

5. **Handle arithmetic precision**:
   - Use floating-point division (5.0/9.0) instead of integer division (5/9)
   - This ensures accurate decimal results

**Important Notes:**
- Use \`double\` for all temperature variables to handle decimal values
- Be careful with integer vs. floating-point division
- The output format must be exactly: "Celsius: " followed by the calculated temperature

**Examples:**
- 32°F → 0°C (freezing point of water)
- 212°F → 100°C (boiling point of water)
- 98.6°F → 37°C (normal body temperature)`,
      hints: [
        {
          question: 'Why should I use 5.0/9.0 instead of 5/9?',
          answer:
            'Using 5/9 performs integer division which results in 0, while 5.0/9.0 performs floating-point division giving the correct result ~0.5556.',
        },
        {
          question: 'How do I ensure accurate decimal calculations?',
          answer:
            'Declare all temperature variables as double and use decimal literals (like 32.0) in calculations.',
        },
        {
          question: 'What is the correct order of operations?',
          answer:
            'First subtract 32, then multiply by 5/9: celsius = (fahrenheit - 32) * 5.0 / 9.0',
        },
      ],
      resources: [
        {
          title: 'Temperature Conversion Formulas',
          url: 'https://www.rapidtables.com/convert/temperature/fahrenheit-to-celsius.html',
        },
        {
          title: 'Java Floating Point Arithmetic',
          url: 'https://docs.oracle.com/javase/specs/jls/se8/html/jls-4.html#jls-4.2.3',
        },
      ],
      starterCode: {
        java: `import java.util.Scanner;

public class Main {
    public static void main(String[] args) {
        // Create Scanner for reading input

        // Read Fahrenheit temperature as double

        // Convert to Celsius using: (F - 32) * 5/9

        // Print result in format "Celsius: <value>"
    }
}`,
      },
      solutionCode: {
        java: `import java.util.Scanner;

public class Main {
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        double fahrenheit = scanner.nextDouble();

        double celsius = (fahrenheit - 32) * 5.0 / 9.0;

        System.out.println("Celsius: " + celsius);

        scanner.close();
    }
}`,
      },
      solutionNotes:
        'This solution demonstrates temperature conversion using proper floating-point arithmetic.',
      // javaTestCases: [
      //   {
      //     title: 'Boiling Point Test',
      //     input: '212',
      //     expectedOutput: 'Celsius: 100.0',
      //     isHidden: false,
      //   },
      // ],
    },
    {
      id: 208,
      createdAt: '2025-05-19T10:03:00.000Z',
      updatedAt: '2025-05-19T10:03:00.000Z',
      tenant: tenantId,

      _status: 'published',
      language: 'java',
      difficulty: 'medium',
      points: 20,
      dueDate: daysFromNow(7),
      title: 'BMI Calculator',
      description: 'Compute Body Mass Index from weight (lb) and height (in).',
      instructions: `In this assignment, you will create a Body Mass Index (BMI) calculator that works with imperial units (pounds and inches) and converts them to metric units for calculation.

**BMI Background:**
- BMI (Body Mass Index) is a measure used to determine if a person's weight is appropriate for their height
- BMI Formula: BMI = weight(kg) / height(m)²
- Since we receive input in imperial units, we need to convert them first

**Unit Conversions Needed:**
- Weight: 1 pound = 0.45359237 kilograms
- Height: 1 inch = 0.0254 meters

**Objective:**
Write a program that:
1. Reads weight in pounds and height in inches
2. Converts them to metric units (kg and meters)
3. Calculates BMI using the metric values
4. **Displays only the numeric BMI value** (no prefix)

**Steps:**
1. **Set up input**: Import Scanner and create a Scanner object.
2. **Read user inputs**:
   - Read weight as a double (in pounds)
   - Read height as a double (in inches)
3. **Convert to metric units**:
   - Convert weight to kilograms: kg = pounds × 0.45359237
   - Convert height to meters: meters = inches × 0.0254
4. **Calculate BMI**:
   - Apply the BMI formula: BMI = kg / (meters × meters)
5. **Display result**: Print **only** the numeric BMI value

**Important Considerations:**
- Use \`double\` for all measurements to handle decimal values accurately
- Conversion factors must be precise for accurate results
- The BMI calculation requires squaring the height in meters

**Example Calculation:**
For 150 pounds and 65 inches:
- Weight in kg: 150 × 0.45359237 = 68.04 kg
- Height in meters: 65 × 0.0254 = 1.651 m
- BMI: 68.04 / (1.651 × 1.651) ≈ 24.96`,
      hints: [
        {
          question: 'How do I square a number in Java?',
          answer:
            'You can multiply the number by itself: meters * meters, or use Math.pow(meters, 2).',
        },
        {
          question: 'Why are the conversion factors so specific?',
          answer:
            'These are exact conversion factors defined internationally. Using precise values ensures accurate BMI calculations.',
        },
        {
          question: 'Should I round the BMI result?',
          answer:
            'No, output the full calculated value as the test case expects the complete precision.',
        },
        {
          question: 'What order should I read the inputs?',
          answer: 'Read weight first, then height, as specified in the test case input format.',
        },
      ],
      resources: [
        {
          title: 'BMI Information - CDC',
          url: 'https://www.cdc.gov/healthyweight/assessing/bmi/',
        },
        {
          title: 'Unit Conversion Reference',
          url: 'https://www.nist.gov/pml/weights-and-measures/metric-si/unit-conversion',
        },
      ],
      starterCode: {
        java: `import java.util.Scanner;

public class Main {
    public static void main(String[] args) {
        // Create Scanner for input

        // Read weight in pounds (double)

        // Read height in inches (double)

        // Convert weight to kilograms: kg = pounds * 0.45359237

        // Convert height to meters: meters = inches * 0.0254

        // Calculate BMI: bmi = kg / (meters * meters)

        // Print only the numeric BMI value
    }
}`,
      },
      solutionCode: {
        java: `import java.util.Scanner;

public class Main {
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        double pounds = scanner.nextDouble();
        double inches = scanner.nextDouble();

        // Convert to metric units
        double kg = pounds * 0.45359237;
        double meters = inches * 0.0254;

        // Calculate BMI
        double bmi = kg / (meters * meters);

        // Print only the numeric value
        System.out.println(bmi);

        scanner.close();
    }
}`,
      },
      solutionNotes:
        'This solution demonstrates unit conversion and applying mathematical formulas with proper precision.',
      // javaTestCases: [
      //   {
      //     title: 'BMI Calculation - Normal Weight',
      //     input: '150 65',
      //     expectedOutput: '24.96',
      //     tolerance: 0.01,
      //     isHidden: false,
      //   },
      //   {
      //     title: 'BMI Calculation - Underweight',
      //     input: '100 65',
      //     expectedOutput: '16.64',
      //     tolerance: 0.01,
      //     isHidden: false,
      //   },
      //   {
      //     title: 'BMI Calculation - Overweight',
      //     input: '180 65',
      //     expectedOutput: '29.95',
      //     tolerance: 0.01,
      //     isHidden: false,
      //   },
      // ],
    },

    // C Assignments
    {
      id: 301,
      createdAt: '2025-05-19T01:35:15.377Z',
      updatedAt: '2025-05-19T01:35:15.377Z',
      tenant: tenantId,
      _status: 'published',

      language: 'c',
      difficulty: 'easy',
      points: 10,
      dueDate: daysFromNow(7),
      title: 'Calculate average of three numbers',
      description: 'Write a C program that calculates and displays the average of three numbers.',
      instructions:
        'Welcome to your C programming assignment! This exercise will introduce you to basic arithmetic operations and console input/output in C.\n\n**Objective:**\nCreate a C program that reads three numbers from the user input and calculates their average.\n\n**Understanding the Structure:**\n\n1. **Include necessary headers**: Start by including the standard input/output library.\n   ```c\n   #include <stdio.h>\n   ```\n\n2. **Main Function**: Every C program needs a main function as the entry point.\n   ```c\n   int main() {\n       // Your code goes here\n       return 0;\n   }\n   ```\n\n3. **Reading Input**: Use `scanf()` to read values from the console.\n\n4. **Arithmetic Operations**: Calculate the average by adding the numbers and dividing by 3.\n\n5. **Printing Output**: Use `printf()` to display the result.\n\n**Step-by-Step Instructions:**\n\n1. Declare variables to store the three numbers and their average\n2. Prompt the user to enter three numbers\n3. Read the three values using `scanf()`\n4. Calculate the average by adding the three numbers and dividing by 3\n5. Display the result with one decimal place using `printf()`\n\n**Important Notes:**\n- Make sure to use the correct format specifiers in `scanf()` and `printf()`\n- The average should be displayed with decimal precision (e.g., "20.0")\n- Remember to handle the division correctly to get a floating-point result',
      hints: [
        {
          question: 'How do I read values from the user in C?',
          answer:
            'Use the `scanf()` function: `scanf("%d %d %d", &num1, &num2, &num3);` will read three integers separated by spaces.',
        },
        {
          question: 'How do I perform the averaging calculation?',
          answer:
            'Add the three numbers and divide by 3.0 (not just 3) to ensure floating-point division: `average = (num1 + num2 + num3) / 3.0;`',
        },
        {
          question: 'How do I display the result with one decimal place?',
          answer:
            'Use the format specifier `%.1f` in your printf statement: `printf("%.1f\\n", average);`',
        },
        {
          question: "Why isn't my calculation giving the correct result?",
          answer:
            "Make sure you're using floating-point division. If all your variables are integers, you might get integer division which truncates the decimal part.",
        },
      ],
      resources: [
        {
          title: 'C Programming - Input and Output',
          url: 'https://www.learn-c.org/en/Input_Output',
        },
        {
          title: 'Understanding scanf and printf in C',
          url: 'https://www.cprogramming.com/tutorial/c/lesson3.html',
        },
        {
          title: 'C Data Types and Arithmetic',
          url: 'https://www.tutorialspoint.com/cprogramming/c_data_types.htm',
        },
      ],
      starterCode: {
        c: '#include <stdio.h>\n\nint main() {\n    // Declare variables to store the three numbers and their average\n    \n    // Read three numbers from the user\n    \n    // Calculate the average\n    \n    // Display the result\n    \n    return 0;\n}',
      },
      solutionCode: {
        c: '#include <stdio.h>\n\nint main() {\n    // Declare variables to store the three numbers and their average\n    int num1, num2, num3;\n    float average;\n    \n    // Read three numbers from the user\n    scanf("%d %d %d", &num1, &num2, &num3);\n    \n    // Calculate the average\n    average = (num1 + num2 + num3) / 3.0;\n    \n    // Display the result\n    printf("%.1f\\n", average);\n    \n    return 0;\n}',
      },
      solutionNotes:
        'This solution demonstrates basic input/output operations in C and proper handling of floating-point arithmetic. It uses scanf() to read three integers, calculates their average using floating-point division, and displays the result with one decimal place using printf().',
      // cTestCases: [
      //   {
      //     title: 'Avg of 10, 20, 30',

      //     input: '10 20 30',
      //     expectedOutput: '20.0',
      //     tolerance: 0.01,
      //     isHidden: false,
      //   },
      //   {
      //     title: 'Avg of negative numbers',

      //     input: '-10 -20 -30',
      //     expectedOutput: '-20.0',
      //     tolerance: 0.01,
      //     isHidden: false,
      //   },
      //   {
      //     title: 'Avg with decimal result',

      //     input: '1 2 3',
      //     expectedOutput: '2.0',
      //     tolerance: 0.01,
      //     isHidden: false,
      //   },
      // ],
    },
    // Assignment 1: Temperature Converter
    {
      id: 302,
      createdAt: '2025-05-23T10:15:30.123Z',
      updatedAt: '2025-05-23T10:15:30.123Z',
      tenant: tenantId,

      _status: 'published',
      language: 'c',
      difficulty: 'easy',
      points: 10,
      dueDate: daysFromNow(7),
      title: 'Temperature Converter (Celsius to Fahrenheit)',
      description: 'Write a C program that converts temperature from Celsius to Fahrenheit.',
      instructions:
        'Welcome to your C programming assignment! This exercise will help you practice arithmetic operations and understand the relationship between different temperature scales.\n\n**Objective:**\nCreate a C program that reads a temperature in Celsius from the user and converts it to Fahrenheit.\n\n**Understanding the Formula:**\nThe formula to convert Celsius to Fahrenheit is:\n`F = (C × 9/5) + 32`\n\n**Understanding the Structure:**\n\n1. **Include necessary headers**: Start by including the standard input/output library.\n   ```c\n   #include <stdio.h>\n   ```\n\n2. **Main Function**: Every C program needs a main function as the entry point.\n   ```c\n   int main() {\n       // Your code goes here\n       return 0;\n   }\n   ```\n\n3. **Reading Input**: Use `scanf()` to read the temperature value from the console.\n\n4. **Arithmetic Operations**: Apply the conversion formula correctly.\n\n5. **Printing Output**: Use `printf()` to display the converted temperature.\n\n**Step-by-Step Instructions:**\n\n1. Declare variables to store the Celsius and Fahrenheit temperatures\n2. Prompt the user to enter temperature in Celsius\n3. Read the Celsius value using `scanf()`\n4. Apply the conversion formula: F = (C × 9.0/5.0) + 32\n5. Display the result in Fahrenheit with one decimal place\n\n**Important Notes:**\n- Use floating-point arithmetic to ensure accurate results\n- Display the result with one decimal place precision\n- Make sure to use proper format specifiers for floating-point numbers',
      hints: [
        {
          question: 'How do I read a floating-point number from the user?',
          answer:
            'Use `scanf("%f", &celsius);` to read a float value. Make sure to use the address-of operator (&) before the variable name.',
        },
        {
          question: 'How do I implement the conversion formula correctly?',
          answer:
            'Use floating-point division: `fahrenheit = (celsius * 9.0 / 5.0) + 32;`. Using 9.0 and 5.0 ensures floating-point arithmetic.',
        },
        {
          question: 'How do I display the result with one decimal place?',
          answer:
            'Use the format specifier `%.1f` in your printf statement: `printf("%.1f\\n", fahrenheit);`',
        },
        {
          question: 'Why am I getting incorrect results?',
          answer:
            "Make sure you're using floating-point variables (float) and floating-point constants (9.0, 5.0) to avoid integer division truncation.",
        },
      ],
      resources: [
        {
          title: 'C Programming - Floating Point Numbers',
          url: 'https://www.learn-c.org/en/Variables_and_Types',
        },
        {
          title: 'Temperature Conversion Formulas',
          url: 'https://www.rapidtables.com/convert/temperature/celsius-to-fahrenheit.html',
        },
        {
          title: 'C Printf Format Specifiers',
          url: 'https://www.cprogramming.com/reference/printf_format_codes.html',
        },
      ],
      starterCode: {
        c: '#include <stdio.h>\n\nint main() {\n    // Declare variables for Celsius and Fahrenheit temperatures\n    \n    // Read temperature in Celsius from user\n    \n    // Convert Celsius to Fahrenheit using the formula: F = (C * 9/5) + 32\n    \n    // Display the result\n    \n    return 0;\n}',
      },
      solutionCode: {
        c: '#include <stdio.h>\n\nint main() {\n    // Declare variables for Celsius and Fahrenheit temperatures\n    float celsius, fahrenheit;\n    \n    // Read temperature in Celsius from user\n    scanf("%f", &celsius);\n    \n    // Convert Celsius to Fahrenheit using the formula: F = (C * 9/5) + 32\n    fahrenheit = (celsius * 9.0 / 5.0) + 32;\n    \n    // Display the result\n    printf("%.1f\\n", fahrenheit);\n    \n    return 0;\n}',
      },
      solutionNotes:
        'This solution demonstrates floating-point arithmetic in C and proper application of mathematical formulas. It uses scanf() to read a float value, applies the temperature conversion formula with floating-point precision, and displays the result with one decimal place using printf().',
      // cTestCases: [
      //   {
      //     title: 'Convert 0°C to Fahrenheit',

      //     input: '0',
      //     expectedOutput: '32.0',
      //     tolerance: 0.01,
      //     isHidden: false,
      //   },
      //   {
      //     title: 'Convert 100°C to Fahrenheit',

      //     input: '100',
      //     expectedOutput: '212.0',
      //     tolerance: 0.01,
      //     isHidden: false,
      //   },
      //   {
      //     title: 'Convert 25°C to Fahrenheit',

      //     input: '25',
      //     expectedOutput: '77.0',
      //     tolerance: 0.01,
      //     isHidden: false,
      //   },
      //   {
      //     title: 'Convert negative temperature',

      //     input: '-40',
      //     expectedOutput: '-40.0',
      //     tolerance: 0.01,
      //     isHidden: true,
      //   },
      // ],
    },

    // Assignment 2: Simple Interest Calculator
    {
      id: 303,
      createdAt: '2025-05-23T10:20:45.456Z',
      updatedAt: '2025-05-23T10:20:45.456Z',
      tenant: tenantId,

      _status: 'published',
      language: 'c',
      difficulty: 'easy',
      points: 12,
      dueDate: daysFromNow(7),
      title: 'Simple Interest Calculator',
      description:
        'Write a C program that calculates simple interest based on principal, rate, and time.',
      instructions:
        'Welcome to your C programming assignment! This exercise will help you practice working with multiple input values and implementing mathematical formulas in C.\n\n**Objective:**\nCreate a C program that calculates simple interest using the formula: SI = (P × R × T) / 100\n\n**Understanding the Formula:**\n- P = Principal amount\n- R = Rate of interest per year\n- T = Time period in years\n- SI = Simple Interest\n\n**Understanding the Structure:**\n\n1. **Include necessary headers**: Start by including the standard input/output library.\n   ```c\n   #include <stdio.h>\n   ```\n\n2. **Main Function**: Every C program needs a main function as the entry point.\n   ```c\n   int main() {\n       // Your code goes here\n       return 0;\n   }\n   ```\n\n3. **Reading Input**: Use `scanf()` to read multiple values from the console.\n\n4. **Arithmetic Operations**: Apply the simple interest formula correctly.\n\n5. **Printing Output**: Use `printf()` to display the calculated interest.\n\n**Step-by-Step Instructions:**\n\n1. Declare variables to store principal, rate, time, and simple interest\n2. Prompt the user to enter the principal amount, rate of interest, and time period\n3. Read the three values using `scanf()`\n4. Calculate simple interest using the formula: SI = (P × R × T) / 100\n5. Display the result with two decimal places\n6. Optionally, also display the total amount (Principal + Interest)\n\n**Important Notes:**\n- Use floating-point arithmetic for accurate calculations\n- Display the result with two decimal places for currency precision\n- Make sure to handle the division by 100 correctly',
      hints: [
        {
          question: 'How do I read multiple floating-point numbers from the user?',
          answer:
            'Use `scanf("%f %f %f", &principal, &rate, &time);` to read three float values separated by spaces.',
        },
        {
          question: 'How do I implement the simple interest formula?',
          answer:
            'Use the formula: `simple_interest = (principal * rate * time) / 100.0;`. The .0 ensures floating-point division.',
        },
        {
          question: 'How do I display money values with two decimal places?',
          answer:
            'Use the format specifier `%.2f` in your printf statement: `printf("%.2f\\n", simple_interest);`',
        },
        {
          question: 'Should I also calculate the total amount?',
          answer:
            'Yes, you can calculate total amount as: `total = principal + simple_interest;` and display both values.',
        },
      ],
      resources: [
        {
          title: 'Simple Interest Formula Explained',
          url: 'https://www.rapidtables.com/calc/finance/interest-calculator.html',
        },
        {
          title: 'C Programming - Multiple Input with scanf',
          url: 'https://www.tutorialspoint.com/cprogramming/c_input_output.htm',
        },
        {
          title: 'Formatting Output in C',
          url: 'https://www.cprogramming.com/reference/printf_format_codes.html',
        },
      ],
      starterCode: {
        c: '#include <stdio.h>\n\nint main() {\n    // Declare variables for principal, rate, time, and simple interest\n    \n    // Read principal amount, rate of interest, and time period\n    \n    // Calculate simple interest using formula: SI = (P * R * T) / 100\n    \n    // Display the simple interest\n    \n    return 0;\n}',
      },
      solutionCode: {
        c: '#include <stdio.h>\n\nint main() {\n    // Declare variables for principal, rate, time, and simple interest\n    float principal, rate, time, simple_interest;\n    \n    // Read principal amount, rate of interest, and time period\n    scanf("%f %f %f", &principal, &rate, &time);\n    \n    // Calculate simple interest using formula: SI = (P * R * T) / 100\n    simple_interest = (principal * rate * time) / 100.0;\n    \n    // Display the simple interest\n    printf("%.2f\\n", simple_interest);\n    \n    return 0;\n}',
      },
      solutionNotes:
        'This solution demonstrates working with multiple input values and implementing mathematical formulas in C. It uses scanf() to read three float values, applies the simple interest formula with proper floating-point arithmetic, and displays the result with two decimal places for financial precision.',
      // cTestCases: [
      //   {
      //     title: 'Standard calculation',
      //     input: '1000 5 2',
      //     expectedOutput: '100.00',
      //     tolerance: 0.01,
      //     isHidden: false,
      //   },
      //   {
      //     title: 'High interest rate',
      //     input: '5000 12 3',
      //     expectedOutput: '1800.00',
      //     tolerance: 0.01,
      //     isHidden: false,
      //   },
      //   {
      //     title: 'Fractional values',
      //     input: '1500.50 7.5 1.5',
      //     expectedOutput: '168.81',
      //     tolerance: 0.01,
      //     isHidden: false,
      //   },
      //   {
      //     title: 'Small principal',
      //     input: '100 10 1',
      //     expectedOutput: '10.00',
      //     tolerance: 0.01,
      //     isHidden: true,
      //   },
      // ],
    },
  ];
};
