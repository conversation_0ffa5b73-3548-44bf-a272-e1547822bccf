import type { z } from 'zod';
import type { Assignment } from '@/payload-types';
import type { GenerationOptions } from '@/schemas/assignment.schemas';

export type IAssignment = {
  id: number;
  title: string;
  description: string;
  points: number;
};

export type StarterCode = {
  html: string;
  css?: string;
  js?: string;
};

export interface Resource {
  title: string;
  url: string;
}

// export type TestSuites = IAssignment['testSuites'];

export type ActionTestCase = {
  title: string;
  type: 'action';
  actionType?: ('click' | 'input' | 'hover') | null;
  actionSelector?: string | null;
  actionValue?: string | null;
  id?: string | null;
};

export type AssertionTestCase = {
  title: string;
  type: 'assertion';
  assertionSelector?: string | null;
  assertionType?:
    | (
        | 'textContent'
        | 'exists'
        | 'notExists'
        | 'hasClass'
        | 'css'
        | 'ariaLabel'
        | 'value'
        | 'alert'
      )
    | null;
  expectedValue?: string | null;
  expectedClass?: string | null;
  cssProperty?: string | null;
  cssValue?: string | null;
  id?: string | null;
};

export type CompleteTestCase = ActionTestCase | AssertionTestCase;

export interface Test {
  name: string;
  description: string;
  testCode: string;
  expectedOutput: string;
  points: number;
  isHidden?: boolean;
}

export type Hint = {
  question: string;
  answer: string;
};

export type AssignmentDetails = IAssignment & {
  instructions: string;
  dueDate: string;
  isActive: boolean;
  starterCode: StarterCode;
  updatedAt: string;
  createdAt: string;
  resources?: Resource[];
  hints?: Hint[];
  tests?: Test[];
};

export interface GenContentPayload {
  title: Assignment['title'];
  difficulty: Assignment['difficulty'];
  points: Assignment['points'];
  additionalRequirements: string;
  generationOptions: z.infer<typeof GenerationOptions>;
}

export type GeneratedAssignmentContent = Omit<
  Assignment,
  'id' | 'createdAt' | 'updatedAt' | 'dueDate'
>;

export interface SubmissionData {
  html?: string;
  css?: string;
  js?: string;
  c?: string;
  java?: string;
}
