export interface LanguageConfig {
  id: string;
  name: string;
  displayName: string;
  fileExtensions: string[];
  codeEditorLanguage: string;
  supportsCommandLineArgs: boolean;
  supportsWebPreview: boolean;
  executionEnvironment: 'web' | 'server';
  testingFramework: 'io' | 'web';
  defaultTemplate?: string;
  compilationRequired: boolean;
  runtimeCommand?: string;
  buildCommand?: string;
}

export const LANGUAGE_CONFIGS: Record<string, LanguageConfig> = {
  web: {
    id: 'web',
    name: 'web',
    displayName: 'Web (HTML/CSS/JS)',
    fileExtensions: ['.html', '.css', '.js'],
    codeEditorLanguage: 'html',
    supportsCommandLineArgs: false,
    supportsWebPreview: true,
    executionEnvironment: 'web',
    testingFramework: 'web',
    compilationRequired: false,
  },
  java: {
    id: 'java',
    name: 'java',
    displayName: 'Java',
    fileExtensions: ['.java'],
    codeEditorLanguage: 'java',
    supportsCommandLineArgs: true,
    supportsWebPreview: false,
    executionEnvironment: 'server',
    testingFramework: 'io',
    compilationRequired: true,
    buildCommand: 'javac $FILENAME.java',
    runtimeCommand: 'java $FILENAME',
  },
  c: {
    id: 'c',
    name: 'c',
    displayName: 'C',
    fileExtensions: ['.c'],
    codeEditorLanguage: 'c',
    supportsCommandLineArgs: true,
    supportsWebPreview: false,
    executionEnvironment: 'server',
    testingFramework: 'io',
    compilationRequired: true,
    buildCommand: 'gcc $FILENAME.c',
    runtimeCommand: './a.out',
  },
};

export const LANGUAGES_OPTIONS = Object.values(LANGUAGE_CONFIGS).map((config) => ({
  label: config.displayName,
  value: config.id,
}));
