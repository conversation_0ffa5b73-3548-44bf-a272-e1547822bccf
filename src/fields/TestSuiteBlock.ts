import type { Block, FieldHook } from 'payload';

export const TestCaseBlocks: Block[] = [
  {
    slug: 'action',
    fields: [
      {
        name: 'actionType',
        type: 'select',
        options: ['click', 'input', 'hover'],
        required: true,
      },
      { name: 'actionSelector', type: 'text', required: true },
      {
        name: 'actionValue',
        type: 'text',
        admin: { condition: (_, data) => data.actionType === 'input' },
      },
    ],
  },
  {
    slug: 'assertion',
    fields: [
      {
        name: 'assertionType',
        type: 'select',
        options: [
          'textContent',
          'exists',
          'css',
          'notExists',
          'hasClass',
          'ariaLabel',
          'value',
          'alert',
        ],
        required: true,
      },
      {
        name: 'assertionSelector',
        type: 'text',
        required: true,
      },
      {
        name: 'expectedValue',
        type: 'text',
        admin: {
          condition: (_, data) =>
            ['textContent', 'ariaLabel', 'value'].includes(data.assertionType),
        },
      },
      {
        name: 'expectedClass',
        type: 'text',
        admin: { condition: (_, data) => data.assertionType === 'hasClass' },
      },
      {
        name: 'cssProperty',
        type: 'text',
        admin: { condition: (_, data) => data.assertionType === 'css' },
      },
      {
        name: 'expectedCssValue',
        type: 'text',
        admin: { condition: (_, data) => data.assertionType === 'css' },
      },
      {
        name: 'expectedAlertText',
        type: 'text',
        admin: { condition: (_, data) => data.assertionType === 'alert' },
      },
    ],
  },
];

export const TestSuiteBlock: Block = {
  slug: 'testSuite',
  fields: [
    {
      type: 'row',
      fields: [
        {
          name: 'points',
          type: 'number',
          required: true,
          defaultValue: 10,
          admin: { description: 'Points for this suite' },
        },
        {
          name: 'visibility',
          type: 'select',
          options: [
            { label: 'Visible', value: 'visible' },
            { label: 'Hidden', value: 'hidden' },
          ],
          defaultValue: 'visible',
          required: true,
          admin: {
            description: 'Should students see this suite?',
          },
        },
      ],
    },
    {
      name: 'tests',
      type: 'blocks',
      blocks: TestCaseBlocks,
    },
  ],
};

// Hook to remove hidden testSuites for students
export const filterHiddenTestSuites: FieldHook = ({ value, req }) => {
  const user = req.user;
  const isStudent = user && user?.tenants?.[0]?.roles?.[0] === 'student';

  // Only filter if it's a student
  if (isStudent && Array.isArray(value)) {
    return value.filter((suite) => suite.visibility !== 'hidden');
  }

  return value;
};
