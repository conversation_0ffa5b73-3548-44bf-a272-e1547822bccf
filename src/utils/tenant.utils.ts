import { User } from '@/payload-types';
import { getTenantId } from '@/utils/getTenantId';

export function extractTenantId(user: User | null): number {
  const tenantId = getTenantId(user);
  if (!tenantId) {
    const tenant = user?.tenants?.at(0)?.tenant;
    if (tenant && typeof tenant === 'object' && 'id' in tenant) {
      return tenant.id;
    }
    throw new Error('No valid tenant found');
  }
  return tenantId;
}
