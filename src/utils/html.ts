interface CodeParts {
  html?: string;
  css?: string;
  js?: string;
}

function escapeForScript(input: string): string {
  return input
    .replace(/\\/g, '\\\\')
    .replace(/`/g, '\\`')
    .replace(/\$/g, '\\$')
    .replace(/<\/script>/gi, '<\\/script>');
}

function sanitizeHTML(html: string): string {
  // Basic HTML sanitization - you might want to use a proper sanitizer library
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/on\w+="[^"]*"/g, '')
    .replace(/on\w+='[^']*'/g, '');
}

export function combineHTML({ html = '', css = '', js = '' }: CodeParts): string {
  const safeHTML = sanitizeHTML(html);
  const safeCSS = css;
  const safeJS = escapeForScript(js);

  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
        background-color: white;
    }

    .preview-container {
      all: initial;
      font-family: system-ui, -apple-system, sans-serif;
    }
    .error-display {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      background: #f44336;
      color: white;
      padding: 10px;
      font-family: monospace;
      z-index: 9999;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .error-close {
      background: none;
      border: none;
      color: white;
      cursor: pointer;
      padding: 0 10px;
    }
    html, body {
      transition: opacity 0.2s ease-in-out;
      margin: 0;
      padding: 0;
    }
    ${safeCSS}
  </style>
</head>
<body>
  <div class="preview-container">
    ${safeHTML}
  </div>
  <script>
    window.onerror = function(message, source, lineno, colno, error) {
      showError('JavaScript Error: ' + message);
      return true;
    };

    function showError(message) {
      const errorDiv = document.createElement('div');
      errorDiv.className = 'error-display';
      errorDiv.innerHTML = \`
        <span>\${message}</span>
        <button class="error-close" onclick="this.parentElement.remove()">x</button>
      \`;
      document.body.appendChild(errorDiv);
    }

    try {
      ${safeJS}
    } catch(error) {
      showError('JavaScript Error: ' + error.message);
    }
  </script>
</body>
</html>`;
}
