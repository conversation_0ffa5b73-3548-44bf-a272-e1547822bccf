import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { decodeJWTPayload } from '@/lib/jwt';
import { DecodedToken } from './types/decoded-token';

function hasRole(payload: DecodedToken, role: string) {
  return payload.tenants?.some((t) => t.roles?.includes(role));
}

function redirectToLogin(error?: string, requestUrl?: string) {
  const url = new URL('/login', requestUrl);
  if (error) url.searchParams.set('error', error);

  // Add the original path as a query parameter
  if (requestUrl) {
    const originalUrl = new URL(requestUrl);
    const redirectPath = originalUrl.pathname + originalUrl.search;
    url.searchParams.set('redirect', redirectPath);
  }

  return NextResponse.redirect(url);
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Allow API routes through
  if (pathname.startsWith('/api')) {
    return NextResponse.next();
  }

  const token = request.cookies.get('super-labs-token')?.value;

  if (!token) {
    return redirectToLogin('no-token', request.url);
  }

  try {
    const payload = decodeJWTPayload(token);

    if (!payload) {
      return redirectToLogin('token-expired', request.url);
    }

    if (pathname.startsWith('/admin') && !hasRole(payload, 'faculty')) {
      return redirectToLogin('not-faculty', request.url);
    }
  } catch {
    return redirectToLogin('malformed-token', request.url);
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/', '/admin/:path*', '/subjects/:path*', '/assignments/:path*', '/submissions/:path*'],
};
