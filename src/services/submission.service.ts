import type { Payload } from 'payload';
import type { Submission } from '@/payload-types';
import type { SubmissionData } from '@/types/assignment';

export class SubmissionService {
  constructor(private payload: Payload) {}

  async findExistingSubmission(assignmentId: number, userId: string, tenantId: number) {
    return await this.payload.find({
      collection: 'submissions',
      where: {
        assignment: { equals: assignmentId },
        student: { equals: userId },
        tenant: { equals: tenantId },
      },
    });
  }

  async findSubmissionsById(id: number) {
    return await this.payload.find({
      collection: 'submissions',
      where: {
        assignment: { equals: id },
      },
      depth: 0,
      limit: 1000,
    });
  }

  async createSubmission(
    data: SubmissionData,
    assignmentId: number,
    userId: string,
    tenantId: number,
  ) {
    // const score = this.calculateScore(data.passedTestCases, data.failedTestCases);

    const submissionData: Submission = {
      id: 0,
      status: 'review' as const,
      assignment: assignmentId,
      student: Number(userId),
      tenant: tenantId,
      // failedTestCases: data.failedTestCases,
      // passedTestCases: data.passedTestCases,
      // score,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      html: data.html || '',
      css: data.css || '',
      js: data.js || '',
      c: data.c || '',
      java: data.java || '',
    };

    return await this.payload.create({
      collection: 'submissions',
      data: submissionData,
    });
  }

  async updateSubmission(
    submissionId: number | string,
    data: SubmissionData,
    assignmentId: number,
    userId: string,
    tenantId: number,
  ) {
    // const score = this.calculateScore(
    // 	data.passedTestCases,
    // 	data.failedTestCases,
    // );

    const submissionData = {
      status: 'review' as const,
      assignment: assignmentId,
      student: Number(userId),
      tenant: tenantId,
      // failedTestCases: data.failedTestCases,
      // passedTestCases: data.passedTestCases,
      // score,
      html: data.html || '',
      css: data.css || '',
      js: data.js || '',
      c: data.c || '',
      java: data.java || '',
    };

    return await this.payload.update({
      collection: 'submissions',
      id: submissionId,
      data: submissionData,
    });
  }

  private calculateScore(passedTestCases: number, failedTestCases: number): number {
    const totalTestCases = passedTestCases + failedTestCases;
    return totalTestCases > 0 ? Math.floor((passedTestCases / totalTestCases) * 100) : 0;
  }
}
