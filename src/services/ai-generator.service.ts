// import { generateObject } from 'ai';
// import { google } from '@ai-sdk/google';
// import { z } from 'zod';
// import { GenContentPayload, GeneratedAssignmentContent } from '@/types/assignment'; // Ensure this is correctly typed

// // --- Schemas for Granular Generation ---

// const AssignmentContentSchema = z.object({
//   assignment: z.object({
//     title: z.string(),
//     description: z.string().max(300, 'Description should be concise (2-3 lines).'),
//     instructions: z
//       .string()
//       .describe(
//         'Detailed, step-by-step instructions in Markdown format, suitable for BCA students learning Java with prior C knowledge. ' +
//           'Should clearly explain the problem, input/output, constraints, and highlight Java-specific approaches. ' +
//           'No top-level Markdown heading needed.',
//       ),
//     requiresCommandLineArgs: z
//       .boolean()
//       .describe(
//         'True if the program should read input from command line arguments, false if it should use Scanner or other interactive input.',
//       ),
//     testCases: z
//       .array(
//         z.object({
//           title: z.string().describe('A short, descriptive title for the test case.'),
//           description: z.string().describe('A brief explanation of what this test case covers.'),
//           input: z
//             .string()
//             .describe(
//               'The exact input for this test case. If command line args, space-separated. If Scanner, new-line separated or as appropriate.',
//             ),
//           expectedOutput: z
//             .string()
//             .describe(
//               'The exact expected string output for this test case. Can be empty. Numerical/boolean outputs as strings (e.g., "42", "false").',
//             ),
//           tolerance: z.number().default(0).describe('Allowed numerical tolerance, if applicable.'),
//           isHidden: z
//             .boolean()
//             .default(false)
//             .describe('Whether this test case is hidden from the student initially.'),
//         }),
//       )
//       .min(3, 'Provide at least 3 test cases.')
//       .max(10, 'Provide no more than 10 test cases.')
//       .describe('A diverse set of test cases covering edge cases and typical scenarios.'),
//   }),
// });
// type AssignmentContent = z.infer<typeof AssignmentContentSchema>['assignment'];

// const StarterCodeSchema = z.object({
//   starterCode: z
//     .string()
//     .describe(
//       'Minimal Java starter code with public class Main, main method, essential imports, and TODO comments. NO actual logic implementation.',
//     ),
// });

// // MODIFIED: Emphasize Main class and compilability in schema description
// const SolutionCodeSchema = z.object({
//   solutionCodeChunks: z
//     .array(z.string())
//     .min(1)
//     .describe(
//       'The complete Java solution code, broken into an array of logical string chunks. ' +
//         'These chunks, when concatenated, MUST form a single, complete, and compilable Main.java file. ' +
//         'The entire solution, including imports and all logic, MUST be wrapped in `public class Main { ... }`.',
//     ),
// });

// export class AIGeneratorService {
//   // Consider using a more powerful model for solution generation if 'flash' struggles:
//   // private static solutionModel = google('models/gemini-1.5-pro-latest');
//   private static contentModel = google('models/gemini-1.5-flash-latest'); // Flash is okay for content & starter
//   private static solutionModel = google('models/gemini-1.5-flash-latest'); // Start with flash, upgrade if needed

//   static async generateAssignmentContent(
//     payload: GenContentPayload,
//   ): Promise<GeneratedAssignmentContent> {
//     console.log('Starting assignment generation with payload:', payload);

//     try {
//       // --- Agent Step 1: Generate Assignment Content (No Code) ---
//       console.log('Step 1: Generating assignment textual content...');
//       const contentPrompt = this.buildContentPrompt(payload);
//       const { object: contentObject } = await generateObject({
//         model: this.contentModel,
//         schema: AssignmentContentSchema,
//         prompt: contentPrompt,
//         maxTokens: 3072,
//       });
//       const assignmentDetails = contentObject.assignment;
//       console.log('Step 1: Assignment textual content generated:', assignmentDetails.title);

//       // --- Agent Step 2: Generate Starter Code ---
//       console.log('Step 2: Generating starter code...');
//       const starterCodePrompt = this.buildStarterCodePrompt(assignmentDetails, payload.difficulty);
//       const { object: starterCodeObject } = await generateObject({
//         model: this.contentModel, // Flash is fine for starter code
//         schema: StarterCodeSchema,
//         prompt: starterCodePrompt,
//         maxTokens: 1024,
//       });
//       console.log('Step 2: Starter code generated.');

//       // --- Agent Step 3: Generate Solution Code (in chunks) ---
//       console.log('Step 3: Generating solution code...');
//       const solutionCodePrompt = this.buildSolutionCodePrompt(
//         assignmentDetails,
//         payload.difficulty,
//       );

//       const { object: solutionCodeObject } = await generateObject({
//         model: this.solutionModel, // Use dedicated model, potentially more powerful
//         schema: SolutionCodeSchema,
//         prompt: solutionCodePrompt,
//         maxTokens: 4096, // Ensure enough tokens
//         temperature: 0.1, // MODIFIED: Lower temperature for more deterministic and correct code
//       });
//       const solutionCode = solutionCodeObject.solutionCodeChunks.join('\n');
//       console.log('Step 3: Solution code generated and assembled.');

//       // Basic sanity check (can be expanded)
//       if (
//         !solutionCode.includes('public class Main') ||
//         !solutionCode.includes('public static void main')
//       ) {
//         console.warn(
//           'Generated solution code might be missing Main class or main method structure.',
//         );
//         // For a robust system, you might throw an error here or attempt a retry.
//       }

//       return {
//         language: 'java',
//         subject: '',
//         title: assignmentDetails.title,
//         description: assignmentDetails.description,
//         points: payload.points,
//         instructions: assignmentDetails.instructions,
//         starterCode: starterCodeObject.starterCode,
//         solutionCode: solutionCode,
//         requiresCommandLineArgs: assignmentDetails.requiresCommandLineArgs,
//         testCases: assignmentDetails.testCases,
//       };
//     } catch (error) {
//       console.error('Error during AI generation process:', error);
//       if (error instanceof Error && 'message' in error && error.message.includes('Zod')) {
//         console.error('Zod validation error details:', (error as any).cause?.issues || error);
//       }
//       // You might want to log the specific step that failed if you break down try-catches further.
//       throw new Error(
//         'Failed to generate complete assignment content. An error occurred in one of the generation steps.',
//       );
//     }
//   }

//   private static buildContentPrompt(payload: GenContentPayload): string {
//     // (Content prompt from previous correct answer - no changes needed here for solution code issue)
//     return `You are an expert Java programming instructor designing lab assignments for BCA (Bachelor of Computer Applications) students.
//     These students have prior programming experience with the C language and are currently learning Java.
//     The assignment should be suitable for a lab session.

//     Focus ONLY on the textual content: title, description, learning objectives, detailed instructions, command-line argument requirement, and test cases.
//     Code will be generated in a separate step.

//     Assignment Topic/Title: ${payload.title}
//     Difficulty Level: ${payload.difficulty} (Keep in mind their C background and current Java learning stage)
//     Points: ${payload.points}
//     Additional Requirements from User: ${payload.additionalRequirements || 'None'}

//     Based on the above, provide the following:
//     1.  **Title**: A clear and descriptive title for the assignment.
//     2.  **Description**: A concise (2-3 lines, max 300 characters) overview of the assignment's goal.
//     3.  **Learning Objectives**: 2-4 specific, actionable learning objectives students should achieve. Examples:
//         *   "Understand and use the Java Scanner class for user input."
//         *   "Implement conditional statements (if-else) in Java."
//         *   "Practice writing a simple Java program with a Main class and main method."
//         *   "Learn to parse string input into numerical types in Java."
//     4.  **Instructions**:
//         This is the most critical part for your BCA students. Provide comprehensive, step-by-step instructions in Markdown format.
//         The instructions MUST be tailored for students transitioning from C to Java.
//         Structure the instructions clearly, perhaps using subheadings like:
//         *   **Problem Statement**: Clearly define what the program needs to do.
//         *   **Java Specifics to Consider**: Briefly highlight any Java classes, methods, or concepts that are particularly relevant for this problem and might be new or different from C (e.g., "For input, you'll use the \`java.util.Scanner\` class, which is different from C's \`scanf\`.").
//         *   **Input Specification**: Detail the exact format of the input. Specify if input comes from command-line arguments or console (Scanner). Mention data types.
//         *   **Output Specification**: Describe the exact format of the expected output. Include examples if helpful.
//         *   **Constraints/Requirements**: Any specific limitations (e.g., "Do not use arrays for this problem," or "Your class must be named Main"). Mention any specific Java methods or structures they should try to use to meet the learning objectives.
//         *   **Hints & Tips (Optional but Recommended for this audience)**: Guide them on how to approach the problem. Point out common pitfalls or differences from C. Example: "Remember, in Java, string comparison uses the .equals() method, not '==' like for primitive types or C strings." Example: "Make sure your main method signature is \`public static void main(String[] args)\`."
//         *   Do NOT include a top-level Markdown heading like "# Instructions" in the instructions string itself; use subheadings within the instructions.
//     5.  **Requires Command Line Arguments**: A boolean value. True if inputs are from 'String[] args', false for 'Scanner'.
//     6.  **Test Cases**: 3 to 5 diverse test cases. For each:
//         *   'title', 'description', 'input', 'expectedOutput' (can be empty string "", numerical/boolean as string).
//         *   'isHidden': Mix of true/false.

//     Ensure the assignment, especially the instructions and difficulty, is appropriate for BCA students familiar with C and learning Java.
//     The instructions must be very clear, actionable, and help bridge their C knowledge to Java concepts.
//     Do NOT generate any Java code in this step.`;
//   }

//   private static buildStarterCodePrompt(assignment: AssignmentContent, difficulty: string): string {
//     // (Starter code prompt from previous correct answer - no changes needed here)
//     const learningObjectivesText = assignment.learningObjectives.join(', ');
//     return `You are an expert Java code generator.
//     Based on the following assignment details, generate ONLY the Java starter code.
//     This is for BCA students learning Java with a C background.

//     Assignment Title: ${assignment.title}
//     Assignment Description: ${assignment.description}
//     Learning Objectives: ${learningObjectivesText}
//     Requires Command Line Args: ${assignment.requiresCommandLineArgs}
//     Instructions Summary (for context): ${assignment.instructions.substring(0, 300)}...

//     Difficulty: ${difficulty}

//     IMPORTANT JAVA STARTER CODE REQUIREMENTS:
//     - The main class MUST be named "Main" (public class Main).
//     - Include a public static void main(String[] args) method.
//     - If NOT using command-line arguments (i.e., requiresCommandLineArgs is false), import java.util.Scanner.
//     - The starter code should be ABSOLUTELY MINIMAL.
//     - Provide only the class structure, the main method, essential imports, and brief "TODO" comments indicating where the student should write their code.
//     - DO NOT include any pre-written logic, helper methods, or substantial implementation details. Students should build from this bare skeleton.

//     Example of minimal starter code if Scanner is needed:
//     \`\`\`java
//     import java.util.Scanner; // Or other necessary imports

//     public class Main {
//         public static void main(String[] args) {
//             // TODO: Create a Scanner object to read input from the console.
//             // TODO: Read the required input(s) using appropriate Scanner methods (e.g., nextInt(), nextLine()).
//             // TODO: Implement the core logic of the assignment.
//             // TODO: Print the result to the console as specified.
//             // TODO: Close the Scanner object if you opened one.
//         }
//     }
//     \`\`\`

//     Example if command line args are used:
//     \`\`\`java
//     public class Main {
//         public static void main(String[] args) {
//             // TODO: Check if the correct number of command-line arguments is provided.
//             // TODO: Parse command-line arguments from the 'args' array (e.g., Integer.parseInt(args[0])).
//             // TODO: Implement the core logic of the assignment.
//             // TODO: Print the result to the console as specified.
//         }
//     }
//     \`\`\`

//     Generate ONLY the starter code string.`;
//   }

//   // MODIFIED: Significantly enhanced solution code prompt for correctness
//   private static buildSolutionCodePrompt(
//     assignment: AssignmentContent,
//     difficulty: string,
//   ): string {
//     const testCasesSummary = assignment.testCases
//       .map(
//         (tc, index) =>
//           `Test Case ${index + 1} (${tc.title}):\n  Input: ${tc.input}\n  Expected Output: "${tc.expectedOutput}"\n  Description: ${tc.description}`,
//       )
//       .join('\n\n');
//     const learningObjectivesText = assignment.learningObjectives.join('; ');

//     return `You are an expert Java solution generator. Your primary goal is to produce a **100% correct, complete, and compilable Java solution** that precisely meets all requirements.
//     This solution is for an assignment targeting BCA students learning Java with a C background.
//     The code should be clear, well-commented, and demonstrate good basic Java practices for this level.

//     **CRITICAL REQUIREMENTS FOR THE GENERATED SOLUTION CODE:**
//     1.  **MUST BE A COMPLETE \`Main.java\` FILE:** The generated code, when chunks are combined, MUST form a fully functional \`Main.java\` file.
//     2.  **\`public class Main\` WRAPPER IS MANDATORY:** The entire Java code, including imports (if any, outside the class) and the \`main\` method, MUST be enclosed within \`public class Main { ... }\`. No exceptions.
//     3.  **\`public static void main(String[] args)\` IS MANDATORY:** This exact method signature must be present as the entry point.
//     4.  **PASS ALL TEST CASES:** The solution MUST correctly process all provided test cases and produce the exact expected output for each.
//     5.  **FOLLOW ALL INSTRUCTIONS:** The solution must adhere to every detail mentioned in the assignment instructions (input/output format, constraints, logic).
//     6.  **COMPILABLE AND RUNNABLE:** The code must compile without errors and run correctly.
//     7.  **HANDLE INPUTS CORRECTLY:** Use command-line arguments (\`args\`) or \`java.util.Scanner\` as specified by \`requiresCommandLineArgs\`. If using Scanner, import it and close it.

//     Assignment Title: ${assignment.title}
//     Learning Objectives: ${learningObjectivesText}
//     Requires Command Line Args: ${assignment.requiresCommandLineArgs}
//     Difficulty: ${difficulty}

//     Full Assignment Instructions (Adhere to these strictly):
//     ---
//     ${assignment.instructions}
//     ---

//     Detailed Test Cases (Your solution MUST pass all of them):
//     ---
//     ${testCasesSummary}
//     ---

//     **OUTPUT FORMAT (Array of Code Chunks):**
//     -   The output MUST be an array of strings. Each string is a logical chunk of the Java code.
//     -   Concatenating these chunks (with newlines) MUST result in a single, valid \`Main.java\` file.
//     -   The first chunk should typically contain imports. The class definition \`public class Main {\` should start in an early chunk. The final chunk must correctly close all braces, especially the main class brace \`}\`.

//     **Example of Correct Chunked Output Structure:**
//     \`\`\`json
//     [
//       "import java.util.Scanner;\\nimport java.util.ArrayList; // Example: only if needed",
//       "// Any other necessary imports here\\n",
//       "public class Main {\\n",
//       "    public static void main(String[] args) {\\n",
//       "        // Determine input method based on: ${assignment.requiresCommandLineArgs}\\n",
//       "        if (!${assignment.requiresCommandLineArgs}) {\\n",
//       "            Scanner scanner = new Scanner(System.in);\\n",
//       "            // --- Your main logic using scanner --- \\n",
//       "            // Example: Read an integer\\n",
//       "            // int number = scanner.nextInt();\\n",
//       "            // System.out.println(\\"Number was: \\" + number);\\n",
//       "            scanner.close();\\n",
//       "        } else {\\n",
//       "            // --- Your main logic using command line args (args array) --- \\n",
//       "            // Example: Check number of args\\n",
//       "            // if (args.length > 0) { System.out.println(\\"First arg: \\" + args[0]); } \\n",
//       "        }\\n",
//       "        // Ensure output matches expectedOutput for given inputs from test cases.\\n",
//       "    }\\n",
//       "\\n",
//       "    // Optional: Helper methods can be defined here, WITHIN the Main class\\n",
//       "    // private static String exampleHelper(String data) { return \\"Helper: \\" + data; }\\n",
//       "}\\n"
//     ]
//     \`\`\`

//     **MENTAL CHECKLIST BEFORE GENERATING:**
//     - Is the entire code wrapped in \`public class Main { ... }\`?
//     - Is there a \`public static void main(String[] args)\` method?
//     - Does the logic correctly handle all aspects of the instructions?
//     - Will this code pass *every single* test case provided?
//     - Are inputs (Scanner/args) handled as specified?
//     - Is the code broken into logical chunks for the array output?

//     Generate the robust, correct, and complete Java solution code that fulfills ALL above requirements.
//     If a test case expects no output (empty string "" for 'expectedOutput'), the solution code's main method should simply not print anything to standard output for that specific input scenario.
//     `;
//   }
// }
