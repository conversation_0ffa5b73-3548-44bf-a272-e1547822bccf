import type { Payload } from 'payload';
import type { Assignment } from '@/payload-types';
import type { GeneratedAssignmentContent } from '@/types/assignment';

export class AssignmentService {
  constructor(private payload: Payload) {}

  async findById(id: number) {
    return await this.payload.findByID({
      collection: 'assignments',
      id,
    });
  }

  async createAssignment(
    content: GeneratedAssignmentContent,
    difficulty: string,
    tenantId: number,
  ) {
    const dueDate = this.calculateDueDate(7);
    const language = content.language as 'java' | 'c';

    return await this.payload.create({
      collection: 'assignments',
      data: {
        title: content.title,
        description: content.description,
        points: content.points,
        subject: content.subject as number,
        instructions: content?.instructions?.replace(/^## Instructions\s*\n*/, ''),
        testsRequirement: content.testsRequirement,
        difficulty: difficulty as Assignment['difficulty'],
        resources: content.resources,
        hints: content.hints,
        language: language,
        tenant: tenantId,
        dueDate,
        // requiresCommandLineArgs: content.requiresCommandLineArgs,
        starterCode: {
          [language]: content.starterCode,
        },
        solutionCode: {
          [language]: content.solutionCode,
        },
        // javaTestCases: language === 'java' ? content.testCases : [],
        // cTestCases: language === 'c' ? content.testCases : [],
      },
    });
  }

  private calculateDueDate(daysFromNow: number = 7): string {
    const dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + daysFromNow);
    return dueDate.toISOString().split('T')[0];
  }
}
