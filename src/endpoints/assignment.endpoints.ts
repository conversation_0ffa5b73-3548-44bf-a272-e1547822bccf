import type { Endpoint } from 'payload';
import { AssignmentController } from '@/controllers/assignment.controller';
import { SubmissionController } from '../controllers/submission.controller';

export const endpoints: Omit<Endpoint, 'root'>[] = [
  {
    path: '/:id/submit',
    method: 'post',
    handler: SubmissionController.handleSubmission,
  },
  {
    path: '/generate',
    method: 'post',
    handler: AssignmentController.handleGeneration,
  },
  {
    path: '/:id/regenerate-test-scenarios',
    method: 'post',
    handler: AssignmentController.handleTestScenariosRegeneration,
  },
  {
    path: '/:id/download-report',
    method: 'get',
    handler: SubmissionController.handleDownloadReport,
  },
];
