import { createLocalStore } from '@/lib/local-store';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

type CopiedContentStore = {
  copiedContent: Map<string, Set<string>>;
  setCopied: (group: string, value: string) => void;
  resetCopied: () => void;
};

export const useCopiedContentStore = create<CopiedContentStore>()(
  persist(
    (set, get) => ({
      copiedContent: get()?.copiedContent || new Map(),

      setCopied: (group, value) =>
        set((state) => {
          const mapCopy = new Map(state.copiedContent);
          const existingSet = new Set(mapCopy.get(group) || []);
          existingSet.add(value);
          mapCopy.set(group, existingSet);
          return { copiedContent: mapCopy };
        }),

      resetCopied: () => set({ copiedContent: new Map() }),
    }),
    createLocalStore({ name: 'copied-content' }),
  ),
);
