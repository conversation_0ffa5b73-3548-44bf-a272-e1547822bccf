export interface TestCase {
  input: string;
  expected: string;
}

export interface ExecutionPayload {
  language_id?: number;
  sourceCode: string;
  testCases: TestCase[];
  useCommandLineArguments: boolean;
}

export interface ExecutionResult {
  token: string;
  status: string;
  output: string;
  error: string | null;
  testCase: TestCase;
  passed: boolean;
  executionTime: string;
  memory: number;
}
