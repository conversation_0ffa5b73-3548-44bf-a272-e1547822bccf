import { ExecutionPayload, ExecutionResult } from './types';
import { toBase64 } from './utils';

const BASE_URL = 'https://judge0-extra-ce.p.rapidapi.com/submissions';

const API_KEY = '**************************************************';

interface SubmissionRequest {
  language_id: number;
  source_code: string;
  expected_output: string;
  stdin?: string;
  command_line_arguments?: string;
}

interface BatchResult {
  token: string;
}

interface Judge0Result {
  stdout?: string;
  stderr?: string;
  status?: { description: string };
  time: string;
  memory: number;
}

async function executeCodeWithJudge0(payload: ExecutionPayload): Promise<ExecutionResult[]> {
  // Default to Java if no language_id is provided
  const language_id = payload.language_id || 4;

  // Generate submission objects
  const generateSubmissions = ({
    testCases,
    sourceCode,
    useCommandLineArguments,
  }: ExecutionPayload): SubmissionRequest[] => {
    const encodedSource = toBase64(sourceCode);

    return testCases.map(({ input, expected }) => ({
      language_id,
      source_code: encodedSource,
      expected_output: toBase64(expected),
      ...(useCommandLineArguments ? { command_line_arguments: input } : { stdin: toBase64(input) }),
    }));
  };

  // API Call to fetch result by token
  async function getSubmissionResult(token: string): Promise<Judge0Result> {
    const url = `${BASE_URL}/${token}?fields=*`;
    const options: RequestInit = {
      method: 'GET',
      headers: {
        'x-rapidapi-key': API_KEY,
        'x-rapidapi-host': new URL(BASE_URL).host,
      },
    };

    const response = await fetch(url, options);
    return response.json() as Promise<Judge0Result>;
  }

  // Create submissions
  const submissions = generateSubmissions(payload);
  const url = `${BASE_URL}/batch?base64_encoded=true&fields=*`;

  const options: RequestInit = {
    method: 'POST',
    headers: {
      'x-rapidapi-key': API_KEY,
      'x-rapidapi-host': new URL(BASE_URL).host,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ submissions }),
  };

  try {
    const response = await fetch(url, options);
    const batchResult: BatchResult[] = await response.json();

    // Wait for all results and format them
    const results: ExecutionResult[] = await Promise.all(
      batchResult.map(async (submission: BatchResult, index: number) => {
        const output = await getSubmissionResult(submission.token);

        // Check if the expected output and stdout match
        const actualOutput = output.stdout ? output.stdout : '';
        const expectedOutput = payload.testCases[index].expected;

        // Normalize line endings and trim whitespace
        const normalizedActual = actualOutput.replace(/\r\n/g, '\n').trim();
        const normalizedExpected = expectedOutput.replace(/\r\n/g, '\n').trim();

        const result: ExecutionResult = {
          token: submission.token,
          status: output.status?.description ?? 'Unknown',
          output: actualOutput,
          error: output.stderr ? output.stderr : null,
          testCase: payload.testCases[index],
          passed: normalizedActual === normalizedExpected,
          executionTime: output.time,
          memory: output.memory,
        };
        return result;
      }),
    );

    return results;
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Judge0 API Error: ${error.message}`);
    }
    throw new Error('Unknown Judge0 API Error occurred');
  }
}

// Example usage:
const payload: ExecutionPayload = {
  useCommandLineArguments: true,
  sourceCode: `
  public class Main {
      public static void main(String[] args) {
          int a = Integer.parseInt(args[0]);
          int b = Integer.parseInt(args[1]);
          System.out.println(a + b);
      }
  }
    `,
  testCases: [
    { input: '2 3', expected: '5' },
    { input: '10 5', expected: '15' },
  ],
};

executeCodeWithJudge0(payload)
  .then((results) => {
    console.log('Results:', JSON.stringify(results, null, 2));

    // Check if all test cases passed
    const allPassed = results.every((result) => result.passed);
    console.log(`All tests passed: ${allPassed}`);
  })
  .catch((error) => {
    console.error('Execution failed:', error);
  });
