/**
 * Test script to verify dynamic model switching works
 */

import { ModelManager } from './model-manager';

export function testDynamicModels() {
  console.log('=== Testing Dynamic Model System ===');

  // Test 1: Get current model
  console.log('1. Current model:', ModelManager.getCurrentModel());

  // Test 2: Switch to different OpenRouter models
  const testModels = [
    'openai/gpt-4o',
    'anthropic/claude-3.5-sonnet',
    'google/gemini-2.0-flash-exp',
    'meta-llama/llama-3.1-70b-instruct',
    'mistralai/mistral-7b-instruct',
    'cohere/command-r-plus',
    'custom/my-model', // Test custom model name
  ];

  testModels.forEach((model, index) => {
    try {
      console.log(`${index + 2}. Switching to: ${model}`);
      ModelManager.switchModel(model);
      console.log(`   ✅ Success! Current model: ${ModelManager.getCurrentModel()}`);
    } catch (error) {
      console.log(`   ❌ Error: ${error}`);
    }
  });

  // Test 3: Test validation
  console.log('\n=== Testing Validation ===');
  const invalidModels = ['', '   ', null, undefined];

  invalidModels.forEach((invalidModel, index) => {
    try {
      console.log(`${index + 1}. Testing invalid model: ${JSON.stringify(invalidModel)}`);
      ModelManager.switchModel(invalidModel as any);
      console.log(`   ❌ Should have failed!`);
    } catch (error) {
      console.log(`   ✅ Correctly rejected: ${error}`);
    }
  });

  // Test 4: Get common models
  console.log('\n=== Common Models ===');
  const commonModels = ModelManager.getCommonModels();
  console.log('Common models:', commonModels);

  // Test 5: Validation function
  console.log('\n=== Validation Tests ===');
  const testValidation = ['openai/gpt-4o', 'custom/model', '', '   ', 'valid-model-name'];

  testValidation.forEach((model) => {
    const isValid = ModelManager.isValidModel(model);
    console.log(`"${model}" is valid: ${isValid}`);
  });

  console.log('\n=== Test Complete ===');
}

// Export for use in browser console or Node.js
if (typeof window !== 'undefined') {
  (window as any).testDynamicModels = testDynamicModels;
}
