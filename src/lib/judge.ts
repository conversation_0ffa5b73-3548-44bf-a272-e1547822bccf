export const API_KEY = '5c337504ffmshdb4bf13eb52a707p1aeacejsnb4b2cab04f03';
export const BASE_URL = 'https://judge0-extra-ce.p.rapidapi.com/submissions';

// Common Judge0 language IDs - these may vary between Judge0 CE and Judge0 Extra CE
const JAVA_LANG_ID = 4; // This might need to be updated based on the Judge0 instance
// const C_LANG_ID = 1;

// Alternative Java language IDs to try if 4 doesn't work
// const ALTERNATIVE_JAVA_IDS = [62, 27, 28]; // Common Java IDs in different Judge0 versions

export const language_id = JAVA_LANG_ID;

export { compileCode } from './compile';

const toBase64 = (str: string): string => Buffer.from(str).toString('base64');
const fromBase64 = (str: string): string => Buffer.from(str, 'base64').toString('utf-8');

type Testcase = {
  input: string;
  expectedOutput: string;
  tolerance?: number;
};

type SubmissionPayload = {
  sourceCode: string;
  // commandLineArguments: boolean;
  testcases: Testcase[];
};

// Liberal comparison function that handles newline differences
const liberalCompare = (actual: string, expected: string, tolerance?: number): boolean => {
  // Handle numerical tolerance
  if (typeof expected === 'number' && tolerance) {
    return Math.abs(Number(actual) - expected) <= tolerance;
  }

  // Normalize whitespace for comparison
  const normalizeOutput = (str: string): string => {
    return str
      .replace(/\r\n/g, '\n') // Convert Windows line endings to Unix
      .replace(/\r/g, '\n') // Convert Mac line endings to Unix
      .replace(/\n+/g, '\n') // Replace multiple newlines with single
      .replace(/[ \t]+/g, ' ') // Replace multiple spaces/tabs with single space
      .split('\n') // Split into lines
      .map((line) => line.trim()) // Trim each line
      .filter((line) => line.length > 0) // Remove empty lines
      .join('\n') // Join back with single newlines
      .trim(); // Final trim
  };

  const normalizedActual = normalizeOutput(actual);
  const normalizedExpected = normalizeOutput(expected);

  // Debug logging
  console.log('=== COMPARISON DEBUG ===');
  console.log('Original Actual:', JSON.stringify(actual));
  console.log('Original Expected:', JSON.stringify(expected));
  console.log('Normalized Actual:', JSON.stringify(normalizedActual));
  console.log('Normalized Expected:', JSON.stringify(normalizedExpected));
  console.log('Match:', normalizedActual === normalizedExpected);
  console.log('========================');

  return normalizedActual === normalizedExpected;
};

// Alternative: Even more liberal comparison that ignores all whitespace differences
const veryLiberalCompare = (actual: string, expected: string, tolerance?: number): boolean => {
  // Handle numerical tolerance
  if (typeof expected === 'number' && tolerance) {
    return Math.abs(Number(actual) - expected) <= tolerance;
  }

  // Extract only the meaningful content (letters, numbers, punctuation)
  const extractContent = (str: string): string => {
    return str
      .replace(/\s+/g, ' ') // Replace all whitespace with single space
      .trim() // Trim
      .toLowerCase(); // Optional: case insensitive
  };

  const contentActual = extractContent(actual);
  const contentExpected = extractContent(expected);

  return contentActual === contentExpected;
};

// Token-based comparison for even more flexibility
const tokenBasedCompare = (actual: string, expected: string, tolerance?: number): boolean => {
  // Handle numerical tolerance
  if (typeof expected === 'number' && tolerance) {
    return Math.abs(Number(actual) - expected) <= tolerance;
  }

  // Split into tokens (words/numbers) and compare
  const tokenize = (str: string): string[] => {
    return str
      .replace(/\s+/g, ' ')
      .trim()
      .split(/\s+/)
      .filter((token) => token.length > 0);
  };

  const actualTokens = tokenize(actual);
  const expectedTokens = tokenize(expected);

  if (actualTokens.length !== expectedTokens.length) {
    return false;
  }

  return actualTokens.every((token, index) => token === expectedTokens[index]);
};

// Fetch result for a single token with polling
const getSubmissionResult = async (token: string, retries = 5, delay = 1000) => {
  const url = `${BASE_URL}/${token}?base64_encoded=true&fields=stdout,stderr,status,compile_output`;

  const options = {
    method: 'GET',
    headers: {
      'x-rapidapi-key': API_KEY,
      'x-rapidapi-host': new URL(BASE_URL).host,
    },
  };

  for (let i = 0; i < retries; i++) {
    try {
      const response = await fetch(url, options);
      if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
      const result = await response.json();

      // If status is "In Queue" (1) or "Processing" (2), retry
      if (result.status?.id === 1 || result.status?.id === 2) {
        await new Promise((resolve) => setTimeout(resolve, delay));
        continue;
      }

      return result;
    } catch (error) {
      console.error(`Error fetching result for token ${token}:`, error);
      if (i === retries - 1) throw error;
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }
};

// export const compileCode = async (sourceCode: string, languageId: number = 4) => {
//   const url = `${BASE_URL}?base64_encoded=true&fields=stdout,stderr,status,compile_output`;
//   const options = {
//     method: 'POST',
//     headers: {
//       'x-rapidapi-key': API_KEY,
//       'x-rapidapi-host': new URL(BASE_URL).host,
//       'Content-Type': 'application/json',
//     },
//     body: JSON.stringify({
//       language_id: languageId,
//       source_code: toBase64(sourceCode),
//       stdin: toBase64(''), // Empty input for compilation check
//     }),
//   };

//   try {
//     const response = await fetch(url, options);
//     if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
//     const { token } = await response.json();

//     // Get compilation result
//     const result = await getSubmissionResult(token);

//     // Check for compilation errors
//     if (result.compile_output) {
//       const compileOutput = fromBase64(result.compile_output);
//       if (compileOutput.trim()) {
//         throw new Error(`Compilation Error: ${compileOutput}`);
//       }
//     }

//     // Check for other errors that might indicate compilation issues
//     if (result.status?.id === 6) {
//       // Compilation Error status
//       const errorMessage = result.stderr ? fromBase64(result.stderr) : 'Unknown compilation error';
//       throw new Error(`Compilation Error: ${errorMessage}`);
//     }

//     return {
//       success: true,
//       status: result.status?.description || 'Unknown',
//       compileOutput: result.compile_output ? fromBase64(result.compile_output) : null,
//     };
//   } catch (error) {
//     console.error('Compilation check failed:', error);
//     throw error;
//   }
// };

export const submitCode = async ({
  submissions,
  languageId,
  comparisonMode = 'liberal', // 'strict', 'liberal', 'very-liberal', 'token-based'
}: {
  submissions: SubmissionPayload[];
  languageId?: number;
  comparisonMode?: 'strict' | 'liberal' | 'very-liberal' | 'token-based';
}) => {
  const outputArr = [];
  const commandLineArguments = false;

  // Create a submission for each test case
  const parsedSubmissions = submissions.flatMap((submission) => {
    const { testcases, sourceCode } = submission;

    // For Java, ensure the class name matches the expected pattern for Judge0
    let processedSourceCode = sourceCode;
    if (languageId === JAVA_LANG_ID) {
      console.log(`[JAVA_CLASS_FIX] Processing Java source code for Judge0 submission`);
      console.log(`[JAVA_CLASS_FIX] Original source code:\n${sourceCode}`);

      // Extract class name from source code (handles both public and non-public classes)
      const classNameMatch = sourceCode.match(/(?:public\s+)?class\s+(\w+)/);
      if (classNameMatch) {
        const currentClassName = classNameMatch[1];
        console.log(`[JAVA_CLASS_FIX] Found class name: "${currentClassName}"`);

        // If the class name is not "Main", we need to rename it for Judge0 compatibility
        // Judge0 expects the main class to be named "Main" for proper execution
        if (currentClassName !== 'Main') {
          console.log(
            `[JAVA_CLASS_FIX] Renaming class from "${currentClassName}" to "Main" for Judge0 compatibility`,
          );

          // Replace class declaration (handle both public and non-public classes)
          processedSourceCode = sourceCode.replace(
            new RegExp(`(?:public\\s+)?class\\s+${currentClassName}\\b`, 'g'),
            'public class Main',
          );

          // Also replace any constructor calls if they exist
          processedSourceCode = processedSourceCode.replace(
            new RegExp(`\\bnew\\s+${currentClassName}\\s*\\(`, 'g'),
            `new Main(`,
          );

          // Replace any static references to the class
          processedSourceCode = processedSourceCode.replace(
            new RegExp(`\\b${currentClassName}\\.`, 'g'),
            'Main.',
          );

          console.log(`[JAVA_CLASS_FIX] Class renaming completed successfully`);
          console.log(`[JAVA_CLASS_FIX] Processed source code:\n${processedSourceCode}`);
        } else {
          console.log(`[JAVA_CLASS_FIX] Class name is already "Main", no renaming needed`);
        }
      } else {
        console.log(
          `[JAVA_CLASS_FIX] WARNING: Could not find public class declaration in source code`,
        );
        console.log(`[JAVA_CLASS_FIX] Source code will be submitted as-is`);
      }

      // Final validation: ensure the processed code has the correct structure
      const hasMainClass = /public\s+class\s+Main\b/.test(processedSourceCode);
      const hasMainMethod = /public\s+static\s+void\s+main\s*\(\s*String\s*\[\s*\]\s*\w+\s*\)/.test(
        processedSourceCode,
      );

      console.log(`[JAVA_CLASS_FIX] Final validation:`);
      console.log(`[JAVA_CLASS_FIX] - Has 'public class Main': ${hasMainClass}`);
      console.log(
        `[JAVA_CLASS_FIX] - Has 'public static void main(String[] args)': ${hasMainMethod}`,
      );

      if (!hasMainClass || !hasMainMethod) {
        console.log(
          `[JAVA_CLASS_FIX] WARNING: Source code may not have correct structure for Judge0`,
        );
        console.log(
          `[JAVA_CLASS_FIX] This might cause 'Could not find or load main class Main' error`,
        );
      }
    }

    const submissionData = testcases.map((testcase) => ({
      language_id: languageId,
      source_code: toBase64(processedSourceCode),
      stdin: commandLineArguments ? null : toBase64(testcase.input),
      command_line_arguments: commandLineArguments ? testcase.input : null,
      expected_output: toBase64(testcase.expectedOutput),
    }));

    // Debug: Log what we're sending to Judge0
    if (languageId === JAVA_LANG_ID) {
      console.log(`[JAVA_CLASS_FIX] Final source code being sent to Judge0:`);
      console.log('='.repeat(50));
      console.log(fromBase64(submissionData[0].source_code));
      console.log('='.repeat(50));
      console.log(`[JAVA_CLASS_FIX] Language ID: ${languageId}`);
      console.log(`[JAVA_CLASS_FIX] Submission data:`, JSON.stringify(submissionData[0], null, 2));
    }

    return submissionData;
  });

  const url = `${BASE_URL}/batch?base64_encoded=true&fields=token`;
  const options = {
    method: 'POST',
    headers: {
      'x-rapidapi-key': API_KEY,
      'x-rapidapi-host': new URL(BASE_URL).host,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ submissions: parsedSubmissions }),
  };

  try {
    const response = await fetch(url, options);
    if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
    const tokens = await response.json();

    // Process each token and validate against expected output
    let testcaseIndex = 0;
    for (let submissionIndex = 0; submissionIndex < submissions.length; submissionIndex++) {
      const submission = submissions[submissionIndex];
      for (const testcase of submission.testcases) {
        const { token } = tokens[testcaseIndex];
        console.log(
          `\nProcessing submission ${submissionIndex + 1}, testcase ${testcaseIndex + 1}, token: ${token}`,
        );
        const result = await getSubmissionResult(token);

        // Debug: Log the full Judge0 response for Java submissions
        if (languageId === JAVA_LANG_ID) {
          console.log(`[JAVA_CLASS_FIX] Judge0 response for token ${token}:`);
          console.log(
            `[JAVA_CLASS_FIX] Status: ${result.status?.description || 'Unknown'} (ID: ${result.status?.id})`,
          );
          if (result.compile_output) {
            console.log(`[JAVA_CLASS_FIX] Compile output: ${fromBase64(result.compile_output)}`);
          }
          if (result.stderr) {
            console.log(`[JAVA_CLASS_FIX] Stderr: ${fromBase64(result.stderr)}`);
          }
          if (result.stdout) {
            console.log(`[JAVA_CLASS_FIX] Stdout: ${fromBase64(result.stdout)}`);
          }
        }

        const actualOutput = result.stdout ? fromBase64(result.stdout) : '';
        const expectedOutput = testcase.expectedOutput;

        let isPassed = false;

        switch (comparisonMode) {
          case 'strict':
            isPassed = actualOutput.trim() === expectedOutput.trim();
            break;
          case 'liberal':
            isPassed = liberalCompare(actualOutput, expectedOutput, testcase.tolerance);
            break;
          case 'very-liberal':
            isPassed = veryLiberalCompare(actualOutput, expectedOutput, testcase.tolerance);
            break;
          case 'token-based':
            isPassed = tokenBasedCompare(actualOutput, expectedOutput, testcase.tolerance);
            break;
          default:
            isPassed = liberalCompare(actualOutput, expectedOutput, testcase.tolerance);
        }

        const status = isPassed ? 'PASS' : 'FAIL';

        console.log(`Status: ${result.status?.description || 'Unknown'}`);
        console.log(`Input: ${testcase.input || 'No input'}`);
        console.log(`Actual Output: ${JSON.stringify(actualOutput)}`);
        console.log(`Expected Output: ${JSON.stringify(expectedOutput)}`);
        console.log(`Pass: ${isPassed ? '✅' : '❌'}`);

        outputArr.push({
          input: testcase.input,
          actual: actualOutput,
          expected: expectedOutput,
          status,
        });

        if (result.stderr) console.error(`Error: ${fromBase64(result.stderr)}`);
        if (result.compile_output)
          console.error(`Compile Output: ${fromBase64(result.compile_output)}`);

        testcaseIndex++;
      }
    }
    return outputArr;
  } catch (error) {
    console.error('Batch submission failed:', error);
    return [];
  }
};
