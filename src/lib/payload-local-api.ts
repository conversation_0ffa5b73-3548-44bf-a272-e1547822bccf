import { headers } from 'next/headers';
import { BasePayload, getPayload } from 'payload';
import config from '@/payload.config';
import { redirect } from 'next/navigation';

let payloadInstance: BasePayload;

export const getPayloadInstance = async () => {
  if (!payloadInstance) {
    const payloadConfig = await config;
    payloadInstance = await getPayload({ config: payloadConfig });
  }
  return payloadInstance;
};

export const getUser = async () => {
  try {
    const payload = await getPayloadInstance();
    const user = await payload.auth({ headers: await headers() });
    if (!user) redirect('/admin/login');
    return { user };
  } catch (error) {
    console.error('Error authenticating user:', error);
    throw new Error('Error authenticating user');
  }
};
