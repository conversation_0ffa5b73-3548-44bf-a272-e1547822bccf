import { exec } from 'child_process';
import { unlink, writeFile } from 'fs/promises';
import { tmpdir } from 'os';
import { join } from 'path';
import { promisify } from 'util';

const execAsync = promisify(exec);

export const compileCode = async (sourceCode: string, languageId: number = 4) => {
  const tempDir = tmpdir();
  let sourceFile: string;
  let outputFile: string;
  let command: string;
  let className: string | undefined; // For Java cleanup

  // Determine file extension and compilation command based on languageId
  if (languageId === 1) {
    // C compilation
    sourceFile = join(tempDir, `source_${Date.now()}.c`);
    outputFile = join(tempDir, `output_${Date.now()}`);
    command = `gcc ${sourceFile} -o ${outputFile}`;
  } else if (languageId === 4) {
    // Java compilation
    // Extract class name from source code (handles both public and non-public classes)
    const classNameMatch = sourceCode.match(/(?:public\s+)?class\s+(\w+)/);
    className = classNameMatch ? classNameMatch[1] : `Main_${Date.now()}`;
    sourceFile = join(tempDir, `${className}.java`);
    // Note: Java compiler creates .class file with the actual class name from source code
    // This might be different from the .java filename if class names don't match
    outputFile = join(tempDir, `${className}.class`);
    command = `javac ${sourceFile}`;
  } else {
    throw new Error(
      `Unsupported languageId: ${languageId}. Only C (1) and Java (4) are supported.`,
    );
  }

  try {
    // Write source code to a temporary file
    await writeFile(sourceFile, sourceCode);

    // Execute compilation command
    const { stdout, stderr } = await execAsync(command);

    // Check for compilation errors
    if (stderr && stderr.trim()) {
      throw new Error(`Compilation Error: ${stderr}`);
    }

    // Clean up temporary files
    await unlink(sourceFile);
    if (languageId === 1) {
      // For C, clean up the output binary
      try {
        await unlink(outputFile);
      } catch (cleanupError) {
        console.warn('Failed to clean up output file:', cleanupError);
      }
    } else if (languageId === 4) {
      // For Java, clean up the .class file(s)
      // Java compiler creates .class files based on actual class names in source code
      // which might be different from the .java filename
      try {
        const { readdir } = await import('fs/promises');
        const files = await readdir(tempDir);
        const classFiles = files.filter((file) => file.endsWith('.class'));

        // Clean up all .class files that were created during compilation
        for (const classFile of classFiles) {
          try {
            await unlink(join(tempDir, classFile));
          } catch (individualCleanupError) {
            console.error(individualCleanupError);
            // Ignore individual cleanup errors - file might already be deleted
          }
        }
      } catch (dirReadError) {
        console.error(dirReadError);
        // If we can't read the directory, try the original approach
        try {
          await unlink(outputFile);
        } catch (cleanupError) {
          console.warn('Failed to clean up class file:', cleanupError);
        }
      }
    }

    return {
      success: true,
      status: 'Compiled successfully',
      compileOutput: stdout || null,
    };
  } catch (error: any) {
    // Clean up source file in case of error
    try {
      await unlink(sourceFile);
    } catch (cleanupError) {
      console.warn('Failed to clean up source file:', cleanupError);
    }

    console.error('Compilation check failed:', error);
    if (error.message.includes('Compilation Error')) {
      throw error;
    }
    throw new Error(`Compilation Error: ${error.message}`);
  }
};
