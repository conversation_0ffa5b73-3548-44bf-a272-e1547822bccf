export const getColor = (score: number | undefined | null): string => {
  if (score == null) return '';

  const colorMap: Record<string, string> = {
    '0': '#EF4444', // Red-500 (vibrant failure)
    '<50': '#F97316', // Orange-500 (moderate warning)
    '<75': '#FBBF24', // Yellow-400 (neutral caution)
    '<90': '#4ADE80', // Green-400 (approachable success)
    '>=90': '#15803D', // Green-700 (strong success, high contrast)
  };

  if (score === 0) return colorMap['0'];
  if (score < 50) return colorMap['<50'];
  if (score < 75) return colorMap['<75'];
  if (score < 90) return colorMap['<90'];
  return colorMap['>=90'];
};
