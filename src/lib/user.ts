import { User } from '@/payload-types';
import { DecodedToken } from '@/types/decoded-token';

export const getUserRole = (user?: User | DecodedToken | null): string => {
  try {
    if (user?.roles?.some((role) => role === 'admin')) {
      return 'admin';
    }
    return user?.tenants?.[0]?.roles?.[0] || 'student';
  } catch (error) {
    console.error('Error getting user role:', error);
    return 'student';
  }
};
