import { getCurrentModelName, setModel } from '@/mastra/config';

export const COMMON_MODELS = [
  'openai/gpt-4.1-nano',
  'openai/gpt-4o',
  'openai/gpt-4o-mini',
  'anthropic/claude-3.5-sonnet',
  'anthropic/claude-3-haiku',
  'google/gemini-2.0-flash-exp',
  'google/gemini-1.5-pro',
  'meta-llama/llama-3.1-8b-instruct',
  'meta-llama/llama-3.1-70b-instruct',
  'mistralai/mistral-7b-instruct',
  'cohere/command-r-plus',
] as const;

export type ModelName = string;

export class ModelManager {
  static switchModel(modelName: ModelName): void {
    if (!modelName || typeof modelName !== 'string' || modelName.trim() === '') {
      throw new Error('Model name must be a non-empty string');
    }

    setModel(modelName.trim());
    console.log(`Model switched to: ${modelName}`);
  }

  static getCurrentModel(): string {
    return getCurrentModelName();
  }

  static getCommonModels(): readonly string[] {
    return COMMON_MODELS;
  }

  static isValidModel(modelName: string): modelName is ModelName {
    return typeof modelName === 'string' && modelName.trim() !== '';
  }
}
