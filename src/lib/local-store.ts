import { createJSONStorage } from 'zustand/middleware';

export const createLocalStore = ({ name }: { name: string }) => {
  return {
    name,
    storage: createJSONStorage(() => {
      if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
        return localStorage;
      } else {
        return {
          getItem: () => null,
          setItem: () => {},
          removeItem: () => {},
        };
      }
    }),
  };
};
