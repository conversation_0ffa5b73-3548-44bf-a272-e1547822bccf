import { spawn } from 'node:child_process';
import { randomUUID } from 'crypto';
import { writeFile } from 'fs/promises';

/**
 * Interface for the result of a process execution.
 */
interface ProcessResult {
  stdout: string;
  stderr: string;
  code: number | null;
}

/**
 * Runs a command and captures stdout/stderr.
 * Resolves with { stdout, stderr, code }.
 */
function runProcess(command: string, args: string[]): Promise<ProcessResult> {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args);
    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data: Buffer) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data: Buffer) => {
      stderr += data.toString();
    });

    child.on('close', (code: number) => {
      resolve({ stdout, stderr, code });
    });

    child.on('error', (err: Error) => {
      reject(new Error(`${command} process failed: ${err.message}`));
    });
  });
}

/**
 * Interface for the parsed complexity line.
 */
interface ComplexityLine {
  cyclomaticComplexity: number;
  tokens: number;
  parameters: number;
  maxDepth: number;
  signature: string;
  startLine: number;
  endLine: number;
}

/**
 * Parses a lizard CSV line into a structured object.
 */
function parseComplexityLine(fields: string[]): ComplexityLine {
  return {
    cyclomaticComplexity: Number.parseInt(fields[1], 10) || 0,
    tokens: Number.parseInt(fields[2], 10) || 0,
    parameters: Number.parseInt(fields[3], 10) || 0,
    maxDepth: Number.parseInt(fields[4], 10) || 0,
    signature: (fields[8] || '').replace('( ', '(').replace(' ,', ','),
    startLine: Number.parseInt(fields[9], 10) || 0,
    endLine: Number.parseInt(fields[10], 10) || 0,
  };
}

/**
 * Get cyclomatic complexity using lizard.
 */
export async function getCyclomaticComplexity(sourceCode: string): Promise<ComplexityLine[]> {
  const filePath = `/tmp/${randomUUID()}.c`;
  await writeFile(filePath, sourceCode, 'utf-8');
  const { stdout, stderr, code } = await runProcess('./venv/bin/lizard', [filePath, '--csv']);

  if (code !== 0) {
    throw new Error(`Lizard exited with code ${code}.`);
  }

  if (stderr) {
    console.error('Lizard stderr:', stderr.trim());
  }

  const csvLines = stdout.trim().split('\n');
  const csvRegex = /,(?=(?:(?:[^"]*"){2})*[^"]*$)/;

  const parsedLines = csvLines
    .map((line) => line.split(csvRegex).map((s) => s.replace(/^"|"$/g, '')))
    .filter((fields) => /^\d+$/.test(fields[1]));

  return parsedLines.map(parseComplexityLine);
}
