import { LogEntry } from './types';

/**
 * Safely stringifies arguments for logging
 * Handles circular references, DOM elements, and special objects
 */
export function safeStringify(args: unknown[]): string {
  try {
    return args
      .map((arg) => {
        if (arg === null) return 'null';
        if (arg === undefined) return 'undefined';

        // Handle Error objects
        if (arg instanceof Error) {
          return `Error: ${arg.message}${arg.stack ? '\n' + arg.stack : ''}`;
        }

        try {
          // Handle DOM objects and special cases
          if (typeof arg === 'object') {
            // Check for DOM nodes and common browser objects
            if (
              typeof window !== 'undefined' &&
              // Node-like objects
              (arg instanceof Node ||
                arg instanceof Window ||
                // Other browser-specific objects
                arg instanceof Event ||
                arg instanceof XMLHttpRequest ||
                // Check for toString that indicates DOM object
                (arg.toString && /\[object HTML.*Element\]/.test(arg.toString())))
            ) {
              return '[DOM Object]';
            }

            // Handle objects and arrays with circular references
            const cache = new WeakSet();
            return JSON.stringify(
              arg,
              (key, value) => {
                if (typeof value === 'object' && value !== null) {
                  if (cache.has(value)) {
                    return '[Circular]';
                  }
                  cache.add(value);

                  // Handle special objects that might slip through
                  if (value instanceof RegExp) return value.toString();
                  if (value instanceof Date) return value.toISOString();
                  if (value instanceof Function) return '[Function]';
                }
                return value;
              },
              2,
            );
          }

          // Handle primitive types
          if (typeof arg === 'symbol') return arg.toString();
          if (typeof arg === 'function') return '[Function]';
          return String(arg);
        } catch {
          return '[Unserializable Object]';
        }
      })
      .join(' ');
  } catch {
    return '[Error during stringification]';
  }
}

/**
 * Sets up console and alert interceptors for observing events in the iframe
 */
export function setupInterceptors(
  iframeWindow: Window & {
    console: {
      log: (...args: unknown[]) => void;
      error: (...args: unknown[]) => void;
      warn: (...args: unknown[]) => void;
    };
  },
): {
  observedEvents: {
    console: {
      log: LogEntry[];
      error: LogEntry[];
      warn: LogEntry[];
    };
    alerts: string[];
  };
  lastAlertMessage: { value: string | null };
  cleanup: () => void;
} {
  // Store observed events
  const observedEvents = {
    console: {
      log: [] as LogEntry[],
      error: [] as LogEntry[],
      warn: [] as LogEntry[],
    },
    alerts: [] as string[],
  };

  // Track last alert message
  const lastAlertMessage = { value: null as string | null };

  // Store original functions to restore later
  const originalAlert = iframeWindow.alert;
  const originalConsole = { ...iframeWindow.console };

  // Override alert function
  iframeWindow.alert = (message?: unknown) => {
    const msgString = String(message ?? ''); // Handle undefined/null message
    lastAlertMessage.value = msgString;
    observedEvents.alerts.push(msgString);
    // Optionally call original alert for debugging, but usually unwanted in tests
    // originalAlert(message);
    console.info(`Intercepted alert: "${msgString}"`); // Log interception
  };

  // Override console functions
  iframeWindow.console.log = (...args: unknown[]) => {
    observedEvents.console.log.push({ timestamp: Date.now(), message: safeStringify(args) });
    originalConsole.log(...args); // Keep logging to the actual console
  };

  iframeWindow.console.error = (...args: unknown[]) => {
    observedEvents.console.error.push({ timestamp: Date.now(), message: safeStringify(args) });
    originalConsole.error(...args);
  };

  iframeWindow.console.warn = (...args: unknown[]) => {
    observedEvents.console.warn.push({ timestamp: Date.now(), message: safeStringify(args) });
    originalConsole.warn(...args);
  };

  // Cleanup function to restore original functions
  const cleanup = () => {
    iframeWindow.alert = originalAlert;
    iframeWindow.console = originalConsole;
  };

  return { observedEvents, lastAlertMessage, cleanup };
}
