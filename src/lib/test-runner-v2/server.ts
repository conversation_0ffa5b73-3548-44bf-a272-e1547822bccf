// import { Window } from 'happy-dom';
// import { TestResult, TestCases } from './types';
// import { runTestsInEnvironment, TestEnvironment } from './runner';

// /**
//  * Server-side test runner that uses happy-dom for virtualized DOM
//  *
//  * @param html - HTML content to test
//  * @param testCases - Array of test cases to execute
//  * @returns Promise resolving to an array of test results
//  */
// export async function runServerTests(html: string, testCases: TestCases): Promise<TestResult[]> {
//   try {
//     // Create a happy-dom window
//     // Create a happy-dom window
//     const window = new Window({
//       // Create a happy-dom window with proper typing
//     }) as Window & { alert: (message?: unknown) => void };

//     // Configure window properties
//     Object.assign(window, {
//       url: 'http://localhost',
//       width: 1024,
//       height: 768,
//     });

//     // Set HTML content
//     window.document.write(html);

//     // Create a custom implementation of console interceptor for happy-dom
//     const observedEvents: TestEnvironment['observedEvents'] = {
//       console: {
//         log: [],
//         error: [],
//         warn: [],
//       },
//       alerts: [],
//     };

//     // Track last alert message
//     const lastAlertMessage: TestEnvironment['lastAlertMessage'] = { value: null };

//     // Store original functions to restore later
//     const originalConsole = { ...window.console };
//     const originalAlert = window.alert;

//     // Override alert
//     window.alert = (message?: unknown) => {
//       const msgString = String(message ?? '');
//       lastAlertMessage.value = msgString;
//       observedEvents.alerts.push(msgString);
//       console.info(`[Server] Intercepted alert: "${msgString}"`);
//     };

//     // Override console methods
//     window.console.log = (...args: unknown[]) => {
//       const message = args.map((arg) => String(arg)).join(' ');
//       observedEvents.console.log.push({ timestamp: Date.now(), message });
//       originalConsole.log(...args);
//     };

//     window.console.error = (...args: unknown[]) => {
//       const message = args.map((arg) => String(arg)).join(' ');
//       observedEvents.console.error.push({ timestamp: Date.now(), message });
//       originalConsole.error(...args);
//     };

//     window.console.warn = (...args: unknown[]) => {
//       const message = args.map((arg) => String(arg)).join(' ');
//       observedEvents.console.warn.push({ timestamp: Date.now(), message });
//       originalConsole.warn(...args);
//     };

//     // Cleanup function
//     const cleanup = () => {
//       window.console = originalConsole;
//       window.alert = originalAlert;
//       // Any additional happy-dom cleanup if needed
//     };

//     // Wait for unknown deferred scripts to load and execute
//     await window.happyDOM?.waitUntilComplete();

//     // Create server-side test environment
//     const environment: TestEnvironment = {
//       // @ts-expect-error this should work
//       document: window.document,
//       // @ts-expect-error this should work
//       window: window as unknown as Window,
//       observedEvents,
//       lastAlertMessage,
//       cleanup,
//     };

//     // Run the tests in the happy-dom environment
//     return runTestsInEnvironment(environment, testCases);
//   } catch (error) {
//     console.error('Error setting up happy-dom:', error);
//     return [
//       {
//         id: '',
//         title: 'Server Setup Error',
//         success: false,
//         error: `Failed to initialize happy-dom: ${error instanceof Error ? error.message : 'Unknown error'}`,
//       },
//     ];
//   }
// }

// export default runServerTests;
