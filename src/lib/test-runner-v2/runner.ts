import {
  TestResult,
  // TestCases,
  // ActionTestCase,
  // AssertionTestCase,
  // AssertionContext,
} from './types';
// import { handleActionTestCase } from './handlers/actionHandler';
// import { handleAssertionTestCase } from './handlers/assertionHandler';

// const ACTION_DELAY = 15;

/**
 * TestEnvironment interface defines what's needed from any environment
 * (browser or happy-dom) to run tests
 */
export interface TestEnvironment {
  document: Document;
  window: Window & typeof globalThis;
  cleanup?: () => void;
  lastAlertMessage: { value: string | null };
  observedEvents: {
    console: {
      log: Array<{ timestamp: number; message: string }>;
      error: Array<{ timestamp: number; message: string }>;
      warn: Array<{ timestamp: number; message: string }>;
    };
    alerts: string[];
  };
}

/**
 * Core test runner that executes the provided test cases in any DOM environment
 *
 * @param env - Test environment with document and window objects
 * @param testCases - Array of test cases to execute
 * @returns Promise resolving to an array of test results
 */
export async function runTestsInEnvironment(
  env: TestEnvironment,
  // testCases: TestCases,
): Promise<TestResult[]> {
  const results: TestResult[] = [];
  const {
    //  document, observedEvents, lastAlertMessage,
    cleanup,
  } = env;

  // Validate test cases
  // if (!testCases || testCases.length === 0) {
  //   return [
  //     {
  //       id: '',
  //       title: 'Setup Error',
  //       success: false,
  //       error: 'No test cases provided.',
  //     },
  //   ];
  // }

  try {
    // Run test cases
    // for (const testCase of testCases) {
    //   try {
    //     if (testCase.blockType === 'action') {
    //       await handleActionTestCase(testCase as ActionTestCase, document, results);
    //     } else if (testCase.blockType === 'assertion') {
    //       // Create context for assertions
    //       const context: AssertionContext = {
    //         lastAlertMessage: lastAlertMessage.value,
    //         observedEvents,
    //       };
    //       await handleAssertionTestCase(testCase as AssertionTestCase, document, context, results);
    //     }
    //     // Wait slightly for potential async updates after action/assertion
    //     await new Promise((resolve) => setTimeout(resolve, ACTION_DELAY));
    //   } catch (error) {
    //     console.error('Error during test case execution:', testCase.blockName, error);
    //     results.push({
    //       id: testCase.id ?? '', // Pass id directly, handles undefined fine
    //       title: testCase.blockName ?? '',
    //       success: false,
    //       error:
    //         error instanceof Error ? `Runtime Error: ${error.message}` : 'Unknown runtime error',
    //     });
    //     // Optionally stop further tests on runtime error
    //     // break;
    //   }
    // }
  } finally {
    // Clean up if cleanup function is provided
    if (cleanup) {
      cleanup();
    }
  }

  return results;
}
