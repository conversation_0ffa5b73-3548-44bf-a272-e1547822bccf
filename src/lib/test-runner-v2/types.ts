export interface TestResult {
  id: string;
  title: string;
  success: boolean;
  error?: string;
  expected?: string;
  actual?: string;
  input?: string;
  detailedError?: string;
}

// type BaseTestCase = NonNullable<NonNullable<Assignment['testSuites']>[number]['tests']>[number];

// export interface ActionTestCase extends Pick<BaseTestCase, 'id' | 'blockName'> {
//   blockType: 'action';
//   actionType: 'click' | 'input' | 'hover' | string;
//   actionSelector: string;
//   actionValue?: unknown;
// }

// export interface AssertionTestCase extends Pick<BaseTestCase, 'id' | 'blockName'> {
//   blockType: 'assertion';
//   assertionType: string;
//   assertionSelector?: string;
//   expectedValue?: unknown;
//   expectedAlertText?: string;
//   expectedClass?: string;
//   expectedCssValue?: string;
//   cssProperty?: string;
//   matchMode?: 'exact' | 'contains';
// }

// export type TestCases = BaseTestCase[];

export interface LogEntry {
  timestamp: number;
  message: string;
}

export interface AssertionContext {
  lastAlertMessage: string | null;
  observedEvents: {
    console: Record<'log' | 'error' | 'warn', LogEntry[]>;
    alerts: string[];
  };
}
