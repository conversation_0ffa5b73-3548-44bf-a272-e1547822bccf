// import { AssertionTestCase, TestResult, AssertionContext } from '../types';
// import { isElementVisible } from '../domUtils';

// /**
//  * Handles assertion test cases for verifying element properties and states
//  *
//  * @param testCase - The assertion test case to execute
//  * @param iframeDocument - The document object of the iframe
//  * @param context - Context containing alert and console logs for assertions
//  * @param results - Array to store test results
//  */
// export async function handleAssertionTestCase(
//   testCase: AssertionTestCase,
//   iframeDocument: Document,
//   context: AssertionContext,
//   results: TestResult[],
// ): Promise<void> {
//   const { lastAlertMessage, observedEvents } = context;
//   const iframeWindow = iframeDocument.defaultView as Window;

//   const id = testCase.id ?? '';
//   const title = testCase.blockName ?? '';

//   function normalizeCssValue(property: string, value: string, iframeWindow: Window) {
//     const testEl = iframeWindow.document.createElement('div');
//     testEl.style.setProperty(property, value);
//     iframeWindow.document.body.appendChild(testEl);
//     const computed = iframeWindow.getComputedStyle(testEl).getPropertyValue(property).trim();
//     testEl.remove();
//     return computed;
//   }

//   // --- Handle Assertions Not Requiring an Element ---
//   if (testCase.assertionType === 'alert') {
//     const expected = testCase.expectedAlertText ?? testCase.expectedValue ?? '';
//     const success = lastAlertMessage === expected;
//     results.push({
//       id,
//       title,
//       success,
//       error: success
//         ? undefined
//         : `Expected alert "${expected}" but got ${lastAlertMessage === null ? 'no alert' : `"${lastAlertMessage}"`}`,
//     });
//     return;
//   }

//   // Handle Console Assertions
//   if (['consoleLog', 'consoleWarn', 'consoleError'].includes(testCase.assertionType)) {
//     const expectedMessage = testCase.expectedValue || '';
//     const matchMode = testCase.matchMode === 'contains' ? 'contains' : 'exact'; // Default to exact
//     let logSource;
//     let logTypeName;

//     switch (testCase.assertionType) {
//       case 'consoleWarn':
//         logSource = observedEvents.console.warn;
//         logTypeName = 'warn';
//         break;
//       case 'consoleError':
//         logSource = observedEvents.console.error;
//         logTypeName = 'error';
//         break;
//       case 'consoleLog':
//       default:
//         logSource = observedEvents.console.log;
//         logTypeName = 'log';
//         break;
//     }

//     let found = false;
//     if (matchMode === 'exact') {
//       found = logSource.some((entry) => entry.message === expectedMessage);
//     } else {
//       // contains
//       found = logSource.some((entry) => entry.message.includes(expectedMessage as string));
//     }

//     results.push({
//       id,
//       title,
//       success: found,
//       error: found
//         ? undefined
//         : `Expected console.${logTypeName} (${matchMode}) "${expectedMessage}" was not found. Logs: ${JSON.stringify(logSource.map((l) => l.message))}`,
//     });
//     // Optionally clear logs after assertion?
//     // if (found && testCase.clearLogsAfterAssert) { logSource.length = 0; }
//     return;
//   }

//   // --- Handle Assertions Requiring a Selector ---
//   if (!testCase.assertionSelector) {
//     results.push({
//       id,
//       title,
//       success: false,
//       error: `Assertion failed: Selector ("assertionSelector") is required for assertion type "${testCase.assertionType}"`,
//     });
//     return;
//   }

//   // Handle 'notExists' specifically before querying the element
//   if (testCase.assertionType === 'notExists') {
//     const element = iframeDocument.querySelector(testCase.assertionSelector);
//     const success = element === null;
//     results.push({
//       id,
//       title,
//       success,
//       error: success
//         ? undefined
//         : `Assertion failed: Element "${testCase.assertionSelector}" exists but should not`,
//     });
//     return;
//   }

//   // For all other types, the element must exist
//   const element = iframeDocument.querySelector(testCase.assertionSelector);
//   if (!element) {
//     results.push({
//       id,
//       title,
//       success: false,
//       error: `Assertion failed: Element not found with selector "${testCase.assertionSelector}"`,
//     });
//     return;
//   }

//   // --- Handle Element-Based Assertions ---
//   switch (testCase.assertionType) {
//     case 'exists': {
//       // Check visibility as part of the 'exists' assertion for more info
//       const isVisible = element instanceof HTMLElement && isElementVisible(element, iframeWindow);
//       results.push({
//         id,
//         title,
//         success: true, // Element was found
//         error: isVisible
//           ? undefined
//           : `Info: Element "${testCase.assertionSelector}" exists but is not visible`,
//       });
//       break;
//     }

//     case 'textContent': {
//       const textContent = element.textContent?.trim() ?? ''; // Use nullish coalescing
//       const expected = testCase.expectedValue || '';
//       const matchMode = testCase.matchMode === 'contains' ? 'contains' : 'exact'; // Default exact

//       let success = false;
//       if (matchMode === 'exact') {
//         success = textContent === expected;
//       } else {
//         // contains
//         success = textContent.includes(expected as string);
//       }

//       results.push({
//         id,
//         title,
//         success,
//         error: success
//           ? undefined
//           : `Expected textContent (${matchMode}) "${expected}" in "${testCase.assertionSelector}", but got "${textContent}"`,
//       });
//       break;
//     }

//     case 'hasClass': {
//       if (testCase.expectedClass) {
//         const hasClass = element.classList.contains(testCase.expectedClass);
//         results.push({
//           id,
//           title,
//           success: hasClass,
//           error: hasClass
//             ? undefined
//             : `Element "${testCase.assertionSelector}" does not have class "${testCase.expectedClass}". Current classes: "${Array.from(element.classList).join(', ')}"`,
//         });
//       } else {
//         results.push({
//           id,
//           title,
//           success: false,
//           error: '"expectedClass" is required for "hasClass" assertion',
//         });
//       }
//       break;
//     }

//     case 'ariaLabel': {
//       const ariaLabel = element.getAttribute('aria-label');
//       // Allow expectedValue to be explicitly null if checking for absence
//       const expected = testCase.expectedValue === null ? null : testCase.expectedValue || '';
//       const success = ariaLabel === expected;
//       results.push({
//         id,
//         title,
//         success,
//         error: success
//           ? undefined
//           : `Expected aria-label "${expected}" for "${testCase.assertionSelector}", but got "${ariaLabel}"`,
//       });
//       break;
//     }

//     case 'value': {
//       if (
//         element instanceof HTMLInputElement ||
//         element instanceof HTMLTextAreaElement ||
//         element instanceof HTMLSelectElement
//       ) {
//         let actualValue: string | boolean;
//         const expected = testCase.expectedValue ?? ''; // Default expected to empty string if null/undefined
//         let success: boolean;

//         if (element instanceof HTMLInputElement && element.type === 'checkbox') {
//           // Explicitly compare boolean state
//           const expectedChecked = ['true', 'checked', 'on', '1'].includes(
//             String(expected).toLowerCase(),
//           );
//           success = element.checked === expectedChecked;
//           actualValue = element.checked;
//         } else if (element instanceof HTMLSelectElement) {
//           const selectedOption = element.options[element.selectedIndex];
//           // Prioritize matching element.value, then selectedOption.value, then selectedOption.text
//           const currentVal = element.value;
//           const optionVal = selectedOption?.value;
//           const optionText = selectedOption?.text;
//           success =
//             currentVal === String(expected) ||
//             optionVal === String(expected) ||
//             optionText === String(expected);
//           actualValue = currentVal; // Report the element's value property
//         } else {
//           actualValue = element.value;
//           success = actualValue === String(expected);
//         }

//         results.push({
//           id,
//           title,
//           success,
//           error: success
//             ? undefined
//             : `Expected value "${expected}" for "${testCase.assertionSelector}", but got "${actualValue}"`,
//         });
//       } else if ((element as HTMLElement).isContentEditable) {
//         const actualValue = element.textContent?.trim() ?? '';
//         const expected = testCase.expectedValue || '';
//         const success = actualValue === expected;
//         results.push({
//           id,
//           title,
//           success,
//           error: success
//             ? undefined
//             : `Expected contenteditable value "${expected}" for "${testCase.assertionSelector}", but got "${actualValue}"`,
//         });
//       } else {
//         results.push({
//           id,
//           title,
//           success: false,
//           error: `Element "${testCase.assertionSelector}" is not an input, textarea, select, or contenteditable element for value assertion`,
//         });
//       }
//       break;
//     }

//     case 'css': {
//       if (testCase.cssProperty && testCase.expectedCssValue !== undefined) {
//         const computedStyle = iframeWindow.getComputedStyle(element);
//         const actualCssValue = computedStyle.getPropertyValue(testCase.cssProperty)?.trim();
//         const expectedCssValue = testCase.expectedCssValue?.trim();
//         const normalizedExpected = normalizeCssValue(
//           testCase.cssProperty,
//           expectedCssValue,
//           iframeWindow,
//         );
//         const success = actualCssValue === normalizedExpected;

//         results.push({
//           id,
//           title,
//           success,
//           error: success
//             ? undefined
//             : `Expected CSS property "${testCase.cssProperty}" of "${testCase.assertionSelector}" to be "${expectedCssValue}" but got "${actualCssValue}"`,
//         });
//       } else {
//         results.push({
//           id,
//           title,
//           success: false,
//           error: '"cssProperty" and "expectedCssValue" are required for "css" assertion',
//         });
//       }
//       break;
//     }

//     default: {
//       results.push({
//         id,
//         title,
//         success: false,
//         error: `Unsupported assertion type: ${testCase.assertionType}`,
//       });
//     }
//   }
// }
