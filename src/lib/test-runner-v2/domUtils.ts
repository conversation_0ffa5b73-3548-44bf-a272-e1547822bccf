/**
 * Checks if an element is currently visible in the DOM
 *
 * @param element - The HTML element to check
 * @param iframeWindow - The window containing the element
 * @returns true if the element is visible, false otherwise
 */
export function isElementVisible(element: HTMLElement, iframeWindow: Window): boolean {
  if (!element) return false;

  // Check if the element is connected to the DOM
  if (!element.isConnected) return false;

  const style = iframeWindow.getComputedStyle(element);
  return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
}

/**
 * Waits for an iframe to fully load
 *
 * @param iframe - The iframe element to wait for
 * @param timeout - Maximum time to wait in milliseconds
 * @returns A promise that resolves when the iframe is loaded
 */
export async function waitForIframeLoad(iframe: HTMLIFrameElement, timeout = 2000): Promise<void> {
  const iframeDocument = iframe.contentDocument;
  const LOAD_TIMEOUT = timeout;

  return new Promise<void>((resolve, reject) => {
    const timer = setTimeout(() => reject(new Error('Iframe load timed out')), LOAD_TIMEOUT * 2);

    const checkReadyState = () => {
      if (iframeDocument && iframeDocument.readyState === 'complete') {
        clearTimeout(timer);
        // Add a small delay even if complete, allows late-running scripts to finish
        setTimeout(resolve, LOAD_TIMEOUT / 2);
      } else {
        // If not complete, try again shortly
        setTimeout(checkReadyState, 100);
      }
    };

    if (iframeDocument && iframeDocument.readyState === 'complete') {
      clearTimeout(timer);
      setTimeout(resolve, LOAD_TIMEOUT / 2); // Consistent delay
    } else {
      iframe.onload = () => {
        clearTimeout(timer);
        setTimeout(resolve, LOAD_TIMEOUT); // Longer delay after explicit onload
      };
      iframe.onerror = () => {
        clearTimeout(timer);
        reject(new Error('Iframe failed to load content (onerror triggered)'));
      };
      // Start checking readyState polling as a backup
      setTimeout(checkReadyState, 100);
    }
  });
}
