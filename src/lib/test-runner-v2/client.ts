import {
  TestResult,
  // TestCases
} from './types';
import { waitForIframeLoad } from './domUtils';
import { setupInterceptors } from './logUtils';
import { runTestsInEnvironment, TestEnvironment } from './runner';

/**
 * Client-side test runner that uses a real browser iframe
 *
 * @param iframe - The iframe element containing the content to test
 * @param testCases - Array of test cases to execute
 * @returns Promise resolving to an array of test results
 */
export async function runTests(
  iframe: HTMLIFrameElement,
  // testCases: TestCases,
): Promise<TestResult[]> {
  const iframeDocument = iframe.contentDocument;
  const iframeWindow = iframe.contentWindow as Window & typeof globalThis;

  // Validate iframe access
  if (!iframeDocument || !iframeWindow) {
    return [
      {
        id: '',
        title: 'Setup Error',
        success: false,
        error: 'Could not access iframe content. Ensure it has loaded and is same-origin.',
      },
    ];
  }

  // Set up interceptors for console and alerts
  const { observedEvents, lastAlertMessage, cleanup } = setupInterceptors(iframeWindow);

  // Wait for iframe to fully load
  try {
    await waitForIframeLoad(iframe);
  } catch (error) {
    cleanup();
    return [
      {
        id: '',
        title: 'Setup Error',
        success: false,
        error: `Iframe load error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      },
    ];
  }

  // Create test environment with real iframe
  const environment: TestEnvironment = {
    document: iframeDocument,
    window: iframeWindow,
    observedEvents,
    lastAlertMessage,
    cleanup,
  };

  // Run the tests in the environment
  return runTestsInEnvironment(
    environment,
    // testCases
  );
}

export default runTests;
