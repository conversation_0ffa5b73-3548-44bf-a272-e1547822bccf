import type { PayloadRequest } from 'payload';
import { z } from 'zod';
import { mastra } from '@/mastra';
import { createTestcasesAgent } from '@/mastra/agents';
import {
  createTestScenariosGeneratorAgent,
  createTestsGeneratorAgent,
} from '@/mastra/workflows/auto-evaluate-workflow/agents';
import { testCasesSchema } from '@/mastra/workflows/steps/schemas';
import type { GeneratedAssignmentContent } from '@/types/assignment';
import { GenerateAssignmentSchema } from '../schemas/assignment.schemas';
import { AssignmentService } from '../services/assignment.service';
import { extractTenantId } from '../utils/tenant.utils';

export class AssignmentController {
  static async handleGeneration({ user, json, payload }: PayloadRequest) {
    try {
      // Validate input
      const rawData = json ? await json() : {};
      const validatedData = GenerateAssignmentSchema.parse(rawData);

      if (!user?.id) {
        return Response.json({ message: 'User not authenticated' }, { status: 401 });
      }

      const tenantId = extractTenantId(user);

      if (!tenantId) {
        return Response.json({ message: 'Tenant not authenticated' }, { status: 401 });
      }

      const run = await mastra.getWorkflow('assignmentWorkflow').createRunAsync();

      const language = validatedData.language as 'java' | 'c';

      const result = await run.start({
        inputData: {
          topic: validatedData.title,
          difficulty: validatedData.difficulty,
          language: language,
          additionalRequirements: validatedData.additionalRequirements,
        },
      });

      if (result.status === 'failed') {
        return Response.json({ message: 'Failed to generate assignment' }, { status: 500 });
      }

      if (result.status === 'suspended') {
        return Response.json({ message: 'Generation suspended' }, { status: 400 });
      }

      const { steps } = result;

      const { output } = steps['generate-testcases'] as { output: any };

      const generatedContent: GeneratedAssignmentContent = {
        title: output.title,
        language: language,
        subject: validatedData.subjectId,
        difficulty: validatedData.difficulty,
        description: output.description,
        points: validatedData.points,
        instructions: output.instructions.replace(/^# [^\n]*\n?/, ''),
        starterCode: output.starterCode,
        solutionCode: output.solutionCode,
        testsRequirement: output?.testsRequirement || [],
        // requiresCommandLineArgs: output?.requiresCommandLineArgs || false,
        hints: output.hints,
        resources: output.resources,
      };

      // TODO: REMOVE THIS DEPRECATED CODE
      // Generate content via AI
      // const generatedContent = await AIGeneratorService.generateAssignmentContent({
      //   title: validatedData.title,
      //   difficulty: validatedData.difficulty,
      //   points: validatedData.points,
      //   additionalRequirements: validatedData.additionalRequirements,
      //   generationOptions: validatedData.generationOptions,
      // });

      // console.log(generatedContent);

      // Create assignment
      const assignmentService = new AssignmentService(payload);
      const createdAssignment = await assignmentService.createAssignment(
        generatedContent,
        validatedData.difficulty,
        tenantId,
      );

      return Response.json({
        message: 'Assignment generated successfully',
        assignment: {
          id: createdAssignment.id,
          title: createdAssignment.title,
          description: createdAssignment.description,
          points: createdAssignment.points,
          difficulty: createdAssignment.difficulty,
          dueDate: createdAssignment.dueDate,
        },
      });
    } catch (error) {
      console.error('Assignment generation error:', error);

      if (error instanceof z.ZodError) {
        return Response.json(
          {
            message: 'Invalid input data',
            errors: error.issues.join(', '),
          },
          { status: 400 },
        );
      }

      return Response.json(
        {
          message: error instanceof Error ? error.message : 'Failed to generate assignment',
        },
        { status: 500 },
      );
    }
  }

  static async handleTestScenariosRegeneration({ user, routeParams, payload }: PayloadRequest) {
    try {
      const assignmentId = routeParams?.id as string;

      if (!assignmentId) {
        return Response.json({ message: 'Assignment ID is required' }, { status: 400 });
      }

      if (!user?.id) {
        return Response.json({ message: 'User not authenticated' }, { status: 401 });
      }

      const tenantId = extractTenantId(user);

      if (!tenantId) {
        return Response.json({ message: 'Tenant not authenticated' }, { status: 401 });
      }

      // Fetch the assignment
      const assignment = await payload.findByID({
        collection: 'assignments',
        id: assignmentId,
        depth: 0,
      });

      if (!assignment) {
        return Response.json({ message: 'Assignment not found' }, { status: 404 });
      }

      // Check if assignment belongs to the user's tenant
      if (assignment.tenant !== tenantId) {
        return Response.json({ message: 'Access denied' }, { status: 403 });
      }

      // Get solution code based on language
      const solutionCode = assignment.solutionCode;
      if (!solutionCode) {
        return Response.json(
          { message: 'No solution code found for this assignment' },
          { status: 400 },
        );
      }

      let codeToAnalyze = '';
      switch (assignment.language) {
        case 'java':
          codeToAnalyze = solutionCode.java || '';
          break;
        case 'c':
          codeToAnalyze = solutionCode.c || '';
          break;
        case 'web':
          // For web assignments, combine HTML, CSS, and JS
          codeToAnalyze = [solutionCode.html || '', solutionCode.css || '', solutionCode.js || '']
            .filter(Boolean)
            .join('\n\n');
          break;
        default:
          return Response.json(
            { message: 'Unsupported language for test scenario generation' },
            { status: 400 },
          );
      }

      if (!codeToAnalyze.trim()) {
        return Response.json(
          { message: 'No solution code found for the specified language' },
          { status: 400 },
        );
      }

      // Generate test scenarios using the testcases agent
      const testScenariosGeneratorAgent = createTestScenariosGeneratorAgent();
      const prompt = `<topic>${assignment.title}</topic><description>${assignment.description}</description>\n<code>${codeToAnalyze}</code>`;

      const response = await testScenariosGeneratorAgent.generate(
        [{ role: 'user', content: prompt }],
        {
          // output: testCasesSchema,
          maxTokens: 4000,
          temperature: 0.3,
        },
      );

      // Convert test cases to the format expected by testsRequirement field
      // const testScenarios = response.object
      // 	.map((testCase, index) => {
      // 		return `#${index + 1}: input: ${testCase.input}, output: ${testCase.expectedOutput}`;
      // 	})
      // 	.join("\n");

      const testScenarios = response.text;

      // Update the assignment with new test scenarios
      const updatedAssignment = await payload.update({
        collection: 'assignments',
        id: assignmentId,
        data: {
          testsRequirement: testScenarios,
        },
      });

      return Response.json({
        message: 'Test scenarios regenerated successfully',
        testsRequirement: testScenarios,
        assignment: {
          id: updatedAssignment.id,
          title: updatedAssignment.title,
        },
      });
    } catch (error) {
      console.error('Test scenarios regeneration error:', error);

      if (error instanceof z.ZodError) {
        return Response.json(
          {
            message: 'Invalid input data',
            errors: error.issues.map((issue) => issue.message).join(', '),
          },
          { status: 400 },
        );
      }

      return Response.json(
        {
          message: error instanceof Error ? error.message : 'Failed to regenerate test scenarios',
        },
        { status: 500 },
      );
    }
  }
}
