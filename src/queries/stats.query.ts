import { QueryOptions } from '@tanstack/react-query';

export type StudentStats = {
  activeAssignments: number;
  completedAssignments: number;
  remainingSubmissions: number;
};

export type InstructorStats = {
  totalAssignments: number;
  totalSubmissions: number;
  totalUsers: number;
};

export type UserStats = StudentStats | InstructorStats;

const API_ENDPOINTS = {
  assignments: '/api/assignments/count',
  submissions: '/api/submissions/count',
  users: '/api/users/count',
} as const;

const fetchData = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Failed to fetch from ${url}: ${response.statusText}`);
  }
  const data = await response.json();
  return safeParseCount(data);
};

const safeParseCount = (data: any): number => data?.totalDocs || 0;

export const isStudentStats = (stats: UserStats): stats is StudentStats =>
  'remainingSubmissions' in stats;

const fetchStudentStats = async (): Promise<StudentStats> => {
  const [activeCount, completedCount] = await Promise.all([
    fetchData(API_ENDPOINTS.assignments),
    fetchData(API_ENDPOINTS.submissions),
  ]);

  return {
    activeAssignments: activeCount,
    completedAssignments: completedCount,
    remainingSubmissions: Math.max(0, activeCount - completedCount),
  };
};

const fetchInstructorStats = async (): Promise<InstructorStats> => {
  const [totalAssignments, totalSubmissions, totalUsers] = await Promise.all([
    fetchData(API_ENDPOINTS.assignments),
    fetchData(API_ENDPOINTS.submissions),
    fetchData(API_ENDPOINTS.users),
  ]);

  return {
    totalAssignments,
    totalSubmissions,
    totalUsers,
  };
};

const getStatsByRole = async (role?: string): Promise<UserStats> => {
  return role === 'student' ? fetchStudentStats() : fetchInstructorStats();
};

export const createSubjectsQueryOption = (role: string): QueryOptions<UserStats> => ({
  queryFn: () => getStatsByRole(role),
});

export const userStatsOptions = (
  role?: string,
  userId?: string | number,
): QueryOptions<UserStats> => {
  return {
    queryKey: ['userStats', role, userId],
    queryFn: () => getStatsByRole(role),
    retry: false,
  };
};
