import { QueryOptions } from '@tanstack/react-query';
import { stringify } from 'qs-esm';
import type { Submission } from '@/payload-types';

export type ISubmission = Pick<
  Submission,
  'testsResult' | 'id' | 'status' | 'isLocked' | 'updatedAt'
>;

export interface SubmissionResponse {
  docs: ISubmission[];
  hasNextPage: boolean;
  hasPrevPage: boolean;
  limit: number;
  nextPage: number | null;
  page: number;
  pagingCounter: number;
  prevPage: number | null;
  totalDocs: number;
  totalPages: number;
}

const fetchSubmissions = async (assignmentId: string): Promise<SubmissionResponse> => {
  const query = stringify(
    {
      depth: 0,
      select: {
        testsResult: true,
        status: true,
        isLocked: true,
        updatedAt: true,
      },
      where: {
        assignment: { equals: assignmentId },
      },
      sort: '-createdAt',
      limit: 5,
    },
    { addQueryPrefix: true },
  );

  const response = await fetch(
    `/api/submissions/${query.replaceAll('%5B', '[').replaceAll('%5D', ']')}`,
  );

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json();
};

export const submissionsQueryOptions = (
  assignmentId: string,
): QueryOptions<SubmissionResponse> => ({
  queryKey: ['submissions', assignmentId],
  queryFn: () => fetchSubmissions(assignmentId),
  gcTime: 2 * 60 * 1000, // 2 minutes
});

export const submissionsQueryKey = (assignmentId: string) => ['submissions', assignmentId];
