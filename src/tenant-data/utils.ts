import { BasePayload, CollectionSlug } from 'payload';
import CSV from './csv';

export const getOrCreateTenant = async (payload: BasePayload) => {
  const tenant = await payload.find({
    collection: 'tenants',
    depth: 0,
    limit: 1,
    where: {
      domain: {
        equals: 'darshan.ac.in',
      },
    },
  });
  try {
    if (tenant.docs.length > 0) {
      return tenant.docs[0];
    }
    return await payload.create({
      collection: 'tenants',
      data: {
        name: 'Darshan University',
        slug: 'darshan-university',
        domain: 'darshan.ac.in',
      },
    });
  } catch {
    console.error('Error creating tenant');
  }
};

type BulkCreateOptions<T> = {
  payload: import('payload').Payload;
  collection: CollectionSlug;
  data: T[];
  transform?: (item: T, index: number) => Partial<T> | Promise<Partial<T>>;
  csv: CSV;
};

export async function bulkCreate<T>({
  payload,
  collection,
  data,
  transform,
  csv,
}: BulkCreateOptions<T>) {
  const results = await Promise.allSettled(
    data.map(async (item, index) => {
      const transformed = transform ? await transform(item, index) : item;
      csv.add({
        ...transformed,
        tenants: undefined,
        admin: undefined,
        roles: undefined,
      });
      const res = await payload.create({
        collection,
        overrideAccess: true,
        data: transformed,
      });
      return res;
    }),
  );

  results.forEach((result, i) => {
    const item = data[i] as any;
    const name = item?.username || item?.email || `item #${i + 1}`;

    if (result.status === 'fulfilled') {
      console.log(`✅ Created: ${name}`);
    } else {
      console.error(`❌ Failed: ${name}`, result.reason);
    }
  });

  csv.removeColumns(['']);
}

export const cleanCode = (code: string): string => {
  if (!code) return '';
  const lines = code.split('\n');
  // Remove leading/trailing blank lines
  while (lines.length > 0 && lines[0].trim() === '') lines.shift();
  while (lines.length > 0 && lines[lines.length - 1].trim() === '') lines.pop();
  if (lines.length === 0) return '';

  // Find minimum indent
  let minIndent = Infinity;
  lines.forEach((line) => {
    const match = line.match(/^\s*/);
    if (line.trim() !== '' && match) {
      minIndent = Math.min(minIndent, match[0].length);
    }
  });

  // Remove common indent
  if (minIndent > 0 && minIndent !== Infinity) {
    return lines.map((line) => line.substring(minIndent)).join('\n');
  }
  return lines.join('\n');
};
