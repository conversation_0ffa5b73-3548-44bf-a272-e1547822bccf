import * as fs from 'fs';
import * as path from 'path';

export interface CSVOptions {
  delimiter?: string;
  headers?: string[];
  createIfNotExists?: boolean;
  append?: boolean;
}

export class CSV<T extends Record<string, any> = Record<string, any>> {
  private filePath: string;
  private delimiter: string;
  private headers: string[];
  private createIfNotExists: boolean;
  private append: boolean;

  constructor(filePath: string, options: CSVOptions = {}) {
    this.filePath = filePath;
    this.delimiter = options.delimiter || ',';
    this.headers = options.headers || [];
    this.createIfNotExists = options.createIfNotExists ?? true;
    this.append = options.append ?? true;

    this.initialize();
  }

  private initialize(): void {
    const dir = path.dirname(this.filePath);

    // Create directory if it doesn't exist
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Create file with headers if it doesn't exist
    if (!fs.existsSync(this.filePath) && this.createIfNotExists) {
      if (this.headers.length > 0) {
        fs.writeFileSync(this.filePath, this.headers.join(this.delimiter) + '\n');
      } else {
        fs.writeFileSync(this.filePath, '');
      }
    } else if (fs.existsSync(this.filePath) && this.headers.length === 0) {
      // If file exists but no headers provided, try to read headers from file
      const content = fs.readFileSync(this.filePath, 'utf-8');
      const lines = content.split('\n').filter((line) => line.trim());
      if (lines.length > 0) {
        this.headers = lines[0].split(this.delimiter).map((h) => h.trim());
      }
    }
  }

  private escapeCSVField(field: string): string {
    if (field.includes(this.delimiter) || field.includes('"') || field.includes('\n')) {
      return `"${field.replace(/"/g, '""')}"`;
    }
    return field;
  }

  private formatRow(data: T): string {
    if (this.headers.length === 0) {
      // If no headers defined, use object keys as headers
      this.headers = Object.keys(data);
      if (!fs.existsSync(this.filePath) || fs.readFileSync(this.filePath, 'utf-8').trim() === '') {
        fs.writeFileSync(this.filePath, this.headers.join(this.delimiter) + '\n');
      }
    }

    const values = this.headers.map((header) => {
      const value = data[header];
      if (value === null || value === undefined) {
        return '';
      }
      return this.escapeCSVField(String(value));
    });

    return values.join(this.delimiter);
  }

  public add(data: T): void {
    const row = this.formatRow(data);

    if (this.append) {
      fs.appendFileSync(this.filePath, row + '\n');
    } else {
      const content = fs.readFileSync(this.filePath, 'utf-8');
      fs.writeFileSync(this.filePath, content + row + '\n');
    }
  }

  public addMany(dataArray: T[]): void {
    const rows = dataArray.map((data) => this.formatRow(data));
    const content = rows.join('\n') + '\n';

    if (this.append) {
      fs.appendFileSync(this.filePath, content);
    } else {
      const existingContent = fs.readFileSync(this.filePath, 'utf-8');
      fs.writeFileSync(this.filePath, existingContent + content);
    }
  }

  public read(): T[] {
    if (!fs.existsSync(this.filePath)) {
      return [];
    }

    const content = fs.readFileSync(this.filePath, 'utf-8');
    const lines = content.split('\n').filter((line) => line.trim());

    if (lines.length === 0) {
      return [];
    }

    // Skip header row
    const dataLines = lines.slice(1);

    return dataLines.map((line) => {
      const values = this.parseCSVLine(line);
      const obj: any = {};

      this.headers.forEach((header, index) => {
        obj[header] = values[index] || '';
      });

      return obj as T;
    });
  }

  private parseCSVLine(line: string): string[] {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;
    let i = 0;

    while (i < line.length) {
      const char = line[i];

      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          // Escaped quote
          current += '"';
          i += 2;
        } else {
          // Toggle quote state
          inQuotes = !inQuotes;
          i++;
        }
      } else if (char === this.delimiter && !inQuotes) {
        result.push(current);
        current = '';
        i++;
      } else {
        current += char;
        i++;
      }
    }

    result.push(current);
    return result;
  }

  public clear(): void {
    if (this.headers.length > 0) {
      fs.writeFileSync(this.filePath, this.headers.join(this.delimiter) + '\n');
    } else {
      fs.writeFileSync(this.filePath, '');
    }
  }

  public getHeaders(): string[] {
    return [...this.headers];
  }

  public setHeaders(headers: string[]): void {
    this.headers = headers;
    // Rewrite file with new headers if it exists
    if (fs.existsSync(this.filePath)) {
      const data = this.read();
      fs.writeFileSync(this.filePath, this.headers.join(this.delimiter) + '\n');
      if (data.length > 0) {
        this.addMany(data);
      }
    }
  }

  public count(): number {
    return this.read().length;
  }

  public removeColumns(columnsToRemove: string[]): void {
    const columnsToRemoveSet = new Set(columnsToRemove);
    const newHeaders = this.headers.filter((header) => !columnsToRemoveSet.has(header));

    if (newHeaders.length === this.headers.length) {
      // No change needed
      return;
    }

    const data = this.read();

    // Update internal headers
    this.headers = newHeaders;

    // Re-write file with new headers and filtered data
    const filteredData = data.map((entry) => {
      const filteredEntry: Record<string, any> = {};
      newHeaders.forEach((header) => {
        filteredEntry[header] = entry[header];
      });
      return filteredEntry as T;
    });

    fs.writeFileSync(this.filePath, newHeaders.join(this.delimiter) + '\n');
    if (filteredData.length > 0) {
      this.addMany(filteredData);
    }
  }

  public exists(): boolean {
    return fs.existsSync(this.filePath);
  }
}

export default CSV;
