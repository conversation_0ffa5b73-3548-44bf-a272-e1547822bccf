import { writeFileSync } from 'node:fs';
import { generatePassword } from './password';

const passwords = [];

for (let i = 0; i < 61; i++) {
  passwords.push({
    username: '<PERSON>ET<PERSON>',
    fullName: 'PARVANI HETVI JAGDISHBHAI',
    email: '<EMAIL>',
    roles: ['student'],
    division: 'D2D-A1',
    labBatchNo: 0,
    password: generatePassword({ length: 8, easyToEnter: true }),
  });
}

writeFileSync('passwords.json', JSON.stringify(passwords, null, 2));
