import fs from "fs";
import type { Config } from "payload";
import { adminData } from "./admin";
import { genAssignmentsData } from "./assignments";
import { genBatchesData } from "./batches";
import CSV from "./csv";
import { dummyData } from "./dummy";
import { facultiesData } from "./faculties";
import { genModulesData } from "./modules";
import { studentsData } from "./students";
import { createSubjectData } from "./subjects";
import type { User } from "./types";
import { getOrCreateTenant } from "./utils";

export const seedDU: NonNullable<Config["onInit"]> = async (
	payload,
): Promise<void> => {
	console.log("Seeding Started...");

	// Delete the CSV file if it exists
	const csvPath = "./src/tenant-data/credentials.csv";
	if (fs.existsSync(csvPath)) {
		fs.unlinkSync(csvPath);
	}

	const csv = new CSV<User>(csvPath);
	let c = 0;

	const tenant = await getOrCreateTenant(payload);

	const batch5 = [];
	const batch6 = [];

	if (!tenant) {
		console.error("Error creating tenant");
		return;
	}

	// Create admins sequentially
	console.log("Creating admins...");
	c = 0;
	for (const user of adminData) {
		try {
			console.log(`Creating admin ${user.username}...`);
			await payload.create({
				collection: "users",
				data: {
					email: user.email,
					username: user.username,
					password: user.password,
					fullName: user.fullName,
					division: user.division,
					roles: ["super-admin"],
					tenants: [
						{
							roles: ["faculty"],
							tenant: tenant.id,
						},
					],
				},
			});
			c++;
			csv.add(user);
		} catch (error) {
			console.error(`Error creating admin ${user.username}:`, error);
		}
	}
	console.log(`Done creating ${c} admins`);

	// Create dummy users sequentially
	console.log("\nCreating dummy users...");
	c = 0;
	for (const user of dummyData) {
		try {
			console.log(`Creating dummy user ${user.username}...`);
			await payload.create({
				collection: "users",
				data: {
					email: user.email,
					username: user.username,
					password: user.password,
					fullName: user.fullName,
					division: user.division,
					roles: ["user"],
					tenants: [
						{
							roles: user.roles,
							tenant: tenant.id,
						},
					],
				},
			});
			c++;
			csv.add(user);
		} catch (error) {
			console.error(`Error creating dummy user ${user.username}:`, error);
		}
	}
	console.log(`Done creating ${c} dummy users`);

	// Create faculties sequentially
	console.log("\nCreating faculties...");
	c = 0;
	for (const user of facultiesData) {
		try {
			console.log(`Creating faculty ${user.username}...`);
			await payload.create({
				collection: "users",
				data: {
					email: user.email,
					username: user.username,
					password: user.password,
					fullName: user.fullName,
					division: user.division,
					roles: ["user"],
					tenants: [
						{
							roles: ["faculty"],
							tenant: tenant.id,
						},
					],
				},
			});
			c++;
			csv.add(user);
		} catch (error) {
			console.error(`Error creating faculty ${user.username}:`, error);
		}
	}
	console.log(`Done creating ${c} faculties`);

	// Create students sequentially
	console.log("\nCreating students...");
	c = 0;
	for (const user of studentsData) {
		try {
			console.log(`Creating student ${user.username}...`);
			const res = await payload.create({
				collection: "users",
				data: {
					email: user.email,
					username: user.username,
					password: user.password,
					fullName: user.fullName,
					division: `${user.division}-A${user.labBatchNo}`,
					roles: ["user"],
					tenants: [
						{
							roles: ["student"],
							tenant: tenant.id,
						},
					],
				},
			});
			c++;
			csv.add(user);
			if (user.labBatchNo === 5) {
				batch5.push(res.id);
			}
			if (user.labBatchNo === 6) {
				batch6.push(res.id);
			}
		} catch (error) {
			console.error(`Error creating student ${user.username}:`, error);
		}
	}
	console.log(`Done creating ${c} students`);

	csv.removeColumns(["tenants", "admin", "roles"]);

	// create Batches
	console.log("Creating batches...");
	const batchData = genBatchesData(batch5, batch6);
	for (const batch of batchData) {
		await payload.create({
			collection: "batches",
			data: {
				batchId: batch.batchId,
				students: batch.students,
			},
		});
	}
	console.log("Done creating 2 batches");

	let subjectId = -1;

	console.log("Creating subjects...");
	const subjects = createSubjectData(tenant.id);
	for (const item of subjects) {
		const res = await payload.create({
			collection: "subjects",
			data: {
				...item,
			},
		});
		if (subjectId == -1) {
			subjectId = res.id;
		}
	}
	console.log("Done creating 1 subjects");

	console.log("Creating assignments...");
	c = 0;
	const assignmentsIds = [];
	const assignments = genAssignmentsData(tenant.id, subjectId);
	for (const item of assignments) {
		const res = await payload.create({
			collection: "assignments",
			data: {
				...item,
				starterCode: {
					c: "",
					css: "",
					html: "",
					java: "",
					js: "",
				},
				dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
			},
		});
		c++;
		if (res.id) {
			assignmentsIds.push(res.id);
		}
	}
	console.log(`Done creating ${c} assignments`);
	c = 0;

	console.log("Creating modules...");
	const modulesData = genModulesData(assignmentsIds, []);
	for (const data of modulesData) {
		await payload.create({
			collection: "modules",
			data: {
				...data,
			},
		});
	}
	console.log("Done creating 2 modules");

	console.log("Seeding completed successfully!");
};
