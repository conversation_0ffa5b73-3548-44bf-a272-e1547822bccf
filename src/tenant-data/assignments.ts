import type { Assignment } from "../payload-types";
import { cleanCode } from "./utils";

export const genAssignmentsData = (tenantId: number, subjectId: number) => {
	const mockData: Omit<
		Assignment,
		"id" | "createdAt" | "dueDate" | "updatedAt"
	>[] = [
		{
			title: "Calculate Area of Circle",
			instructions: `Your task is to write a C program that calculates the area of a circle. The program should prompt the user to enter the radius of the circle, then use the standard formula to compute its area, and finally display the result.

**Steps to follow:**
1.  **Include Header**: Start by including the \`stdio.h\` header for input/output operations.
2.  **Define PI**: Define a constant for PI (π) with a precision of at least 5 decimal places (e.g., \`3.14159\`). You can use a \`#define\` preprocessor directive for this.
3.  **Declare Variables**: Declare two floating-point variables, one for the \`radius\` and one for the \`area\`. Use \`float\` for these.
4.  **Prompt for Input**: Display a message to the user asking them to "Enter the radius of the circle: ".
5.  **Read Radius**: Use \`scanf\` to read the floating-point value entered by the user into your \`radius\` variable. Remember to use the correct format specifier for \`float\`.
6.  **Calculate Area**: Apply the formula \`Area = PI * radius * radius\` to calculate the area and store it in your \`area\` variable.
7.  **Print Result**: Print the calculated area. The output should be formatted to two decimal places, like: \`The area of the circle is: <calculated_area>\`.

**Important Notes:**
*   Assume the input will always be a valid floating-point number.
*   The \`printf\` format specifier \`%.2f\` will display a float with two digits after the decimal point.

**Example Inputs/Outputs:**
*   Input: \`5\` → Output: \`The area of the circle is: 78.54\`
*   Input: \`0\` → Output: \`The area of the circle is: 0.00\`
*   Input: \`10.5\` → Output: \`The area of the circle is: 346.36\` (approx, depends on PI precision)
`,
			description:
				"Write a C program to calculate and print the area of a circle given its radius as input from the user.",
			difficulty: "easy",
			language: "c",
			hints: [
				{
					question: "How do I read a floating-point number?",
					answer: 'Use `scanf("%f", &variable)` to read a float from the user.',
				},
				{
					question:
						"How do I print a floating-point number to two decimal places?",
					answer:
						'Use `printf("%.2f", variable)` to format the output to two decimal places.',
				},
				{
					question: "What is the formula for the area of a circle?",
					answer:
						"The area of a circle is calculated as `PI * radius * radius`.",
				},
			],
			resources: [
				{
					title: "C Input/Output (printf/scanf) - GeeksforGeeks",
					url: "https://www.geeksforgeeks.org/c-input-output-g-fact-1/",
				},
				{
					title: "Data Types in C - GeeksforGeeks",
					url: "https://www.geeksforgeeks.org/data-types-in-c/",
				},
				{
					title: "Area of a Circle Formula",
					url: "https://www.mathsisfun.com/geometry/circle-area.html",
				},
			],
			testsRequirement: `-   **Scenario 1: Positive Radius**
    -   Input: \`5\`
    -   Expected Output: \`The area of the circle is: 78.54\`

-   **Scenario 2: Zero Radius**
    -   Input: \`0\`
    -   Expected Output: \`The area of the circle is: 0.00\`

-   **Scenario 3: Another Positive Radius (e.g., 10.0)**
    -   Input: \`10\`
    -   Expected Output: \`The area of the circle is: 314.16\`
`,
			solutionCode: {
				c: `// Include the standard input/output library
#include <stdio.h>

// Define PI as a constant for better precision
#define PI 3.14159

int main() {
    // Declare variables for radius and area as floating-point numbers
    float radius, area;

    // Prompt the user to enter the radius of the circle
    printf("Enter the radius of the circle: ");

    // Read the radius from the user input
    scanf("%f", &radius);

    // Calculate the area of the circle using the formula: PI * radius * radius
    area = PI * radius * radius;

    // Print the calculated area, formatted to two decimal places
    printf("The area of the circle is: %.2f\\n", area);

    // Indicate successful program execution
    return 0;
}`,
			},
			points: 10,
		},
		//     {
		//       instructions: `Create a program to implement a single node for a singly linked list in C. The node should store an integer and a pointer to the next node. Read the integer using scanf, create the node dynamically, print its data, and free the memory.

		// **Steps:**
		// 1. Define a \`struct Node\` with an integer \`data\` and a \`next\` pointer.
		// 2. Use \`scanf\` to read an integer from the user.
		// 3. Allocate memory for a node using \`malloc\`.
		// 4. Assign the scanned integer to the node's \`data\`. Set \`next\` to NULL.
		// 5. Print the node's data as \`Node data: <value>\`.
		// 6. Free the allocated memory using \`free\`.

		// **Notes:**
		// - Assume the input is a valid integer.
		// - No need to check if \`malloc\` fails.
		// - Use \`int\` for the node's data.

		// **Example Inputs/Outputs:**
		// - Input: \`10\` → Output: \`Node data: 10\`
		// - Input: \`-5\` → Output: \`Node data: -5\`
		// - Input: \`0\` → Output: \`Node data: 0\``,
		//       title: 'Singly Linked List Node',
		//       description:
		//         'Write a C program to create a single node for a singly linked list, read its data using scanf, print the data, and free the memory.',
		//       difficulty: 'easy',

		//       language: 'c',
		//       hints: [
		//         {
		//           question: 'How do I read input from the user?',
		//           answer: 'Use `scanf("%d", &variable)` to read an integer from the user.',
		//         },
		//         {
		//           question: 'How do I create a node dynamically?',
		//           answer:
		//             'Use `malloc(sizeof(struct Node))` to allocate memory, set the `data` field, and make `next` NULL.',
		//         },
		//         {
		//           question: 'How do I avoid memory leaks?',
		//           answer: "Use `free` to deallocate the node's memory after printing.",
		//         },
		//       ],
		//       resources: [
		//         {
		//           title: 'Singly Linked List in C - GeeksforGeeks',
		//           url: 'https://www.geeksforgeeks.org/singly-linked-list-tutorial',
		//         },
		//         {
		//           title: 'Understanding Linked List (YouTube)',
		//           url: 'https://www.youtube.com/watch?v=VOpjAHCee7c',
		//         },
		//       ],
		//       testsRequirement: `-   **Scenario 1: Positive Integer Input**
		//     -   Input: \`10\`
		//     -   Expected Output: \`10\`

		// -   **Scenario 2: Negative Integer Input**
		//     -   Input: \`-5\`
		//     -   Expected Output: \`-5\`

		// -   **Scenario 3: Zero Input**
		//     -   Input: \`0\`
		//     -   Expected Output: \`0\`
		// `,
		//       solutionCode: {
		//         c: cleanCode(`#include <stdio.h>
		// #include <stdlib.h>

		// struct Node {
		//     int data;
		//     struct Node* next;
		// };

		// void main() {
		//     struct Node* node = (struct Node*)malloc(sizeof(struct Node));

		//     int val;
		//     scanf("%d", &val);

		//     node->data = val;

		//     node->next = NULL;

		//     printf("%d", node->data);
		//     free(node);
		// }`),
		//       },
		//       points: 10,
		//     },
		//     {
		//       instructions: `Write a menu-driven program to implement a singly linked list with multiple operations. The program should display a menu and allow users to perform various operations until they choose to exit.

		// **Required Operations:**
		// 1. Insert a node at the front of the linked list
		// 2. Display all nodes
		// 3. Delete the first node of the linked list
		// 4. Insert a node at the end of the linked list
		// 5. Delete the last node of the linked list
		// 6. Delete a node from specified position
		// 7. Count the number of nodes
		// 8. Exit

		// **Menu Format:**
		// \`\`\`
		// 1. Insert at front
		// 2. Display all nodes
		// 3. Delete first node
		// 4. Insert at end
		// 5. Delete last node
		// 6. Delete from position
		// 7. Count nodes
		// 8. Exit
		// Enter your choice:
		// \`\`\`

		// **Implementation Details:**
		// - Use \`struct Node\` with \`int data\` and \`struct Node* next\`
		// - Handle empty list cases appropriately
		// - For display: print nodes as \`data1 -> data2 -> data3 -> NULL\`
		// - For empty list display: print \`List is empty\`
		// - For position-based deletion, positions start from 1
		// - Continue showing menu until user selects exit option

		// **Example Interaction:**
		// - Insert 10 at front → \`Node inserted at front\`
		// - Display → \`10 -> NULL\`
		// - Count → \`Number of nodes: 1\``,
		//       title: 'Menu-Driven Singly Linked List',
		//       description:
		//         'Implement a complete menu-driven singly linked list program with insert, delete, display, and count operations.',
		//       difficulty: 'medium',

		//       language: 'c',
		//       hints: [
		//         {
		//           question: 'How do I handle an empty list?',
		//           answer:
		//             'Check if head is NULL before performing operations. Initialize head to NULL at the start.',
		//         },
		//         {
		//           question: 'How do I insert at the front?',
		//           answer:
		//             'Create new node, set its next to current head, then update head to point to new node.',
		//         },
		//         {
		//           question: 'How do I insert at the end?',
		//           answer:
		//             'If list is empty, insert at front. Otherwise, traverse to last node and link the new node.',
		//         },
		//         {
		//           question: 'How do I delete from a specific position?',
		//           answer:
		//             'Traverse to the position, keep track of previous node, then adjust the links and free the node.',
		//         },
		//       ],
		//       resources: [
		//         {
		//           title: 'Singly Linked List in C - GeeksforGeeks',
		//           url: 'https://www.geeksforgeeks.org/singly-linked-list-tutorial',
		//         },
		//         {
		//           title: 'Understanding Linked List (YouTube)',
		//           url: 'https://www.youtube.com/watch?v=VOpjAHCee7c',
		//         },
		//       ],
		//       testsRequirement: `-   **Scenario 1: Insert Front, Display, Count**
		//     -   Input:
		//         \`\`\`
		//         1 10
		//         2
		//         7
		//         8
		//         \`\`\`
		//     -   Expected Output:
		//         \`\`\`
		//         Node inserted at front
		//         10 -> NULL
		//         Number of nodes: 1
		//         \`\`\`

		// -   **Scenario 2: Insert End, Delete Last, Display**
		//     -   Input:
		//         \`\`\`
		//         4 20
		//         4 30
		//         2
		//         5
		//         2
		//         8
		//         \`\`\`
		//     -   Expected Output:
		//         \`\`\`
		//         Node inserted at end
		//         Node inserted at end
		//         20 -> 30 -> NULL
		//         Last node deleted
		//         20 -> NULL
		//         \`\`\`

		// -   **Scenario 3: Insert Front/End, Delete First, Delete from Position**
		//     -   Input:
		//         \`\`\`
		//         1 5
		//         4 15
		//         1 0
		//         2
		//         3
		//         2
		//         6 2
		//         2
		//         8
		//         \`\`\`
		//     -   Expected Output:
		//         \`\`\`
		//         Node inserted at front
		//         Node inserted at end
		//         Node inserted at front
		//         0 -> 5 -> 15 -> NULL
		//         First node deleted
		//         5 -> 15 -> NULL
		//         Enter position: Node deleted from position 2
		//         5 -> NULL
		//         \`\`\`

		// -   **Scenario 4: Deleting from Empty List**
		//     -   Input:
		//         \`\`\`
		//         3
		//         5
		//         6 1
		//         2
		//         8
		//         \`\`\`
		//     -   Expected Output:
		//         \`\`\`
		//         List is empty
		//         List is empty
		//         Enter position: List is empty
		//         List is empty
		//         \`\`\`
		// `,
		//       solutionCode: {
		//         c: cleanCode(`#include <stdio.h>

		// #include <stdlib.h>

		// struct Node {
		//     int data;
		//     struct Node * next;
		// };

		// void insertFront(struct Node ** head, int data) {
		//     struct Node * newNode = (struct Node * ) malloc(sizeof(struct Node));
		//     newNode -> data = data;
		//     newNode -> next = * head;
		//     * head = newNode;
		//     printf("Node inserted at front\\n");
		// }

		// void display(struct Node * head) {
		//     if (head == NULL) {
		//         printf("List is empty");
		//         return;
		//     }
		//     struct Node * temp = head;
		//     while (temp != NULL) {
		//         printf("%d", temp -> data);
		//         if (temp -> next != NULL) {
		//             printf(" -> ");
		//         }
		//         temp = temp -> next;
		//     }
		//     printf(" -> NULL\\n");
		// }

		// void deleteFirst(struct Node ** head) {
		//     if ( * head == NULL) {
		//         printf("List is empty");
		//         return;
		//     }
		//     struct Node * temp = * head;
		//     * head = ( * head) -> next;
		//     free(temp);
		//     printf("First node deleted\\n");
		// }

		// void insertEnd(struct Node ** head, int data) {
		//     struct Node * newNode = (struct Node * ) malloc(sizeof(struct Node));
		//     newNode -> data = data;
		//     newNode -> next = NULL;

		//     if ( * head == NULL) {
		//         * head = newNode;
		//         printf("Node inserted at end\\n");
		//         return;
		//     }

		//     struct Node * temp = * head;
		//     while (temp -> next != NULL) {
		//         temp = temp -> next;
		//     }
		//     temp -> next = newNode;
		//     printf("Node inserted at end\\n");
		// }

		// void deleteLast(struct Node ** head) {
		//     if ( * head == NULL) {
		//         printf("List is empty\\n");
		//         return;
		//     }

		//     if (( * head) -> next == NULL) {
		//         free( * head);
		//         * head = NULL;
		//         printf("Last node deleted\\n");
		//         return;
		//     }

		//     struct Node * temp = * head;
		//     while (temp -> next -> next != NULL) {
		//         temp = temp -> next;
		//     }
		//     free(temp -> next);
		//     temp -> next = NULL;
		//     printf("Last node deleted\\n");
		// }

		// void deletePosition(struct Node ** head, int pos) {
		//     if ( * head == NULL) {
		//         printf("List is empty\\n");
		//         return;
		//     }

		//     if (pos == 1) {
		//         deleteFirst(head);
		//         return;
		//     }

		//     struct Node * temp = * head;
		//     for (int i = 1; i < pos - 1 && temp != NULL; i++) {
		//         temp = temp -> next;
		//     }

		//     if (temp == NULL || temp -> next == NULL) {
		//         printf("Position not found\\n");
		//         return;
		//     }

		//     struct Node * nodeToDelete = temp -> next;
		//     temp -> next = nodeToDelete -> next;
		//     free(nodeToDelete);
		//     printf("Node deleted from position %d\\n", pos);
		// }

		// int countNodes(struct Node * head) {
		//     int count = 0;
		//     struct Node * temp = head;
		//     while (temp != NULL) {
		//         count++;
		//         temp = temp -> next;
		//     }
		//     return count;
		// }

		// int main() {
		//     struct Node * head = NULL;
		//     int choice, data, pos;

		//     while (1) {
		//         scanf("%d", & choice);

		//         switch (choice) {
		//         case 1:
		//             scanf("%d", & data);
		//             insertFront( & head, data);
		//             break;
		//         case 2:
		//             display(head);
		//             break;
		//         case 3:
		//             deleteFirst( & head);
		//             break;
		//         case 4:
		//             scanf("%d", & data);
		//             insertEnd( & head, data);
		//             break;
		//         case 5:
		//             deleteLast( & head);
		//             break;
		//         case 6:
		//             printf("Enter position: ");
		//             scanf("%d", & pos);
		//             deletePosition( & head, pos);
		//             break;
		//         case 7:
		//             printf("Number of nodes: %d", countNodes(head));
		//             break;
		//         case 8:
		//             exit(0);
		//         default:
		//             printf("Invalid choice");
		//         }
		//     }

		//     return 0;
		// }`),
		//       },
		//       points: 25,
		//     },
		//     {
		//       instructions: `Write a C program to compare two singly linked lists and determine if they are identical. Two lists are considered identical if they have the same number of nodes, and the data in corresponding nodes is the same.

		// **Input Format:**
		// The program will read integers from standard input to build two lists. A sentinel value of \`-1\` will be used to signify the end of input for each list.
		// Example: \`10 20 30 -1 10 20 30 -1\` would create two lists: (10 -> 20 -> 30) and (10 -> 20 -> 30).

		// **Steps:**
		// 1. Define the \`struct Node\` with an integer \`data\` and a pointer \`next\`.
		// 2. Create a helper function (e.g., \`insertEnd\`) to add nodes to the end of a list. This will simplify your main function.
		// 3. In \`main\`, create two head pointers, \`head1\` and \`head2\`, initialized to NULL.
		// 4. Read integers in a loop for the first list, calling your insert function for each one, until \`-1\` is read.
		// 5. Do the same for the second list.
		// 6. Implement a comparison function, e.g., \`areSame(struct Node* a, struct Node* b)\`, that returns 1 if the lists are identical and 0 otherwise.
		// 7. The comparison logic should traverse both lists simultaneously. If a data mismatch is found, or if one list is longer than the other, they are not the same.
		// 8. Based on the return value of your comparison function, print either \`"Lists are same"\` or \`"Lists are not same"\`.

		// **Example Scenarios:**
		// - Input: \`10 20 -1 10 20 -1\` → Output: \`Lists are same\`
		// - Input: \`10 20 -1 10 20 30 -1\` → Output: \`Lists are not same\`
		// - Input: \`5 15 -1 5 25 -1\` → Output: \`Lists are not same\``,
		//       title: 'Compare Two Singly Linked Lists',
		//       description:
		//         'Write a C program that takes two singly linked lists as input and determines if they are identical in structure and content.',
		//       difficulty: 'medium',

		//       language: 'c',
		//       hints: [
		//         {
		//           question: 'How should I approach the comparison logic?',
		//           answer:
		//             'Traverse both lists at the same time using two pointers. In each iteration, check if the data of the current nodes is equal. If not, they are different. The loop should continue as long as both pointers are not NULL.',
		//         },
		//         {
		//           question: 'What if the lists have different lengths?',
		//           answer:
		//             'After your traversal loop finishes, one pointer might be NULL while the other is not. If both pointers are not NULL at the same time, the lists have different lengths and are therefore not the same.',
		//         },
		//         {
		//           question: 'How do I handle empty lists?',
		//           answer:
		//             'Your logic should naturally handle this. If both heads are NULL, the loop condition will be false, and the final check (are both pointers NULL?) will pass. If one is NULL and the other is not, it will correctly fail.',
		//         },
		//       ],
		//       resources: [
		//         {
		//           title: 'Singly Linked List in C - GeeksforGeeks',
		//           url: 'https://www.geeksforgeeks.org/singly-linked-list-tutorial',
		//         },
		//         {
		//           title: 'Identical Linked Lists - GeeksforGeeks',
		//           url: 'https://www.geeksforgeeks.org/identical-linked-lists/',
		//         },
		//       ],
		//       testsRequirement: `-   **Scenario 1: Identical Lists (Same length, same elements)**
		//     -   Input: \`10 20 30 -1 10 20 30 -1\`
		//     -   Expected Output: \`Lists are same\`

		// -   **Scenario 2: Different Lengths (First list shorter)**
		//     -   Input: \`10 20 -1 10 20 30 -1\`
		//     -   Expected Output: \`Lists are not same\`

		// -   **Scenario 3: Different Lengths (Second list shorter)**
		//     -   Input: \`10 20 30 -1 10 20 -1\`
		//     -   Expected Output: \`Lists are not same\`

		// -   **Scenario 4: Same Length, Different Elements**
		//     -   Input: \`5 15 25 -1 5 20 25 -1\`
		//     -   Expected Output: \`Lists are not same\`

		// -   **Scenario 5: Both Lists Empty**
		//     -   Input: \`-1 -1\`
		//     -   Expected Output: \`Lists are same\`
		// `,
		//       solutionCode: {
		//         c: cleanCode(`#include <stdio.h>
		// #include <stdlib.h>

		// struct Node {
		//     int data;
		//     struct Node* next;
		// };

		// void insertEnd(struct Node** head_ref, int new_data) {
		//     struct Node* new_node = (struct Node*)malloc(sizeof(struct Node));
		//     struct Node* last = *head_ref;
		//     new_node->data = new_data;
		//     new_node->next = NULL;
		//     if (*head_ref == NULL) {
		//         *head_ref = new_node;
		//         return;
		//     }
		//     while (last->next != NULL) {
		//         last = last->next;
		//     }
		//     last->next = new_node;
		// }

		// int areSame(struct Node* a, struct Node* b) {
		//     while (a != NULL && b != NULL) {
		//         if (a->data != b->data) {
		//             return 0; // Data is different
		//         }
		//         a = a->next;
		//         b = b->next;
		//     }

		//     return (a == NULL && b == NULL);
		// }

		// int main() {
		//     struct Node* head1 = NULL;
		//     struct Node* head2 = NULL;
		//     int data;

		//     while (scanf("%d", &data) == 1 && data != -1) {
		//         insertEnd(&head1, data);
		//     }

		//     while (scanf("%d", &data) == 1 && data != -1) {
		//         insertEnd(&head2, data);
		//     }

		//     if (areSame(head1, head2)) {
		//         printf("Lists are same");
		//     } else {
		//         printf("Lists are not same");
		//     }

		//     return 0;
		// }`),
		//       },
		//       points: 15,
		//     },
		//     {
		//       instructions: `Write a C program that removes duplicate elements from a sorted singly linked list. The list should be modified in-place, and the remaining elements should still be sorted.

		// **Input Format:**
		// The program will read a sequence of sorted integers from standard input to build the linked list. A sentinel value of \`-1\` will signify the end of the input sequence.

		// **Steps:**
		// 1.  Define the \`struct Node\` with an integer \`data\` and a pointer \`next\`.
		// 2.  Implement a helper function (e.g., \`insertEnd\`) to build the linked list from the input integers.
		// 3.  Implement the core function, \`removeDuplicates(struct Node* head)\`.
		// 4.  In this function, traverse the list with a pointer, let's call it \`current\`. The traversal should continue as long as \`current\` and \`current->next\` are not NULL.
		// 5.  Inside the loop, compare the data of the \`current\` node with the data of the \`current->next\` node.
		// 6.  If the data is the same, it's a duplicate. You need to bypass this duplicate node:
		//     a. Create a temporary pointer to \`current->next\` (the node to be deleted).
		//     b. Update \`current->next\` to point to the node *after* the duplicate (\`temp->next\`).
		//     c. Free the memory of the temporary pointer.
		// 7.  **Important:** If you delete a node, do *not* advance the \`current\` pointer in that iteration. This allows you to check for multiple duplicates (e.g., 13 → 13 → 13).
		// 8.  If the data is different, it means there's no duplicate, so you can safely move to the next node by setting \`current = current->next\`.
		// 9.  Implement a \`display\` function to print the final list in the format \`data1 -> data2 -> NULL\`.

		// **Example:**
		// -   Input: \`1 1 6 13 13 13 27 27 -1\`
		// -   Output: \`1 -> 6 -> 13 -> 27 -> NULL\``,
		//       title: 'Remove Duplicates from Sorted List',
		//       description:
		//         'Write a C program to remove duplicate nodes from a given sorted singly linked list.',
		//       difficulty: 'hard',

		//       language: 'c',
		//       hints: [
		//         {
		//           question: 'How do I check for a duplicate?',
		//           answer:
		//             'Since the list is sorted, you only need to compare a node with the one immediately following it (`current->data == current->next->data`).',
		//         },
		//         {
		//           question: 'What is the correct way to delete the node?',
		//           answer:
		//             'Store the node to be deleted (`current->next`) in a temporary variable, update `current->next` to `current->next->next`, and then `free` the temporary variable.',
		//         },
		//         {
		//           question: 'What if there are more than two duplicates in a row, like 5 -> 5 -> 5?',
		//           answer:
		//             'When you delete a duplicate, do not advance your main pointer (`current`). The loop will run again, comparing `current` with its new `next` node, handling consecutive duplicates correctly.',
		//         },
		//       ],
		//       resources: [
		//         {
		//           title: 'Remove duplicates from a sorted linked list - GeeksforGeeks',
		//           url: 'https://www.geeksforgeeks.org/remove-duplicates-from-a-sorted-linked-list/',
		//         },
		//         {
		//           title: 'Singly Linked List in C (Tutorial)',
		//           url: 'https://www.programiz.com/dsa/singly-linked-list',
		//         },
		//       ],
		//       testsRequirement: `-   **Scenario 1: Mixed duplicates and unique elements**
		//     -   Input: \`1 1 6 13 13 13 27 27 -1\`
		//     -   Expected Output: \`1 -> 6 -> 13 -> 27 -> NULL\`

		// -   **Scenario 2: No duplicates (all unique elements)**
		//     -   Input: \`1 2 3 4 5 -1\`
		//     -   Expected Output: \`1 -> 2 -> 3 -> 4 -> 5 -> NULL\`

		// -   **Scenario 3: All elements are duplicates**
		//     -   Input: \`7 7 7 7 -1\`
		//     -   Expected Output: \`7 -> NULL\`

		// -   **Scenario 4: Empty list**
		//     -   Input: \`-1\`
		//     -   Expected Output: \`List is empty\`

		// -   **Scenario 5: Single element list**
		//     -   Input: \`42 -1\`
		//     -   Expected Output: \`42 -> NULL\`
		// `,
		//       solutionCode: {
		//         c: cleanCode(`#include <stdio.h>
		// #include <stdlib.h>

		// struct Node {
		//     int data;
		//     struct Node* next;
		// };

		// void insertEnd(struct Node** head_ref, int new_data) {
		//     struct Node* new_node = (struct Node*)malloc(sizeof(struct Node));
		//     new_node->data = new_data;
		//     new_node->next = NULL;

		//     if (*head_ref == NULL) {
		//         *head_ref = new_node;
		//         return;
		//     }
		//     struct Node* last = *head_ref;
		//     while (last->next != NULL) {
		//         last = last->next;
		//     }
		//     last->next = new_node;
		// }

		// void display(struct Node* node) {
		//     if (node == NULL) {
		//         printf("List is empty");
		//         return;
		//     }
		//     while (node != NULL) {
		//         printf("%d", node->data);
		//         if (node->next != NULL) {
		//             printf(" -> ");
		//         }
		//         node = node->next;
		//     }
		//     printf(" -> NULL");
		// }

		// void removeDuplicates(struct Node* head) {
		//     struct Node* current = head;
		//     if (current == NULL) return;

		//     while (current->next != NULL) {
		//         if (current->data == current->next->data) {
		//             struct Node* temp = current->next;
		//             current->next = temp->next;
		//             free(temp);
		//         } else {
		//             current = current->next;
		//         }
		//     }
		// }

		// int main() {
		//     struct Node* head = NULL;
		//     int data;

		//     while (scanf("%d", &data) == 1 && data != -1) {
		//         insertEnd(&head, data);
		//     }

		//     removeDuplicates(head);

		//     display(head);

		//     return 0;
		// }`),
		//       },
		//       points: 0,
		//     },
		//     {
		//       instructions: `Write a C program to add two matrices. The program should read the dimensions of the matrices, read the elements of both matrices, perform matrix addition, and display the result.

		// **Input Format:**
		// 1. First line: Two integers \`rows\` and \`cols\` representing the dimensions of the matrices
		// 2. Next \`rows\` lines: Elements of the first matrix (space-separated)
		// 3. Next \`rows\` lines: Elements of the second matrix (space-separated)

		// **Steps:**
		// 1. Read the number of rows and columns for the matrices
		// 2. Declare two 2D arrays to store the input matrices and one for the result
		// 3. Read elements of the first matrix row by row
		// 4. Read elements of the second matrix row by row
		// 5. Add corresponding elements of both matrices and store in the result matrix
		// 6. Display the result matrix in proper format

		// **Output Format:**
		// Display the result matrix with each row on a new line and elements separated by spaces.

		// **Example:**
		// - Input:
		//   \`\`\`
		//   2 3
		//   1 2 3
		//   4 5 6
		//   7 8 9
		//   10 11 12
		//   \`\`\`
		// - Output:
		//   \`\`\`
		//   8 10 12
		//   14 16 18
		//   \`\`\`

		// **Notes:**
		// - Assume the input matrices have the same dimensions
		// - Use integers for matrix elements
		// - Matrix dimensions will be at most 10x10`,
		//       title: 'Matrix Addition',
		//       description:
		//         'Write a C program to add two matrices of the same dimensions and display the result.',
		//       difficulty: 'easy',

		//       language: 'c',
		//       hints: [
		//         {
		//           question: 'How do I read a 2D array in C?',
		//           answer:
		//             'Use nested loops: outer loop for rows, inner loop for columns. Use scanf("%d", &matrix[i][j]) to read each element.',
		//         },
		//         {
		//           question: 'How do I add two matrices?',
		//           answer:
		//             'Use nested loops to iterate through each position and add corresponding elements: result[i][j] = matrix1[i][j] + matrix2[i][j].',
		//         },
		//         {
		//           question: 'How should I display the result matrix?',
		//           answer:
		//             'Use nested loops with printf("%d ", result[i][j]) for elements and printf("\\n") after each row.',
		//         },
		//       ],
		//       resources: [
		//         {
		//           title: 'Matrix Addition in C - GeeksforGeeks',
		//           url: 'https://www.geeksforgeeks.org/c-program-to-add-two-matrices/',
		//         },
		//         {
		//           title: 'Two Dimensional Arrays in C (Tutorial)',
		//           url: 'https://www.programiz.com/c-programming/c-multi-dimensional-arrays',
		//         },
		//       ],
		//       testsRequirement: `-   **Scenario 1: 2x3 matrices with positive integers**
		//     -   Input:
		//         \`\`\`
		//         2 3
		//         1 2 3
		//         4 5 6
		//         7 8 9
		//         10 11 12
		//         \`\`\`
		//     -   Expected Output:
		//         \`\`\`
		//         8 10 12
		//         14 16 18
		//         \`\`\`

		// -   **Scenario 2: 3x2 matrices with mixed positive and negative integers**
		//     -   Input:
		//         \`\`\`
		//         3 2
		//         1 -2
		//         3 4
		//         -5 6
		//         -1 2
		//         -3 -4
		//         5 -6
		//         \`\`\`
		//     -   Expected Output:
		//         \`\`\`
		//         0 0
		//         0 0
		//         0 0
		//         \`\`\`

		// -   **Scenario 3: 1x1 matrix (single element)**
		//     -   Input:
		//         \`\`\`
		//         1 1
		//         5
		//         3
		//         \`\`\`
		//     -   Expected Output:
		//         \`\`\`
		//         8
		//         \`\`\`
		// `,
		//       solutionCode: {
		//         c: cleanCode(`#include <stdio.h>

		// int main() {
		//     int rows, cols;
		//     scanf("%d %d", &rows, &cols);

		//     int matrix1[10][10], matrix2[10][10], result[10][10];

		//     // Read first matrix
		//     for (int i = 0; i < rows; i++) {
		//         for (int j = 0; j < cols; j++) {
		//             scanf("%d", &matrix1[i][j]);
		//         }
		//     }

		//     // Read second matrix
		//     for (int i = 0; i < rows; i++) {
		//         for (int j = 0; j < cols; j++) {
		//             scanf("%d", &matrix2[i][j]);
		//         }
		//     }

		//     // Add matrices
		//     for (int i = 0; i < rows; i++) {
		//         for (int j = 0; j < cols; j++) {
		//             result[i][j] = matrix1[i][j] + matrix2[i][j];
		//         }
		//     }

		//     // Display result
		//     for (int i = 0; i < rows; i++) {
		//         for (int j = 0; j < cols; j++) {
		//             printf("%d", result[i][j]);
		//             if (j < cols - 1) {
		//                 printf(" ");
		//             }
		//         }
		//         printf("\\n");
		//     }

		//     return 0;
		// }`),
		//       },
		//       points: 15,
		//     },
	];

	return mockData.map((item) => ({
		...item,
		tenant: tenantId,
		starterCode: "",
		subject: subjectId,
	}));
};
