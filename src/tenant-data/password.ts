type PasswordOptions = {
  length: number;
  easyToEnter?: boolean;
};

export function generatePassword({ length, easyToEnter = false }: PasswordOptions): string {
  const UPPERCASE = 'ABCDEFGHJKLMNPQRSTUVWXYZ'; // excluding similar-looking chars like I, O
  const LOWERCASE = 'abcdefghjkmnpqrstuvwxyz';
  const DIGITS = '23456789'; // exclude 0, 1
  const SYMBOLS = '!@#$%&*?';

  let characters = UPPERCASE + LOWERCASE + DIGITS + SYMBOLS;

  if (easyToEnter) {
    characters = UPPERCASE;
  }

  let password = '';
  for (let i = 0; i < length; i++) {
    const index = Math.floor(Math.random() * characters.length);
    password += characters[index];
  }

  return password;
}
