import { type Subject } from '../payload-types';

export const createSubjectData = (tenantId: number) => {
  const subjectsData: Omit<Subject, 'id' | 'createdAt' | '' | 'updatedAt'>[] = [
    {
      name: 'Data Structures',
      description:
        'Explore efficient methods to organize and store data, essential for optimizing algorithms and enhancing software performance.',
      tenant: tenantId,
    },
  ];

  return subjectsData;
};
