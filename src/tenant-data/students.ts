import type { User } from "./types";

export const studentsData: User[] = [
	{
		username: "<PERSON><PERSON><PERSON>",
		fullName: "PARVANI HETVI JAGDISHBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 1,
		password: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
	},
	{
		username: "<PERSON><PERSON><PERSON>",
		fullName: "RAMOLIYA SHLOK SAMIRBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 1,
		password: "SQFGHILX",
	},
	{
		username: "<PERSON><PERSON><PERSON><PERSON>",
		fullName: "GOHEL DHANVI JAYESHBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 1,
		password: "J<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
	},
	{
		username: "<PERSON><PERSON>",
		fullName: "<PERSON><PERSON> ATARA",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 1,
		password: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
	},
	{
		username: "AVANI",
		fullName: "SATROTIYA AVANI DILIPBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 1,
		password: "FGUIPOSD",
	},
	{
		username: "BHAVYABEN",
		fullName: "DESAI BHAVYABEN UDAYBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 1,
		password: "WERTYUIP",
	},
	{
		username: "KRINA",
		fullName: "KANKRECHA KRINA HITENDRABHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 1,
		password: "ASXDFGHK",
	},
	{
		username: "KRUPA",
		fullName: "TRIVEDI KRUPA KETANBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 1,
		password: "LZXCVBNM",
	},
	{
		username: "VRUNDA",
		fullName: "PARSANIA VRUNDA DARSHANBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 1,
		password: "QWERTYUI",
	},
	{
		username: "NEEL",
		fullName: "DAVA NEEL HARSHADBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 1,
		password: "ASDFGHJK",
	},
	{
		username: "DEVANG",
		fullName: "MATHUKIYA DEVANG JIVANBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 1,
		password: "POIUYTTR",
	},
	{
		username: "JENISH",
		fullName: "GANDHI JENISH KETAN",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 1,
		password: "LKJHGFDS",
	},
	{
		username: "DHRUVIL",
		fullName: "VADGAMA DHRUVIL JAYESHBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 1,
		password: "MNBVCXZZ",
	},
	{
		username: "SAKSHI",
		fullName: "LODHIA SAKSHI BHAVESH",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 1,
		password: "QAZWSXED",
	},
	{
		username: "NIR",
		fullName: "GAMBHAVA NIR BHAVESHBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 1,
		password: "RFVTGBNH",
	},
	{
		username: "PRIYAL",
		fullName: "RATHOD PRIYAL SANJAYSINH",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 1,
		password: "YUJMKLOP",
	},
	{
		username: "PREET",
		fullName: "PARMAR PREET VIPULKUMAR",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 1,
		password: "GHNJKLMN",
	},
	{
		username: "MEET",
		fullName: "BAGHORA MEET RAJESH",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 1,
		password: "ZAQWSCED",
	},
	{
		username: "NEHA",
		fullName: "KAVAIYA NEHA AMITBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 1,
		password: "RFVTGHYU",
	},
	{
		username: "HITESH",
		fullName: "CHAVDA HITESH JITESHBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 1,
		password: "IKOLPMNB",
	},
	{
		username: "UCHIT",
		fullName: "KAMDAR UCHIT SAMIRBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 2,
		password: "CXSWDFGT",
	},
	{
		username: "MARGI",
		fullName: "SHAH MARGI RAKESHBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 2,
		password: "HNJUYTGF",
	},
	{
		username: "DHRUV",
		fullName: "MACHCHHAR DHRUV BIMALBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 2,
		password: "KLOPMNBG",
	},
	{
		username: "DHAIRY",
		fullName: "KAILA DHAIRY ASHOKBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 2,
		password: "QWERTYUY",
	},
	{
		username: "YASHVI",
		fullName: "RAMANI YASHVI VIMALBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 2,
		password: "ASDFGHJK",
	},
	{
		username: "RISHABH",
		fullName: "JOSHI RISHABH DHARITBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 2,
		password: "POIUYTRD",
	},
	{
		username: "DHARM",
		fullName: "VACHHANI DHARM SHAILESHBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 2,
		password: "LKJHGFRT",
	},
	{
		username: "VEERBHADRASINH",
		fullName: "JADEJA VEERBHADRASINH DIGVIJAYSINH",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 2,
		password: "MNBVCXSA",
	},
	{
		username: "ELVISH",
		fullName: "KANSAGARA ELVISH PARESHBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 2,
		password: "ZXCVBNML",
	},
	{
		username: "RISHI",
		fullName: "PARMAR RISHI VIJAYBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 2,
		password: "QWERTYUI",
	},
	{
		username: "VINAYAK",
		fullName: "LAKHTARIYA VINAYAK PINAKINBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 2,
		password: "ASDFGHJK",
	},
	{
		username: "HET",
		fullName: "KANASAGARA HET SANJAYBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 2,
		password: "POIUYTRF",
	},
	{
		username: "JEEL",
		fullName: "GOHEL JEEL KAPILKUMAR",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 2,
		password: "LKJHGFRT",
	},
	{
		username: "SUHANI",
		fullName: "PARMAR SUHANI AMITBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 2,
		password: "MNBVCXZX",
	},
	{
		username: "MEET",
		fullName: "KIKANI MEET VIJAYBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 2,
		password: "ZXCVBNMC",
	},
	{
		username: "NAMRA",
		fullName: "BHATTI NAMRA DIPENBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 2,
		password: "QAZXSWED",
	},
	{
		username: "HIRENKUMAR",
		fullName: "RATHOD HIRENKUMAR JITENDRAKUMAR",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 2,
		password: "RFVTGBNH",
	},
	{
		username: "ROHAN",
		fullName: "ROHAN VYAS",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 2,
		password: "YUJMKIOL",
	},
	{
		username: "HEMANG",
		fullName: "JOSHI HEMANG GIRISHKUMAR",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 2,
		password: "EDCFRTGB",
	},
	{
		username: "ANJALI",
		fullName: "VAGHELA ANJALI JAYESHBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 2,
		password: "YHNMKJUY",
	},
	{
		username: "YASH",
		fullName: "GAJERA YASH MANISHBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "GFDSARQW",
	},
	{
		username: "PARESH",
		fullName: "PARMAR PARESH MANUBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "XCVBNMKL",
	},
	{
		username: "MAULIK",
		fullName: "RATHOD MAULIK MANAHARBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "QWERTYUI",
	},
	{
		username: "KARTAVYA",
		fullName: "HINDOCHA KARTAVYA KALPESHBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "ASDFGHJK",
	},
	{
		username: "RACHIT",
		fullName: "PATEL RACHIT ASHISHBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "POIUYTRF",
	},
	{
		username: "ROHAN",
		fullName: "SANKHAT ROHAN BABUBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "LKJHGFRT",
	},
	{
		username: "MAMTA",
		fullName: "CHHAIYA MAMTA RAMESHBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "MNBVCXZQ",
	},
	{
		username: "KUSHAL",
		fullName: "BHUVA KUSHAL HITESHBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "ZXCVBNMA",
	},
	{
		username: "PRACHI",
		fullName: "DAVDA PRACHI RAJESHBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "QWERTYUU",
	},
	{
		username: "HARMIT",
		fullName: "DODIYA HARMIT UPENDRABHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "ASDFGHJJ",
	},
	{
		username: "JENIL",
		fullName: "RACHCHH JENIL AJAYBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "POIUYTRF",
	},
	{
		username: "PARV",
		fullName: "DAVE PARV BHADRESHBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "LKJHGFRS",
	},
	{
		username: "KARAN",
		fullName: "VASANI KARAN MITALBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "MNBVCXAA",
	},
	{
		username: "SMIT",
		fullName: "TANK SMIT NATHALAL",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "ZXCVBNMV",
	},
	{
		username: "PREET",
		fullName: "VORA PREET BHAVESH",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "QAZWSXDC",
	},
	{
		username: "BHAGIRATH",
		fullName: "VAJA BHAGIRATH JAGMALBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "RFVTGBHNN",
	},
	{
		username: "VANSH",
		fullName: "KACHA VANSH NIKUNJBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "YUJMKIOP",
	},
	{
		username: "VED",
		fullName: "GHETIYA VED SAMIR",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "EDCFRTGH",
	},
	{
		username: "DHAVAL",
		fullName: "JETHVA DHAVAL NARENDRABHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "YHNMKJUI",
	},
	{
		username: "NENCY",
		fullName: "RATHOD NENCY VIJAYBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "GFDSAQAZ",
	},
	{
		username: "MEET",
		fullName: "INDORIYA MEET KIRANBHAI",
		email: "<EMAIL>",
		roles: ["student"],
		division: "D2D-A1",
		labBatchNo: 3,
		password: "XCVBNMLK",
	},

	// {
	//   username: '188',
	//   fullName: 'Ambasana Venisha Hitenbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   password: 'FHVBOBDU',
	//   labBatchNo: 5,
	// },
	// {
	//   username: '195',
	//   fullName: 'Buddhadev Misri Sanjaybhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 5,
	//   password: '7V0SAXCP',
	// },
	// {
	//   username: '181',
	//   fullName: 'Charadva Abhishek Virenbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 5,
	//   password: 'L5BV4MET',
	// },
	// {
	//   username: '191',
	//   fullName: 'Dave Dhruv Viralkumar',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 5,
	//   password: 'C67RPIAZ',
	// },
	// {
	//   username: '182',
	//   fullName: 'Dave Krisha Kalpeshbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 5,
	//   password: 'X4QKAK1D',
	// },
	// {
	//   username: '190',
	//   fullName: 'Gambhava Mirali Sureshbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 5,
	//   password: '9BWW6XR4',
	// },
	// {
	//   username: '192',
	//   fullName: 'Ghetiya Ritu Pankajbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 5,
	//   password: 'RBCETLF7',
	// },
	// {
	//   username: '196',
	//   fullName: 'Joshi Kairav Paren',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 5,
	//   password: 'UPWTS2EI',
	// },
	// {
	//   username: '183',
	//   fullName: 'Janvi Kalaria',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 5,
	//   password: '8LH6OLPL',
	// },
	// {
	//   username: '187',
	//   fullName: 'Panchal Vedantkumar Niravbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 5,
	//   password: 'ZX78A0HI',
	// },
	// {
	//   username: '193',
	//   fullName: 'Radhanpara Saumya Jatinbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 5,
	//   password: 'BU5BCDE4',
	// },
	// {
	//   username: '198',
	//   fullName: 'Radhanpura Jeel Nimish',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 5,
	//   password: 'BBKWJWKZ',
	// },
	// {
	//   username: '189',
	//   fullName: 'Raichura Smeet Rohitkumar',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 5,
	//   password: 'YHN3UPSB',
	// },
	// {
	//   username: '184',
	//   fullName: 'Rajwani Rohan Malik',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 5,
	//   password: 'BW4ZJ4SI',
	// },
	// {
	//   username: '185',
	//   fullName: 'Rana Parthrajsinh Bhupatsinh',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 5,
	//   password: '6DQWZY6N',
	// },
	// {
	//   username: '197',
	//   fullName: 'Savaliya Dhruvi Bharatbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 5,
	//   password: 'DGXH2IVF',
	// },
	// {
	//   username: '199',
	//   fullName: 'Savaliya Priyanshi Sureshbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 5,
	//   password: 'BDFOU2HX',
	// },
	// {
	//   username: '186',
	//   fullName: 'Solanki Ronak Arvindbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 5,
	//   password: 'Z0FHHQIK',
	// },
	// {
	//   username: '194',
	//   fullName: 'Yadav Gaurav Kaushikbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 5,
	//   password: '9UUGNWYV',
	// },
	// {
	//   username: '200',
	//   fullName: 'Zala Bhavyadeepsinh Pruthvirajsinh',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 5,
	//   password: 'DDFSHLXT',
	// },
	// {
	//   username: '215',
	//   fullName: 'Ambaliya Manan Jayeshbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 6,
	//   password: 'HQDIBEFM',
	// },
	// {
	//   username: '213',
	//   fullName: 'Badrakiya Meet Bharatbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 6,
	//   password: '8UBFVCG7',
	// },
	// {
	//   username: '210',
	//   fullName: 'Bhanushali Hiral Bhaven',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 6,
	//   password: 'XEUKWB6X',
	// },
	// {
	//   username: '216',
	//   fullName: 'Hinsu Umang Bharatbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 6,
	//   password: 'H1WK9BFQ',
	// },
	// {
	//   username: '217',
	//   fullName: 'Kakaniya Bhavy Niteshbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 6,
	//   password: 'JMXF2X8Q',
	// },
	// {
	//   username: '211',
	//   fullName: 'Kamaliya Akashkumar Rameshbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 6,
	//   password: 'ABAVO4KI',
	// },
	// {
	//   username: '214',
	//   fullName: 'Kanani Kavit Nareshbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 6,
	//   password: 'CDJJMJXH',
	// },
	// {
	//   username: '202',
	//   fullName: 'Lakhtaria Kevin Homin',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 6,
	//   password: 'LNWP9YOY',
	// },
	// {
	//   username: '219',
	//   fullName: 'Mankada Hasan Shabbirhusain',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 6,
	//   password: 'Y5SQWGZU',
	// },
	// {
	//   username: '220',
	//   fullName: 'Mehta Dhairy Kalpeshbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 6,
	//   password: '0DFWK5RO',
	// },
	// {
	//   username: '206',
	//   fullName: 'Mendapara Piyu Amishbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 6,
	//   password: 'LVZ9AKBN',
	// },
	// {
	//   username: '203',
	//   fullName: 'Moliya Kartavya Bhaveshbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 6,
	//   password: 'I3OEFPQP',
	// },
	// {
	//   username: '204',
	//   fullName: 'Monpara Nij Hareshbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 6,
	//   password: 'NXHNFXMC',
	// },
	// {
	//   username: '218',
	//   fullName: 'Padia Kartik Balkrishna',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 6,
	//   password: 'H6FHVIB3',
	// },
	// {
	//   username: '207',
	//   fullName: 'Pandya Karan Tusharbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 6,
	//   password: 'M7OCTR3F',
	// },
	// {
	//   username: '212',
	//   fullName: 'Popat Honey Nileshbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 6,
	//   password: 'FRG8T55E',
	// },
	// {
	//   username: '208',
	//   fullName: 'Raval Meet Mohitkumar',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 6,
	//   password: 'CI1T4M2W',
	// },
	// {
	//   username: '201',
	//   fullName: 'Sinha Milankumar Sujitkumar',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 6,
	//   password: 'A3VTDRVG',
	// },
	// {
	//   username: '205',
	//   fullName: 'Divya Thakar',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 6,
	//   password: '3JEVTOTZ',
	// },
	// {
	//   username: '209',
	//   fullName: 'Virpariya Heer Manishbhai',
	//   email: '<EMAIL>',
	//   roles: ['student'],
	//   division: 'CSE-3A',
	//   labBatchNo: 6,
	//   password: 'QZHRAFAD',
	// },
];

async function testLogins(users: User[]) {
	let successfulLogins = 0;
	let failedLogins = 0;
	const results: { email: string; status: string; message?: string }[] = [];

	async function testLogin(user: User) {
		const { email, password } = user;
		try {
			const res = await fetch("http://localhost:3000/api/users/login", {
				method: "POST",
				credentials: "include",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ email, password }),
			});

			if (res.ok) {
				successfulLogins++;
				results.push({ email, status: "SUCCESS" });
				console.log(`✅ Login successful for: ${email}`);
			} else {
				failedLogins++;
				let errorMessage = `HTTP Error ${res.status}: ${res.statusText}`;
				try {
					const errorBody = await res.json();
					if (errorBody && errorBody.message) {
						errorMessage = errorBody.message;
					}
				} catch (e) {}
				results.push({ email, status: "FAILED", message: errorMessage });
				console.error(`❌ Login failed for: ${email} - ${errorMessage}`);
			}
		} catch (e: any) {
			failedLogins++;
			const errorMessage = e instanceof Error ? e.message : String(e);
			results.push({
				email,
				status: "FAILED",
				message: `Network/Client Error: ${errorMessage}`,
			});
			console.error(`❗ Error testing login for ${email}: ${errorMessage}`);
		}
	}

	await Promise.allSettled(users.map((user) => testLogin(user)));

	console.log("\n--- Login Test Summary ---");
	console.log(`Total users tested: ${users.length}`);
	console.log(`Successful logins: ${successfulLogins}`);
	console.log(`Failed logins: ${failedLogins}`);
	console.log("------------------------");
}

if (import.meta.url === `file://${process.argv[1]}`) {
	testLogins(studentsData);
}
