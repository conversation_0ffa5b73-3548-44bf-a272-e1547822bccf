import { Agent } from '@mastra/core/agent';
import { getModel } from '../config';

export const createSolutionAgent = () => {
  const model = getModel(true);
  return new Agent({
    name: 'Solution Agent',
    instructions: `You are an expert programming instructor. Your task is to generate a solution for a given programming assignment, based on the provided starter code and instructions.`,
    model,
  });
};
