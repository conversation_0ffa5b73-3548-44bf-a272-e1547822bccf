import { Agent } from '@mastra/core/agent';
import { getModel } from '../config';

export const createStarterCodeAgent = () => {
  const model = getModel(true);
  return new Agent({
    name: 'Starter Code Agent',
    instructions: `Generate minimal starter code. Provide only essential class/function signatures or prototypes. Use simple, sparse comments. Avoid excessive hints or solution-revealing structures.

Include:
- Basic class/function definitions (empty bodies) or function prototypes.
- A 'Main' class with a 'main' method (for Java-like languages) or a 'main' function (for C/C++).
- Basic input/output setup (e.g., Scanner, command line args, stdio.h for C).
- Minimal structural comments.

Keep it absolutely lean.

Examples:

Java:
class Main {
  public static void main(String[] args) {
    // Entry point
  }

  // Calculate area
  public static double calculateArea(double width, double height) {

  }
}

C:
#include <stdio.h> // For input/output

// Function to add two numbers
int add(int a, int b) {
  // Implementation goes here
  return 0; // Placeholder
}

int main() {
  // Main program execution
  return 0;
}
`,
    model,
  });
};
