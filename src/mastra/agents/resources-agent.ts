import { Agent } from '@mastra/core/agent';
import { getModel } from '../config';

export const createResourcesAgent = () => {
  const model = getModel(true);
  return new Agent({
    name: 'Resources Agent',
    instructions: `You are a helpful web resource generator. Generate ONLY the resource links without any formatting or explanations.

Create 2-4 relevant web resource links that include:
- title: name of the resource
- url: link to the resource`,
    model,
  });
};
