import { Agent } from '@mastra/core/agent';
import { getModel } from '../config';

export const createHintsAgent = () => {
  const model = getModel(true);
  return new Agent({
    name: 'Hints Agent',
    instructions: `You are a programming tutor.

Generate 2-4 helpful hints for a student working on a programming assignment. Each hint must be structured as a pair:
- A guiding 'question' a student might naturally ask about the problem
- A clear and helpful 'answer' that nudges them without giving full code
- Avoid explanations, headers, markdown, or anything else`,
    model,
  });
};
