import { <PERSON><PERSON> } from "@mastra/core";
import { LibSQLStore } from "@mastra/libsql";
import { PinoLogger } from "@mastra/loggers";
import {
	createHintsAgent,
	createInstructionsAgent,
	createResourcesAgent,
} from "./agents";
import { assignmentWorkflow } from "./workflows";
import {
	createSummaryAgent,
	createTestScenariosGeneratorAgent,
	createTestsGeneratorAgent,
} from "./workflows/auto-evaluate-workflow/agents";
import { demoWorkflow } from "./workflows/demo-workflow";
import { summaryWorkflow } from "./workflows/summary-workflow";

const instructionsAgent = createInstructionsAgent();

const hintsAgent = createHintsAgent();

const resourcesAgent = createResourcesAgent();

const testsGeneratorAgent = createTestsGeneratorAgent();

const testScenariosGeneratorAgent = createTestScenariosGeneratorAgent();

const summaryAgent = createSummaryAgent();

export const mastra = new Mastra({
	workflows: {
		demoWorkflow,
		assignmentWorkflow,
		summaryWorkflow,
	},
	agents: {
		instructionsAgent,
		resourcesAgent,
		hintsAgent,
		testScenariosGeneratorAgent,
		testsGeneratorAgent,
		summaryAgent,
	},
	storage: new LibSQLStore({
		// stores telemetry, evals, ... into memory storage, if it needs to persist, change to file:../mastra.db
		url: ":memory:",
	}),
	logger: new PinoLogger({
		name: "Mastra",
		level: "info",
	}),
});
