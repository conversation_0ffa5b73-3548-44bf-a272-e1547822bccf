import { createStep } from '@mastra/core/workflows';
import { z } from 'zod';
import { createStarterCodeAgent } from '../../agents';
import { generateResourcesOutputSchema, generateStarterCodeOutputSchema } from './schemas';

export const generateStarterCode = createStep({
  id: 'generate-starter-code',
  description: 'Generate starter code for students',
  inputSchema: generateResourcesOutputSchema,
  outputSchema: generateStarterCodeOutputSchema,
  execute: async ({ inputData }) => {
    const { title, language } = inputData;
    const starterCodeAgent = createStarterCodeAgent();

    const prompt = `Generate starter code for a programming assignment on the topic: ${title} in ${language}.`;

    const response = await starterCodeAgent.generate([{ role: 'user', content: prompt }], {
      output: z.object({
        starterCode: z.string(),
      }),
    });

    return {
      ...inputData,
      starterCode: response.object.starterCode,
    };
  },
});
