import { createStep } from '@mastra/core/workflows';
import { createResourcesAgent } from '../../agents';
import { resourcesSchema } from '../../schema';
import { baseStepInputSchema, generateResourcesOutputSchema } from './schemas';

export const generateResources = createStep({
  id: 'generate-resources',
  description: 'Create relevant learning resources',
  inputSchema: baseStepInputSchema,
  outputSchema: generateResourcesOutputSchema,
  execute: async ({ inputData }) => {
    const prompt = `Topic: ${inputData!.title}
Language: ${inputData!.language}
Difficulty: ${inputData!.difficulty}`.trim();
    const resourcesAgent = createResourcesAgent();

    const response = await resourcesAgent.generate([{ role: 'user', content: prompt }], {
      output: resourcesSchema,
      temperature: 0.3,
    });

    return {
      ...inputData!,
      resources: response.object,
    };
  },
});
