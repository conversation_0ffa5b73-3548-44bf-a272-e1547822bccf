import { z } from 'zod';
import { assignmentComponentSchema, hintsSchema } from '../../schema';

export { hintsSchema };

const baseFields = {
  testsRequirement: z.string(),
  instructions: z.string(),
  title: z.string(),
  description: z.string(),
  difficulty: z.string(),
  language: z.string(),
};

export const testCasesSchema = z.array(
  z.object({
    title: z.string(),
    input: z.string(),
    expectedOutput: z.string(),
    tolerance: z.number(),
    isHidden: z.boolean(),
  }),
);

const testCasesField = {
  testCases: testCasesSchema,
};

const hintsField = {
  hints: hintsSchema,
};

const resourcesField = {
  resources: z.array(z.object({ title: z.string(), url: z.string().url() })),
};

const starterCodeField = {
  starterCode: z.string(),
};

const solutionCodeField = {
  solutionCode: z.string(),
};

export const baseStepInputSchema = z
  .object({
    ...baseFields,
    ...hintsField,
  })
  .partial();

export const generateInstructionsOutputSchema = z.object({ ...baseFields }).partial();

export const generateHintsOutputSchema = z
  .object({
    ...baseFields,
    ...hintsField,
  })
  .partial();

export const generateResourcesOutputSchema = assignmentComponentSchema.partial();

export const generateStarterCodeOutputSchema = z
  .object({
    ...baseFields,
    ...hintsField,
    ...resourcesField,
    ...starterCodeField,
  })
  .partial();

export const generateSolutionCodeInputSchema = generateStarterCodeOutputSchema.partial();

export const generateSolutionCodeOutputSchema = z
  .object({
    ...baseFields,
    ...hintsField,
    ...resourcesField,
    ...starterCodeField,
    solutionCode: z.string(),
  })
  .partial();

export const generateTestCasesOutputSchema = z.object({
  ...baseFields,
  ...hintsField,
  ...resourcesField,
  ...starterCodeField,
  ...solutionCodeField,
  ...testCasesField,
});
