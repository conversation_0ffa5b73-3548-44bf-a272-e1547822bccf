import { createStep } from '@mastra/core/workflows';
import { z } from 'zod';
import { createInstructionsAgent } from '../../agents';
import { assignmentRequestSchema } from '../../schema';
import { generateInstructionsOutputSchema } from './schemas';

export const generateInstructions = createStep({
  id: 'generate-instructions',
  description: 'Create comprehensive assignment instructions',
  inputSchema: assignmentRequestSchema.partial(),
  outputSchema: generateInstructionsOutputSchema,
  execute: async ({ inputData }) => {
    const { topic, difficulty, language, additionalRequirements } = inputData!;
    const instructionsAgent = createInstructionsAgent();

    const prompt = `Topic: "${topic}"
Language: ${language}
Difficulty: ${difficulty}${additionalRequirements ? `\nAdditional Requirements: ${additionalRequirements}` : ''}`.trim();

    const response = await instructionsAgent.generate([{ role: 'user', content: prompt }], {
      output: z.object({
        sampleScenarios: z.string(),
        instructions: z.string(),
        title: z.string().describe('a descriptive title for the assignment'),
        description: z.string().describe('short 2 line description of the assignment'),
        requiresCommandLineArgs: z
          .boolean()
          .describe('does the assignment require command line arguments?'),
      }),
    });

    return {
      testsRequirement: response.object.sampleScenarios,
      instructions: response.object.instructions,
      title: response.object.title,
      description: response.object.description,
      difficulty,
      requiresCommandLineArgs: response.object.requiresCommandLineArgs,
      language,
    };
  },
});
