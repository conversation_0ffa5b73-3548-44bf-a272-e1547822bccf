import { createStep } from '@mastra/core/workflows';
import { createTestcasesAgent } from '../../agents';
import {
  generateSolutionCodeOutputSchema,
  generateTestCasesOutputSchema,
  testCasesSchema,
} from './schemas';

export const generateTestCases = createStep({
  id: 'generate-testcases',
  description: 'Generate test cases for students',
  inputSchema: generateSolutionCodeOutputSchema,
  outputSchema: generateTestCasesOutputSchema.partial(),
  execute: async ({ inputData }) => {
    const { solutionCode, title, description } = inputData;
    const testcasesAgent = createTestcasesAgent();

    const prompt = `Generate test cases for a programming assignment on the topic: ${title}, description: ${description}\n\n${solutionCode}`;

    const response = await testcasesAgent.generate([{ role: 'user', content: prompt }], {
      output: testCasesSchema,
      maxTokens: 4000,
      temperature: 0.3,
    });

    return {
      ...inputData,
      testCases: response.object,
    };
  },
});
