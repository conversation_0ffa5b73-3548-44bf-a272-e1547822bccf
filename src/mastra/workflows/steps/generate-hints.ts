import { createStep } from '@mastra/core/workflows';
import { createHintsAgent } from '../../agents';
import { hintsSchema } from '../../schema';
import { generateHintsOutputSchema, generateInstructionsOutputSchema } from './schemas';

export const generateHints = createStep({
  id: 'generate-hints',
  description: 'Create strategic hints as Q&A pairs',
  inputSchema: generateInstructionsOutputSchema,
  outputSchema: generateHintsOutputSchema,
  execute: async ({ inputData }) => {
    const { title, difficulty, language } = inputData;

    const hintsAgent = createHintsAgent();

    const userPrompt = `Assignment Details:
- Topic: ${title}
- Programming Language: ${language}
- Difficulty Level: ${difficulty}`.trim();

    const response = await hintsAgent.generate([{ role: 'user', content: userPrompt }], {
      output: hintsSchema,
      temperature: 0.3,
    });

    return {
      ...inputData,
      hints: response.object,
    };
  },
});
