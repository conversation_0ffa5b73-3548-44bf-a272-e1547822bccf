import { createStep } from '@mastra/core/workflows';
import { z } from 'zod';
import { createSolutionAgent } from '../../agents';
import { generateSolutionCodeOutputSchema, generateStarterCodeOutputSchema } from './schemas';

export const generateSolution = createStep({
  id: 'generate-solution',
  description: 'Generate a solution for instructor only',
  inputSchema: generateStarterCodeOutputSchema,
  outputSchema: generateSolutionCodeOutputSchema,
  execute: async ({ inputData }) => {
    const { starterCode, title, description } = inputData;

    const solutionAgent = createSolutionAgent();

    const prompt = `Generate a solution for the following assignment:
- Title: ${title}
- Description: ${description}
- Starter Code:\n${starterCode}`.trim();

    const response = await solutionAgent.generate([{ role: 'user', content: prompt }], {
      output: z.object({
        solutionCode: z.string(),
      }),
      maxTokens: 4000,
      temperature: 0.3,
    });

    return {
      ...inputData,
      solutionCode: response.object.solutionCode,
    };
  },
});
