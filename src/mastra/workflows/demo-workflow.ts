import { Agent, createStep, createWorkflow } from "@mastra/core";
import z from "zod";
import { getModel } from "../config";

const inputSchema = z.object({
	assignment: z.object({
		title: z.string(),
		description: z.string(),
		testsRequirement: z.string(),
	}),
	sourceCode: z.string(),
});

const outputSchema = z.object({
	testcases: z
		.array(
			z.object({
				input: z.string().describe("Input string for the program"),
				expected: z
					.string()
					.describe(
						"EXACT Complete expected stdout output containing all the stdout as per the flow of code as per the inputs given",
					),
			}),
		)
		.min(1, "Must generate at least one test case")
		.describe("Array of test cases for validation"),
});

const demoAgent = new Agent({
	name: "demoAgent",
	instructions: `YOU ARE A COMPREHENSIVE TEST CASE GENERATOR for programming assignments. Your task is to analyze source code and generate precise test cases with exact expected outputs.

**CRITICAL REQUIREMENTS:**

1. **EXACT OUTPUT FORMATTING IS ABSOLUTELY NON-NEGOTIABLE**
   - Generate COMPLETE, LITERAL console output text
   - Include every character that stdout produces
   - Maintain exact spacing, newlines, and formatting
   - NEVER summarize, describe, or truncate outputs

2. **COMPREHENSIVE TEST COVERAGE**
   - Generate test cases that cover normal operations, edge cases, and boundary conditions
   - Include tests for empty inputs, invalid data, and error conditions
   - Test all major code paths and control flow branches
   - Cover loops, conditionals, functions, and object-oriented constructs

3. **LANGUAGE AND PATTERN AGNOSTIC**
   - Handle any programming language (C, Java, Python, JavaScript, etc.)
   - Support all I/O patterns: stdin/stdout, command line arguments, file operations
   - Work with any programming construct: loops, conditionals, functions, OOP
   - Adapt to different coding styles and paradigms

4. **METHODOLOGY:**
   - Analyze the assignment requirements and source code thoroughly
   - Identify all input/output statements and program logic
   - For each test scenario: trace execution step-by-step
   - When encountering output statements, capture EXACTLY what they print
   - Combine all outputs in proper execution order

5. **TEST CASE STRUCTURE:**
   - Each test case must have clear input data
   - Expected output must be the complete, exact stdout text
   - Cover realistic scenarios that students would encounter
   - Include edge cases that reveal common programming errors

**FORBIDDEN ACTIONS:**
- Providing descriptions instead of actual output
- Summarizing or explaining what the output represents
- Partial outputs or truncations
- Adding formatting markers or explanations

**OUTPUT STANDARD:**
Generate test cases that enable precise validation of student code against exact expected outputs, ensuring comprehensive coverage of all programming scenarios while maintaining absolute formatting precision.`,
	model: getModel(false),
});

const demoStep = createStep({
	id: "demoStep",
	inputSchema,
	outputSchema,
	execute: async ({ inputData: { assignment, sourceCode } }) => {
		const response = await demoAgent.generate(
			[
				{
					role: "user",
					content:
						`<assignment><title>${assignment.title}</title><description>${assignment.description}</description></assignment><testsRequirement>${assignment.testsRequirement?.trim()}</testsRequirement><sourceCode>${sourceCode.trim()}</sourceCode>`.trim(),
				},
			],
			{
				output: outputSchema,
			},
		);
		return response.object;
	},
});

export const demoWorkflow = createWorkflow({
	id: "demo-workflow",
	inputSchema,
	outputSchema,
}).then(demoStep);

demoWorkflow.commit();
