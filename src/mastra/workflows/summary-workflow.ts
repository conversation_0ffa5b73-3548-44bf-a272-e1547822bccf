import { createStep, createWorkflow } from '@mastra/core';
import { createSummaryAgent } from './auto-evaluate-workflow/agents';
import {
  summaryGenerationInputSchema,
  summaryGenerationOutputSchema,
} from './auto-evaluate-workflow/schema';

const summaryStep = createStep({
  id: 'summaryStep',
  inputSchema: summaryGenerationInputSchema,
  outputSchema: summaryGenerationOutputSchema,
  execute: async ({ inputData: { code, testsResult } }) => {
    const summaryAgent = createSummaryAgent();
    
    const prompt = `<code>
${code.trim()}
</code>
<testsResult>${JSON.stringify(testsResult)}</testsResult>`.trim();

    const response = await summaryAgent.generate(
      [
        {
          role: 'user',
          content: prompt,
        },
      ],
      {
        output: summaryGenerationOutputSchema,
      },
    );
    
    return response.object;
  },
});

export const summaryWorkflow = createWorkflow({
  id: 'summary-workflow',
  inputSchema: summaryGenerationInputSchema,
  outputSchema: summaryGenerationOutputSchema,
}).then(summaryStep);

summaryWorkflow.commit();
