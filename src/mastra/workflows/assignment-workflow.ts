import { createWorkflow } from '@mastra/core/workflows';
import { assignmentRequestSchema, assignmentComponentSchema } from '../schema';
import {
  generateInstructions,
  generateHints,
  generateResources,
  generateStarterCode,
  generateSolution,
  generateTestCases,
} from './steps';

export const assignmentWorkflow = createWorkflow({
  id: 'ai-assignment-generator',
  inputSchema: assignmentRequestSchema.partial(),
  outputSchema: assignmentComponentSchema.partial(),
})
  .then(generateInstructions)
  .then(generateHints)
  .then(generateResources)
  .then(generateStarterCode)
  .then(generateSolution)
  .then(generateTestCases);

assignmentWorkflow.commit();
