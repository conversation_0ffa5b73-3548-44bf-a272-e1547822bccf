#include <stdio.h> int main() { int n, i, sum = 0; printf("Enter a positive integer: "); scanf("%d", &n); for (i = 1; i <= n; ++i) { sum += i; } printf("Sum = %d\n", sum); return 0; }

**Test Case 1: Positive Integer**

- **Input:** 5
- **Expected Output:** 15 (1 + 2 + 3 + 4 + 5)

**Test Case 2: Small Positive Integer**

- **Input:** 1
- **Expected Output:** 1

**Test Case 3: Larger Positive Integer**

- **Input:** 10
- **Expected Output:** 55 (1 + 2 + 3 + 4 + 5 + 6 + 7 + 8 + 9 + 10)

**Test Case 4: Zero (Edge Case - Depending on how "n numbers" is interpreted, often means 1 to n)**

- **Input:** 0
- **Expected Output:** 0 (The loop `for (i = 1; i <= 0; ++i)` will not execute, so sum remains 0)

**Test Case 5: Negative Integer (Unexpected Input)**

- **Input:** -5
- **Expected Output:** 0 (The loop `for (i = 1; i <= -5; ++i)` will not execute, so sum remains 0. The program as written does not handle negative input gracefully to calculate sum of negative numbers.)

#include <stdio.h>

int main() {
double num1, num2, num3, average;

printf("Enter the first number: ");
scanf("%lf", &num1);

printf("Enter the second number: ");
scanf("%lf", &num2);

printf("Enter the third number: ");
scanf("%lf", &num3);

// Calculate the sum and then the average
average = (num1 + num2 + num3) / 3.0;

printf("The average of %.2f, %.2f, and %.2f is: %.2f\n", num1, num2, num3,
average);

return 0;
}

**Test Case 1: Positive Integers**

- **Input:** 10, 20, 30
- **Expected Output:** 20.0 (\\((10 + 20 + 30) / 3 = 60 / 3 = 20\\))

**Test Case 2: Mixed Positive and Negative Integers**

- **Input:** -5, 10, 15
- **Expected Output:** 6.666... or 6.67 (depending on precision) (\\((-5 + 10 + 15) / 3 = 20 / 3 = 6.666...\\))

**Test Case 3: All Negative Integers**

- **Input:** -10, -20, -30
- **Expected Output:** -20.0 (\\((-10 + -20 + -30) / 3 = -60 / 3 = -20\\))

**Test Case 4: Zeroes and Positive**

- **Input:** 0, 0, 100
- **Expected Output:** 33.333... or 33.33 (depending on precision) (\\((0 + 0 + 100) / 3 = 100 / 3 = 33.333...\\))

**Test Case 5: All Zeroes**

- **Input:** 0, 0, 0
- **Expected Output:** 0.0 (\\((0 + 0 + 0) / 3 = 0 / 3 = 0\\))

**Test Case 6: Floating Point Numbers (If the program is expected to handle doubles/floats)**

- **Input:** 2.5, 3.5, 4.0
- **Expected Output:** 3.333... or 3.33 (depending on precision) (\\((2.5 + 3.5 + 4.0) / 3 = 10.0 / 3 = 3.333...\\))

**Test Case 7: Numbers Resulting in a Non-Repeating Decimal**

- **Input:** 1, 2, 3
- **Expected Output:** 2.0 (\\((1 + 2 + 3) / 3 = 6 / 3 = 2\\))
