// import { createStep } from '@mastra/core/workflows';
// import { testsGeneratorAgent } from './agents';
// import { testsGenerationInputSchema, testsGenerationOutputSchema } from './schema';

// // export const summaryGeneration = createStep({
// //   id: 'summary-generation',
// //   description: "Generates the summary on student's code",
// //   inputSchema: summaryGenerationInputSchema,
// //   outputSchema: summaryGenerationOutputSchema,
// //   execute: async ({ inputData }) => {
// //     const { code, testsResult } = inputData!;

// //     const prompt = `<code>
// // ${code.trim()}
// // </code>
// // <testsResult>${testsResult}</testsResult>`.trim();

// //     const response = await testsGeneratorAgent.generate([{ role: 'user', content: prompt }], {
// //       output: summaryGenerationOutputSchema,
// //     });

// //     return {
// //       summary: response.object.summary,
// //     };
// //   },
// // });

// export const testsGeneration = createStep({
//   id: 'tests-generation',
//   description: 'Generates the testcases for the code on the provided plain text testcases',
//   inputSchema: testsGenerationInputSchema,
//   outputSchema: testsGenerationOutputSchema,
//   execute: async ({ inputData }) => {
//     const { code, testsRequirement } = inputData!;

//     const prompt = `<testsRequirement>${testsRequirement.trim()}</testsRequirement>
// <code>
// ${code.trim()}
// </code>`.trim();

//     const response = await testsGeneratorAgent.generate([{ role: 'user', content: prompt }], {
//       output: testsGenerationOutputSchema,
//       temperature: 0.2,
//     });

//     return {
//       testcases: response.object.testcases,
//     };
//   },
// });
