import z from 'zod';

export const summaryGenerationInputSchema = z.object({
  code: z.string(),
  testsResult: z.array(
    z.object({
      input: z.string(),
      expected: z.string(),
      actual: z.string(),
      status: z.string(),
    }),
  ),
});

export const summaryGenerationOutputSchema = z.object({
  summary: z.string(),
  feedback: z.string(),
  score: z.number().max(100).min(0),
});

export const testsGenerationInputSchema = z.object({
  code: z.string(),
  testsRequirement: z.string(),
});

export const _testsGenerationOutputSchema = z.object({
  testcases: z
    .array(
      z.object({
        input: z.string().describe('UNIVERSAL INPUT FORMAT'),
        expectedOutput: z.string().describe('COMPLETE STDOUT CAPTURE'),
      }),
    )
    .min(1, 'Must generate at least one test case'),
});

export const testsGenerationOutputSchema = z.object({
  testcases: z
    .array(
      z.object({
        input: z
          .string()
          .describe(
            'UNIVERSAL INPUT FORMAT: Exact input string for ANY program type. Handle stdin (newline-separated), command-line args (space-separated), file input, interactive prompts, or mixed input methods. Format according to how the specific program expects input.',
          ),
        expectedOutput: z
          .string()
          .describe(
            'COMPLETE STDOUT CAPTURE: Character-perfect expected output representing the ENTIRE stdout stream for ANY programming language and pattern. Must include EVERY character the program produces during execution: ALL output statements (print, printf, cout, System.out.println, console.log), prompts, messages, intermediate outputs, final results, formatting, spaces, newlines, tabs, escape sequences, error messages. Captures the complete stdout from program start to finish with ZERO omissions and ZERO tolerance for formatting deviations. Works universally across all programming languages and output mechanisms.',
          ),
      }),
    )
    .min(1, 'Must generate at least one test case')
    .describe(
      'COMPREHENSIVE TEST COVERAGE WITH COMPLETE STDOUT CAPTURE: Array of test cases providing complete validation with full stdout stream capture. MUST include exactly one test case for each scenario provided in requirements. Each test case captures the ENTIRE stdout output from program execution, including all output statements, intermediate results, and final outputs. Covers all edge cases, error conditions, and boundary values. Works universally across all programming languages and patterns with zero stdout omissions.',
    ),
});

export const autoEvaluateInputSchema = testsGenerationInputSchema.extend({});

export const autoEvaluateOutputSchema = testsGenerationOutputSchema.extend({});

export const testRequirementsInputSchema = z.object({
  title: z.string(),
  description: z.string(),
  solutionCode: z.string(),
});

export const testRequirementsOutputSchema = z.object({
  testsRequirements: z.string(),
});
