import { createTool } from '@mastra/core/tools';
import { z } from 'zod';

export const codeFormatterTool = createTool({
  id: 'format-code',
  description: 'Formats code with proper syntax highlighting and structure',
  inputSchema: z.object({
    code: z.string().describe('The code to format'),
    language: z.string().describe('Programming language (e.g., java, python, javascript)'),
  }),
  outputSchema: z.object({
    formattedCode: z.string(),
    language: z.string(),
  }),
  execute: async ({ context }) => {
    const { code, language } = context;

    // Simple formatting - in a real implementation you might use prettier or similar
    const formattedCode = `\`\`\`${language}\n${code.trim()}\n\`\`\``;

    return {
      formattedCode,
      language,
    };
  },
});

// export const conceptAnalyzerTool = createTool({
//   id: 'analyze-concepts',
//   description: 'Analyzes programming concepts required for an assignment',
//   inputSchema: z.object({
//     topic: z.string().describe('The programming topic to analyze'),
//     difficulty: z.enum(['beginner', 'intermediate', 'advanced']).describe('Difficulty level'),
//   }),
//   outputSchema: z.object({
//     concepts: z.array(z.string()),
//     prerequisites: z.array(z.string()),
//     estimatedTime: z.string(),
//   }),
//   execute: async ({ context }) => {
//     const { topic, difficulty } = context;

//     // Concept mapping based on topic and difficulty
//     const conceptMap: Record<
//       string,
//       { concepts: string[]; prerequisites: string[]; time: string }
//     > = {
//       factorial: {
//         concepts: [
//           'Recursion',
//           'Loops',
//           'Mathematical operations',
//           'Base cases',
//           'Function design',
//         ],
//         prerequisites: ['Basic Java syntax', 'Methods', 'Variables', 'Conditional statements'],
//         time:
//           difficulty === 'beginner'
//             ? '2-3 hours'
//             : difficulty === 'intermediate'
//               ? '1-2 hours'
//               : '30-60 minutes',
//       },
//       fibonacci: {
//         concepts: ['Recursion', 'Dynamic programming', 'Memoization', 'Time complexity'],
//         prerequisites: ['Basic Java syntax', 'Methods', 'Arrays', 'Loops'],
//         time:
//           difficulty === 'beginner'
//             ? '3-4 hours'
//             : difficulty === 'intermediate'
//               ? '2-3 hours'
//               : '1-2 hours',
//       },
//     };

//     const topicKey =
//       Object.keys(conceptMap).find((key) => topic.toLowerCase().includes(key)) || 'factorial'; // default fallback

//     const result = conceptMap[topicKey];

//     return {
//       concepts: result.concepts,
//       prerequisites: result.prerequisites,
//       estimatedTime: result.time,
//     };
//   },
// });
