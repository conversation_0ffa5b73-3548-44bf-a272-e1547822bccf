import { google } from '@ai-sdk/google';

// export const model = google("gemini-2.5-flash");

import fs from 'node:fs';
import path from 'node:path';
import { createOpenRouter } from '@openrouter/ai-sdk-provider';

const openRouter = createOpenRouter();

const configFilePath = path.resolve('./model-config.json');

let currentModelName = process.env.OPENROUTER_MODEL || 'openai/gpt-4.1-mini';

if (fs.existsSync(configFilePath)) {
  try {
    const configData = JSON.parse(fs.readFileSync(configFilePath, 'utf-8'));
    currentModelName = configData.modelName;
  } catch (err) {
    console.error('Failed to read model config:', err);
  }
}

function persistModelName(modelName: string) {
  try {
    fs.writeFileSync(configFilePath, JSON.stringify({ modelName }, null, 2), 'utf-8');
  } catch (err) {
    console.error('Failed to save model config:', err);
  }
}

export const getModel = (generation: boolean) =>
  generation ? google('gemini-2.5-flash') : openRouter.chat(currentModelName);

export const setModel = (modelName: string) => {
  currentModelName = modelName;
  persistModelName(modelName);
};

export const getCurrentModelName = () => currentModelName;
