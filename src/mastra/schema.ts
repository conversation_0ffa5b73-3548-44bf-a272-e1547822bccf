import { z } from 'zod';

export const assignmentRequestSchema = z.object({
  topic: z.string(),
  difficulty: z.enum(['easy', 'medium', 'hard']).default('medium'),
  language: z.enum(['java', 'c']).default('java'),
  additionalRequirements: z.string().optional(),
});

export const hintsSchema = z.array(
  z.object({
    question: z.string(),
    answer: z.string(),
  }),
);

export const resourcesSchema = z.array(
  z.object({
    title: z.string(),
    url: z.string().url(),
  }),
);

export const assignmentComponentSchema = z.object({
  instructions: z.string(),
  hints: hintsSchema,
  resources: resourcesSchema,
  title: z.string(),
  description: z.string(),
  difficulty: z.string(),
  language: z.string(),
  starterCode: z.string().optional(),
  solutionCode: z.string().optional(),
});
