import type { CollectionConfig } from 'payload';

export const Modules: CollectionConfig = {
  slug: 'modules',
  admin: {
    group: 'Academic & Curriculum',
    useAsTitle: 'title',
    defaultColumns: ['title', 'createdAt', 'updatedAt'],
  },
  // access: {
  //   read: async ({ req }) => {
  //     if (!req.user) return false;
  //     if (req.user.role === 'admin' || req.user.role === 'faculty') {
  //       return true; // <PERSON><PERSON> and faculty have full access
  //     }
  //     if (req.user.role === 'student') {
  //       const currentTime = new Date().toISOString();
  //       // Find batches the student belongs to
  //       const userBatches = await req.payload.find({
  //         collection: 'batches',
  //         where: {
  //           students: { equals: req.user.id },
  //         },
  //       });
  //       const batchIds = userBatches.docs.map(b => b.id);
  //       // Find accessible module-batch pairs
  //       const accessibleModuleBatchAccess = await req.payload.find({
  //         collection: 'batch-access',
  //         where: {
  //           batch: { in: batchIds },
  //           accessStart: { less_than_equal: currentTime },
  //           accessEnd: { greater_than_equal: currentTime },
  //         },
  //       });
  //       const moduleIds = [...new Set(accessibleModuleBatchAccess.docs.map(mba => mba.module.id))];
  //       return { id: { in: moduleIds } }; // Filter modules by accessible IDs
  //     }
  //     return false;
  //   },
  //   // Add other access controls (create, update, delete) as needed
  // },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      label: 'Title',
    },
    {
      name: 'assignments',
      type: 'relationship',
      relationTo: 'assignments',
      hasMany: true,
      label: 'Assignments',
    },
  ],
  hooks: {
    afterChange: [
      // Hook to sync the module field in assignments when assignments are added/removed from a module
      // async ({ doc, previousDoc, req }) => {
      //   const { payload } = req;
      //   const currentAssignments = (doc.assignments || []).map((assignment: Assignment) =>
      //     typeof assignment === 'object' ? assignment.id : assignment,
      //   );
      //   const previousAssignments = (previousDoc?.assignments || []).map((assignment: Assignment) =>
      //     typeof assignment === 'object' ? assignment.id : assignment,
      //   );
      //   // Find assignments added to the module
      //   const addedAssignments = currentAssignments.filter(
      //     (id: Assignment['id']) => !previousAssignments.includes(id),
      //   );
      //   // Find assignments removed from the module
      //   const removedAssignments = previousAssignments.filter(
      //     (id: Assignment['id']) => !currentAssignments.includes(id),
      //   );
      //   // Update added assignments to link to this module
      //   // for (const assignmentId of addedAssignments) {
      //   //   await payload.update({
      //   //     collection: 'assignments',
      //   //     id: assignmentId,
      //   //     data: {
      //   //       module: doc.id,
      //   //     },
      //   //   });
      //   // }
      //   // // Update removed assignments to clear their module field
      //   // for (const assignmentId of removedAssignments) {
      //   //   await payload.update({
      //   //     collection: 'assignments',
      //   //     id: assignmentId,
      //   //     data: {
      //   //       module: null,
      //   //     },
      //   //   });
      //   // }
      // },
    ],
  },
};
