import type { CollectionConfig } from 'payload';

export const Enrollments: CollectionConfig = {
  slug: 'enrollments',
  admin: {
    group: 'Management & Access',
    useAsTitle: 'module',
    defaultColumns: ['module', 'batch', 'accessStart', 'accessEnd'],
  },
  fields: [
    {
      type: 'row',
      fields: [
        {
          name: 'module',
          type: 'relationship',
          relationTo: 'modules',
          required: true,
          label: 'Module',
        },
        {
          name: 'batch',
          type: 'relationship',
          relationTo: 'batches',
          required: true,
          label: 'Batch',
        },
      ],
    },
    {
      type: 'row',
      fields: [
        {
          name: 'accessStart',
          type: 'date',
          required: true,
          label: 'Access Start',
          admin: {
            date: {
              displayFormat: 'dd/MM/yyyy',
            },
          },
        },
        {
          name: 'accessEnd',
          type: 'date',
          required: true,
          label: 'Access End',
          admin: {
            date: {
              displayFormat: 'dd/MM/yyyy',
            },
          },
        },
      ],
    },
  ],
  indexes: [{ fields: ['batch', 'accessStart', 'accessEnd'] }],
};
