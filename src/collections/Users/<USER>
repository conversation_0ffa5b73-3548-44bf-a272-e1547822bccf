import type { CollectionConfig } from 'payload';

import { ensureUniqueUsername } from './hooks/ensureUniqueUsername';
import { setCookieBasedOnDomain } from './hooks/setCookieBasedOnDomain';
import { tenantsArrayField } from '@payloadcms/plugin-multi-tenant/fields';
import type { Tenant } from '@/payload-types';
import { UserCollectionConfig, usernameAccess } from './access';
import { canEditField, isAdminOnlyFieldAccess } from '@/access';

const defaultTenantArrayField = tenantsArrayField({
  tenantsArrayFieldName: 'tenants',
  tenantsArrayTenantFieldName: 'tenant',
  tenantsCollectionSlug: 'tenants',
  arrayFieldAccess: {
    read: canEditField,
    create: isAdminOnlyFieldAccess,
    update: isAdminOnlyFieldAccess,
  },
  tenantFieldAccess: {
    read: canEditField,
    create: isAdminOnlyFieldAccess,
    update: isAdminOnlyFieldAccess,
  },
  rowFields: [
    {
      name: 'roles',
      type: 'select',
      defaultValue: ['student'],
      hasMany: true,
      options: ['faculty', 'student'],
      required: true,
      access: {
        create: isAdminOnlyFieldAccess,
      },
    },
  ],
});

const Users: CollectionConfig = {
  slug: 'users',
  access: UserCollectionConfig,
  admin: {
    useAsTitle: 'fullName',
    group: 'Management & Access',
    defaultColumns: ['username', 'fullName', 'email', 'division'],
  },
  auth: {
    maxLoginAttempts: 3,
    loginWithUsername: false,
  },
  fields: [
    {
      admin: {
        position: 'sidebar',
      },
      name: 'roles',
      type: 'select',
      defaultValue: ['user'],
      hasMany: true,
      options: ['super-admin', 'user'],
      access: {
        create: isAdminOnlyFieldAccess,
        update: isAdminOnlyFieldAccess,
      },
    },
    {
      name: 'username',
      type: 'text',
      hooks: {
        beforeValidate: [ensureUniqueUsername],
      },
      index: true,
      access: {
        create: usernameAccess,
        update: usernameAccess,
      },
    },
    {
      name: 'email',
      type: 'email',
      required: true,
      unique: true,
      label: 'Email',
    },
    {
      name: 'fullName',
      type: 'text',
      label: 'Full Name',
    },
    {
      name: 'division',
      type: 'text',
    },
    {
      ...defaultTenantArrayField,
      defaultValue: ({ user }) => {
        if (Array.isArray(user?.tenants) && (user.tenants.length ?? 0) > 0) {
          return [
            {
              tenant: (user.tenants[0]?.tenant as Tenant)?.id,
            },
          ];
        }
        return [{}];
      },
      admin: {
        ...(defaultTenantArrayField?.admin || {}),
        position: 'sidebar',
      },
    },
  ],
  // The following hook sets a cookie based on the domain a user logs in from.
  // It checks the domain and matches it to a tenant in the system, then sets
  // a 'payload-tenant' cookie for that tenant.

  hooks: {
    afterLogin: [setCookieBasedOnDomain],
  },
};

export default Users;
