import {
  FieldConfig,
  canCreateUser,
  isAuthenticated,
  CollectionConfig,
  canAccessTenantData,
  isAdminOnlyFieldAccess,
  isFaculty,
} from '@/access';
import { FieldAccess } from 'payload';

export type UserFieldKeys = 'roles' | 'solution' | 'submission';

export const UserFieldConfig: FieldConfig<UserFieldKeys> = {
  roles: {
    read: isAdminOnlyFieldAccess,
  },
  solution: {
    read: isAdminOnlyFieldAccess,
  },
  submission: {
    read: isAdminOnlyFieldAccess,
  },
};

export const UserCollectionConfig: CollectionConfig = {
  read: ({ req }) => {
    if (req.user && isFaculty(req.user)) return true;
    if (isAuthenticated({ req })) {
      return {
        id: {
          equals: req.user?.id,
        },
      };
    }
    return false;
  },
  create: canCreateUser,
  update: ({ req }) => {
    if (!req.user) return false;
    if (req.user.roles?.includes('super-admin')) return true; // Super admins can update any user
    if (isFaculty(req.user)) return canAccessTenantData({ req }); // Faculty can update users in their tenant
    if (isAuthenticated({ req })) {
      return { id: { equals: req.user.id } }; // Students can update their own record
    }
    return false;
  },
  delete: canAccessTenantData,
};

export const usernameAccess: FieldAccess = async ({ req }) => {
  // Super admins can update any user
  if (req.user?.roles?.includes('super-admin')) return true;
  // Faculty can update users in their tenant
  if (isFaculty(req.user))
    return req.data?.roles.includes('student') && (await canAccessTenantData({ req })) === true;
  // Students can update their own record
  if (isAuthenticated({ req })) {
    if (req.user?.id === req.data?.id) return true;
  }
  return false;
};
