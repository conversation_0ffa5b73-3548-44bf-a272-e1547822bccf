import type { CollectionConfig } from 'payload';

export const Batches: CollectionConfig = {
  slug: 'batches',
  admin: {
    group: 'Management & Access',
    useAsTitle: 'batchId',
    defaultColumns: ['batchId', 'updatedAt'],
  },
  fields: [
    {
      name: 'batchId',
      type: 'text',
      required: true,
      unique: true,
      label: 'Batch ID',
    },
    {
      name: 'students',
      type: 'relationship',
      relationTo: 'users',
      hasMany: true,
      label: 'Students',
    },
  ],
};
