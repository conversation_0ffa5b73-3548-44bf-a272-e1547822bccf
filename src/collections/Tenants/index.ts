import type { CollectionConfig } from 'payload';
import { TenantCollectionConfig } from './access';

export const Tenants: CollectionConfig = {
  slug: 'tenants',
  access: TenantCollectionConfig,
  admin: {
    useAsTitle: 'name',
    group: 'Management & Access',
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'domain',
      type: 'text',
      admin: {
        description: 'Used for domain-based tenant handling',
      },
    },
    {
      name: 'slug',
      type: 'text',
      unique: true,
      index: true,
      required: true,
    },
  ],
};
