/* Alert Components */
.alert {
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  border: 1px solid;
  font-size: 0.875rem;
  line-height: 1.5;
}

.alert-error {
  background: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.alert-success {
  background: #f0fdf4;
  border-color: #bbf7d0;
  color: #16a34a;
}

.alert-warning {
  background: #fffbeb;
  border-color: #fed7aa;
  color: #d97706;
}

.alert-info {
  background: #eff6ff;
  border-color: #bfdbfe;
  color: #2563eb;
}

.alert-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.alert-icon {
  font-size: 1.125rem;
  flex-shrink: 0;
}

.alert-text {
  flex: 1;
}

/* Main Container */
.ai-generator-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem 2rem 1rem;
}

/* Header Section */
.ai-generator-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--theme-elevation-50, #e5e7eb);
  position: relative;
}

.ai-generator-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  flex-shrink: 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.ai-generator-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: var(--theme-text, #111827);
  line-height: 1.3;
}

.ai-generator-subtitle {
  font-size: 0.875rem;
  color: var(--theme-elevation-400, #6b7280);
  margin: 0;
  line-height: 1.4;
  max-width: 400px;
}

/* Beta Badge */
.beta-badge {
  position: absolute;
  top: 0;
  right: 0;
  transform: rotate(12deg);
  animation: pulse 2s infinite;
}

.beta-text {
  display: inline-block;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  font-size: 0.75rem;
  font-weight: 700;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Form Styles */
.ai-generator-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--theme-text, #111827);
  border-bottom: 2px solid var(--theme-elevation-100, #f3f4f6);
  padding-bottom: 0.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1rem;
  align-items: start;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Checkbox Styles */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.checkbox-grid {
  display: grid;
  gap: 0.8rem;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.checkbox-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  padding: 1rem;
  border: 1px solid var(--theme-elevation-100, #f3f4f6);
  border-radius: 8px;
  transition: all 0.2s ease;
  background: var(--theme-elevation-0, #ffffff);
}

.checkbox-item:hover {
  border-color: var(--theme-elevation-200, #e5e7eb);
  background-color: var(--theme-elevation-50, #f9fafb);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.checkbox-item input[type='checkbox'] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--theme-elevation-300, #d1d5db);
  border-radius: 4px;
  position: relative;
  flex-shrink: 0;
  transition: all 0.2s ease;
  background: white;
}

.checkbox-item input[type='checkbox']:checked + .checkmark {
  background-color: var(--theme-success-500, #10b981);
  border-color: var(--theme-success-500, #10b981);
}

.checkbox-item input[type='checkbox']:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.checkbox-label {
  font-weight: 500;
  color: var(--theme-text, #111827);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  user-select: none;
  margin: 0;
}

.checkbox-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.checkbox-text > label {
  padding: 0;
  margin: 0;
  font-weight: 500;
  color: var(--theme-text, #111827);
}

.checkbox-description {
  font-size: 0.875rem;
  color: var(--theme-elevation-400, #6b7280);
  line-height: 1.4;
  margin: 0;
}

.option-icon {
  color: var(--theme-elevation-400, #6b7280);
  flex-shrink: 0;
}

/* Collapsible Styles */
.collapsible-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  font-weight: 500;
  color: var(--theme-text, #111827);
}

.option-count {
  font-size: 0.875rem;
  color: var(--theme-elevation-400, #6b7280);
  background: var(--theme-elevation-100, #f3f4f6);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-weight: 400;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--theme-elevation-50, #f9fafb);
  margin-top: 1rem;
}

/* Error and Loading States */
.error-text {
  color: var(--theme-error-500, #ef4444);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.loading-spinner {
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-generator-container {
    padding: 0 0.5rem 2rem 0.5rem;
  }

  .ai-generator-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .beta-badge {
    position: static;
    transform: none;
    align-self: flex-start;
    margin-top: 0.5rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .checkbox-grid {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column-reverse;
    gap: 0.75rem;
  }

  .form-actions .btn {
    width: 100%;
    justify-content: center;
  }

  .ai-generator-title {
    font-size: 1.25rem;
  }

  .ai-generator-subtitle {
    max-width: none;
  }
}

@media (max-width: 480px) {
  .ai-generator-container {
    padding: 0 0.25rem 1.5rem 0.25rem;
  }

  .ai-generator-header {
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
  }

  .ai-generator-icon {
    width: 40px;
    height: 40px;
  }

  .form-section {
    gap: 0.75rem;
  }

  .ai-generator-form {
    gap: 1.5rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .alert-error {
    background: #7f1d1d;
    border-color: #991b1b;
    color: #fca5a5;
  }

  .alert-success {
    background: #14532d;
    border-color: #166534;
    color: #86efac;
  }

  .alert-warning {
    background: #78350f;
    border-color: #92400e;
    color: #fcd34d;
  }

  .alert-info {
    background: #1e3a8a;
    border-color: #1d4ed8;
    color: #93c5fd;
  }
}

/* Focus Styles for Accessibility */
.checkbox-item:focus-within {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .ai-generator-container {
    box-shadow: none;
    border: 1px solid #ccc;
  }

  .beta-badge {
    display: none;
  }

  .form-actions {
    display: none;
  }
}
