'use client';

import './GenerateContentButton.css';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>er<PERSON>oggler,
  SelectInput,
  TextareaInput,
  TextInput,
  useModal,
} from '@payloadcms/ui';
import { Loader2Icon, RotateCcw, WandSparklesIcon } from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';
import type { OptionObject, UIFieldClientComponent } from 'payload';
import { type ChangeEvent, useCallback, useEffect, useState } from 'react';
import type { Subject } from '@/payload-types';
import { DIFFICULTIES_OPTIONS, LANGUAGES, LANGUAGES_OPTIONS } from '../constants';

const DRAWER_SLUG = 'ai-content-generator';

interface GenerationOptions {
  includeInstructions: boolean;
  includeTests: boolean;
  includeSolution: boolean;
  includeStarterCode: boolean;
  includeHints: boolean;
}

interface GenerationPayload {
  title: string;
  language: string;
  difficulty: string;
  subjectId: string;
  points: number;
  additionalRequirements: string;
  generationOptions: GenerationOptions;
}

const AIContentGenerator: UIFieldClientComponent = () => {
  const router = useRouter();
  // const [isCollapsed, setIsCollapsed] = useState(true);
  const { closeModal } = useModal();

  // Java-focused defaults
  const [title, setTitle] = useState('');
  const [language, setLanguage] = useState('java');
  const [difficulty, setDifficulty] = useState('medium');
  const [subjects, setSubjects] = useState([]);
  const [subjectId, setSubjectId] = useState<string>('');
  const [points, setPoints] = useState<number | null>(50);
  const [context, setContext] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Enhanced generation options for Java assignments
  const [generationOptions, setGenerationOptions] = useState<GenerationOptions>({
    includeInstructions: true,
    includeTests: true,
    includeSolution: true,
    includeStarterCode: true,
    includeHints: true,
  });

  const isValidPoints = typeof points === 'number' && points > 0 && points <= 1000;
  const canGenerate = title.trim() && language && difficulty && points !== null && isValidPoints;

  // Auto-clear success message after 3 seconds
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => setSuccess(null), 3000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  useEffect(() => {
    fetch('/api/subjects?select[id]=true&select[name]=true&select[description]=true&depth=0')
      .then((res) => res.json())
      .then((data) => {
        setSubjects(
          data.docs.map((subject: Subject) => ({
            label: subject.name,
            value: subject.id,
          })),
        );
      });
  }, []);

  // const updateGenerationOption = useCallback((key: keyof GenerationOptions, value: boolean) => {
  //   setGenerationOptions((prev) => ({
  //     ...prev,
  //     [key]: value,
  //   }));
  // }, []);

  const handleGenerate = async () => {
    if (!canGenerate) return;

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const payload: GenerationPayload = {
        title: title.trim(),
        language,
        difficulty,
        subjectId: String(subjectId),
        points: points!,
        additionalRequirements: context.trim(),
        generationOptions,
      };

      const response = await fetch('/api/assignments/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to generate content: ${response.statusText}`);
      }

      const generatedContent = await response.json();

      setSuccess('Assignment generated successfully! Redirecting to edit...');

      if (generatedContent?.assignment?.id) {
        // Close drawer and navigate after a brief delay to show success message
        closeModal(DRAWER_SLUG);
        router.push(`/admin/collections/assignments/${generatedContent.assignment.id}`);
      }
    } catch (err) {
      console.error('Failed to generate content:', err);
      setError(
        err instanceof Error
          ? err.message
          : 'An unexpected error occurred while generating the assignment',
      );
    } finally {
      setLoading(false);
    }
  };

  const resetForm = useCallback(() => {
    setTitle('');
    setLanguage('java');
    setDifficulty('medium');
    setPoints(50);
    setContext('');
    setError(null);
    setSuccess(null);
    // Reset to default options
    setGenerationOptions({
      includeInstructions: true,
      includeTests: true,
      includeSolution: true,
      includeStarterCode: true,
      includeHints: true,
    });
  }, []);

  // const handlePointsChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
  //   const val = e.target.value.trim();
  //   if (val === '') {
  //     setPoints(null);
  //     return;
  //   }
  //   const parsed = Number.parseInt(val, 10);
  //   setPoints(!isNaN(parsed) ? parsed : null);
  // }, []);

  // const getOptionIcon = (option: keyof GenerationOptions) => {
  //   const iconProps = { size: 16, className: 'option-icon' };
  //   switch (option) {
  //     case 'includeInstructions':
  //       return <BookOpen {...iconProps} />;
  //     case 'includeTests':
  //       return <TestTube {...iconProps} />;
  //     case 'includeSolution':
  //       return <Code {...iconProps} />;
  //     case 'includeStarterCode':
  //       return <Code {...iconProps} />;
  //     case 'includeHints':
  //       return <Lightbulb {...iconProps} />;
  //     default:
  //       return <Sparkles {...iconProps} />;
  //   }
  // };

  const pathname = usePathname();

  return pathname.endsWith('create') ? (
    <>
      <Drawer slug={DRAWER_SLUG}>
        <div className="ai-generator-container">
          <div className="ai-generator-header">
            <div className="ai-generator-icon">
              <WandSparklesIcon size={24} color="white" />
            </div>
            <div>
              <h2 className="ai-generator-title">AI Assignment Generator</h2>
              <p className="ai-generator-subtitle">
                Generate comprehensive programming assignments with ease
              </p>
            </div>
            <div className="beta-badge">
              <span className="beta-text">BETA</span>
            </div>
          </div>

          <div className="ai-generator-form">
            {error && (
              <div className="alert alert-error" role="alert">
                <div className="alert-content">
                  <span className="alert-icon">⚠️</span>
                  <span className="alert-text">{error}</span>
                </div>
              </div>
            )}

            {success && (
              <div className="alert alert-success" role="alert">
                <div className="alert-content">
                  <span className="alert-icon">✅</span>
                  <span className="alert-text">{success}</span>
                </div>
              </div>
            )}

            <div className="form-section">
              <div className="form-group">
                <TextInput
                  label="Assignment Title"
                  path="ai-title"
                  value={title}
                  onChange={(e: ChangeEvent<HTMLInputElement>) => setTitle(e.target.value)}
                  placeholder="e.g., Binary Search Tree Implementation"
                  required
                />
              </div>

              <div className="form-row">
                <SelectInput
                  name="subjectId"
                  label="Subject"
                  path="ai-subjectId"
                  isClearable={false}
                  onChange={(val) => setSubjectId((val as OptionObject).value)}
                  value={subjectId}
                  options={subjects}
                  required
                />

                <SelectInput
                  name="language"
                  label="Programming Language"
                  path="ai-language"
                  isClearable={false}
                  onChange={(val) => setLanguage((val as OptionObject).value)}
                  value={language}
                  options={LANGUAGES_OPTIONS.filter((option) => option.value !== LANGUAGES.WEB)}
                  required
                />

                <SelectInput
                  name="difficulty"
                  label="Difficulty Level"
                  path="ai-difficulty"
                  isClearable={false}
                  onChange={(val) => setDifficulty((val as OptionObject).value)}
                  value={difficulty}
                  options={DIFFICULTIES_OPTIONS}
                  required
                />
              </div>
            </div>

            <div className="form-section">
              <TextareaInput
                label="Additional Requirements or Context (optional)"
                path="ai-context"
                value={context}
                onChange={(e) => setContext(e.target.value)}
                placeholder="e.g., Must implement specific design patterns, handle concurrent access, include performance optimizations..."
                description="Specify your requirements, coding standards, or additional context about this assignment"
                rows={4}
              />
            </div>

            {/* <Collapsible
              header={
                <div className="collapsible-header">
                  <span>Generation Options</span>
                  <span className="option-count">
                    {Object.values(generationOptions).filter(Boolean).length} selected
                  </span>
                </div>
              }
              isCollapsed={isCollapsed}
              onToggle={setIsCollapsed}
            >
              <div className="checkbox-grid">
                {(Object.keys(generationOptions) as Array<keyof GenerationOptions>).map(
                  (option) => {
                    const labels = {
                      includeInstructions: 'Detailed Instructions',
                      includeTests: 'Java Test Cases',
                      includeSolution: 'Complete Solution',
                      includeStarterCode: 'Starter Code',
                      includeHints: 'Hints & Resources',
                    };

                    const descriptions = {
                      includeInstructions: 'Step-by-step assignment instructions',
                      includeTests: 'JUnit test cases for validation',
                      includeSolution: 'Complete working solution code',
                      includeStarterCode: 'Basic code structure to start with',
                      includeHints: 'Learning hints and helpful resources',
                    };

                    return (
                      <div
                        key={option}
                        className="checkbox-item"
                        onClick={() => updateGenerationOption(option, !generationOptions[option])}
                        style={{ cursor: 'pointer' }}
                      >
                        <CheckboxInput
                          id={`gen-${option}`}
                          checked={generationOptions[option]}
                          onToggle={() =>
                            updateGenerationOption(option, !generationOptions[option])
                          }
                          Label={
                            <div className="checkbox-label">
                              {getOptionIcon(option)}
                              <div className="checkbox-text">
                                <FieldLabel label={labels[option]} htmlFor={`gen-${option}`} />
                                <span className="checkbox-description">{descriptions[option]}</span>
                              </div>
                            </div>
                          }
                        />
                      </div>
                    );
                  },
                )}
              </div>
            </Collapsible> */}

            <div className="form-actions">
              <Button
                buttonStyle="secondary"
                onClick={resetForm}
                disabled={loading}
                size="medium"
                icon={<RotateCcw size={16} />}
              >
                Reset Form
              </Button>

              <Button
                buttonStyle="primary"
                onClick={handleGenerate}
                disabled={!canGenerate || loading}
                size="medium"
                aria-describedby={error ? 'generation-error' : undefined}
                icon={
                  loading ? (
                    <Loader2Icon className="animate-spin mr-2" />
                  ) : (
                    <WandSparklesIcon size={16} />
                  )
                }
              >
                {loading ? 'Generating...' : 'Generate'}
              </Button>
            </div>
          </div>
        </div>
      </Drawer>

      <DrawerToggler
        className="btn btn--style-primary btn--size-medium"
        slug={DRAWER_SLUG}
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '.5rem',
        }}
        disabled={loading}
      >
        <WandSparklesIcon size={18} />
        Generate
      </DrawerToggler>
    </>
  ) : null;
};

export default AIContentGenerator;
