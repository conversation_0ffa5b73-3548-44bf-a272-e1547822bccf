import { CheckIcon, XIcon } from 'lucide-react';
import { DefaultCellComponentProps } from 'payload';
import React, { memo } from 'react';

const IsActiveCell: React.FC<DefaultCellComponentProps> = ({ cellData }) => {
  const IconComponent = cellData ? CheckIcon : XIcon;
  const color = cellData ? 'var(--theme-success-500)' : 'var(--theme-error-500)';

  return (
    <span style={{ color }}>
      <IconComponent size={20} strokeWidth={2} />
    </span>
  );
};

export default memo(IsActiveCell);
