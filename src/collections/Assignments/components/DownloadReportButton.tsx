'use client';

import Link from 'next/link';
import { useParams, usePathname } from 'next/navigation';
import { UIFieldClientComponent } from 'payload';
// import { DownloadIcon } from 'lucide-react';

const DownloadReportButton: UIFieldClientComponent = () => {
  const params = useParams();
  const pathname = usePathname();
  const segments = params?.segments as string[] | undefined;
  const assignmentId = segments?.[1] === 'assignments' && segments?.[2];

  if (!assignmentId) return null;

  return (
    !pathname.endsWith('create') && (
      <Link
        target="_blank"
        href={`/api/assignments/${assignmentId}/download-report`}
        className="btn btn--style-secondary btn--size-medium"
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '.5rem',
        }}
      >
        {/* <DownloadIcon size={16} color="white" /> */}
        Report
      </Link>
    )
  );
};

export default DownloadReportButton;
