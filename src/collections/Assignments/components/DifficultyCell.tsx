import { CircleIcon, TriangleIcon, OctagonIcon } from 'lucide-react';
import type { DefaultCellComponentProps } from 'payload';
import type React from 'react';
import { memo } from 'react';

const DifficultyCell: React.FC<DefaultCellComponentProps> = ({ cellData }) => {
  const difficulty = (cellData as string)?.toLowerCase() || '';

  const Icon =
    difficulty === 'easy'
      ? CircleIcon
      : difficulty === 'medium'
        ? TriangleIcon
        : difficulty === 'hard'
          ? OctagonIcon
          : null;

  const getColor = (difficulty: string): string => {
    switch (difficulty) {
      case 'easy':
        return '#16a34a';
      case 'medium':
        return '#ea580c';
      case 'hard':
        return '#dc2626';
      default:
        return '#6b7280';
    }
  };

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        gap: '0.5rem',
        textTransform: 'capitalize',
        color: getColor(difficulty),
      }}
    >
      {Icon && <Icon size={18} />}
      <span>{cellData || 'N/A'}</span>
    </div>
  );
};

export default memo(DifficultyCell);
