import { CoffeeIcon, GlobeIcon, TerminalSquareIcon } from 'lucide-react';
import type { DefaultCellComponentProps } from 'payload';
import type React from 'react';
import { memo } from 'react';

const LanguageCell: React.FC<DefaultCellComponentProps> = ({ cellData }) => {
  const Icon =
    cellData === 'web'
      ? GlobeIcon
      : cellData === 'java'
        ? CoffeeIcon
        : cellData === 'c'
          ? TerminalSquareIcon
          : null;

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        gap: '0.5rem',
        textTransform: 'capitalize',
      }}
    >
      {Icon && <Icon size={18} />}
      <span>{cellData || 'N/A'}</span>
    </div>
  );
};

export default memo(LanguageCell);
