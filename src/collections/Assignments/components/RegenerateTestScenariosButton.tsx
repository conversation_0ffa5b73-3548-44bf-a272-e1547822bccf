'use client';

import { Button, toast, useDocumentInfo } from '@payloadcms/ui';
import { RefreshCw } from 'lucide-react';
import { useParams, usePathname, useRouter } from 'next/navigation';
import type { UIFieldClientComponent } from 'payload';
import { useState } from 'react';

const RegenerateTestScenariosButton: UIFieldClientComponent = () => {
  const router = useRouter();
  const params = useParams();
  const pathname = usePathname();
  const { id } = useDocumentInfo();
  const [loading, setLoading] = useState(false);

  const segments = params?.segments as string[] | undefined;
  const assignmentId = segments?.[1] === 'assignments' ? segments?.[2] : id;

  const handleRegenerate = async () => {
    if (!assignmentId) {
      toast.error('Assignment ID not found');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`/api/assignments/${assignmentId}/regenerate-test-scenarios`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `Failed to regenerate test scenarios: ${response.statusText}`,
        );
      }

      await response.json();

      toast.success('Test scenarios regenerated successfully!');

      // Refresh the page to show the updated test scenarios
      router.refresh();
    } catch (error) {
      console.error('Failed to regenerate test scenarios:', error);
      toast.error(
        error instanceof Error
          ? error.message
          : 'An unexpected error occurred while regenerating test scenarios',
      );
    } finally {
      setLoading(false);
    }
  };

  if (!assignmentId) return null;

  return (
    !pathname.endsWith('create') && (
      <Button
        buttonStyle="secondary"
        onClick={handleRegenerate}
        disabled={loading}
        size="medium"
        icon={<RefreshCw size={16} className={loading ? 'animate-spin' : ''} />}
      >
        {loading ? 'Regenerating...' : 'Regenerate Tests'}
      </Button>
    )
  );
};

export default RegenerateTestScenariosButton;
