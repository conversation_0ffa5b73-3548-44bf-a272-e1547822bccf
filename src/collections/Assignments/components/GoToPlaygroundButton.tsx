'use client';

import { useDocumentInfo } from '@payloadcms/ui';
import Link from 'next/link';
import { useParams, usePathname } from 'next/navigation';
import { UIFieldClientComponent } from 'payload';

const GoToPlaygroundButton: UIFieldClientComponent = () => {
  const { id } = useDocumentInfo();
  const params = useParams();
  const pathname = usePathname();
  const segments = params?.segments as string[] | undefined;
  const assignmentId = segments?.[1] === 'assignments' ? segments?.[2] : id;

  if (!assignmentId) return null;

  return (
    !pathname.endsWith('create') && (
      <Link
        target="_blank"
        href={`/assignments/${assignmentId}`}
        className="btn btn--style-primary btn--size-medium"
      >
        Playground
      </Link>
    )
  );
};

export default GoToPlaygroundButton;
