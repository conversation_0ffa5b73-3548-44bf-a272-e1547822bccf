import {
  FieldConfig,
  isAuthenticated,
  CollectionConfig,
  canAccessTenantData,
  isSuperAdmin,
  isFaculty,
} from '@/access';

export type AssignmentFieldKeys = 'solution' | 'submission';

export const AssignmentFieldConfig: FieldConfig<AssignmentFieldKeys> = {
  solution: {
    read: ({ req }) => {
      if (!isAuthenticated({ req })) return false;

      if (isSuperAdmin(req.user) || isFaculty(req.user)) return true;

      return false;
    },
  },
  submission: {
    read: ({ req }) => {
      if (!isAuthenticated({ req })) return false;

      if (isSuperAdmin(req.user) || isFaculty(req.user)) return true;

      return false;
    },
  },
};

export const AssignmentCollectionConfig: CollectionConfig = {
  read: ({ req }) => {
    if (!isAuthenticated({ req })) return false;

    if (isSuperAdmin(req.user)) return true;

    if (!canAccessTenantData({ req })) {
      return {
        _status: { equals: 'published' },
      };
    }

    return true;
  },
  create: canAccessTenantData,
  update: canAccessTenantData,
  delete: canAccessTenantData,
};
