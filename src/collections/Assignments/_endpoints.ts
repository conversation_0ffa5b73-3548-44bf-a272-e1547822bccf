// import { google } from '@ai-sdk/google';
// import { generateObject } from 'ai';
// import type { Endpoint } from 'payload';
// import { z } from 'zod';
// import type { Assignment, Submission } from '@/payload-types';
// import { extractTenantId } from '@/utils/tenant.utils';

// interface GenContentPayload {
//   title: Assignment['title'];
//   difficulty: Assignment['difficulty'];
//   points: Assignment['points'];
//   additionalRequirements: string;
// }

// interface GeneratedAssignmentContent {
//   title: string;
//   description: string;
//   points: number;
//   instructions: string;
//   starterCode: string;
//   solutionCode: string;
//   requiresCommandLineArgs: boolean;
//   testCases: Array<{
//     title: string;
//     description: string;
//     expectedOutput: string;
//     input: string;
//     tolerance: number;
//     isHidden: boolean;
//   }>;
// }

// // Validation schemas
// const SubmissionDataSchema = z.object({
//   passedTestCases: z.number().min(0).default(0),
//   failedTestCases: z.number().min(0).default(0),
//   html: z.string().optional(),
//   css: z.string().optional(),
//   js: z.string().optional(),
//   c: z.string().optional(),
//   java: z.string().optional(),
// });

// const GenerateAssignmentSchema = z.object({
//   title: z.string().min(1, 'Title is required'),
//   difficulty: z.enum(['easy', 'medium', 'hard']),
//   points: z.number().min(1).max(1000),
//   additionalRequirements: z.string().default(''),
// });

// async function genContent(payload: GenContentPayload): Promise<GeneratedAssignmentContent> {
//   try {
//     const { object } = await generateObject({
//       model: google('models/gemini-2.0-flash-exp'),
//       schema: z.object({
//         assignment: z.object({
//           title: z.string(),
//           description: z.string().max(300, 'Description should be concise'),
//           instructions: z.string(),
//           starterCode: z.string(),
//           solutionCode: z.string(),
//           requiresCommandLineArgs: z.boolean(),
//           testCases: z
//             .array(
//               z.object({
//                 title: z.string(),
//                 description: z.string(),
//                 expectedOutput: z.string(),
//                 input: z.string(),
//                 tolerance: z.number().default(0),
//                 isHidden: z.boolean().default(false),
//               }),
//             )
//             .min(1)
//             .max(10),
//         }),
//       }),
//       prompt: `Generate a Java programming assignment with the following requirements:
//         - Title: ${payload.title}
//         - Difficulty: ${payload.difficulty}
//         - Points: ${payload.points}
//         - Additional Requirements: ${payload.additionalRequirements}

//         IMPORTANT JAVA CODE STRUCTURE REQUIREMENTS:
//         - The main class MUST be named "Main" (public class Main)
//         - Include a public static void main(String[] args) method as the execution entry point
//         - The main method should contain the primary logic and may create objects of other classes
//         - For input handling, use either command line arguments (args) or Scanner class for user input
//         - If using Scanner, import java.util.Scanner at the top
//         - Follow standard Java naming conventions (PascalCase for classes, camelCase for methods/variables)

//         Create:
//         1. A clear, concise description (2-3 lines)
//         2. Instructions (Markdown, no top-level heading):
//            - What the program should do
//            - Input format (via args or Scanner)
//            - Expected output format
//            - Any specific requirements
//         3. Starter code template in Java with:
//            - public class Main
//            - public static void main(String[] args) method
//            - Basic structure/comments for students to fill in
//            - Any additional classes if needed
//         4. Complete solution code in Java with:
//            - Fully implemented public class Main
//            - Complete main method implementation
//            - All necessary imports
//            - Proper input handling (args or Scanner)
//            - Clean, well-commented code
//         5. At least 3 test cases with:
//            - Clear input values
//            - Expected output values
//            - Mix of visible and hidden test cases

//         Example structure for starter code:
//         \`\`\`java
//         import java.util.Scanner; // if needed

//         public class Main {
//             public static void main(String[] args) {
//                 // TODO: Implement the solution
//                 // Students fill in the logic here
//             }

//             // Additional helper methods or classes if needed
//         }
//         \`\`\`

//         Ensure the assignment is appropriate for ${payload.difficulty} difficulty level.`,
//     });

//     return {
//       title: object.assignment.title,
//       description: object.assignment.description,
//       points: payload.points,
//       instructions: object.assignment.instructions,
//       starterCode: object.assignment.starterCode,
//       solutionCode: object.assignment.solutionCode,
//       requiresCommandLineArgs: object.assignment.requiresCommandLineArgs,
//       testCases: object.assignment.testCases,
//     };
//   } catch (error) {
//     console.error('Error generating assignment content:', error);
//     throw new Error('Failed to generate assignment content');
//   }
// }

// function calculateDueDate(daysFromNow: number = 7): string {
//   const dueDate = new Date();
//   dueDate.setDate(dueDate.getDate() + daysFromNow);
//   return dueDate.toISOString().split('T')[0];
// }

// export const endpoints: Omit<Endpoint, 'root'>[] = [
//   {
//     path: '/:id/submit',
//     method: 'post',
//     handler: async ({ user, routeParams, json, payload }) => {
//       try {
//         // Validate input
//         const rawData = json ? await json() : {};
//         const validatedData = SubmissionDataSchema.parse(rawData);

//         const id = parseInt((routeParams?.id as string) || '0');
//         if (!id || isNaN(id)) {
//           return Response.json({ message: 'Invalid assignment ID' }, { status: 400 });
//         }

//         const userId = user?.id;
//         if (!userId) {
//           return Response.json({ message: 'User not authenticated' }, { status: 401 });
//         }

//         const tenantId = extractTenantId(user);

//         // Check if assignment exists
//         const assignment = await payload.findByID({
//           collection: 'assignments',
//           id,
//         });

//         if (!assignment) {
//           return Response.json({ message: 'Assignment not found' }, { status: 404 });
//         }

//         // Check for existing submission
//         const submissions = await payload.find({
//           collection: 'submissions',
//           where: {
//             assignment: { equals: id },
//             student: { equals: userId },
//             tenant: { equals: tenantId },
//           },
//         });

//         // Calculate score
//         const { passedTestCases, failedTestCases } = validatedData;
//         const totalTestCases = passedTestCases + failedTestCases;
//         const score = totalTestCases > 0 ? Math.floor((passedTestCases / totalTestCases) * 100) : 0;

//         const submissionData: Submission = {
//           status: 'review' as const,
//           assignment: id,
//           student: userId,
//           tenant: tenantId,
//           score,
//           createdAt: new Date().toISOString(),
//           updatedAt: new Date().toISOString(),
//           id: 0,
//           html: validatedData.html || '',
//           css: validatedData.css || '',
//           js: validatedData.js || '',
//           c: validatedData.c || '',
//           java: validatedData.java || '',
//         };

//         if (submissions.totalDocs > 0) {
//           // Update existing submission
//           await payload.update({
//             collection: 'submissions',
//             id: submissions.docs[0].id,
//             data: submissionData,
//           });

//           return Response.json({
//             message: 'Assignment updated successfully',
//             score,
//             status: 'updated',
//           });
//         }

//         // Create new submission
//         const newSubmission = await payload.create({
//           collection: 'submissions',
//           data: submissionData,
//         });

//         return Response.json({
//           message: 'Assignment submitted successfully',
//           score,
//           status: 'created',
//           submissionId: newSubmission.id,
//         });
//       } catch (error) {
//         console.error('Submission error:', error);

//         if (error instanceof z.ZodError) {
//           return Response.json(
//             {
//               message: 'Invalid submission data',
//               errors: error.errors,
//             },
//             { status: 400 },
//           );
//         }

//         return Response.json(
//           {
//             message: error instanceof Error ? error.message : 'Internal server error',
//           },
//           { status: 500 },
//         );
//       }
//     },
//   },
//   {
//     path: '/generate',
//     method: 'post',
//     handler: async ({ user, json, payload }) => {
//       try {
//         // Validate input
//         const rawData = json ? await json() : {};
//         const validatedData = GenerateAssignmentSchema.parse(rawData);

//         if (!user?.id) {
//           return Response.json({ message: 'User not authenticated' }, { status: 401 });
//         }

//         const tenantId = extractTenantId(user);

//         // Generate content via AI
//         const generatedContent = await genContent({
//           title: validatedData.title,
//           difficulty: validatedData.difficulty,
//           points: validatedData.points,
//           additionalRequirements: validatedData.additionalRequirements,
//         });

//         // Create assignment
//         const createdAssignment = await payload.create({
//           collection: 'assignments',
//           data: {
//             title: generatedContent.title,
//             description: generatedContent.description,
//             points: generatedContent.points,
//             instructions: generatedContent.instructions.replace(/^## Instructions\s*\n*/, ''),
//             difficulty: validatedData.difficulty,
//             language: 'java',
//             tenant: tenantId,
//             dueDate: calculateDueDate(7),
//             // requiresCommandLineArgs: generatedContent.requiresCommandLineArgs,
//             starterCode: {
//               java: generatedContent.starterCode,
//             },
//             solutionCode: {
//               java: generatedContent.solutionCode,
//             },
//             // javaTestCases: generatedContent.testCases,
//           },
//         });

//         return Response.json({
//           message: 'Assignment generated successfully',
//           assignment: {
//             id: createdAssignment.id,
//             title: createdAssignment.title,
//             description: createdAssignment.description,
//             points: createdAssignment.points,
//             difficulty: createdAssignment.difficulty,
//             dueDate: createdAssignment.dueDate,
//           },
//         });
//       } catch (error) {
//         console.error('Assignment generation error:', error);

//         if (error instanceof z.ZodError) {
//           return Response.json(
//             {
//               message: 'Invalid input data',
//               errors: error.errors,
//             },
//             { status: 400 },
//           );
//         }

//         return Response.json(
//           {
//             message: error instanceof Error ? error.message : 'Failed to generate assignment',
//           },
//           { status: 500 },
//         );
//       }
//     },
//   },
// ];
