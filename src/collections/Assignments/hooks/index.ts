import {
  Collection<PERSON><PERSON><PERSON><PERSON>Hook,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Hook,
  CollectionA<PERSON><PERSON>hangeHook,
} from 'payload';
import { DIFFICULTY_LEVELS } from '../constants';
import { isStudent } from '@/access';

export const RootHooks: {
  beforeChange?: CollectionBeforeChangeHook[];
  afterChange?: CollectionAfterChangeHook[];
  afterRead?: CollectionAfterReadHook[];
} = {
  beforeChange: [
    ({ data, operation }) => {
      // Set default due date if not provided (7 days from now)
      if (operation === 'create' && !data.dueDate) {
        data.dueDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString();
      }

      // Automatically set points based on difficulty if not specified
      if (operation === 'create' && !data.points) {
        switch (data.difficulty) {
          case DIFFICULTY_LEVELS.EASY:
            data.points = 10;
            break;
          case DIFFICULTY_LEVELS.MEDIUM:
            data.points = 20;
            break;
          case DIFFICULTY_LEVELS.HARD:
            data.points = 30;
            break;
          default:
            data.points = 10;
        }
      }
      return data;
    },
  ],
  afterRead: [
    async ({ doc, req }) => {
      // Filter out hidden test cases
      if (doc.javaTestCases) {
        doc.javaTestCases = doc.javaTestCases.filter((test: any) => !test.isHidden);
      }

      if (doc.cTestCases) {
        doc.cTestCases = doc.cTestCases.filter((test: any) => !test.isHidden);
      }

      // Add isCompleted status - optimized version
      doc.isCompleted = false;

      if (req?.user?.id && isStudent(req.user)) {
        try {
          // Use count instead of find for better performance
          const submissionCount = await req.payload.count({
            collection: 'submissions',
            where: {
              and: [{ assignment: { equals: doc.id } }, { student: { equals: req.user.id } }],
            },
          });

          doc.isCompleted = submissionCount.totalDocs > 0;
        } catch (error) {
          // Gracefully handle errors
          req.payload.logger.error(`Error checking completion for assignment ${doc.id}:`, error);
          doc.isCompleted = false;
        }
      }

      return doc;
    },
  ],
};
