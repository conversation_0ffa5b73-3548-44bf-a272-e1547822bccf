// import { TestSuiteBlock } from '@/fields/TestSuiteBlock';

import type { CollectionConfig } from 'payload';
import { endpoints } from '@/endpoints/assignment.endpoints';
import { AssignmentCollectionConfig, AssignmentFieldConfig } from './access';
import { DIFFICULTIES_OPTIONS, DIFFICULTY_LEVELS, LANGUAGES, LANGUAGES_OPTIONS } from './constants';
import { RootHooks } from './hooks';

export const Assignments: CollectionConfig = {
  slug: 'assignments',
  access: AssignmentCollectionConfig,
  versions: {
    drafts: true,
  },
  admin: {
    group: 'Academic & Curriculum',
    useAsTitle: 'title',
    defaultColumns: ['id', 'title', 'language', 'difficulty', 'dueDate', 'points'],
    components: {
      edit: {
        beforeDocumentControls: [
          {
            path: './collections/Assignments/components/GenerateContentButton',
          },
          { path: './collections/Assignments/components/GoToPlaygroundButton' },
          {
            path: './collections/Assignments/components/RegenerateTestScenariosButton',
          },
          { path: './collections/Assignments/components/DownloadReportButton' },
        ],
      },
    },
  },
  hooks: RootHooks,
  fields: [
    {
      type: 'tabs',
      tabs: [
        {
          label: 'Overview',
          fields: [
            {
              type: 'row',
              fields: [
                {
                  name: 'title',
                  type: 'text',
                  required: true,
                  admin: {
                    placeholder: 'Assignment title',
                  },
                },
                {
                  name: 'subject',
                  type: 'relationship',
                  relationTo: 'subjects',
                  required: false,
                },
                {
                  name: 'module',
                  type: 'relationship',
                  relationTo: 'modules',
                  required: false,
                  label: 'Module',
                  admin: {
                    allowEdit: false,
                  },
                },
              ],
            },
            {
              name: 'description',
              type: 'textarea',
              admin: {
                placeholder: 'Brief description of the assignment',
              },
            },
            {
              type: 'row',
              fields: [
                {
                  name: 'language',
                  type: 'select',
                  options: LANGUAGES_OPTIONS,
                  required: true,
                  defaultValue: LANGUAGES.WEB,
                  index: true,
                  admin: {
                    components: {
                      Cell: './collections/Assignments/components/LanguageCell',
                    },
                  },
                },
                {
                  name: 'difficulty',
                  type: 'select',
                  options: DIFFICULTIES_OPTIONS,
                  required: true,
                  defaultValue: DIFFICULTY_LEVELS.EASY,
                  index: true,
                  admin: {
                    components: {
                      Cell: './collections/Assignments/components/DifficultyCell',
                    },
                  },
                },
                {
                  name: 'points',
                  type: 'number',
                  required: true,
                },
                {
                  name: 'dueDate',
                  type: 'date',
                  required: true,
                  index: true,
                  admin: {
                    date: {
                      displayFormat: 'dd/MM/yyyy',
                      pickerAppearance: 'dayAndTime',
                    },
                  },
                },
              ],
            },
            {
              name: 'instructions',
              type: 'textarea',
              admin: {
                disableListColumn: true,
                description: 'Detailed instructions for completing the assignment',
              },
            },
            {
              name: 'testsRequirement',
              label: 'Test Scenarios',
              type: 'textarea',
              admin: {
                placeholder: `#1: input: 5, output: 15
#2: input: 4, output: 10
#3: input: 0, output: 0`,
              },
            },
            // {
            //   name: 'requiresCommandLineArgs',
            //   type: 'checkbox',
            //   label: 'Requires Command Line Arguments',
            //   admin: {
            //     description: 'Check this if the assignment requires command line arguments',
            //     condition: (data) => [LANGUAGES.JAVA, LANGUAGES.C].includes(data.language),
            //   },
            // },
          ],
        },
        {
          label: 'Support',
          fields: [
            {
              name: 'hints',
              type: 'array',
              label: 'Hints',
              admin: {
                description: 'Provide helpful hints for common problems',
              },
              fields: [
                {
                  name: 'question',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'answer',
                  type: 'text',
                  required: true,
                },
              ],
            },
            {
              name: 'resources',
              type: 'array',
              label: 'Resources',
              admin: {
                description: 'Provide helpful resources for completing the assignment',
              },
              fields: [
                {
                  name: 'title',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'url',
                  type: 'text',
                  required: true,
                },
              ],
            },
          ],
        },
        {
          label: 'Starter',
          fields: [
            {
              type: 'group',
              name: 'starterCode',
              label: '',
              fields: [
                {
                  label: 'HTML',
                  name: 'html',
                  type: 'code',
                  admin: {
                    language: 'html',
                    condition: (data) => data.language === LANGUAGES.WEB,
                  },
                },
                {
                  label: 'CSS',
                  name: 'css',
                  type: 'code',
                  admin: {
                    language: 'css',
                    condition: (data) => data.language === LANGUAGES.WEB,
                  },
                },
                {
                  label: 'JavaScript',
                  name: 'js',
                  type: 'code',
                  admin: {
                    language: 'javascript',
                    condition: (data) => data.language === LANGUAGES.WEB,
                  },
                },
                {
                  label: 'Java',
                  name: 'java',
                  type: 'code',
                  admin: {
                    language: 'java',
                    condition: (data) => data.language === LANGUAGES.JAVA,
                  },
                },
                {
                  label: 'C',
                  name: 'c',
                  type: 'code',
                  admin: {
                    language: 'c',
                    condition: (data) => data.language === LANGUAGES.C,
                  },
                },
              ],
            },
          ],
        },
        {
          label: 'Solution',
          fields: [
            {
              type: 'group',
              name: 'solutionCode',
              access: {
                read: AssignmentFieldConfig.solution?.read,
              },
              label: '',
              fields: [
                {
                  name: 'html',
                  label: 'HTML',
                  type: 'code',
                  admin: {
                    language: 'html',
                    condition: (data) => data.language === LANGUAGES.WEB,
                  },
                },
                {
                  name: 'css',
                  label: 'CSS',
                  type: 'code',
                  admin: {
                    language: 'css',
                    condition: (data) => data.language === LANGUAGES.WEB,
                  },
                },
                {
                  name: 'js',
                  label: 'JavaScript',
                  type: 'code',
                  admin: {
                    language: 'javascript',
                    condition: (data) => data.language === LANGUAGES.WEB,
                  },
                },
                {
                  name: 'java',
                  label: 'Java',
                  type: 'code',
                  admin: {
                    language: 'java',
                    condition: (data) => data.language === LANGUAGES.JAVA,
                  },
                },
                {
                  name: 'c',
                  label: 'C',
                  type: 'code',
                  admin: {
                    language: 'c',
                    condition: (data) => data.language === LANGUAGES.C,
                  },
                },
              ],
            },
            {
              name: 'solutionNotes',
              type: 'textarea',
              admin: {
                description: 'Add notes about the solution (visible only to instructors)',
              },
              access: {
                read: AssignmentFieldConfig.solution?.read,
              },
            },
          ],
        },
        // {
        //   label: 'Tests',
        //   fields: [
        //     {
        //       name: 'testSuites',
        //       label: 'Test Suites',
        //       type: 'array',
        //       access: {
        //         read: ({ data }) => data?.language === LANGUAGES.WEB,
        //       },
        //       fields: [...TestSuiteBlock.fields],
        //       minRows: 1,
        //       admin: {
        //         description: 'Define one or more suites of test cases.',
        //         condition: (data) => data.language === LANGUAGES.WEB,
        //       },
        //     },
        //     {
        //       name: 'javaTestCases',
        //       label: 'Java Test Cases',
        //       type: 'array',
        //       access: {
        //         read: ({ data }) => data?.language === LANGUAGES.JAVA,
        //       },
        //       admin: {
        //         description: 'Define test cases specifically for Java assignments',
        //         condition: (data) => data.language === LANGUAGES.JAVA,
        //       },
        //       fields: [
        //         {
        //           name: 'title',
        //           type: 'text',
        //           required: true,
        //         },
        //         {
        //           name: 'input',
        //           type: 'textarea',
        //           admin: {
        //             description: 'Command line arguments or input values for the test',
        //           },
        //         },
        //         {
        //           name: 'expectedOutput',
        //           type: 'textarea',
        //           required: true,
        //           admin: {
        //             description: 'Expected console output',
        //           },
        //         },
        //         {
        //           name: 'tolerance',
        //           type: 'number',
        //           required: false,
        //           admin: {
        //             description: 'Allowed tolerance for numeric outputs (optional)',
        //             condition: (data) => {
        //               const val = data.expectedOutput?.trim();
        //               return !isNaN(val) && val !== '';
        //             },
        //           },
        //         },
        //         {
        //           name: 'isHidden',
        //           type: 'checkbox',
        //           label: 'Hidden Test',
        //           defaultValue: false,
        //           admin: {
        //             description: 'Test details are hidden from students until submission',
        //           },
        //         },
        //       ],
        //     },
        //     {
        //       name: 'cTestCases',
        //       label: 'C Test Cases',
        //       type: 'array',
        //       admin: {
        //         description: 'Define test cases specifically for C assignments',
        //         condition: (data) => data.language === LANGUAGES.C,
        //       },
        //       access: {
        //         read: ({ data }) => data?.language === LANGUAGES.C,
        //       },
        //       fields: [
        //         {
        //           name: 'title',
        //           type: 'text',
        //           required: true,
        //         },
        //         {
        //           name: 'input',
        //           type: 'textarea',
        //           admin: {
        //             description: 'Command line arguments or input values for the test',
        //           },
        //         },
        //         {
        //           name: 'expectedOutput',
        //           type: 'textarea',
        //           required: true,
        //           admin: {
        //             description: 'Expected console output',
        //           },
        //         },
        //         {
        //           name: 'tolerance',
        //           type: 'number',
        //           required: false,
        //           admin: {
        //             description: 'Allowed tolerance for numeric outputs (optional)',
        //             condition: (data) => {
        //               const val = data.expectedOutput?.trim();
        //               return !isNaN(val) && val !== '';
        //             },
        //           },
        //         },
        //         {
        //           name: 'isHidden',
        //           type: 'checkbox',
        //           label: 'Hidden Test',
        //           defaultValue: false,
        //           admin: {
        //             description: 'Test details are hidden from students until submission',
        //           },
        //         },
        //       ],
        //     },
        //   ],
        // },
        {
          label: 'Submissions',
          fields: [
            {
              name: 'submissions',
              label: 'Student Submissions',
              access: {
                read: AssignmentFieldConfig.submission?.read,
              },
              type: 'join',
              on: 'assignment',
              collection: 'submissions',
              hasMany: true,
              admin: {
                description: 'View all submissions for this assignment',
                allowCreate: false,
                defaultColumns: ['_id', 'student', 'score', 'status', 'updatedAt', 'actions'],
              },
            },
          ],
        },
      ],
    },
  ],
  endpoints,
};
