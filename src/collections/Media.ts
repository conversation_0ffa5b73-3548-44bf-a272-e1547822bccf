// import { isSuperAdminAccess } from '@/access/isSuperAdmin';
// import type { CollectionConfig } from 'payload';

// export const Media: CollectionConfig = {
//   slug: 'media',
//   access: {
//     read: isSuperAdminAccess,
//     create: isSuperAdminAccess,
//     delete: isSuperAdminAccess,
//     update: isSuperAdminAccess,
//   },
//   fields: [
//     {
//       name: 'alt',
//       type: 'text',
//       required: true,
//     },
//   ],
//   upload: true,
// };
