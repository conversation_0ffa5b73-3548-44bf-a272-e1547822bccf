import type { CollectionConfig } from 'payload';

import { SubjectCollectionConfig } from './access';

const Subjects: CollectionConfig = {
  slug: 'subjects',
  access: SubjectCollectionConfig,
  admin: {
    useAsTitle: 'name',
    group: 'Academic & Curriculum',
  },
  disableDuplicate: true,
  lockDocuments: false,
  versions: false,
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      type: 'textarea',
    },
  ],
};

export default Subjects;
