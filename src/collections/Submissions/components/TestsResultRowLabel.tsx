import { RowLabel, type Row<PERSON>abelProps } from '@payloadcms/ui';
import type { ServerComponentProps } from 'payload';
import { getColor } from '@/lib/colors';

const TestResultLabel = ({
  rowNumber,
  input,
  status,
}: {
  rowNumber: number;
  status: string;
  input: string;
}) => {
  const score = status === 'PASS' ? 100 : 0;
  const color = getColor(score);

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      <span
        style={{
          color,
          fontWeight: 500,
          padding: '4px 10px',
          borderRadius: '6px',
          backgroundColor: color ? `${color}10` : 'transparent',
          transition: 'all 0.2s ease',
          boxShadow: color ? '0 1px 3px rgba(0, 0, 0, 0.1)' : 'none',
        }}
      >
        {`Test ${rowNumber}`}
      </span>
      <span
        style={{
          padding: '4px 10px',
          fontSize: '0.9em',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          maxWidth: '150px',
        }}
      >
        Input: {input}
      </span>
    </div>
  );
};

const TestsResultRowLabel = ({
  rowNumber,
  path,
  siblingData,
}: RowLabelProps & ServerComponentProps) => {
  if (!siblingData?.testsResult || !rowNumber) return null;

  const status = siblingData?.testsResult[rowNumber - 1]?.status as string;
  const input = siblingData?.testsResult[rowNumber - 1]?.input as string;

  return (
    <RowLabel
      CustomComponent={TestResultLabel({ rowNumber, status, input })}
      path={path}
      label={`Test ${rowNumber}`}
    />
  );
};

export default TestsResultRowLabel;
