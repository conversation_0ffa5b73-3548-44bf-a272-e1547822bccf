'use client';

import './EvaluateButton.css';

import { Button, useDocumentInfo } from '@payloadcms/ui';
import { useRouter } from 'next/navigation';
import type { UIFieldClientComponent } from 'payload';
import { useJobPolling } from '@/hooks/useJobPolling';

const EvaluateButton: UIFieldClientComponent = () => {
  const router = useRouter();
  const { id } = useDocumentInfo();

  const { status, startJob, reset } = useJobPolling({
    onComplete: () => {
      router.refresh();
    },
  });

  const startEvaluateJob = async () => {
    await startJob(`/api/submissions/${id}/evaluate`);
  };

  const getButtonText = () => {
    switch (status) {
      case 'processing':
        return 'Processing...';
      case 'completed':
        return 'Processed';
      case 'error':
        return 'Error - Retry';
      default:
        return 'Auto Evaluate';
    }
  };

  const handleButtonClick = () => {
    if (status === 'completed') {
      reset(); // Reset to allow re-evaluation
    } else if (status === 'error') {
      reset();
      startEvaluateJob();
    } else {
      startEvaluateJob();
    }
  };

  const getButtonClassName = () => {
    switch (status) {
      case 'completed':
        return 'btn btn--style-success btn--size-medium';
      case 'error':
        return 'btn btn--style-error btn--size-medium';
      default:
        return 'btn btn--style-primary btn--size-medium';
    }
  };

  return (
    <Button
      onClick={handleButtonClick}
      disabled={status === 'processing'}
      className={getButtonClassName()}
      size="medium"
    >
      {getButtonText()}
    </Button>
  );
};

export default EvaluateButton;
