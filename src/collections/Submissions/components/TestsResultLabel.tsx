import { RowLabel } from '@payloadcms/ui';
import type { UIFieldServerComponent } from 'payload';
import { getColor } from '@/lib/colors';
import type { Submission } from '@/payload-types';

const TestsResultLabel: UIFieldServerComponent = ({ path, data }) => {
  const testsResult: Submission['testsResult'] = data?.testsResult;

  if (!testsResult || testsResult?.length === 0) {
    return <RowLabel path={path} label="Tests Result: Not available" />;
  }

  const numberOfTotal = testsResult.length;
  const numberOfPassed = testsResult.filter((tr) => tr.status === 'PASS').length;
  const scoreRatio = numberOfPassed / numberOfTotal;
  const percentage = Math.round(scoreRatio * 100);

  const color = getColor(percentage);

  return (
    <RowLabel
      path={path}
      label={
        <div
          style={{
            fontWeight: '500',
            fontSize: '1.4rem',
          }}
        >
          <span>Tests Result: </span>
          <span
            style={{
              color,
            }}
          >
            {numberOfPassed}/{numberOfTotal} ({percentage}%)
          </span>
        </div>
      }
    />
  );
};

export default TestsResultLabel;
