import type { DefaultCellComponentProps } from 'payload';
import { getColor } from '@/lib/colors';

export const ScoreCell = ({ cellData }: DefaultCellComponentProps) => {
  if (!cellData) return <span style={{ color: '#888' }}>N/A</span>;
  const color = getColor(cellData);
  return <span style={{ color }}>{cellData}</span>;
};

export const StatusCell = ({ cellData }: DefaultCellComponentProps) => {
  if (!cellData) return <span style={{ color: '#888' }}>N/A</span>;

  const statusMap: Record<string, { label: string; color: string }> = {
    review: { label: 'Review', color: '#007bff' },
    graded: { label: 'Graded', color: '#28a745' },
    resubmit: { label: 'Resubmit', color: '#dc3545' },
  };

  const status = statusMap[cellData];

  if (!status) {
    return <span>{cellData}</span>;
  }

  return (
    <span
      style={{
        color: status.color,
        fontWeight: 500,
      }}
    >
      {status.label}
    </span>
  );
};
