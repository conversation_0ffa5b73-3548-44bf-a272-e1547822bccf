'use client';

import React, { FC } from 'react';
import { useConfig } from '@payloadcms/ui';
import { DefaultCellComponentProps } from 'payload';

const ActionsCell: FC<DefaultCellComponentProps> = ({ rowData }) => {
  const { config } = useConfig();

  if (!rowData?.id) {
    return <span>-</span>;
  }

  const handleViewSubmission = (e: any) => {
    e.preventDefault();
    const submissionUrl = `${config.routes.admin}/collections/submissions/${rowData.id}`;
    window.open(submissionUrl, '_blank');
  };

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
      }}
    >
      <button
        onClick={handleViewSubmission}
        className="btn btn--style-primary btn--size-small"
        style={{
          margin: 0,
        }}
        title="View submission details"
      >
        View Details
      </button>
    </div>
  );
};

export default ActionsCell;
