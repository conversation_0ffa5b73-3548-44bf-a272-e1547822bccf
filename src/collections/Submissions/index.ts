import type { CollectionConfig, Field } from 'payload';
import { endpoints } from '@/endpoints/submission.endpoints';
import {
  canModifySubmissions,
  canReadSubmissions,
  canReadSubmissionsVersions,
  isAdminOnlyFieldAccess,
  preventLockedEdit,
} from './access';

// Helper function to create code fields with consistent structure
const createCodeFields = (
  configs: Array<{ name: string; label: string; language: string }>,
): Field[] => {
  return configs.map(({ name, label, language }) => ({
    name,
    label,
    type: 'code' as const,
    admin: {
      language,
      condition: (data: any) => Boolean(data?.[name]?.trim()),
    },
  }));
};

const Submissions: CollectionConfig = {
  slug: 'submissions',
  disableDuplicate: true,
  versions: {
    maxPerDoc: 5,
  },
  admin: {
    useAsTitle: 'id',
    defaultColumns: ['id', 'assignment', 'student', 'score', 'status', 'updatedAt', 'actions'],
    group: 'Academic & Curriculum',
    components: {
      edit: {
        beforeDocumentControls: [{ path: './collections/Submissions/components/EvaluateButton' }],
      },
    },
  },
  access: {
    read: canReadSubmissions,
    create: canModifySubmissions,
    update: canModifySubmissions,
    delete: canModifySubmissions,
    readVersions: canReadSubmissionsVersions,
  },
  hooks: {
    beforeChange: [preventLockedEdit],
  },
  indexes: [
    {
      fields: ['student', 'assignment'],
      unique: true,
    },
  ],
  fields: [
    {
      name: 'actions',
      type: 'ui',
      admin: {
        components: {
          Cell: './collections/Submissions/components/ActionsCell',
        },
      },
    },
    {
      name: '_id',
      label: 'ID',
      type: 'ui',
      admin: {
        components: {
          Cell: './collections/Submissions/components/IdCell',
        },
        disableListColumn: true,
      },
    },
    {
      type: 'tabs',
      tabs: [
        {
          label: 'Submitted Code',
          fields: [
            {
              type: 'row',
              fields: [
                {
                  name: 'student',
                  type: 'relationship',
                  relationTo: 'users',
                  required: true,
                  hasMany: false,
                  access: {
                    read: isAdminOnlyFieldAccess,
                  },
                },
                {
                  name: 'assignment',
                  type: 'relationship',
                  relationTo: 'assignments',
                  required: true,
                  hasMany: false,
                  access: {
                    read: ({ req }) => !!req.user,
                  },
                },
              ],
            },
            ...createCodeFields([
              { name: 'html', label: 'HTML', language: 'html' },
              { name: 'css', label: 'CSS', language: 'css' },
              { name: 'js', label: 'JavaScript', language: 'javascript' },
              { name: 'java', label: 'Java', language: 'java' },
              { name: 'c', label: 'C', language: 'c' },
            ]),
          ],
        },
        {
          label: 'Evaluation',
          fields: [
            {
              name: 'summary',
              type: 'textarea',
              label: 'Summary',
            },
            {
              type: 'row',
              fields: [
                {
                  name: 'status',
                  type: 'select',
                  defaultValue: 'review',
                  options: [
                    { label: 'Review', value: 'review' },
                    { label: 'Graded', value: 'graded' },
                    { label: 'Resubmit', value: 'resubmit' },
                  ],
                  admin: {
                    components: {
                      Cell: './collections/Submissions/components/CustomCells#StatusCell',
                    },
                  },
                },
                {
                  name: 'score',
                  label: 'Score',
                  type: 'number',
                  min: 0,
                  max: 100,
                  admin: {
                    components: {
                      Cell: './collections/Submissions/components/CustomCells#ScoreCell',
                    },
                  },
                },
              ],
            },

            {
              name: 'testsResult',
              label: 'Tests Result',
              admin: {
                initCollapsed: true,
                components: {
                  Label: './collections/Submissions/components/TestsResultLabel',
                  RowLabel: './collections/Submissions/components/TestsResultRowLabel',
                },
              },
              type: 'array',
              fields: [
                {
                  name: 'input',
                  type: 'textarea',
                },
                {
                  name: 'expected',
                  type: 'textarea',
                },
                {
                  name: 'actual',
                  type: 'textarea',
                },
                {
                  name: 'status',
                  type: 'select',
                  options: ['PASS', 'FAIL'],
                },
              ],
            },
            {
              name: 'feedback',
              type: 'textarea',
              label: 'Instructor Feedback',
            },
            {
              name: 'isLocked',
              type: 'checkbox',
              label: 'Submission Locked',
              defaultValue: false,
              admin: {
                position: 'sidebar',
                description: 'Prevents further edits by the student.',
              },
              access: {
                update: isAdminOnlyFieldAccess,
              },
            },
          ],
        },
      ],
    },
  ],
  endpoints,
};

export default Submissions;
