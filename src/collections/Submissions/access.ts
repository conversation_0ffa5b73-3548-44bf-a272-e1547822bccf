import type { Access, CollectionBeforeC<PERSON>eHook, FieldAccess } from 'payload';
import { isFaculty, isSuperAdmin } from '@/access';

export const isAdminOnlyFieldAccess: FieldAccess = ({ req }) => {
  return isFaculty(req.user) || isSuperAdmin(req.user);
};

export const canReadSubmissionsVersions: Access = async ({ req }) => {
  const user = req.user;

  if (!user) return false;

  if (isSuperAdmin(req.user) || isFaculty(req.user)) {
    return true;
  }

  // Extract parent ID from query parameters safely
  const whereClause = req.query?.where as any;
  const parentId = whereClause?.parent?.equals;
  if (!parentId) return false;

  try {
    const document = await req.payload.findByID({
      collection: 'submissions',
      id: String(parentId),
      depth: 0,
      select: {
        student: true,
      },
    });

    return document.student === user.id;
  } catch (error) {
    console.error('Error checking submission access:', error);
    return false;
  }
};

export const canReadSubmissions: Access = ({ req }) => {
  const user = req.user;

  if (!user) return false;

  if (isSuperAdmin(req.user) || isFaculty(req.user)) {
    return true;
  }

  return {
    student: {
      equals: user.id,
    },
  };
};

// Only allow create/update/delete if the user is part of the tenant (your existing check)
export const canModifySubmissions: Access = ({ req }) => {
  return (req.user?.tenants?.length ?? 0) > 0;
};

// Hook to block updates if isLocked is true (for non-admins/faculty)
export const preventLockedEdit: CollectionBeforeChangeHook = async ({
  req,
  data,
  operation,
  originalDoc,
}) => {
  if (
    operation === 'update' &&
    originalDoc?.isLocked &&
    !(isSuperAdmin(req.user) || isFaculty(req.user))
  ) {
    throw new Error('This submission is locked and cannot be edited.');
  }

  return data;
};
