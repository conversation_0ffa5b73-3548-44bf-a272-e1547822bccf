import path from 'node:path';
import { fileURLToPath } from 'node:url';
import { postgresAdapter } from '@payloadcms/db-postgres';
import { multiTenantPlugin } from '@payloadcms/plugin-multi-tenant';
import { lexicalEditor } from '@payloadcms/richtext-lexical';
import { buildConfig } from 'payload';
import sharp from 'sharp';
import { isAuthenticated } from './access';
import { isSuperAdmin } from './access/roles';
import { Assignments } from './collections/Assignments';
import { Batches } from './collections/Batches';
import { Modules } from './collections/Module';
import { Enrollments } from './collections/Module/BatchAccess';
import Subjects from './collections/Subjects';
import Submissions from './collections/Submissions';
// import { Media } from './collections/Media';
import { Tenants } from './collections/Tenants';
// Collections
import Users from './collections/Users';
import { companyName } from './config/site';
import type { Config } from './payload-types';
// import { getCyclomaticComplexity } from "./lib/cyclomatic-complexity";
import { SubmissionEvaluationService } from './services/submission-evaluation.service';
// Utilities
// import { seed } from './seed';
import { seedDU } from './tenant-data';
import { getUserTenantIDs } from './utilities/getUserTenantIDs';

// Get dirname from ESM
const filename = fileURLToPath(import.meta.url);
const dirname = path.dirname(filename);

export default buildConfig({
  graphQL: {
    disable: true,
  },
  jobs: {
    tasks: [
      {
        slug: 'submission-worker',
        retries: 3,
        inputSchema: [
          {
            name: 'submissionId',
            type: 'text',
            required: true,
          },
        ],
        outputSchema: [
          {
            name: 'resultId',
            type: 'text',
          },
        ],
        handler: async ({ input, req: { payload } }) => {
          const evaluationService = new SubmissionEvaluationService(payload);
          return await evaluationService.evaluateSubmission(input.submissionId);
        },
      },
    ],
  },

  i18n: {
    fallbackLanguage: 'en',
    translations: {
      en: {
        general: {
          payloadSettings: 'Settings',
        },
      },
    },
  },
  admin: {
    user: 'users',
    avatar: 'default',
    meta: {
      title: companyName,
      titleSuffix: ` - ${companyName}`,
      description:
        'A next-gen lab management platform built for institutes offering Computer Science courses',
      creator: companyName,
      category: 'education',
      generator: companyName.toLowerCase(),
      icons: [
        {
          url: '/favicon.png',
          rel: 'icon',
        },
      ],
    },
    importMap: {
      baseDir: path.resolve(dirname),
    },
    components: {
      afterNavLinks: [{ path: './components/AfterNavLinks' }],
      actions: ['./components/CustomNav'],
      views: {
        'pending-submissions': {
          Component: './components/pending-submissions/index',
          path: '/pending-submissions',
        },
      },
      graphics: {
        Icon: './components/Icon',
        Logo: './components/Logo',
      },
    },
  },

  onInit: async (args) => {
    // if(process.env.CHANGE_PASSWORD)

    if (process.env.SEED_DB) {
      await seedDU(args);
    }
  },

  collections: [Users, Tenants, Batches, Enrollments, Subjects, Modules, Assignments, Submissions],

  editor: lexicalEditor(),

  cookiePrefix: 'super-labs',

  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },

  secret: process.env.PAYLOAD_SECRET!,

  db: postgresAdapter({
    pool: {
      connectionString: process.env.DATABASE_URI!,
      ...(String(process.env.DATABASE_URI!).includes('aivencloud.com')
        ? {
            ssl: {
              rejectUnauthorized: false,
            },
          }
        : {}),
    },
  }),

  sharp,

  plugins: [
    multiTenantPlugin<Config>({
      cleanupAfterTenantDelete: false,
      collections: {
        subjects: {},
        submissions: {},
        assignments: {},
      },
      tenantField: {
        access: {
          read: ({ req }: { req: any }) => {
            if (isAuthenticated({ req })) return true;
            return false;
          },
          update: (req: any) =>
            isSuperAdmin(req.user) || getUserTenantIDs(req.user, 'faculty').length > 0,
        },
      },
      tenantsArrayField: {
        includeDefaultField: false,
      },
      userHasAccessToAllTenants: (user) => isSuperAdmin(user),
    }),
    // payloadCloudPlugin(),
    // storage-adapter-placeholder
  ],
});
