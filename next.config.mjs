import { withSentryConfig } from '@sentry/nextjs';
import { withPayload } from '@payloadcms/next/withPayload';
import BuildAnalyzer from '@next/bundle-analyzer';

const withBundleAnalyzer = BuildAnalyzer({
  enabled: process.env.ANALYZE === 'true',
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    NEXT_PUBLIC_VERSION: '1.0.22',
  },

  // Basic optimizations
  experimental: {
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },

  webpack: (config, { webpack }) => {
    config.plugins.push(
      new webpack.IgnorePlugin({
        resourceRegExp: new RegExp(
          '^pg-native$|^cloudflare:sockets$|^/src/lib/test-runner/server.ts$',
        ),
      }),
    );

    return config;
  },
};

// Configure Sentry
const sentryConfig = {
  org: 'soham901',
  project: 'easelabs-web',
  silent: !process.env.CI,
  disableLogger: true,
  hideSourceMaps: true,
  dryRun: !process.env.CI,
};

export default withBundleAnalyzer(
  withSentryConfig(
    withPayload(nextConfig, {
      devBundleServerPackages: false,
    }),
    sentryConfig,
  ),
);
