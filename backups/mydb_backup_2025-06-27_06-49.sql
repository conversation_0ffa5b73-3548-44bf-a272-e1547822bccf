--
-- PostgreSQL database dump
--

-- Dumped from database version 15.13 (Debian 15.13-1.pgdg120+1)
-- Dumped by pg_dump version 15.13 (Debian 15.13-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: public; Type: SCHEMA; Schema: -; Owner: sandip
--

-- *not* creating schema, since initdb creates it


ALTER SCHEMA public OWNER TO sandip;

--
-- Name: SCHEMA public; Type: COMMENT; Schema: -; Owner: sandip
--

COMMENT ON SCHEMA public IS '';


--
-- Name: enum__assignments_v_blocks_action_action_type; Type: TYPE; Schema: public; Owner: sandip
--

CREATE TYPE public.enum__assignments_v_blocks_action_action_type AS ENUM (
    'click',
    'input',
    'hover'
);


ALTER TYPE public.enum__assignments_v_blocks_action_action_type OWNER TO sandip;

--
-- Name: enum__assignments_v_blocks_assertion_assertion_type; Type: TYPE; Schema: public; Owner: sandip
--

CREATE TYPE public.enum__assignments_v_blocks_assertion_assertion_type AS ENUM (
    'textContent',
    'exists',
    'css',
    'notExists',
    'hasClass',
    'ariaLabel',
    'value',
    'alert'
);


ALTER TYPE public.enum__assignments_v_blocks_assertion_assertion_type OWNER TO sandip;

--
-- Name: enum__assignments_v_version_difficulty; Type: TYPE; Schema: public; Owner: sandip
--

CREATE TYPE public.enum__assignments_v_version_difficulty AS ENUM (
    'easy',
    'medium',
    'hard'
);


ALTER TYPE public.enum__assignments_v_version_difficulty OWNER TO sandip;

--
-- Name: enum__assignments_v_version_language; Type: TYPE; Schema: public; Owner: sandip
--

CREATE TYPE public.enum__assignments_v_version_language AS ENUM (
    'web',
    'java',
    'c'
);


ALTER TYPE public.enum__assignments_v_version_language OWNER TO sandip;

--
-- Name: enum__assignments_v_version_status; Type: TYPE; Schema: public; Owner: sandip
--

CREATE TYPE public.enum__assignments_v_version_status AS ENUM (
    'draft',
    'published'
);


ALTER TYPE public.enum__assignments_v_version_status OWNER TO sandip;

--
-- Name: enum__assignments_v_version_test_suites_visibility; Type: TYPE; Schema: public; Owner: sandip
--

CREATE TYPE public.enum__assignments_v_version_test_suites_visibility AS ENUM (
    'visible',
    'hidden'
);


ALTER TYPE public.enum__assignments_v_version_test_suites_visibility OWNER TO sandip;

--
-- Name: enum__submissions_v_version_status; Type: TYPE; Schema: public; Owner: sandip
--

CREATE TYPE public.enum__submissions_v_version_status AS ENUM (
    'review',
    'graded',
    'resubmit'
);


ALTER TYPE public.enum__submissions_v_version_status OWNER TO sandip;

--
-- Name: enum_assignments_blocks_action_action_type; Type: TYPE; Schema: public; Owner: sandip
--

CREATE TYPE public.enum_assignments_blocks_action_action_type AS ENUM (
    'click',
    'input',
    'hover'
);


ALTER TYPE public.enum_assignments_blocks_action_action_type OWNER TO sandip;

--
-- Name: enum_assignments_blocks_assertion_assertion_type; Type: TYPE; Schema: public; Owner: sandip
--

CREATE TYPE public.enum_assignments_blocks_assertion_assertion_type AS ENUM (
    'textContent',
    'exists',
    'css',
    'notExists',
    'hasClass',
    'ariaLabel',
    'value',
    'alert'
);


ALTER TYPE public.enum_assignments_blocks_assertion_assertion_type OWNER TO sandip;

--
-- Name: enum_assignments_difficulty; Type: TYPE; Schema: public; Owner: sandip
--

CREATE TYPE public.enum_assignments_difficulty AS ENUM (
    'easy',
    'medium',
    'hard'
);


ALTER TYPE public.enum_assignments_difficulty OWNER TO sandip;

--
-- Name: enum_assignments_language; Type: TYPE; Schema: public; Owner: sandip
--

CREATE TYPE public.enum_assignments_language AS ENUM (
    'web',
    'java',
    'c'
);


ALTER TYPE public.enum_assignments_language OWNER TO sandip;

--
-- Name: enum_assignments_status; Type: TYPE; Schema: public; Owner: sandip
--

CREATE TYPE public.enum_assignments_status AS ENUM (
    'draft',
    'published'
);


ALTER TYPE public.enum_assignments_status OWNER TO sandip;

--
-- Name: enum_assignments_test_suites_visibility; Type: TYPE; Schema: public; Owner: sandip
--

CREATE TYPE public.enum_assignments_test_suites_visibility AS ENUM (
    'visible',
    'hidden'
);


ALTER TYPE public.enum_assignments_test_suites_visibility OWNER TO sandip;

--
-- Name: enum_payload_jobs_log_state; Type: TYPE; Schema: public; Owner: sandip
--

CREATE TYPE public.enum_payload_jobs_log_state AS ENUM (
    'failed',
    'succeeded'
);


ALTER TYPE public.enum_payload_jobs_log_state OWNER TO sandip;

--
-- Name: enum_payload_jobs_log_task_slug; Type: TYPE; Schema: public; Owner: sandip
--

CREATE TYPE public.enum_payload_jobs_log_task_slug AS ENUM (
    'inline',
    'submission-worker'
);


ALTER TYPE public.enum_payload_jobs_log_task_slug OWNER TO sandip;

--
-- Name: enum_payload_jobs_task_slug; Type: TYPE; Schema: public; Owner: sandip
--

CREATE TYPE public.enum_payload_jobs_task_slug AS ENUM (
    'inline',
    'submission-worker'
);


ALTER TYPE public.enum_payload_jobs_task_slug OWNER TO sandip;

--
-- Name: enum_submissions_status; Type: TYPE; Schema: public; Owner: sandip
--

CREATE TYPE public.enum_submissions_status AS ENUM (
    'review',
    'graded',
    'resubmit'
);


ALTER TYPE public.enum_submissions_status OWNER TO sandip;

--
-- Name: enum_users_roles; Type: TYPE; Schema: public; Owner: sandip
--

CREATE TYPE public.enum_users_roles AS ENUM (
    'super-admin',
    'user'
);


ALTER TYPE public.enum_users_roles OWNER TO sandip;

--
-- Name: enum_users_tenants_roles; Type: TYPE; Schema: public; Owner: sandip
--

CREATE TYPE public.enum_users_tenants_roles AS ENUM (
    'faculty',
    'student'
);


ALTER TYPE public.enum_users_tenants_roles OWNER TO sandip;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: _assignments_v; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public._assignments_v (
    id integer NOT NULL,
    parent_id integer,
    version_tenant_id integer,
    version_title character varying,
    version_subject_id integer,
    version_module_id integer,
    version_description character varying,
    version_language public.enum__assignments_v_version_language DEFAULT 'web'::public.enum__assignments_v_version_language,
    version_difficulty public.enum__assignments_v_version_difficulty DEFAULT 'easy'::public.enum__assignments_v_version_difficulty,
    version_points numeric,
    version_due_date timestamp(3) with time zone,
    version_instructions character varying,
    version_requires_command_line_args boolean,
    version_starter_code_html character varying,
    version_starter_code_css character varying,
    version_starter_code_js character varying,
    version_starter_code_java character varying,
    version_starter_code_c character varying,
    version_solution_code_html character varying,
    version_solution_code_css character varying,
    version_solution_code_js character varying,
    version_solution_code_java character varying,
    version_solution_code_c character varying,
    version_solution_notes character varying,
    version_updated_at timestamp(3) with time zone,
    version_created_at timestamp(3) with time zone,
    version__status public.enum__assignments_v_version_status DEFAULT 'draft'::public.enum__assignments_v_version_status,
    created_at timestamp(3) with time zone DEFAULT now() NOT NULL,
    updated_at timestamp(3) with time zone DEFAULT now() NOT NULL,
    latest boolean
);


ALTER TABLE public._assignments_v OWNER TO sandip;

--
-- Name: _assignments_v_blocks_action; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public._assignments_v_blocks_action (
    _order integer NOT NULL,
    _parent_id integer NOT NULL,
    _path text NOT NULL,
    id integer NOT NULL,
    action_type public.enum__assignments_v_blocks_action_action_type,
    action_selector character varying,
    action_value character varying,
    _uuid character varying,
    block_name character varying
);


ALTER TABLE public._assignments_v_blocks_action OWNER TO sandip;

--
-- Name: _assignments_v_blocks_action_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public._assignments_v_blocks_action_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public._assignments_v_blocks_action_id_seq OWNER TO sandip;

--
-- Name: _assignments_v_blocks_action_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public._assignments_v_blocks_action_id_seq OWNED BY public._assignments_v_blocks_action.id;


--
-- Name: _assignments_v_blocks_assertion; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public._assignments_v_blocks_assertion (
    _order integer NOT NULL,
    _parent_id integer NOT NULL,
    _path text NOT NULL,
    id integer NOT NULL,
    assertion_type public.enum__assignments_v_blocks_assertion_assertion_type,
    assertion_selector character varying,
    expected_value character varying,
    expected_class character varying,
    css_property character varying,
    expected_css_value character varying,
    expected_alert_text character varying,
    _uuid character varying,
    block_name character varying
);


ALTER TABLE public._assignments_v_blocks_assertion OWNER TO sandip;

--
-- Name: _assignments_v_blocks_assertion_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public._assignments_v_blocks_assertion_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public._assignments_v_blocks_assertion_id_seq OWNER TO sandip;

--
-- Name: _assignments_v_blocks_assertion_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public._assignments_v_blocks_assertion_id_seq OWNED BY public._assignments_v_blocks_assertion.id;


--
-- Name: _assignments_v_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public._assignments_v_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public._assignments_v_id_seq OWNER TO sandip;

--
-- Name: _assignments_v_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public._assignments_v_id_seq OWNED BY public._assignments_v.id;


--
-- Name: _assignments_v_version_c_test_cases; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public._assignments_v_version_c_test_cases (
    _order integer NOT NULL,
    _parent_id integer NOT NULL,
    id integer NOT NULL,
    title character varying,
    input character varying,
    expected_output character varying,
    tolerance numeric,
    is_hidden boolean DEFAULT false,
    _uuid character varying
);


ALTER TABLE public._assignments_v_version_c_test_cases OWNER TO sandip;

--
-- Name: _assignments_v_version_c_test_cases_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public._assignments_v_version_c_test_cases_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public._assignments_v_version_c_test_cases_id_seq OWNER TO sandip;

--
-- Name: _assignments_v_version_c_test_cases_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public._assignments_v_version_c_test_cases_id_seq OWNED BY public._assignments_v_version_c_test_cases.id;


--
-- Name: _assignments_v_version_hints; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public._assignments_v_version_hints (
    _order integer NOT NULL,
    _parent_id integer NOT NULL,
    id integer NOT NULL,
    question character varying,
    answer character varying,
    _uuid character varying
);


ALTER TABLE public._assignments_v_version_hints OWNER TO sandip;

--
-- Name: _assignments_v_version_hints_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public._assignments_v_version_hints_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public._assignments_v_version_hints_id_seq OWNER TO sandip;

--
-- Name: _assignments_v_version_hints_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public._assignments_v_version_hints_id_seq OWNED BY public._assignments_v_version_hints.id;


--
-- Name: _assignments_v_version_java_test_cases; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public._assignments_v_version_java_test_cases (
    _order integer NOT NULL,
    _parent_id integer NOT NULL,
    id integer NOT NULL,
    title character varying,
    input character varying,
    expected_output character varying,
    tolerance numeric,
    is_hidden boolean DEFAULT false,
    _uuid character varying
);


ALTER TABLE public._assignments_v_version_java_test_cases OWNER TO sandip;

--
-- Name: _assignments_v_version_java_test_cases_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public._assignments_v_version_java_test_cases_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public._assignments_v_version_java_test_cases_id_seq OWNER TO sandip;

--
-- Name: _assignments_v_version_java_test_cases_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public._assignments_v_version_java_test_cases_id_seq OWNED BY public._assignments_v_version_java_test_cases.id;


--
-- Name: _assignments_v_version_resources; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public._assignments_v_version_resources (
    _order integer NOT NULL,
    _parent_id integer NOT NULL,
    id integer NOT NULL,
    title character varying,
    url character varying,
    _uuid character varying
);


ALTER TABLE public._assignments_v_version_resources OWNER TO sandip;

--
-- Name: _assignments_v_version_resources_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public._assignments_v_version_resources_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public._assignments_v_version_resources_id_seq OWNER TO sandip;

--
-- Name: _assignments_v_version_resources_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public._assignments_v_version_resources_id_seq OWNED BY public._assignments_v_version_resources.id;


--
-- Name: _assignments_v_version_test_suites; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public._assignments_v_version_test_suites (
    _order integer NOT NULL,
    _parent_id integer NOT NULL,
    id integer NOT NULL,
    points numeric DEFAULT 10,
    visibility public.enum__assignments_v_version_test_suites_visibility DEFAULT 'visible'::public.enum__assignments_v_version_test_suites_visibility,
    _uuid character varying
);


ALTER TABLE public._assignments_v_version_test_suites OWNER TO sandip;

--
-- Name: _assignments_v_version_test_suites_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public._assignments_v_version_test_suites_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public._assignments_v_version_test_suites_id_seq OWNER TO sandip;

--
-- Name: _assignments_v_version_test_suites_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public._assignments_v_version_test_suites_id_seq OWNED BY public._assignments_v_version_test_suites.id;


--
-- Name: _submissions_v; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public._submissions_v (
    id integer NOT NULL,
    parent_id integer,
    version_tenant_id integer,
    version_student_id integer NOT NULL,
    version_assignment_id integer NOT NULL,
    version_status public.enum__submissions_v_version_status DEFAULT 'review'::public.enum__submissions_v_version_status,
    version_is_locked boolean DEFAULT false,
    version_solution_code_html character varying,
    version_solution_code_css character varying,
    version_solution_code_js character varying,
    version_solution_code_java character varying,
    version_solution_code_c character varying,
    version_score numeric NOT NULL,
    version_passed_test_cases numeric NOT NULL,
    version_failed_test_cases numeric NOT NULL,
    version_feedback character varying,
    version_updated_at timestamp(3) with time zone,
    version_created_at timestamp(3) with time zone,
    created_at timestamp(3) with time zone DEFAULT now() NOT NULL,
    updated_at timestamp(3) with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public._submissions_v OWNER TO sandip;

--
-- Name: _submissions_v_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public._submissions_v_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public._submissions_v_id_seq OWNER TO sandip;

--
-- Name: _submissions_v_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public._submissions_v_id_seq OWNED BY public._submissions_v.id;


--
-- Name: assignments; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.assignments (
    id integer NOT NULL,
    tenant_id integer,
    title character varying,
    subject_id integer,
    module_id integer,
    description character varying,
    language public.enum_assignments_language DEFAULT 'web'::public.enum_assignments_language,
    difficulty public.enum_assignments_difficulty DEFAULT 'easy'::public.enum_assignments_difficulty,
    points numeric,
    due_date timestamp(3) with time zone,
    instructions character varying,
    requires_command_line_args boolean,
    starter_code_html character varying,
    starter_code_css character varying,
    starter_code_js character varying,
    starter_code_java character varying,
    starter_code_c character varying,
    solution_code_html character varying,
    solution_code_css character varying,
    solution_code_js character varying,
    solution_code_java character varying,
    solution_code_c character varying,
    solution_notes character varying,
    updated_at timestamp(3) with time zone DEFAULT now() NOT NULL,
    created_at timestamp(3) with time zone DEFAULT now() NOT NULL,
    _status public.enum_assignments_status DEFAULT 'draft'::public.enum_assignments_status
);


ALTER TABLE public.assignments OWNER TO sandip;

--
-- Name: assignments_blocks_action; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.assignments_blocks_action (
    _order integer NOT NULL,
    _parent_id integer NOT NULL,
    _path text NOT NULL,
    id character varying NOT NULL,
    action_type public.enum_assignments_blocks_action_action_type,
    action_selector character varying,
    action_value character varying,
    block_name character varying
);


ALTER TABLE public.assignments_blocks_action OWNER TO sandip;

--
-- Name: assignments_blocks_assertion; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.assignments_blocks_assertion (
    _order integer NOT NULL,
    _parent_id integer NOT NULL,
    _path text NOT NULL,
    id character varying NOT NULL,
    assertion_type public.enum_assignments_blocks_assertion_assertion_type,
    assertion_selector character varying,
    expected_value character varying,
    expected_class character varying,
    css_property character varying,
    expected_css_value character varying,
    expected_alert_text character varying,
    block_name character varying
);


ALTER TABLE public.assignments_blocks_assertion OWNER TO sandip;

--
-- Name: assignments_c_test_cases; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.assignments_c_test_cases (
    _order integer NOT NULL,
    _parent_id integer NOT NULL,
    id character varying NOT NULL,
    title character varying,
    input character varying,
    expected_output character varying,
    tolerance numeric,
    is_hidden boolean DEFAULT false
);


ALTER TABLE public.assignments_c_test_cases OWNER TO sandip;

--
-- Name: assignments_hints; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.assignments_hints (
    _order integer NOT NULL,
    _parent_id integer NOT NULL,
    id character varying NOT NULL,
    question character varying,
    answer character varying
);


ALTER TABLE public.assignments_hints OWNER TO sandip;

--
-- Name: assignments_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public.assignments_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.assignments_id_seq OWNER TO sandip;

--
-- Name: assignments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public.assignments_id_seq OWNED BY public.assignments.id;


--
-- Name: assignments_java_test_cases; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.assignments_java_test_cases (
    _order integer NOT NULL,
    _parent_id integer NOT NULL,
    id character varying NOT NULL,
    title character varying,
    input character varying,
    expected_output character varying,
    tolerance numeric,
    is_hidden boolean DEFAULT false
);


ALTER TABLE public.assignments_java_test_cases OWNER TO sandip;

--
-- Name: assignments_resources; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.assignments_resources (
    _order integer NOT NULL,
    _parent_id integer NOT NULL,
    id character varying NOT NULL,
    title character varying,
    url character varying
);


ALTER TABLE public.assignments_resources OWNER TO sandip;

--
-- Name: assignments_test_suites; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.assignments_test_suites (
    _order integer NOT NULL,
    _parent_id integer NOT NULL,
    id character varying NOT NULL,
    points numeric DEFAULT 10,
    visibility public.enum_assignments_test_suites_visibility DEFAULT 'visible'::public.enum_assignments_test_suites_visibility
);


ALTER TABLE public.assignments_test_suites OWNER TO sandip;

--
-- Name: batches; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.batches (
    id integer NOT NULL,
    batch_id character varying NOT NULL,
    updated_at timestamp(3) with time zone DEFAULT now() NOT NULL,
    created_at timestamp(3) with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.batches OWNER TO sandip;

--
-- Name: batches_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public.batches_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.batches_id_seq OWNER TO sandip;

--
-- Name: batches_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public.batches_id_seq OWNED BY public.batches.id;


--
-- Name: batches_rels; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.batches_rels (
    id integer NOT NULL,
    "order" integer,
    parent_id integer NOT NULL,
    path character varying NOT NULL,
    users_id integer
);


ALTER TABLE public.batches_rels OWNER TO sandip;

--
-- Name: batches_rels_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public.batches_rels_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.batches_rels_id_seq OWNER TO sandip;

--
-- Name: batches_rels_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public.batches_rels_id_seq OWNED BY public.batches_rels.id;


--
-- Name: enrollments; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.enrollments (
    id integer NOT NULL,
    module_id integer NOT NULL,
    batch_id integer NOT NULL,
    access_start timestamp(3) with time zone NOT NULL,
    access_end timestamp(3) with time zone NOT NULL,
    updated_at timestamp(3) with time zone DEFAULT now() NOT NULL,
    created_at timestamp(3) with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.enrollments OWNER TO sandip;

--
-- Name: enrollments_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public.enrollments_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.enrollments_id_seq OWNER TO sandip;

--
-- Name: enrollments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public.enrollments_id_seq OWNED BY public.enrollments.id;


--
-- Name: modules; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.modules (
    id integer NOT NULL,
    title character varying NOT NULL,
    updated_at timestamp(3) with time zone DEFAULT now() NOT NULL,
    created_at timestamp(3) with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.modules OWNER TO sandip;

--
-- Name: modules_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public.modules_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.modules_id_seq OWNER TO sandip;

--
-- Name: modules_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public.modules_id_seq OWNED BY public.modules.id;


--
-- Name: modules_rels; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.modules_rels (
    id integer NOT NULL,
    "order" integer,
    parent_id integer NOT NULL,
    path character varying NOT NULL,
    assignments_id integer
);


ALTER TABLE public.modules_rels OWNER TO sandip;

--
-- Name: modules_rels_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public.modules_rels_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.modules_rels_id_seq OWNER TO sandip;

--
-- Name: modules_rels_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public.modules_rels_id_seq OWNED BY public.modules_rels.id;


--
-- Name: payload_jobs; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.payload_jobs (
    id integer NOT NULL,
    input jsonb,
    completed_at timestamp(3) with time zone,
    total_tried numeric DEFAULT 0,
    has_error boolean DEFAULT false,
    error jsonb,
    task_slug public.enum_payload_jobs_task_slug,
    queue character varying DEFAULT 'default'::character varying,
    wait_until timestamp(3) with time zone,
    processing boolean DEFAULT false,
    updated_at timestamp(3) with time zone DEFAULT now() NOT NULL,
    created_at timestamp(3) with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.payload_jobs OWNER TO sandip;

--
-- Name: payload_jobs_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public.payload_jobs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.payload_jobs_id_seq OWNER TO sandip;

--
-- Name: payload_jobs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public.payload_jobs_id_seq OWNED BY public.payload_jobs.id;


--
-- Name: payload_jobs_log; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.payload_jobs_log (
    _order integer NOT NULL,
    _parent_id integer NOT NULL,
    id character varying NOT NULL,
    executed_at timestamp(3) with time zone NOT NULL,
    completed_at timestamp(3) with time zone NOT NULL,
    task_slug public.enum_payload_jobs_log_task_slug NOT NULL,
    task_i_d character varying NOT NULL,
    input jsonb,
    output jsonb,
    state public.enum_payload_jobs_log_state NOT NULL,
    error jsonb
);


ALTER TABLE public.payload_jobs_log OWNER TO sandip;

--
-- Name: payload_locked_documents; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.payload_locked_documents (
    id integer NOT NULL,
    global_slug character varying,
    updated_at timestamp(3) with time zone DEFAULT now() NOT NULL,
    created_at timestamp(3) with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.payload_locked_documents OWNER TO sandip;

--
-- Name: payload_locked_documents_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public.payload_locked_documents_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.payload_locked_documents_id_seq OWNER TO sandip;

--
-- Name: payload_locked_documents_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public.payload_locked_documents_id_seq OWNED BY public.payload_locked_documents.id;


--
-- Name: payload_locked_documents_rels; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.payload_locked_documents_rels (
    id integer NOT NULL,
    "order" integer,
    parent_id integer NOT NULL,
    path character varying NOT NULL,
    users_id integer,
    tenants_id integer,
    batches_id integer,
    enrollments_id integer,
    subjects_id integer,
    modules_id integer,
    assignments_id integer,
    submissions_id integer,
    payload_jobs_id integer
);


ALTER TABLE public.payload_locked_documents_rels OWNER TO sandip;

--
-- Name: payload_locked_documents_rels_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public.payload_locked_documents_rels_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.payload_locked_documents_rels_id_seq OWNER TO sandip;

--
-- Name: payload_locked_documents_rels_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public.payload_locked_documents_rels_id_seq OWNED BY public.payload_locked_documents_rels.id;


--
-- Name: payload_migrations; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.payload_migrations (
    id integer NOT NULL,
    name character varying,
    batch numeric,
    updated_at timestamp(3) with time zone DEFAULT now() NOT NULL,
    created_at timestamp(3) with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.payload_migrations OWNER TO sandip;

--
-- Name: payload_migrations_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public.payload_migrations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.payload_migrations_id_seq OWNER TO sandip;

--
-- Name: payload_migrations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public.payload_migrations_id_seq OWNED BY public.payload_migrations.id;


--
-- Name: payload_preferences; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.payload_preferences (
    id integer NOT NULL,
    key character varying,
    value jsonb,
    updated_at timestamp(3) with time zone DEFAULT now() NOT NULL,
    created_at timestamp(3) with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.payload_preferences OWNER TO sandip;

--
-- Name: payload_preferences_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public.payload_preferences_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.payload_preferences_id_seq OWNER TO sandip;

--
-- Name: payload_preferences_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public.payload_preferences_id_seq OWNED BY public.payload_preferences.id;


--
-- Name: payload_preferences_rels; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.payload_preferences_rels (
    id integer NOT NULL,
    "order" integer,
    parent_id integer NOT NULL,
    path character varying NOT NULL,
    users_id integer
);


ALTER TABLE public.payload_preferences_rels OWNER TO sandip;

--
-- Name: payload_preferences_rels_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public.payload_preferences_rels_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.payload_preferences_rels_id_seq OWNER TO sandip;

--
-- Name: payload_preferences_rels_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public.payload_preferences_rels_id_seq OWNED BY public.payload_preferences_rels.id;


--
-- Name: subjects; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.subjects (
    id integer NOT NULL,
    tenant_id integer,
    name character varying NOT NULL,
    description character varying,
    updated_at timestamp(3) with time zone DEFAULT now() NOT NULL,
    created_at timestamp(3) with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.subjects OWNER TO sandip;

--
-- Name: subjects_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public.subjects_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.subjects_id_seq OWNER TO sandip;

--
-- Name: subjects_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public.subjects_id_seq OWNED BY public.subjects.id;


--
-- Name: submissions; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.submissions (
    id integer NOT NULL,
    tenant_id integer,
    student_id integer NOT NULL,
    assignment_id integer NOT NULL,
    status public.enum_submissions_status DEFAULT 'review'::public.enum_submissions_status,
    is_locked boolean DEFAULT false,
    solution_code_html character varying,
    solution_code_css character varying,
    solution_code_js character varying,
    solution_code_java character varying,
    solution_code_c character varying,
    score numeric NOT NULL,
    passed_test_cases numeric NOT NULL,
    failed_test_cases numeric NOT NULL,
    feedback character varying,
    updated_at timestamp(3) with time zone DEFAULT now() NOT NULL,
    created_at timestamp(3) with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.submissions OWNER TO sandip;

--
-- Name: submissions_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public.submissions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.submissions_id_seq OWNER TO sandip;

--
-- Name: submissions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public.submissions_id_seq OWNED BY public.submissions.id;


--
-- Name: tenants; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.tenants (
    id integer NOT NULL,
    name character varying NOT NULL,
    domain character varying,
    slug character varying NOT NULL,
    updated_at timestamp(3) with time zone DEFAULT now() NOT NULL,
    created_at timestamp(3) with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.tenants OWNER TO sandip;

--
-- Name: tenants_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public.tenants_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.tenants_id_seq OWNER TO sandip;

--
-- Name: tenants_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public.tenants_id_seq OWNED BY public.tenants.id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.users (
    id integer NOT NULL,
    username character varying,
    full_name character varying,
    division character varying,
    updated_at timestamp(3) with time zone DEFAULT now() NOT NULL,
    created_at timestamp(3) with time zone DEFAULT now() NOT NULL,
    email character varying NOT NULL,
    reset_password_token character varying,
    reset_password_expiration timestamp(3) with time zone,
    salt character varying,
    hash character varying,
    login_attempts numeric DEFAULT 0,
    lock_until timestamp(3) with time zone
);


ALTER TABLE public.users OWNER TO sandip;

--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.users_id_seq OWNER TO sandip;

--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: users_roles; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.users_roles (
    "order" integer NOT NULL,
    parent_id integer NOT NULL,
    value public.enum_users_roles,
    id integer NOT NULL
);


ALTER TABLE public.users_roles OWNER TO sandip;

--
-- Name: users_roles_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public.users_roles_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.users_roles_id_seq OWNER TO sandip;

--
-- Name: users_roles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public.users_roles_id_seq OWNED BY public.users_roles.id;


--
-- Name: users_tenants; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.users_tenants (
    _order integer NOT NULL,
    _parent_id integer NOT NULL,
    id character varying NOT NULL,
    tenant_id integer NOT NULL
);


ALTER TABLE public.users_tenants OWNER TO sandip;

--
-- Name: users_tenants_roles; Type: TABLE; Schema: public; Owner: sandip
--

CREATE TABLE public.users_tenants_roles (
    "order" integer NOT NULL,
    parent_id character varying NOT NULL,
    value public.enum_users_tenants_roles,
    id integer NOT NULL
);


ALTER TABLE public.users_tenants_roles OWNER TO sandip;

--
-- Name: users_tenants_roles_id_seq; Type: SEQUENCE; Schema: public; Owner: sandip
--

CREATE SEQUENCE public.users_tenants_roles_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.users_tenants_roles_id_seq OWNER TO sandip;

--
-- Name: users_tenants_roles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: sandip
--

ALTER SEQUENCE public.users_tenants_roles_id_seq OWNED BY public.users_tenants_roles.id;


--
-- Name: _assignments_v id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v ALTER COLUMN id SET DEFAULT nextval('public._assignments_v_id_seq'::regclass);


--
-- Name: _assignments_v_blocks_action id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_blocks_action ALTER COLUMN id SET DEFAULT nextval('public._assignments_v_blocks_action_id_seq'::regclass);


--
-- Name: _assignments_v_blocks_assertion id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_blocks_assertion ALTER COLUMN id SET DEFAULT nextval('public._assignments_v_blocks_assertion_id_seq'::regclass);


--
-- Name: _assignments_v_version_c_test_cases id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_version_c_test_cases ALTER COLUMN id SET DEFAULT nextval('public._assignments_v_version_c_test_cases_id_seq'::regclass);


--
-- Name: _assignments_v_version_hints id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_version_hints ALTER COLUMN id SET DEFAULT nextval('public._assignments_v_version_hints_id_seq'::regclass);


--
-- Name: _assignments_v_version_java_test_cases id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_version_java_test_cases ALTER COLUMN id SET DEFAULT nextval('public._assignments_v_version_java_test_cases_id_seq'::regclass);


--
-- Name: _assignments_v_version_resources id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_version_resources ALTER COLUMN id SET DEFAULT nextval('public._assignments_v_version_resources_id_seq'::regclass);


--
-- Name: _assignments_v_version_test_suites id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_version_test_suites ALTER COLUMN id SET DEFAULT nextval('public._assignments_v_version_test_suites_id_seq'::regclass);


--
-- Name: _submissions_v id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._submissions_v ALTER COLUMN id SET DEFAULT nextval('public._submissions_v_id_seq'::regclass);


--
-- Name: assignments id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.assignments ALTER COLUMN id SET DEFAULT nextval('public.assignments_id_seq'::regclass);


--
-- Name: batches id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.batches ALTER COLUMN id SET DEFAULT nextval('public.batches_id_seq'::regclass);


--
-- Name: batches_rels id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.batches_rels ALTER COLUMN id SET DEFAULT nextval('public.batches_rels_id_seq'::regclass);


--
-- Name: enrollments id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.enrollments ALTER COLUMN id SET DEFAULT nextval('public.enrollments_id_seq'::regclass);


--
-- Name: modules id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.modules ALTER COLUMN id SET DEFAULT nextval('public.modules_id_seq'::regclass);


--
-- Name: modules_rels id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.modules_rels ALTER COLUMN id SET DEFAULT nextval('public.modules_rels_id_seq'::regclass);


--
-- Name: payload_jobs id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_jobs ALTER COLUMN id SET DEFAULT nextval('public.payload_jobs_id_seq'::regclass);


--
-- Name: payload_locked_documents id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_locked_documents ALTER COLUMN id SET DEFAULT nextval('public.payload_locked_documents_id_seq'::regclass);


--
-- Name: payload_locked_documents_rels id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_locked_documents_rels ALTER COLUMN id SET DEFAULT nextval('public.payload_locked_documents_rels_id_seq'::regclass);


--
-- Name: payload_migrations id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_migrations ALTER COLUMN id SET DEFAULT nextval('public.payload_migrations_id_seq'::regclass);


--
-- Name: payload_preferences id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_preferences ALTER COLUMN id SET DEFAULT nextval('public.payload_preferences_id_seq'::regclass);


--
-- Name: payload_preferences_rels id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_preferences_rels ALTER COLUMN id SET DEFAULT nextval('public.payload_preferences_rels_id_seq'::regclass);


--
-- Name: subjects id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.subjects ALTER COLUMN id SET DEFAULT nextval('public.subjects_id_seq'::regclass);


--
-- Name: submissions id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.submissions ALTER COLUMN id SET DEFAULT nextval('public.submissions_id_seq'::regclass);


--
-- Name: tenants id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.tenants ALTER COLUMN id SET DEFAULT nextval('public.tenants_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Name: users_roles id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.users_roles ALTER COLUMN id SET DEFAULT nextval('public.users_roles_id_seq'::regclass);


--
-- Name: users_tenants_roles id; Type: DEFAULT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.users_tenants_roles ALTER COLUMN id SET DEFAULT nextval('public.users_tenants_roles_id_seq'::regclass);


--
-- Data for Name: _assignments_v; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public._assignments_v (id, parent_id, version_tenant_id, version_title, version_subject_id, version_module_id, version_description, version_language, version_difficulty, version_points, version_due_date, version_instructions, version_requires_command_line_args, version_starter_code_html, version_starter_code_css, version_starter_code_js, version_starter_code_java, version_starter_code_c, version_solution_code_html, version_solution_code_css, version_solution_code_js, version_solution_code_java, version_solution_code_c, version_solution_notes, version_updated_at, version_created_at, version__status, created_at, updated_at, latest) FROM stdin;
1	1	1	Singly Linked List Node	1	\N	Write a C program to create a single node for a singly linked list, read its data using scanf, print the data, and free the memory.	c	easy	10	2025-07-02 02:19:53.441+00	Create a program to implement a single node for a singly linked list in C. The node should store an integer and a pointer to the next node. Read the integer using scanf, create the node dynamically, print its data, and free the memory.\n\n**Steps:**\n1. Define a `struct Node` with an integer `data` and a `next` pointer.\n2. Use `scanf` to read an integer from the user.\n3. Allocate memory for a node using `malloc`.\n4. Assign the scanned integer to the node's `data`. Set `next` to NULL.\n5. Print the node's data as `Node data: <value>`.\n6. Free the allocated memory using `free`.\n\n**Notes:**\n- Assume the input is a valid integer.\n- No need to check if `malloc` fails.\n- Use `int` for the node's data.\n\n**Example Inputs/Outputs:**\n- Input: `10` → Output: `Node data: 10`\n- Input: `-5` → Output: `Node data: -5`\n- Input: `0` → Output: `Node data: 0`	f	\N	\N	\N	\N	#include <stdio.h>\n\n// define the structure of Node\n\nvoid main() {\n    // initialize the node\n\n    // take input from user\n\n    // assign value to data member of node\n\n    // assign next member of node to NULL\n\n    // print the data member of node\n}	\N	\N	\N	\N	#include <stdio.h>\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node* next;\n};\n\nvoid main() {\n    struct Node* node = (struct Node*)malloc(sizeof(struct Node));\n\n    int val;\n    scanf("%d", &val);\n\n    node->data = val;\n\n    node->next = NULL;\n\n    printf("%d", node->data);\n    free(node);\n}	\N	2025-06-25 02:19:53.604+00	2025-06-25 02:19:53.518+00	draft	2025-06-25 02:19:54.458+00	2025-06-25 02:19:54.46+00	f
19	2	1	Menu-Driven Singly Linked List 123	1	\N	Implement a complete menu-driven singly linked list program with insert, delete, display, and count operations.	c	medium	25	2025-07-02 02:19:55.435+00	Write a menu-driven program to implement a singly linked list with multiple operations. The program should display a menu and allow users to perform various operations until they choose to exit.\n\n**Required Operations:**\n1. Insert a node at the front of the linked list\n2. Display all nodes\n3. Delete the first node of the linked list\n4. Insert a node at the end of the linked list\n5. Delete the last node of the linked list\n6. Delete a node from specified position\n7. Count the number of nodes\n8. Exit\n\n**Menu Format:**\n```\n1. Insert at front\n2. Display all nodes\n3. Delete first node\n4. Insert at end\n5. Delete last node\n6. Delete from position\n7. Count nodes\n8. Exit\nEnter your choice:\n```\n\n**Implementation Details:**\n- Use `struct Node` with `int data` and `struct Node* next`\n- Handle empty list cases appropriately\n- For display: print nodes as `data1 -> data2 -> data3 -> NULL`\n- For empty list display: print `List is empty`\n- For position-based deletion, positions start from 1\n- Continue showing menu until user selects exit option\n\n**Example Interaction:**\n- Insert 10 at front → `Node inserted at front`\n- Display → `10 -> NULL`\n- Count → `Number of nodes: 1`	f	\N	\N	\N	\N	#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node * next;\n};\n\nint main() {\n    struct Node * head = NULL;\n    int choice, data, pos;\n\n    // TODO: Display menu\n    // TODO: Get user choice\n    // TODO: Implement switch case for all operations\n}	\N	\N	\N	\N	#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node * next;\n};\n\nvoid insertFront(struct Node ** head, int data) {\n    struct Node * newNode = (struct Node * ) malloc(sizeof(struct Node));\n    newNode -> data = data;\n    newNode -> next = * head;\n    * head = newNode;\n    printf("Node inserted at front\\n");\n}\n\nvoid display(struct Node * head) {\n    if (head == NULL) {\n        printf("List is empty\\n");\n        return;\n    }\n    struct Node * temp = head;\n    while (temp != NULL) {\n        printf("%d", temp -> data);\n        if (temp -> next != NULL) {\n            printf(" -> \\n\\n");\n        }\n        temp = temp -> next;\n    }\n    printf(" -> NULL\\n");\n}\n\nvoid deleteFirst(struct Node ** head) {\n    if ( * head == NULL) {\n        printf("List is empty\\n");\n        return;\n    }\n    struct Node * temp = * head;\n    * head = ( * head) -> next;\n    free(temp);\n    printf("First node deleted\\n");\n}\n\nvoid insertEnd(struct Node ** head, int data) {\n    struct Node * newNode = (struct Node * ) malloc(sizeof(struct Node));\n    newNode -> data = data;\n    newNode -> next = NULL;\n\n    if ( * head == NULL) {\n        * head = newNode;\n        printf("Node inserted at end\\n");\n        return;\n    }\n\n    struct Node * temp = * head;\n    while (temp -> next != NULL) {\n        temp = temp -> next;\n    }\n    temp -> next = newNode;\n    printf("Node inserted at end\\n");\n}\n\nvoid deleteLast(struct Node ** head) {\n    if ( * head == NULL) {\n        printf("List is empty\\n");\n        return;\n    }\n\n    if (( * head) -> next == NULL) {\n        free( * head);\n        * head = NULL;\n        printf("Last node deleted\\n");\n        return;\n    }\n\n    struct Node * temp = * head;\n    while (temp -> next -> next != NULL) {\n        temp = temp -> next;\n    }\n    free(temp -> next);\n    temp -> next = NULL;\n    printf("Last node deleted\\n");\n}\n\nvoid deletePosition(struct Node ** head, int pos) {\n    if ( * head == NULL) {\n        printf("List is empty\\n");\n        return;\n    }\n\n    if (pos == 1) {\n        deleteFirst(head);\n        return;\n    }\n\n    struct Node * temp = * head;\n    for (int i = 1; i < pos - 1 && temp != NULL; i++) {\n        temp = temp -> next;\n    }\n\n    if (temp == NULL || temp -> next == NULL) {\n        printf("Position not found\\n");\n        return;\n    }\n\n    struct Node * nodeToDelete = temp -> next;\n    temp -> next = nodeToDelete -> next;\n    free(nodeToDelete);\n    printf("Node deleted from position %d\\n", pos);\n}\n\nint countNodes(struct Node * head) {\n    int count = 0;\n    struct Node * temp = head;\n    while (temp != NULL) {\n        count++;\n        temp = temp -> next;\n    }\n    return count;\n}\n\nint main() {\n    struct Node * head = NULL;\n    int choice, data, pos;\n\n    while (1) {\n        scanf("%d", & choice);\n\n        switch (choice) {\n        case 1:\n            scanf("%d", & data);\n            insertFront( & head, data);\n            break;\n        case 2:\n            display(head);\n            break;\n        case 3:\n            deleteFirst( & head);\n            break;\n        case 4:\n            scanf("%d", & data);\n            insertEnd( & head, data);\n            break;\n        case 5:\n            deleteLast( & head);\n            break;\n        case 6:\n            printf("Enter position: \\n");\n            scanf("%d", & pos);\n            deletePosition( & head, pos);\n            break;\n        case 7:\n            printf("Number of nodes: %d", countNodes(head));\n            break;\n        case 8:\n            exit(0);\n        default:\n            printf("Invalid choice");\n        }\n    }\n\n    return 0;\n}	\N	2025-06-25 03:51:34.413+00	2025-06-25 02:19:55.512+00	published	2025-06-25 03:51:35.212+00	2025-06-25 03:51:35.214+00	t
5	2	1	Menu-Driven Singly Linked List	1	\N	Implement a complete menu-driven singly linked list program with insert, delete, display, and count operations.	c	medium	25	2025-07-02 02:19:55.435+00	Write a menu-driven program to implement a singly linked list with multiple operations. The program should display a menu and allow users to perform various operations until they choose to exit.\n\n**Required Operations:**\n1. Insert a node at the front of the linked list\n2. Display all nodes\n3. Delete the first node of the linked list\n4. Insert a node at the end of the linked list\n5. Delete the last node of the linked list\n6. Delete a node from specified position\n7. Count the number of nodes\n8. Exit\n\n**Menu Format:**\n```\n1. Insert at front\n2. Display all nodes\n3. Delete first node\n4. Insert at end\n5. Delete last node\n6. Delete from position\n7. Count nodes\n8. Exit\nEnter your choice:\n```\n\n**Implementation Details:**\n- Use `struct Node` with `int data` and `struct Node* next`\n- Handle empty list cases appropriately\n- For display: print nodes as `data1 -> data2 -> data3 -> NULL`\n- For empty list display: print `List is empty`\n- For position-based deletion, positions start from 1\n- Continue showing menu until user selects exit option\n\n**Example Interaction:**\n- Insert 10 at front → `Node inserted at front`\n- Display → `10 -> NULL`\n- Count → `Number of nodes: 1`	f	\N	\N	\N	\N	#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node * next;\n};\n\n// Function to insert at front\nvoid insertFront(struct Node ** head, int data) {\n    // TODO: Implement insert at front\n}\n\n// Function to display all nodes\nvoid display(struct Node * head) {\n    // TODO: Implement display\n}\n\n// Function to delete first node\nvoid deleteFirst(struct Node ** head) {\n    // TODO: Implement delete first\n}\n\n// Function to insert at end\nvoid insertEnd(struct Node ** head, int data) {\n    // TODO: Implement insert at end\n}\n\n// Function to delete last node\nvoid deleteLast(struct Node ** head) {\n    // TODO: Implement delete last\n}\n\n// Function to delete from position\nvoid deletePosition(struct Node ** head, int pos) {\n    // TODO: Implement delete from position\n}\n\n// Function to count nodes\nint countNodes(struct Node * head) {\n    // TODO: Implement count\n    return 0;\n}\n\nint main() {\n    struct Node * head = NULL;\n    int choice, data, pos;\n\n    while (1) {\n        // TODO: Display menu\n        // TODO: Get user choice\n        // TODO: Implement switch case for all operations\n    }\n}	\N	\N	\N	\N	#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node * next;\n};\n\nvoid insertFront(struct Node ** head, int data) {\n    struct Node * newNode = (struct Node * ) malloc(sizeof(struct Node));\n    newNode -> data = data;\n    newNode -> next = * head;\n    * head = newNode;\n    printf("Node inserted at front\\n");\n}\n\nvoid display(struct Node * head) {\n    if (head == NULL) {\n        printf("List is empty\\n");\n        return;\n    }\n    struct Node * temp = head;\n    while (temp != NULL) {\n        printf("%d", temp -> data);\n        if (temp -> next != NULL) {\n            printf(" -> \\n\\n");\n        }\n        temp = temp -> next;\n    }\n    printf(" -> NULL\\n");\n}\n\nvoid deleteFirst(struct Node ** head) {\n    if ( * head == NULL) {\n        printf("List is empty\\n");\n        return;\n    }\n    struct Node * temp = * head;\n    * head = ( * head) -> next;\n    free(temp);\n    printf("First node deleted\\n");\n}\n\nvoid insertEnd(struct Node ** head, int data) {\n    struct Node * newNode = (struct Node * ) malloc(sizeof(struct Node));\n    newNode -> data = data;\n    newNode -> next = NULL;\n\n    if ( * head == NULL) {\n        * head = newNode;\n        printf("Node inserted at end\\n");\n        return;\n    }\n\n    struct Node * temp = * head;\n    while (temp -> next != NULL) {\n        temp = temp -> next;\n    }\n    temp -> next = newNode;\n    printf("Node inserted at end\\n");\n}\n\nvoid deleteLast(struct Node ** head) {\n    if ( * head == NULL) {\n        printf("List is empty\\n");\n        return;\n    }\n\n    if (( * head) -> next == NULL) {\n        free( * head);\n        * head = NULL;\n        printf("Last node deleted\\n");\n        return;\n    }\n\n    struct Node * temp = * head;\n    while (temp -> next -> next != NULL) {\n        temp = temp -> next;\n    }\n    free(temp -> next);\n    temp -> next = NULL;\n    printf("Last node deleted\\n");\n}\n\nvoid deletePosition(struct Node ** head, int pos) {\n    if ( * head == NULL) {\n        printf("List is empty\\n");\n        return;\n    }\n\n    if (pos == 1) {\n        deleteFirst(head);\n        return;\n    }\n\n    struct Node * temp = * head;\n    for (int i = 1; i < pos - 1 && temp != NULL; i++) {\n        temp = temp -> next;\n    }\n\n    if (temp == NULL || temp -> next == NULL) {\n        printf("Position not found\\n");\n        return;\n    }\n\n    struct Node * nodeToDelete = temp -> next;\n    temp -> next = nodeToDelete -> next;\n    free(nodeToDelete);\n    printf("Node deleted from position %d\\n", pos);\n}\n\nint countNodes(struct Node * head) {\n    int count = 0;\n    struct Node * temp = head;\n    while (temp != NULL) {\n        count++;\n        temp = temp -> next;\n    }\n    return count;\n}\n\nint main() {\n    struct Node * head = NULL;\n    int choice, data, pos;\n\n    while (1) {\n        scanf("%d", & choice);\n\n        switch (choice) {\n        case 1:\n            scanf("%d", & data);\n            insertFront( & head, data);\n            break;\n        case 2:\n            display(head);\n            break;\n        case 3:\n            deleteFirst( & head);\n            break;\n        case 4:\n            scanf("%d", & data);\n            insertEnd( & head, data);\n            break;\n        case 5:\n            deleteLast( & head);\n            break;\n        case 6:\n            printf("Enter position: \\n");\n            scanf("%d", & pos);\n            deletePosition( & head, pos);\n            break;\n        case 7:\n            printf("Number of nodes: %d", countNodes(head));\n            break;\n        case 8:\n            exit(0);\n        default:\n            printf("Invalid choice");\n        }\n    }\n\n    return 0;\n}	\N	2025-06-25 02:31:42.048+00	2025-06-25 02:19:55.512+00	published	2025-06-25 02:31:42.874+00	2025-06-25 02:31:42.877+00	f
11	2	1	Menu-Driven Singly Linked List	1	\N	Implement a complete menu-driven singly linked list program with insert, delete, display, and count operations.	c	medium	25	2025-07-02 02:19:55.435+00	Write a menu-driven program to implement a singly linked list with multiple operations. The program should display a menu and allow users to perform various operations until they choose to exit.\n\n**Required Operations:**\n1. Insert a node at the front of the linked list\n2. Display all nodes\n3. Delete the first node of the linked list\n4. Insert a node at the end of the linked list\n5. Delete the last node of the linked list\n6. Delete a node from specified position\n7. Count the number of nodes\n8. Exit\n\n**Menu Format:**\n```\n1. Insert at front\n2. Display all nodes\n3. Delete first node\n4. Insert at end\n5. Delete last node\n6. Delete from position\n7. Count nodes\n8. Exit\nEnter your choice:\n```\n\n**Implementation Details:**\n- Use `struct Node` with `int data` and `struct Node* next`\n- Handle empty list cases appropriately\n- For display: print nodes as `data1 -> data2 -> data3 -> NULL`\n- For empty list display: print `List is empty`\n- For position-based deletion, positions start from 1\n- Continue showing menu until user selects exit option\n\n**Example Interaction:**\n- Insert 10 at front → `Node inserted at front`\n- Display → `10 -> NULL`\n- Count → `Number of nodes: 1`	f	\N	\N	\N	\N	#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node * next;\n};\n\nint main() {\n    struct Node * head = NULL;\n    int choice, data, pos;\n\n    // TODO: Display menu\n    // TODO: Get user choice\n    // TODO: Implement switch case for all operations\n}	\N	\N	\N	\N	#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node * next;\n};\n\nvoid insertFront(struct Node ** head, int data) {\n    struct Node * newNode = (struct Node * ) malloc(sizeof(struct Node));\n    newNode -> data = data;\n    newNode -> next = * head;\n    * head = newNode;\n    printf("Node inserted at front\\n");\n}\n\nvoid display(struct Node * head) {\n    if (head == NULL) {\n        printf("List is empty\\n");\n        return;\n    }\n    struct Node * temp = head;\n    while (temp != NULL) {\n        printf("%d", temp -> data);\n        if (temp -> next != NULL) {\n            printf(" -> \\n\\n");\n        }\n        temp = temp -> next;\n    }\n    printf(" -> NULL\\n");\n}\n\nvoid deleteFirst(struct Node ** head) {\n    if ( * head == NULL) {\n        printf("List is empty\\n");\n        return;\n    }\n    struct Node * temp = * head;\n    * head = ( * head) -> next;\n    free(temp);\n    printf("First node deleted\\n");\n}\n\nvoid insertEnd(struct Node ** head, int data) {\n    struct Node * newNode = (struct Node * ) malloc(sizeof(struct Node));\n    newNode -> data = data;\n    newNode -> next = NULL;\n\n    if ( * head == NULL) {\n        * head = newNode;\n        printf("Node inserted at end\\n");\n        return;\n    }\n\n    struct Node * temp = * head;\n    while (temp -> next != NULL) {\n        temp = temp -> next;\n    }\n    temp -> next = newNode;\n    printf("Node inserted at end\\n");\n}\n\nvoid deleteLast(struct Node ** head) {\n    if ( * head == NULL) {\n        printf("List is empty\\n");\n        return;\n    }\n\n    if (( * head) -> next == NULL) {\n        free( * head);\n        * head = NULL;\n        printf("Last node deleted\\n");\n        return;\n    }\n\n    struct Node * temp = * head;\n    while (temp -> next -> next != NULL) {\n        temp = temp -> next;\n    }\n    free(temp -> next);\n    temp -> next = NULL;\n    printf("Last node deleted\\n");\n}\n\nvoid deletePosition(struct Node ** head, int pos) {\n    if ( * head == NULL) {\n        printf("List is empty\\n");\n        return;\n    }\n\n    if (pos == 1) {\n        deleteFirst(head);\n        return;\n    }\n\n    struct Node * temp = * head;\n    for (int i = 1; i < pos - 1 && temp != NULL; i++) {\n        temp = temp -> next;\n    }\n\n    if (temp == NULL || temp -> next == NULL) {\n        printf("Position not found\\n");\n        return;\n    }\n\n    struct Node * nodeToDelete = temp -> next;\n    temp -> next = nodeToDelete -> next;\n    free(nodeToDelete);\n    printf("Node deleted from position %d\\n", pos);\n}\n\nint countNodes(struct Node * head) {\n    int count = 0;\n    struct Node * temp = head;\n    while (temp != NULL) {\n        count++;\n        temp = temp -> next;\n    }\n    return count;\n}\n\nint main() {\n    struct Node * head = NULL;\n    int choice, data, pos;\n\n    while (1) {\n        scanf("%d", & choice);\n\n        switch (choice) {\n        case 1:\n            scanf("%d", & data);\n            insertFront( & head, data);\n            break;\n        case 2:\n            display(head);\n            break;\n        case 3:\n            deleteFirst( & head);\n            break;\n        case 4:\n            scanf("%d", & data);\n            insertEnd( & head, data);\n            break;\n        case 5:\n            deleteLast( & head);\n            break;\n        case 6:\n            printf("Enter position: \\n");\n            scanf("%d", & pos);\n            deletePosition( & head, pos);\n            break;\n        case 7:\n            printf("Number of nodes: %d", countNodes(head));\n            break;\n        case 8:\n            exit(0);\n        default:\n            printf("Invalid choice");\n        }\n    }\n\n    return 0;\n}	\N	2025-06-25 02:39:59.284+00	2025-06-25 02:19:55.512+00	published	2025-06-25 02:40:00.015+00	2025-06-25 02:40:00.016+00	f
6	1	1	Singly Linked List Node	1	\N	Write a C program to create a single node for a singly linked list, read its data using scanf, print the data, and free the memory.	c	easy	10	2025-07-02 02:19:53.441+00	Create a program to implement a single node for a singly linked list in C. The node should store an integer and a pointer to the next node. Read the integer using scanf, create the node dynamically, print its data, and free the memory.\n\n**Steps:**\n1. Define a `struct Node` with an integer `data` and a `next` pointer.\n2. Use `scanf` to read an integer from the user.\n3. Allocate memory for a node using `malloc`.\n4. Assign the scanned integer to the node's `data`. Set `next` to NULL.\n5. Print the node's data as `Node data: <value>`.\n6. Free the allocated memory using `free`.\n\n**Notes:**\n- Assume the input is a valid integer.\n- No need to check if `malloc` fails.\n- Use `int` for the node's data.\n\n**Example Inputs/Outputs:**\n- Input: `10` → Output: `Node data: 10`\n- Input: `-5` → Output: `Node data: -5`\n- Input: `0` → Output: `Node data: 0`	f	\N	\N	\N	\N	#include <stdio.h>\n\n// define the structure of Node\n\nvoid main() {\n    // initialize the node\n\n    // take input from user\n\n    // assign value to data member of node\n\n    // assign next member of node to NULL\n\n    // print the data member of node\n}	\N	\N	\N	\N	#include <stdio.h>\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node* next;\n};\n\nvoid main() {\n    struct Node* node = (struct Node*)malloc(sizeof(struct Node));\n\n    int val;\n    scanf("%d", &val);\n\n    node->data = val;\n\n    node->next = NULL;\n\n    printf("%d", node->data);\n    free(node);\n}	\N	2025-06-25 02:34:26.515+00	2025-06-25 02:19:53.518+00	published	2025-06-25 02:34:28.659+00	2025-06-25 02:34:28.66+00	t
2	2	1	Menu-Driven Singly Linked List	1	\N	Implement a complete menu-driven singly linked list program with insert, delete, display, and count operations.	c	medium	25	2025-07-02 02:19:55.435+00	Write a menu-driven program to implement a singly linked list with multiple operations. The program should display a menu and allow users to perform various operations until they choose to exit.\n\n**Required Operations:**\n1. Insert a node at the front of the linked list\n2. Display all nodes\n3. Delete the first node of the linked list\n4. Insert a node at the end of the linked list\n5. Delete the last node of the linked list\n6. Delete a node from specified position\n7. Count the number of nodes\n8. Exit\n\n**Menu Format:**\n```\n1. Insert at front\n2. Display all nodes\n3. Delete first node\n4. Insert at end\n5. Delete last node\n6. Delete from position\n7. Count nodes\n8. Exit\nEnter your choice:\n```\n\n**Implementation Details:**\n- Use `struct Node` with `int data` and `struct Node* next`\n- Handle empty list cases appropriately\n- For display: print nodes as `data1 -> data2 -> data3 -> NULL`\n- For empty list display: print `List is empty`\n- For position-based deletion, positions start from 1\n- Continue showing menu until user selects exit option\n\n**Example Interaction:**\n- Insert 10 at front → `Node inserted at front`\n- Display → `10 -> NULL`\n- Count → `Number of nodes: 1`	f	\N	\N	\N	\N	#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node * next;\n};\n\n// Function to insert at front\nvoid insertFront(struct Node ** head, int data) {\n    // TODO: Implement insert at front\n}\n\n// Function to display all nodes\nvoid display(struct Node * head) {\n    // TODO: Implement display\n}\n\n// Function to delete first node\nvoid deleteFirst(struct Node ** head) {\n    // TODO: Implement delete first\n}\n\n// Function to insert at end\nvoid insertEnd(struct Node ** head, int data) {\n    // TODO: Implement insert at end\n}\n\n// Function to delete last node\nvoid deleteLast(struct Node ** head) {\n    // TODO: Implement delete last\n}\n\n// Function to delete from position\nvoid deletePosition(struct Node ** head, int pos) {\n    // TODO: Implement delete from position\n}\n\n// Function to count nodes\nint countNodes(struct Node * head) {\n    // TODO: Implement count\n    return 0;\n}\n\nint main() {\n    struct Node * head = NULL;\n    int choice, data, pos;\n\n    while (1) {\n        // TODO: Display menu\n        // TODO: Get user choice\n        // TODO: Implement switch case for all operations\n    }\n}	\N	\N	\N	\N	#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node * next;\n};\n\nvoid insertFront(struct Node ** head, int data) {\n    struct Node * newNode = (struct Node * ) malloc(sizeof(struct Node));\n    newNode -> data = data;\n    newNode -> next = * head;\n    * head = newNode;\n    printf("Node inserted at front\n");\n}\n\nvoid display(struct Node * head) {\n    if (head == NULL) {\n        printf("List is empty");\n        return;\n    }\n    struct Node * temp = head;\n    while (temp != NULL) {\n        printf("%d", temp -> data);\n        if (temp -> next != NULL) {\n            printf(" -> ");\n        }\n        temp = temp -> next;\n    }\n    printf(" -> NULL\n");\n}\n\nvoid deleteFirst(struct Node ** head) {\n    if ( * head == NULL) {\n        printf("List is empty");\n        return;\n    }\n    struct Node * temp = * head;\n    * head = ( * head) -> next;\n    free(temp);\n    printf("First node deleted\n");\n}\n\nvoid insertEnd(struct Node ** head, int data) {\n    struct Node * newNode = (struct Node * ) malloc(sizeof(struct Node));\n    newNode -> data = data;\n    newNode -> next = NULL;\n\n    if ( * head == NULL) {\n        * head = newNode;\n        printf("Node inserted at end\n");\n        return;\n    }\n\n    struct Node * temp = * head;\n    while (temp -> next != NULL) {\n        temp = temp -> next;\n    }\n    temp -> next = newNode;\n    printf("Node inserted at end\n");\n}\n\nvoid deleteLast(struct Node ** head) {\n    if ( * head == NULL) {\n        printf("List is empty\n");\n        return;\n    }\n\n    if (( * head) -> next == NULL) {\n        free( * head);\n        * head = NULL;\n        printf("Last node deleted\n");\n        return;\n    }\n\n    struct Node * temp = * head;\n    while (temp -> next -> next != NULL) {\n        temp = temp -> next;\n    }\n    free(temp -> next);\n    temp -> next = NULL;\n    printf("Last node deleted\n");\n}\n\nvoid deletePosition(struct Node ** head, int pos) {\n    if ( * head == NULL) {\n        printf("List is empty\n");\n        return;\n    }\n\n    if (pos == 1) {\n        deleteFirst(head);\n        return;\n    }\n\n    struct Node * temp = * head;\n    for (int i = 1; i < pos - 1 && temp != NULL; i++) {\n        temp = temp -> next;\n    }\n\n    if (temp == NULL || temp -> next == NULL) {\n        printf("Position not found\n");\n        return;\n    }\n\n    struct Node * nodeToDelete = temp -> next;\n    temp -> next = nodeToDelete -> next;\n    free(nodeToDelete);\n    printf("Node deleted from position %d\n", pos);\n}\n\nint countNodes(struct Node * head) {\n    int count = 0;\n    struct Node * temp = head;\n    while (temp != NULL) {\n        count++;\n        temp = temp -> next;\n    }\n    return count;\n}\n\nint main() {\n    struct Node * head = NULL;\n    int choice, data, pos;\n\n    while (1) {\n        scanf("%d", & choice);\n\n        switch (choice) {\n        case 1:\n            scanf("%d", & data);\n            insertFront( & head, data);\n            break;\n        case 2:\n            display(head);\n            break;\n        case 3:\n            deleteFirst( & head);\n            break;\n        case 4:\n            scanf("%d", & data);\n            insertEnd( & head, data);\n            break;\n        case 5:\n            deleteLast( & head);\n            break;\n        case 6:\n            printf("Enter position: ");\n            scanf("%d", & pos);\n            deletePosition( & head, pos);\n            break;\n        case 7:\n            printf("Number of nodes: %d", countNodes(head));\n            break;\n        case 8:\n            exit(0);\n        default:\n            printf("Invalid choice");\n        }\n    }\n\n    return 0;\n}	\N	2025-06-25 02:19:55.597+00	2025-06-25 02:19:55.512+00	draft	2025-06-25 02:19:56.053+00	2025-06-25 02:19:56.054+00	f
9	4	1	Remove Duplicates from Sorted List	1	\N	Write a C program to remove duplicate nodes from a given sorted singly linked list.	c	hard	30	2025-07-02 02:19:58.323+00	Write a C program that removes duplicate elements from a sorted singly linked list. The list should be modified in-place, and the remaining elements should still be sorted.\n\n**Input Format:**\nThe program will read a sequence of sorted integers from standard input to build the linked list. A sentinel value of `-1` will signify the end of the input sequence.\n\n**Steps:**\n1.  Define the `struct Node` with an integer `data` and a pointer `next`.\n2.  Implement a helper function (e.g., `insertEnd`) to build the linked list from the input integers.\n3.  Implement the core function, `removeDuplicates(struct Node* head)`.\n4.  In this function, traverse the list with a pointer, let's call it `current`. The traversal should continue as long as `current` and `current->next` are not NULL.\n5.  Inside the loop, compare the data of the `current` node with the data of the `current->next` node.\n6.  If the data is the same, it's a duplicate. You need to bypass this duplicate node:\n    a. Create a temporary pointer to `current->next` (the node to be deleted).\n    b. Update `current->next` to point to the node *after* the duplicate (`temp->next`).\n    c. Free the memory of the temporary pointer.\n7.  **Important:** If you delete a node, do *not* advance the `current` pointer in that iteration. This allows you to check for multiple duplicates (e.g., 13 → 13 → 13).\n8.  If the data is different, it means there's no duplicate, so you can safely move to the next node by setting `current = current->next`.\n9.  Implement a `display` function to print the final list in the format `data1 -> data2 -> NULL`.\n\n**Example:**\n-   Input: `1 1 6 13 13 13 27 27 -1`\n-   Output: `1 -> 6 -> 13 -> 27 -> NULL`	f	\N	\N	\N	\N	#include <stdio.h>\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node* next;\n};\n\n\nint main() {\n    struct Node* head = NULL;\n    int data;\n\n    // remove duplicates\n\n    // display result\n\n    return 0;\n}	\N	\N	\N	\N	#include <stdio.h>\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node* next;\n};\n\nvoid insertEnd(struct Node** head_ref, int new_data) {\n    struct Node* new_node = (struct Node*)malloc(sizeof(struct Node));\n    new_node->data = new_data;\n    new_node->next = NULL;\n\n    if (*head_ref == NULL) {\n        *head_ref = new_node;\n        return;\n    }\n    struct Node* last = *head_ref;\n    while (last->next != NULL) {\n        last = last->next;\n    }\n    last->next = new_node;\n}\n\nvoid display(struct Node* node) {\n    if (node == NULL) {\n        printf("List is empty");\n        return;\n    }\n    while (node != NULL) {\n        printf("%d", node->data);\n        if (node->next != NULL) {\n            printf(" -> ");\n        }\n        node = node->next;\n    }\n    printf(" -> NULL");\n}\n\nvoid removeDuplicates(struct Node* head) {\n    struct Node* current = head;\n    if (current == NULL) return;\n\n    while (current->next != NULL) {\n        if (current->data == current->next->data) {\n            struct Node* temp = current->next;\n            current->next = temp->next;\n            free(temp);\n        } else {\n            current = current->next;\n        }\n    }\n}\n\nint main() {\n    struct Node* head = NULL;\n    int data;\n\n    while (scanf("%d", &data) == 1 && data != -1) {\n        insertEnd(&head, data);\n    }\n\n    removeDuplicates(head);\n\n    display(head);\n\n    return 0;\n}	\N	2025-06-25 02:37:45.568+00	2025-06-25 02:19:58.399+00	published	2025-06-25 02:37:46.3+00	2025-06-25 02:37:46.302+00	t
7	4	1	Remove Duplicates from Sorted List	1	\N	Write a C program to remove duplicate nodes from a given sorted singly linked list.	c	hard	30	2025-07-02 02:19:58.323+00	Write a C program that removes duplicate elements from a sorted singly linked list. The list should be modified in-place, and the remaining elements should still be sorted.\n\n**Input Format:**\nThe program will read a sequence of sorted integers from standard input to build the linked list. A sentinel value of `-1` will signify the end of the input sequence.\n\n**Steps:**\n1.  Define the `struct Node` with an integer `data` and a pointer `next`.\n2.  Implement a helper function (e.g., `insertEnd`) to build the linked list from the input integers.\n3.  Implement the core function, `removeDuplicates(struct Node* head)`.\n4.  In this function, traverse the list with a pointer, let's call it `current`. The traversal should continue as long as `current` and `current->next` are not NULL.\n5.  Inside the loop, compare the data of the `current` node with the data of the `current->next` node.\n6.  If the data is the same, it's a duplicate. You need to bypass this duplicate node:\n    a. Create a temporary pointer to `current->next` (the node to be deleted).\n    b. Update `current->next` to point to the node *after* the duplicate (`temp->next`).\n    c. Free the memory of the temporary pointer.\n7.  **Important:** If you delete a node, do *not* advance the `current` pointer in that iteration. This allows you to check for multiple duplicates (e.g., 13 → 13 → 13).\n8.  If the data is different, it means there's no duplicate, so you can safely move to the next node by setting `current = current->next`.\n9.  Implement a `display` function to print the final list in the format `data1 -> data2 -> NULL`.\n\n**Example:**\n-   Input: `1 1 6 13 13 13 27 27 -1`\n-   Output: `1 -> 6 -> 13 -> 27 -> NULL`	f	\N	\N	\N	\N	#include <stdio.h>\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node* next;\n};\n\n// Function to insert a new node at the end of a list\nvoid insertEnd(struct Node** head_ref, int new_data) {\n    struct Node* new_node = (struct Node*)malloc(sizeof(struct Node));\n    new_node->data = new_data;\n    new_node->next = NULL;\n\n    if (*head_ref == NULL) {\n        *head_ref = new_node;\n        return;\n    }\n    struct Node* last = *head_ref;\n    while (last->next != NULL) {\n        last = last->next;\n    }\n    last->next = new_node;\n}\n\n// Function to print the linked list\nvoid display(struct Node* node) {\n    if (node == NULL) {\n        printf("List is empty");\n        return;\n    }\n    while (node != NULL) {\n        printf("%d", node->data);\n        if (node->next != NULL) {\n            printf(" -> ");\n        }\n        node = node->next;\n    }\n     printf(" -> NULL");\n}\n\n// Function to remove duplicate nodes from a sorted list\nvoid removeDuplicates(struct Node* head) {\n    // TODO: Implement the logic to remove duplicates\n}\n\nint main() {\n    struct Node* head = NULL;\n    int data;\n\n    while (scanf("%d", &data) == 1 && data != -1) {\n        insertEnd(&head, data);\n    }\n\n    removeDuplicates(head);\n\n    display(head);\n\n    return 0;\n}	\N	\N	\N	\N	#include <stdio.h>\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node* next;\n};\n\nvoid insertEnd(struct Node** head_ref, int new_data) {\n    struct Node* new_node = (struct Node*)malloc(sizeof(struct Node));\n    new_node->data = new_data;\n    new_node->next = NULL;\n\n    if (*head_ref == NULL) {\n        *head_ref = new_node;\n        return;\n    }\n    struct Node* last = *head_ref;\n    while (last->next != NULL) {\n        last = last->next;\n    }\n    last->next = new_node;\n}\n\nvoid display(struct Node* node) {\n    if (node == NULL) {\n        printf("List is empty");\n        return;\n    }\n    while (node != NULL) {\n        printf("%d", node->data);\n        if (node->next != NULL) {\n            printf(" -> ");\n        }\n        node = node->next;\n    }\n    printf(" -> NULL");\n}\n\nvoid removeDuplicates(struct Node* head) {\n    struct Node* current = head;\n    if (current == NULL) return;\n\n    while (current->next != NULL) {\n        if (current->data == current->next->data) {\n            struct Node* temp = current->next;\n            current->next = temp->next;\n            free(temp);\n        } else {\n            current = current->next;\n        }\n    }\n}\n\nint main() {\n    struct Node* head = NULL;\n    int data;\n\n    while (scanf("%d", &data) == 1 && data != -1) {\n        insertEnd(&head, data);\n    }\n\n    removeDuplicates(head);\n\n    display(head);\n\n    return 0;\n}	\N	2025-06-25 02:34:26.517+00	2025-06-25 02:19:58.399+00	published	2025-06-25 02:34:28.735+00	2025-06-25 02:34:28.736+00	f
8	3	1	Compare Two Singly Linked Lists	1	\N	Write a C program that takes two singly linked lists as input and determines if they are identical in structure and content.	c	medium	15	2025-07-02 02:19:56.882+00	Write a C program to compare two singly linked lists and determine if they are identical. Two lists are considered identical if they have the same number of nodes, and the data in corresponding nodes is the same.\n\n**Input Format:**\nThe program will read integers from standard input to build two lists. A sentinel value of `-1` will be used to signify the end of input for each list.\nExample: `10 20 30 -1 10 20 30 -1` would create two lists: (10 -> 20 -> 30) and (10 -> 20 -> 30).\n\n**Steps:**\n1. Define the `struct Node` with an integer `data` and a pointer `next`.\n2. Create a helper function (e.g., `insertEnd`) to add nodes to the end of a list. This will simplify your main function.\n3. In `main`, create two head pointers, `head1` and `head2`, initialized to NULL.\n4. Read integers in a loop for the first list, calling your insert function for each one, until `-1` is read.\n5. Do the same for the second list.\n6. Implement a comparison function, e.g., `areSame(struct Node* a, struct Node* b)`, that returns 1 if the lists are identical and 0 otherwise.\n7. The comparison logic should traverse both lists simultaneously. If a data mismatch is found, or if one list is longer than the other, they are not the same.\n8. Based on the return value of your comparison function, print either `"Lists are same"` or `"Lists are not same"`.\n\n**Example Scenarios:**\n- Input: `10 20 -1 10 20 -1` → Output: `Lists are same`\n- Input: `10 20 -1 10 20 30 -1` → Output: `Lists are not same`\n- Input: `5 15 -1 5 25 -1` → Output: `Lists are not same`	f	\N	\N	\N	\N	#include <stdio.h>\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node* next;\n};\n\n// Helper function to insert a new node at the end of a list\nvoid insertEnd(struct Node** head_ref, int new_data) {\n    // TODO: Implement insertion logic\n}\n\n// Function to compare two linked lists\n// Should return 1 if same, 0 otherwise\nint areSame(struct Node* a, struct Node* b) {\n    // TODO: Implement comparison logic\n    return 0;\n}\n\n\nint main() {\n    struct Node* head1 = NULL;\n    struct Node* head2 = NULL;\n    int data;\n\n    // Read first list\n    // printf("Enter elements for the first list (-1 to end):\\n");\n    while (scanf("%d", &data) == 1 && data != -1) {\n        insertEnd(&head1, data);\n    }\n\n    // Read second list\n    // printf("Enter elements for the second list (-1 to end):\\n");\n    while (scanf("%d", &data) == 1 && data != -1) {\n        insertEnd(&head2, data);\n    }\n\n    if (areSame(head1, head2)) {\n        printf("Lists are same");\n    } else {\n        printf("Lists are not same");\n    }\n\n    return 0;\n}	\N	\N	\N	\N	#include <stdio.h>\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node* next;\n};\n\nvoid insertEnd(struct Node** head_ref, int new_data) {\n    struct Node* new_node = (struct Node*)malloc(sizeof(struct Node));\n    struct Node* last = *head_ref;\n    new_node->data = new_data;\n    new_node->next = NULL;\n    if (*head_ref == NULL) {\n        *head_ref = new_node;\n        return;\n    }\n    while (last->next != NULL) {\n        last = last->next;\n    }\n    last->next = new_node;\n}\n\nint areSame(struct Node* a, struct Node* b) {\n    while (a != NULL && b != NULL) {\n        if (a->data != b->data) {\n            return 0; // Data is different\n        }\n        a = a->next;\n        b = b->next;\n    }\n    // If both are NULL, lists are same length and content\n    return (a == NULL && b == NULL);\n}\n\nint main() {\n    struct Node* head1 = NULL;\n    struct Node* head2 = NULL;\n    int data;\n\n    while (scanf("%d", &data) == 1 && data != -1) {\n        insertEnd(&head1, data);\n    }\n\n    while (scanf("%d", &data) == 1 && data != -1) {\n        insertEnd(&head2, data);\n    }\n\n    if (areSame(head1, head2)) {\n        printf("Lists are same");\n    } else {\n        printf("Lists are not same");\n    }\n\n    return 0;\n}	\N	2025-06-25 02:34:26.518+00	2025-06-25 02:19:56.959+00	published	2025-06-25 02:34:28.81+00	2025-06-25 02:34:28.811+00	f
4	4	1	Remove Duplicates from Sorted List	1	\N	Write a C program to remove duplicate nodes from a given sorted singly linked list.	c	hard	30	2025-07-02 02:19:58.323+00	Write a C program that removes duplicate elements from a sorted singly linked list. The list should be modified in-place, and the remaining elements should still be sorted.\n\n**Input Format:**\nThe program will read a sequence of sorted integers from standard input to build the linked list. A sentinel value of `-1` will signify the end of the input sequence.\n\n**Steps:**\n1.  Define the `struct Node` with an integer `data` and a pointer `next`.\n2.  Implement a helper function (e.g., `insertEnd`) to build the linked list from the input integers.\n3.  Implement the core function, `removeDuplicates(struct Node* head)`.\n4.  In this function, traverse the list with a pointer, let's call it `current`. The traversal should continue as long as `current` and `current->next` are not NULL.\n5.  Inside the loop, compare the data of the `current` node with the data of the `current->next` node.\n6.  If the data is the same, it's a duplicate. You need to bypass this duplicate node:\n    a. Create a temporary pointer to `current->next` (the node to be deleted).\n    b. Update `current->next` to point to the node *after* the duplicate (`temp->next`).\n    c. Free the memory of the temporary pointer.\n7.  **Important:** If you delete a node, do *not* advance the `current` pointer in that iteration. This allows you to check for multiple duplicates (e.g., 13 → 13 → 13).\n8.  If the data is different, it means there's no duplicate, so you can safely move to the next node by setting `current = current->next`.\n9.  Implement a `display` function to print the final list in the format `data1 -> data2 -> NULL`.\n\n**Example:**\n-   Input: `1 1 6 13 13 13 27 27 -1`\n-   Output: `1 -> 6 -> 13 -> 27 -> NULL`	f	\N	\N	\N	\N	#include <stdio.h>\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node* next;\n};\n\n// Function to insert a new node at the end of a list\nvoid insertEnd(struct Node** head_ref, int new_data) {\n    struct Node* new_node = (struct Node*)malloc(sizeof(struct Node));\n    new_node->data = new_data;\n    new_node->next = NULL;\n\n    if (*head_ref == NULL) {\n        *head_ref = new_node;\n        return;\n    }\n    struct Node* last = *head_ref;\n    while (last->next != NULL) {\n        last = last->next;\n    }\n    last->next = new_node;\n}\n\n// Function to print the linked list\nvoid display(struct Node* node) {\n    if (node == NULL) {\n        printf("List is empty");\n        return;\n    }\n    while (node != NULL) {\n        printf("%d", node->data);\n        if (node->next != NULL) {\n            printf(" -> ");\n        }\n        node = node->next;\n    }\n     printf(" -> NULL");\n}\n\n// Function to remove duplicate nodes from a sorted list\nvoid removeDuplicates(struct Node* head) {\n    // TODO: Implement the logic to remove duplicates\n}\n\nint main() {\n    struct Node* head = NULL;\n    int data;\n\n    while (scanf("%d", &data) == 1 && data != -1) {\n        insertEnd(&head, data);\n    }\n\n    removeDuplicates(head);\n\n    display(head);\n\n    return 0;\n}	\N	\N	\N	\N	#include <stdio.h>\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node* next;\n};\n\nvoid insertEnd(struct Node** head_ref, int new_data) {\n    struct Node* new_node = (struct Node*)malloc(sizeof(struct Node));\n    new_node->data = new_data;\n    new_node->next = NULL;\n\n    if (*head_ref == NULL) {\n        *head_ref = new_node;\n        return;\n    }\n    struct Node* last = *head_ref;\n    while (last->next != NULL) {\n        last = last->next;\n    }\n    last->next = new_node;\n}\n\nvoid display(struct Node* node) {\n    if (node == NULL) {\n        printf("List is empty");\n        return;\n    }\n    while (node != NULL) {\n        printf("%d", node->data);\n        if (node->next != NULL) {\n            printf(" -> ");\n        }\n        node = node->next;\n    }\n    printf(" -> NULL");\n}\n\nvoid removeDuplicates(struct Node* head) {\n    struct Node* current = head;\n    if (current == NULL) return;\n\n    while (current->next != NULL) {\n        if (current->data == current->next->data) {\n            struct Node* temp = current->next;\n            current->next = temp->next;\n            free(temp);\n        } else {\n            current = current->next;\n        }\n    }\n}\n\nint main() {\n    struct Node* head = NULL;\n    int data;\n\n    while (scanf("%d", &data) == 1 && data != -1) {\n        insertEnd(&head, data);\n    }\n\n    removeDuplicates(head);\n\n    display(head);\n\n    return 0;\n}	\N	2025-06-25 02:19:58.482+00	2025-06-25 02:19:58.399+00	draft	2025-06-25 02:19:58.868+00	2025-06-25 02:19:58.869+00	f
10	3	1	Compare Two Singly Linked Lists	1	\N	Write a C program that takes two singly linked lists as input and determines if they are identical in structure and content.	c	medium	15	2025-07-02 02:19:56.882+00	Write a C program to compare two singly linked lists and determine if they are identical. Two lists are considered identical if they have the same number of nodes, and the data in corresponding nodes is the same.\n\n**Input Format:**\nThe program will read integers from standard input to build two lists. A sentinel value of `-1` will be used to signify the end of input for each list.\nExample: `10 20 30 -1 10 20 30 -1` would create two lists: (10 -> 20 -> 30) and (10 -> 20 -> 30).\n\n**Steps:**\n1. Define the `struct Node` with an integer `data` and a pointer `next`.\n2. Create a helper function (e.g., `insertEnd`) to add nodes to the end of a list. This will simplify your main function.\n3. In `main`, create two head pointers, `head1` and `head2`, initialized to NULL.\n4. Read integers in a loop for the first list, calling your insert function for each one, until `-1` is read.\n5. Do the same for the second list.\n6. Implement a comparison function, e.g., `areSame(struct Node* a, struct Node* b)`, that returns 1 if the lists are identical and 0 otherwise.\n7. The comparison logic should traverse both lists simultaneously. If a data mismatch is found, or if one list is longer than the other, they are not the same.\n8. Based on the return value of your comparison function, print either `"Lists are same"` or `"Lists are not same"`.\n\n**Example Scenarios:**\n- Input: `10 20 -1 10 20 -1` → Output: `Lists are same`\n- Input: `10 20 -1 10 20 30 -1` → Output: `Lists are not same`\n- Input: `5 15 -1 5 25 -1` → Output: `Lists are not same`	f	\N	\N	\N	\N	#include <stdio.h>\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node* next;\n};\n\n\nint main() {\n    struct Node* head1 = NULL;\n    struct Node* head2 = NULL;\n    int data;\n\n    // Read first list\n\n    // Read second list\n\n    // show result\n\n    return 0;\n}	\N	\N	\N	\N	#include <stdio.h>\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node* next;\n};\n\nvoid insertEnd(struct Node** head_ref, int new_data) {\n    struct Node* new_node = (struct Node*)malloc(sizeof(struct Node));\n    struct Node* last = *head_ref;\n    new_node->data = new_data;\n    new_node->next = NULL;\n    if (*head_ref == NULL) {\n        *head_ref = new_node;\n        return;\n    }\n    while (last->next != NULL) {\n        last = last->next;\n    }\n    last->next = new_node;\n}\n\nint areSame(struct Node* a, struct Node* b) {\n    while (a != NULL && b != NULL) {\n        if (a->data != b->data) {\n            return 0; // Data is different\n        }\n        a = a->next;\n        b = b->next;\n    }\n    // If both are NULL, lists are same length and content\n    return (a == NULL && b == NULL);\n}\n\nint main() {\n    struct Node* head1 = NULL;\n    struct Node* head2 = NULL;\n    int data;\n\n    while (scanf("%d", &data) == 1 && data != -1) {\n        insertEnd(&head1, data);\n    }\n\n    while (scanf("%d", &data) == 1 && data != -1) {\n        insertEnd(&head2, data);\n    }\n\n    if (areSame(head1, head2)) {\n        printf("Lists are same");\n    } else {\n        printf("Lists are not same");\n    }\n\n    return 0;\n}	\N	2025-06-25 02:38:48.865+00	2025-06-25 02:19:56.959+00	published	2025-06-25 02:38:49.631+00	2025-06-25 02:38:49.633+00	t
3	3	1	Compare Two Singly Linked Lists	1	\N	Write a C program that takes two singly linked lists as input and determines if they are identical in structure and content.	c	medium	15	2025-07-02 02:19:56.882+00	Write a C program to compare two singly linked lists and determine if they are identical. Two lists are considered identical if they have the same number of nodes, and the data in corresponding nodes is the same.\n\n**Input Format:**\nThe program will read integers from standard input to build two lists. A sentinel value of `-1` will be used to signify the end of input for each list.\nExample: `10 20 30 -1 10 20 30 -1` would create two lists: (10 -> 20 -> 30) and (10 -> 20 -> 30).\n\n**Steps:**\n1. Define the `struct Node` with an integer `data` and a pointer `next`.\n2. Create a helper function (e.g., `insertEnd`) to add nodes to the end of a list. This will simplify your main function.\n3. In `main`, create two head pointers, `head1` and `head2`, initialized to NULL.\n4. Read integers in a loop for the first list, calling your insert function for each one, until `-1` is read.\n5. Do the same for the second list.\n6. Implement a comparison function, e.g., `areSame(struct Node* a, struct Node* b)`, that returns 1 if the lists are identical and 0 otherwise.\n7. The comparison logic should traverse both lists simultaneously. If a data mismatch is found, or if one list is longer than the other, they are not the same.\n8. Based on the return value of your comparison function, print either `"Lists are same"` or `"Lists are not same"`.\n\n**Example Scenarios:**\n- Input: `10 20 -1 10 20 -1` → Output: `Lists are same`\n- Input: `10 20 -1 10 20 30 -1` → Output: `Lists are not same`\n- Input: `5 15 -1 5 25 -1` → Output: `Lists are not same`	f	\N	\N	\N	\N	#include <stdio.h>\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node* next;\n};\n\n// Helper function to insert a new node at the end of a list\nvoid insertEnd(struct Node** head_ref, int new_data) {\n    // TODO: Implement insertion logic\n}\n\n// Function to compare two linked lists\n// Should return 1 if same, 0 otherwise\nint areSame(struct Node* a, struct Node* b) {\n    // TODO: Implement comparison logic\n    return 0;\n}\n\n\nint main() {\n    struct Node* head1 = NULL;\n    struct Node* head2 = NULL;\n    int data;\n\n    // Read first list\n    // printf("Enter elements for the first list (-1 to end):\\n");\n    while (scanf("%d", &data) == 1 && data != -1) {\n        insertEnd(&head1, data);\n    }\n\n    // Read second list\n    // printf("Enter elements for the second list (-1 to end):\\n");\n    while (scanf("%d", &data) == 1 && data != -1) {\n        insertEnd(&head2, data);\n    }\n\n    if (areSame(head1, head2)) {\n        printf("Lists are same");\n    } else {\n        printf("Lists are not same");\n    }\n\n    return 0;\n}	\N	\N	\N	\N	#include <stdio.h>\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node* next;\n};\n\nvoid insertEnd(struct Node** head_ref, int new_data) {\n    struct Node* new_node = (struct Node*)malloc(sizeof(struct Node));\n    struct Node* last = *head_ref;\n    new_node->data = new_data;\n    new_node->next = NULL;\n    if (*head_ref == NULL) {\n        *head_ref = new_node;\n        return;\n    }\n    while (last->next != NULL) {\n        last = last->next;\n    }\n    last->next = new_node;\n}\n\nint areSame(struct Node* a, struct Node* b) {\n    while (a != NULL && b != NULL) {\n        if (a->data != b->data) {\n            return 0; // Data is different\n        }\n        a = a->next;\n        b = b->next;\n    }\n    // If both are NULL, lists are same length and content\n    return (a == NULL && b == NULL);\n}\n\nint main() {\n    struct Node* head1 = NULL;\n    struct Node* head2 = NULL;\n    int data;\n\n    while (scanf("%d", &data) == 1 && data != -1) {\n        insertEnd(&head1, data);\n    }\n\n    while (scanf("%d", &data) == 1 && data != -1) {\n        insertEnd(&head2, data);\n    }\n\n    if (areSame(head1, head2)) {\n        printf("Lists are same");\n    } else {\n        printf("Lists are not same");\n    }\n\n    return 0;\n}	\N	2025-06-25 02:19:57.038+00	2025-06-25 02:19:56.959+00	draft	2025-06-25 02:19:57.42+00	2025-06-25 02:19:57.421+00	f
24	5	1	Create a UDF to add two numbers 	\N	\N	\N	c	easy	10	2025-06-25 06:00:00+00		\N	\N	\N	\N	\N	// include the standard input output lib\r\n\r\nvoid main() {\r\n  // declare the variables\r\n\r\n  // read the values from user\r\n\r\n  // calculate the sum\r\n\r\n  // print the sum\r\n}	\N	\N	\N	\N	void main(){\r\n\r\n}	\N	2025-06-26 12:57:22.951+00	2025-06-25 03:20:35.818+00	published	2025-06-26 12:57:23.027+00	2025-06-26 12:57:23.027+00	t
23	5	1	Create a UDF to add two numbers 	\N	\N	\N	c	easy	10	2025-06-25 06:00:00+00		\N	\N	\N	\N	\N	void main(){\r\n\r\n}	\N	\N	\N	\N	void main(){\r\n\r\n}	\N	2025-06-26 12:54:58.154+00	2025-06-25 03:20:35.818+00	published	2025-06-26 12:54:58.166+00	2025-06-26 12:54:58.166+00	f
18	5	1	Create a UDF to add two numbers 	\N	\N	\N	c	easy	10	2025-06-25 06:00:00+00		\N	\N	\N	\N	\N	void main(){\r\n\r\n}	\N	\N	\N	\N	void main(){\r\n\r\n}	\N	2025-06-25 03:31:53.858+00	2025-06-25 03:20:35.818+00	published	2025-06-25 03:31:54.5+00	2025-06-25 03:31:54.501+00	f
12	5	1	Create a UDF to add two numbers 	\N	\N	\N	c	easy	10	2025-06-25 06:00:00+00		\N	\N	\N	\N	\N	void main(){\r\n\r\n}	\N	\N	\N	\N	\N	\N	2025-06-25 03:20:35.834+00	2025-06-25 03:20:35.818+00	draft	2025-06-25 03:20:35.988+00	2025-06-25 03:20:35.989+00	f
13	5	1	Create a UDF to add two numbers 	\N	\N	\N	c	easy	10	2025-06-25 06:00:00+00		\N	\N	\N	\N	\N	void main(){\r\n\r\n}	\N	\N	\N	\N	\N	\N	2025-06-25 03:20:44.55+00	2025-06-25 03:20:35.818+00	published	2025-06-25 03:20:45.061+00	2025-06-25 03:20:45.062+00	f
14	5	1	Create a UDF to add two numbers 	\N	\N	\N	c	easy	0	2025-06-25 06:00:00+00		\N	\N	\N	\N	\N	void main(){\r\n\r\n}	\N	\N	\N	\N	\N	\N	2025-06-25 03:20:54.707+00	2025-06-25 03:20:35.818+00	published	2025-06-25 03:20:55.213+00	2025-06-25 03:20:55.213+00	f
16	5	1	Create a UDF to add two numbers 	\N	\N	\N	c	easy	10	2025-06-25 06:00:00+00		\N	\N	\N	\N	\N	void main(){\r\n\r\n}	\N	\N	\N	\N	void main(){\r\n\r\n}	\N	2025-06-25 03:21:41.681+00	2025-06-25 03:20:35.818+00	published	2025-06-25 03:21:42.24+00	2025-06-25 03:21:42.241+00	f
15	5	1	Create a UDF to add two numbers 	\N	\N	\N	c	easy	10	2025-06-25 06:00:00+00		\N	\N	\N	\N	\N	void main(){\r\n\r\n}	\N	\N	\N	\N	\N	\N	2025-06-25 03:21:01.044+00	2025-06-25 03:20:35.818+00	published	2025-06-25 03:21:01.55+00	2025-06-25 03:21:01.551+00	f
\.


--
-- Data for Name: _assignments_v_blocks_action; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public._assignments_v_blocks_action (_order, _parent_id, _path, id, action_type, action_selector, action_value, _uuid, block_name) FROM stdin;
\.


--
-- Data for Name: _assignments_v_blocks_assertion; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public._assignments_v_blocks_assertion (_order, _parent_id, _path, id, assertion_type, assertion_selector, expected_value, expected_class, css_property, expected_css_value, expected_alert_text, _uuid, block_name) FROM stdin;
\.


--
-- Data for Name: _assignments_v_version_c_test_cases; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public._assignments_v_version_c_test_cases (_order, _parent_id, id, title, input, expected_output, tolerance, is_hidden, _uuid) FROM stdin;
1	1	1	Positive Integer	10	10	0.001	f	685b5cc9e1277af85f2050c7
2	1	2	Negative Integer	-5	-5	0.001	f	685b5cc9e1277af85f2050c8
3	1	3	Zero	0	0	0.001	f	685b5cc9e1277af85f2050c9
4	1	4	Large Positive Integer	1000	1000	0.001	f	685b5cc9e1277af85f2050ca
5	1	5	Large Negative Integer	-1000	-1000	0.001	f	685b5cc9e1277af85f2050cb
1	2	6	Insert Front and Delete First	1\n10\n3\n2\n8	Node inserted at front\nFirst node deleted\nList is empty	0.001	f	685b5ccbe1277af85f2050d2
2	2	7	Insert at End and Display	4\n10\n2\n8	Node inserted at end\n10 -> NULL	0.001	f	685b5ccbe1277af85f2050d3
3	2	8	Display Empty List	2\n8	List is empty	0.001	f	685b5ccbe1277af85f2050d4
4	2	9	Count Empty List	7\n8	Number of nodes: 0	0.001	f	685b5ccbe1277af85f2050d5
5	2	10	Multiple Insert Front and Count	1\n10\n1\n20\n2\n7\n8	Node inserted at front\nNode inserted at front\n20 -> 10 -> NULL\nNumber of nodes: 2	0.001	f	685b5ccbe1277af85f2050d6
6	2	11	Multiple Insert End	4\n10\n4\n20\n2\n8	Node inserted at end\nNode inserted at end\n10 -> 20 -> NULL	0.001	f	685b5ccbe1277af85f2050d7
7	2	12	Delete First from Empty List	3\n8	List is empty	0.001	f	685b5ccbe1277af85f2050d8
8	2	13	Delete Last from Empty List	5\n8	List is empty	0.001	f	685b5ccbe1277af85f2050d9
9	2	14	Delete from Position	1\n10\n1\n20\n1\n30\n2\n6\n2\n2\n8	Node inserted at front\nNode inserted at front\nNode inserted at front\n30 -> 20 -> 10 -> NULL\nEnter position:\nNode deleted from position 2\n30 -> 10 -> NULL	0.001	f	685b5ccbe1277af85f2050da
10	2	15	Insert End and Delete Last Single Node	4\n10\n5\n2\n8	Node inserted at end\nLast node deleted\nList is empty	0.001	f	685b5ccbe1277af85f2050db
1	3	16	Identical Lists	10 20 30 -1 10 20 30 -1	Lists are same	0.001	f	685b5ccce1277af85f2050e1
2	3	17	Different Data	10 20 30 -1 10 99 30 -1	Lists are not same	0.001	f	685b5ccce1277af85f2050e2
3	3	18	Different Length (First Shorter)	10 20 -1 10 20 30 -1	Lists are not same	0.001	f	685b5ccce1277af85f2050e3
4	3	19	Different Length (Second Shorter)	10 20 30 -1 10 20 -1	Lists are not same	0.001	f	685b5ccce1277af85f2050e4
5	3	20	Both Lists Empty	-1 -1	Lists are same	0.001	f	685b5ccce1277af85f2050e5
6	3	21	One List Empty	10 -1 -1	Lists are not same	0.001	f	685b5ccce1277af85f2050e6
7	3	22	Single Node Identical	5 -1 5 -1	Lists are same	0.001	f	685b5ccce1277af85f2050e7
1	4	23	Provided Example Case	1 1 6 13 13 13 27 27 -1	1 -> 6 -> 13 -> 27 -> NULL	0.001	f	685b5ccee1277af85f2050ed
2	4	24	No Duplicates	1 2 3 4 5 -1	1 -> 2 -> 3 -> 4 -> 5 -> NULL	0.001	f	685b5ccee1277af85f2050ee
3	4	25	All Nodes are Duplicates	5 5 5 5 5 -1	5 -> NULL	0.001	f	685b5ccee1277af85f2050ef
4	4	26	Empty List	-1	List is empty	0.001	f	685b5ccee1277af85f2050f0
5	4	27	Duplicates at the End	10 20 30 30 -1	10 -> 20 -> 30 -> NULL	0.001	f	685b5ccee1277af85f2050f1
6	4	28	Duplicates at the Beginning	5 5 10 20 -1	5 -> 10 -> 20 -> NULL	0.001	f	685b5ccee1277af85f2050f2
1	5	29	Insert Front and Delete First	1\n10\n3\n2\n8	Node inserted at front\nFirst node deleted\nList is empty	0.001	f	685b5ccbe1277af85f2050d2
2	5	30	Insert at End and Display	4\n10\n2\n8	Node inserted at end\n10 -> NULL	0.001	f	685b5ccbe1277af85f2050d3
3	5	31	Display Empty List	2\n8	List is empty	0.001	f	685b5ccbe1277af85f2050d4
4	5	32	Count Empty List	7\n8	Number of nodes: 0	0.001	f	685b5ccbe1277af85f2050d5
5	5	33	Multiple Insert Front and Count	1\n10\n1\n20\n2\n7\n8	Node inserted at front\nNode inserted at front\n20 -> 10 -> NULL\nNumber of nodes: 2	0.001	f	685b5ccbe1277af85f2050d6
6	5	34	Multiple Insert End	4\n10\n4\n20\n2\n8	Node inserted at end\nNode inserted at end\n10 -> 20 -> NULL	0.001	f	685b5ccbe1277af85f2050d7
7	5	35	Delete First from Empty List	3\n8	List is empty	0.001	f	685b5ccbe1277af85f2050d8
8	5	36	Delete Last from Empty List	5\n8	List is empty	0.001	f	685b5ccbe1277af85f2050d9
9	5	37	Delete from Position	1\n10\n1\n20\n1\n30\n2\n6\n2\n2\n8	Node inserted at front\nNode inserted at front\nNode inserted at front\n30 -> 20 -> 10 -> NULL\nEnter position:\nNode deleted from position 2\n30 -> 10 -> NULL	0.001	f	685b5ccbe1277af85f2050da
10	5	38	Insert End and Delete Last Single Node	4\n10\n5\n2\n8	Node inserted at end\nLast node deleted\nList is empty	0.001	f	685b5ccbe1277af85f2050db
1	6	39	Positive Integer	10	10	0.001	f	685b5cc9e1277af85f2050c7
2	6	40	Negative Integer	-5	-5	0.001	f	685b5cc9e1277af85f2050c8
3	6	41	Zero	0	0	0.001	f	685b5cc9e1277af85f2050c9
4	6	42	Large Positive Integer	1000	1000	0.001	f	685b5cc9e1277af85f2050ca
5	6	43	Large Negative Integer	-1000	-1000	0.001	f	685b5cc9e1277af85f2050cb
1	7	44	Provided Example Case	1 1 6 13 13 13 27 27 -1	1 -> 6 -> 13 -> 27 -> NULL	0.001	f	685b5ccee1277af85f2050ed
2	7	45	No Duplicates	1 2 3 4 5 -1	1 -> 2 -> 3 -> 4 -> 5 -> NULL	0.001	f	685b5ccee1277af85f2050ee
3	7	46	All Nodes are Duplicates	5 5 5 5 5 -1	5 -> NULL	0.001	f	685b5ccee1277af85f2050ef
4	7	47	Empty List	-1	List is empty	0.001	f	685b5ccee1277af85f2050f0
5	7	48	Duplicates at the End	10 20 30 30 -1	10 -> 20 -> 30 -> NULL	0.001	f	685b5ccee1277af85f2050f1
6	7	49	Duplicates at the Beginning	5 5 10 20 -1	5 -> 10 -> 20 -> NULL	0.001	f	685b5ccee1277af85f2050f2
1	8	50	Identical Lists	10 20 30 -1 10 20 30 -1	Lists are same	0.001	f	685b5ccce1277af85f2050e1
2	8	51	Different Data	10 20 30 -1 10 99 30 -1	Lists are not same	0.001	f	685b5ccce1277af85f2050e2
3	8	52	Different Length (First Shorter)	10 20 -1 10 20 30 -1	Lists are not same	0.001	f	685b5ccce1277af85f2050e3
4	8	53	Different Length (Second Shorter)	10 20 30 -1 10 20 -1	Lists are not same	0.001	f	685b5ccce1277af85f2050e4
5	8	54	Both Lists Empty	-1 -1	Lists are same	0.001	f	685b5ccce1277af85f2050e5
6	8	55	One List Empty	10 -1 -1	Lists are not same	0.001	f	685b5ccce1277af85f2050e6
7	8	56	Single Node Identical	5 -1 5 -1	Lists are same	0.001	f	685b5ccce1277af85f2050e7
1	9	57	Provided Example Case	1 1 6 13 13 13 27 27 -1	1 -> 6 -> 13 -> 27 -> NULL	0.001	f	685b5ccee1277af85f2050ed
2	9	58	No Duplicates	1 2 3 4 5 -1	1 -> 2 -> 3 -> 4 -> 5 -> NULL	0.001	f	685b5ccee1277af85f2050ee
3	9	59	All Nodes are Duplicates	5 5 5 5 5 -1	5 -> NULL	0.001	f	685b5ccee1277af85f2050ef
4	9	60	Empty List	-1	List is empty	0.001	f	685b5ccee1277af85f2050f0
5	9	61	Duplicates at the End	10 20 30 30 -1	10 -> 20 -> 30 -> NULL	0.001	f	685b5ccee1277af85f2050f1
6	9	62	Duplicates at the Beginning	5 5 10 20 -1	5 -> 10 -> 20 -> NULL	0.001	f	685b5ccee1277af85f2050f2
1	10	63	Identical Lists	10 20 30 -1 10 20 30 -1	Lists are same	0.001	f	685b5ccce1277af85f2050e1
2	10	64	Different Data	10 20 30 -1 10 99 30 -1	Lists are not same	0.001	f	685b5ccce1277af85f2050e2
3	10	65	Different Length (First Shorter)	10 20 -1 10 20 30 -1	Lists are not same	0.001	f	685b5ccce1277af85f2050e3
4	10	66	Different Length (Second Shorter)	10 20 30 -1 10 20 -1	Lists are not same	0.001	f	685b5ccce1277af85f2050e4
5	10	67	Both Lists Empty	-1 -1	Lists are same	0.001	f	685b5ccce1277af85f2050e5
6	10	68	One List Empty	10 -1 -1	Lists are not same	0.001	f	685b5ccce1277af85f2050e6
7	10	69	Single Node Identical	5 -1 5 -1	Lists are same	0.001	f	685b5ccce1277af85f2050e7
1	11	70	Insert Front and Delete First	1\n10\n3\n2\n8	Node inserted at front\nFirst node deleted\nList is empty	0.001	f	685b5ccbe1277af85f2050d2
2	11	71	Insert at End and Display	4\n10\n2\n8	Node inserted at end\n10 -> NULL	0.001	f	685b5ccbe1277af85f2050d3
3	11	72	Display Empty List	2\n8	List is empty	0.001	f	685b5ccbe1277af85f2050d4
4	11	73	Count Empty List	7\n8	Number of nodes: 0	0.001	f	685b5ccbe1277af85f2050d5
5	11	74	Multiple Insert Front and Count	1\n10\n1\n20\n2\n7\n8	Node inserted at front\nNode inserted at front\n20 -> 10 -> NULL\nNumber of nodes: 2	0.001	f	685b5ccbe1277af85f2050d6
6	11	75	Multiple Insert End	4\n10\n4\n20\n2\n8	Node inserted at end\nNode inserted at end\n10 -> 20 -> NULL	0.001	f	685b5ccbe1277af85f2050d7
7	11	76	Delete First from Empty List	3\n8	List is empty	0.001	f	685b5ccbe1277af85f2050d8
8	11	77	Delete Last from Empty List	5\n8	List is empty	0.001	f	685b5ccbe1277af85f2050d9
9	11	78	Delete from Position	1\n10\n1\n20\n1\n30\n2\n6\n2\n2\n8	Node inserted at front\nNode inserted at front\nNode inserted at front\n30 -> 20 -> 10 -> NULL\nEnter position:\nNode deleted from position 2\n30 -> 10 -> NULL	0.001	f	685b5ccbe1277af85f2050da
10	11	79	Insert End and Delete Last Single Node	4\n10\n5\n2\n8	Node inserted at end\nLast node deleted\nList is empty	0.001	f	685b5ccbe1277af85f2050db
1	18	80	Adding 2 numbers	5 10	15	\N	f	685b6d89c00a3949d984cf91
1	19	81	Insert Front and Delete First	1\n10\n3\n2\n8	Node inserted at front\nFirst node deleted\nList is empty	0.001	f	685b5ccbe1277af85f2050d2
2	19	82	Insert at End and Display	4\n10\n2\n8	Node inserted at end\n10 -> NULL	0.001	f	685b5ccbe1277af85f2050d3
3	19	83	Display Empty List	2\n8	List is empty	0.001	f	685b5ccbe1277af85f2050d4
4	19	84	Count Empty List	7\n8	Number of nodes: 0	0.001	f	685b5ccbe1277af85f2050d5
5	19	85	Multiple Insert Front and Count	1\n10\n1\n20\n2\n7\n8	Node inserted at front\nNode inserted at front\n20 -> 10 -> NULL\nNumber of nodes: 2	0.001	f	685b5ccbe1277af85f2050d6
6	19	86	Multiple Insert End	4\n10\n4\n20\n2\n8	Node inserted at end\nNode inserted at end\n10 -> 20 -> NULL	0.001	f	685b5ccbe1277af85f2050d7
7	19	87	Delete First from Empty List	3\n8	List is empty	0.001	f	685b5ccbe1277af85f2050d8
8	19	88	Delete Last from Empty List	5\n8	List is empty	0.001	f	685b5ccbe1277af85f2050d9
9	19	89	Delete from Position	1\n10\n1\n20\n1\n30\n2\n6\n2\n2\n8	Node inserted at front\nNode inserted at front\nNode inserted at front\n30 -> 20 -> 10 -> NULL\nEnter position:\nNode deleted from position 2\n30 -> 10 -> NULL	0.001	f	685b5ccbe1277af85f2050da
10	19	90	Insert End and Delete Last Single Node	4\n10\n5\n2\n8	Node inserted at end\nLast node deleted\nList is empty	0.001	f	685b5ccbe1277af85f2050db
1	23	121	Sum of 5 and 10	5 10	15	\N	f	685b6d89c00a3949d984cf91
2	23	122	Sum of 11 and 22	11 22	33	\N	f	685d42ec21f772de1ebb3700
3	23	123	Sum of -89 and 2	-89 2	-87	\N	f	685d430321f772de1ebb3702
1	24	124	Sum of 5 and 10	5 10	15	\N	f	685b6d89c00a3949d984cf91
2	24	125	Sum of 11 and 22	11 22	33	\N	f	685d42ec21f772de1ebb3700
3	24	126	Sum of -89 and 2	-89 2	-87	\N	f	685d430321f772de1ebb3702
\.


--
-- Data for Name: _assignments_v_version_hints; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public._assignments_v_version_hints (_order, _parent_id, id, question, answer, _uuid) FROM stdin;
1	1	1	How do I read input from the user?	Use `scanf("%d", &variable)` to read an integer from the user.	685b5cc9e1277af85f2050c2
2	1	2	How do I create a node dynamically?	Use `malloc(sizeof(struct Node))` to allocate memory, set the `data` field, and make `next` NULL.	685b5cc9e1277af85f2050c3
3	1	3	How do I avoid memory leaks?	Use `free` to deallocate the node's memory after printing.	685b5cc9e1277af85f2050c4
1	2	4	How do I handle an empty list?	Check if head is NULL before performing operations. Initialize head to NULL at the start.	685b5ccbe1277af85f2050cc
2	2	5	How do I insert at the front?	Create new node, set its next to current head, then update head to point to new node.	685b5ccbe1277af85f2050cd
3	2	6	How do I insert at the end?	If list is empty, insert at front. Otherwise, traverse to last node and link the new node.	685b5ccbe1277af85f2050ce
4	2	7	How do I delete from a specific position?	Traverse to the position, keep track of previous node, then adjust the links and free the node.	685b5ccbe1277af85f2050cf
1	3	8	How should I approach the comparison logic?	Traverse both lists at the same time using two pointers. In each iteration, check if the data of the current nodes is equal. If not, they are different. The loop should continue as long as both pointers are not NULL.	685b5ccce1277af85f2050dc
2	3	9	What if the lists have different lengths?	After your traversal loop finishes, one pointer might be NULL while the other is not. If both pointers are not NULL at the same time, the lists have different lengths and are therefore not the same.	685b5ccce1277af85f2050dd
3	3	10	How do I handle empty lists?	Your logic should naturally handle this. If both heads are NULL, the loop condition will be false, and the final check (are both pointers NULL?) will pass. If one is NULL and the other is not, it will correctly fail.	685b5ccce1277af85f2050de
1	4	11	How do I check for a duplicate?	Since the list is sorted, you only need to compare a node with the one immediately following it (`current->data == current->next->data`).	685b5ccee1277af85f2050e8
2	4	12	What is the correct way to delete the node?	Store the node to be deleted (`current->next`) in a temporary variable, update `current->next` to `current->next->next`, and then `free` the temporary variable.	685b5ccee1277af85f2050e9
3	4	13	What if there are more than two duplicates in a row, like 5 -> 5 -> 5?	When you delete a duplicate, do not advance your main pointer (`current`). The loop will run again, comparing `current` with its new `next` node, handling consecutive duplicates correctly.	685b5ccee1277af85f2050ea
1	5	14	How do I handle an empty list?	Check if head is NULL before performing operations. Initialize head to NULL at the start.	685b5ccbe1277af85f2050cc
2	5	15	How do I insert at the front?	Create new node, set its next to current head, then update head to point to new node.	685b5ccbe1277af85f2050cd
3	5	16	How do I insert at the end?	If list is empty, insert at front. Otherwise, traverse to last node and link the new node.	685b5ccbe1277af85f2050ce
4	5	17	How do I delete from a specific position?	Traverse to the position, keep track of previous node, then adjust the links and free the node.	685b5ccbe1277af85f2050cf
1	6	18	How do I read input from the user?	Use `scanf("%d", &variable)` to read an integer from the user.	685b5cc9e1277af85f2050c2
2	6	19	How do I create a node dynamically?	Use `malloc(sizeof(struct Node))` to allocate memory, set the `data` field, and make `next` NULL.	685b5cc9e1277af85f2050c3
3	6	20	How do I avoid memory leaks?	Use `free` to deallocate the node's memory after printing.	685b5cc9e1277af85f2050c4
1	7	21	How do I check for a duplicate?	Since the list is sorted, you only need to compare a node with the one immediately following it (`current->data == current->next->data`).	685b5ccee1277af85f2050e8
2	7	22	What is the correct way to delete the node?	Store the node to be deleted (`current->next`) in a temporary variable, update `current->next` to `current->next->next`, and then `free` the temporary variable.	685b5ccee1277af85f2050e9
3	7	23	What if there are more than two duplicates in a row, like 5 -> 5 -> 5?	When you delete a duplicate, do not advance your main pointer (`current`). The loop will run again, comparing `current` with its new `next` node, handling consecutive duplicates correctly.	685b5ccee1277af85f2050ea
1	8	24	How should I approach the comparison logic?	Traverse both lists at the same time using two pointers. In each iteration, check if the data of the current nodes is equal. If not, they are different. The loop should continue as long as both pointers are not NULL.	685b5ccce1277af85f2050dc
2	8	25	What if the lists have different lengths?	After your traversal loop finishes, one pointer might be NULL while the other is not. If both pointers are not NULL at the same time, the lists have different lengths and are therefore not the same.	685b5ccce1277af85f2050dd
3	8	26	How do I handle empty lists?	Your logic should naturally handle this. If both heads are NULL, the loop condition will be false, and the final check (are both pointers NULL?) will pass. If one is NULL and the other is not, it will correctly fail.	685b5ccce1277af85f2050de
1	9	27	How do I check for a duplicate?	Since the list is sorted, you only need to compare a node with the one immediately following it (`current->data == current->next->data`).	685b5ccee1277af85f2050e8
2	9	28	What is the correct way to delete the node?	Store the node to be deleted (`current->next`) in a temporary variable, update `current->next` to `current->next->next`, and then `free` the temporary variable.	685b5ccee1277af85f2050e9
3	9	29	What if there are more than two duplicates in a row, like 5 -> 5 -> 5?	When you delete a duplicate, do not advance your main pointer (`current`). The loop will run again, comparing `current` with its new `next` node, handling consecutive duplicates correctly.	685b5ccee1277af85f2050ea
1	10	30	How should I approach the comparison logic?	Traverse both lists at the same time using two pointers. In each iteration, check if the data of the current nodes is equal. If not, they are different. The loop should continue as long as both pointers are not NULL.	685b5ccce1277af85f2050dc
2	10	31	What if the lists have different lengths?	After your traversal loop finishes, one pointer might be NULL while the other is not. If both pointers are not NULL at the same time, the lists have different lengths and are therefore not the same.	685b5ccce1277af85f2050dd
3	10	32	How do I handle empty lists?	Your logic should naturally handle this. If both heads are NULL, the loop condition will be false, and the final check (are both pointers NULL?) will pass. If one is NULL and the other is not, it will correctly fail.	685b5ccce1277af85f2050de
1	11	33	How do I handle an empty list?	Check if head is NULL before performing operations. Initialize head to NULL at the start.	685b5ccbe1277af85f2050cc
2	11	34	How do I insert at the front?	Create new node, set its next to current head, then update head to point to new node.	685b5ccbe1277af85f2050cd
3	11	35	How do I insert at the end?	If list is empty, insert at front. Otherwise, traverse to last node and link the new node.	685b5ccbe1277af85f2050ce
4	11	36	How do I delete from a specific position?	Traverse to the position, keep track of previous node, then adjust the links and free the node.	685b5ccbe1277af85f2050cf
1	19	37	How do I handle an empty list?	Check if head is NULL before performing operations. Initialize head to NULL at the start.	685b5ccbe1277af85f2050cc
2	19	38	How do I insert at the front?	Create new node, set its next to current head, then update head to point to new node.	685b5ccbe1277af85f2050cd
3	19	39	How do I insert at the end?	If list is empty, insert at front. Otherwise, traverse to last node and link the new node.	685b5ccbe1277af85f2050ce
4	19	40	How do I delete from a specific position?	Traverse to the position, keep track of previous node, then adjust the links and free the node.	685b5ccbe1277af85f2050cf
\.


--
-- Data for Name: _assignments_v_version_java_test_cases; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public._assignments_v_version_java_test_cases (_order, _parent_id, id, title, input, expected_output, tolerance, is_hidden, _uuid) FROM stdin;
\.


--
-- Data for Name: _assignments_v_version_resources; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public._assignments_v_version_resources (_order, _parent_id, id, title, url, _uuid) FROM stdin;
1	1	1	Singly Linked List in C - GeeksforGeeks	https://www.geeksforgeeks.org/singly-linked-list-tutorial	685b5cc9e1277af85f2050c5
2	1	2	Understanding Linked List (YouTube)	https://www.youtube.com/watch?v=VOpjAHCee7c	685b5cc9e1277af85f2050c6
1	2	3	Singly Linked List in C - GeeksforGeeks	https://www.geeksforgeeks.org/singly-linked-list-tutorial	685b5ccbe1277af85f2050d0
2	2	4	Understanding Linked List (YouTube)	https://www.youtube.com/watch?v=VOpjAHCee7c	685b5ccbe1277af85f2050d1
1	3	5	Singly Linked List in C - GeeksforGeeks	https://www.geeksforgeeks.org/singly-linked-list-tutorial	685b5ccce1277af85f2050df
2	3	6	Identical Linked Lists - GeeksforGeeks	https://www.geeksforgeeks.org/identical-linked-lists/	685b5ccce1277af85f2050e0
1	4	7	Remove duplicates from a sorted linked list - GeeksforGeeks	https://www.geeksforgeeks.org/remove-duplicates-from-a-sorted-linked-list/	685b5ccee1277af85f2050eb
2	4	8	Singly Linked List in C (Tutorial)	https://www.programiz.com/dsa/singly-linked-list	685b5ccee1277af85f2050ec
1	5	9	Singly Linked List in C - GeeksforGeeks	https://www.geeksforgeeks.org/singly-linked-list-tutorial	685b5ccbe1277af85f2050d0
2	5	10	Understanding Linked List (YouTube)	https://www.youtube.com/watch?v=VOpjAHCee7c	685b5ccbe1277af85f2050d1
1	6	11	Singly Linked List in C - GeeksforGeeks	https://www.geeksforgeeks.org/singly-linked-list-tutorial	685b5cc9e1277af85f2050c5
2	6	12	Understanding Linked List (YouTube)	https://www.youtube.com/watch?v=VOpjAHCee7c	685b5cc9e1277af85f2050c6
1	7	13	Remove duplicates from a sorted linked list - GeeksforGeeks	https://www.geeksforgeeks.org/remove-duplicates-from-a-sorted-linked-list/	685b5ccee1277af85f2050eb
2	7	14	Singly Linked List in C (Tutorial)	https://www.programiz.com/dsa/singly-linked-list	685b5ccee1277af85f2050ec
1	8	15	Singly Linked List in C - GeeksforGeeks	https://www.geeksforgeeks.org/singly-linked-list-tutorial	685b5ccce1277af85f2050df
2	8	16	Identical Linked Lists - GeeksforGeeks	https://www.geeksforgeeks.org/identical-linked-lists/	685b5ccce1277af85f2050e0
1	9	17	Remove duplicates from a sorted linked list - GeeksforGeeks	https://www.geeksforgeeks.org/remove-duplicates-from-a-sorted-linked-list/	685b5ccee1277af85f2050eb
2	9	18	Singly Linked List in C (Tutorial)	https://www.programiz.com/dsa/singly-linked-list	685b5ccee1277af85f2050ec
1	10	19	Singly Linked List in C - GeeksforGeeks	https://www.geeksforgeeks.org/singly-linked-list-tutorial	685b5ccce1277af85f2050df
2	10	20	Identical Linked Lists - GeeksforGeeks	https://www.geeksforgeeks.org/identical-linked-lists/	685b5ccce1277af85f2050e0
1	11	21	Singly Linked List in C - GeeksforGeeks	https://www.geeksforgeeks.org/singly-linked-list-tutorial	685b5ccbe1277af85f2050d0
2	11	22	Understanding Linked List (YouTube)	https://www.youtube.com/watch?v=VOpjAHCee7c	685b5ccbe1277af85f2050d1
1	19	23	Singly Linked List in C - GeeksforGeeks	https://www.geeksforgeeks.org/singly-linked-list-tutorial	685b5ccbe1277af85f2050d0
2	19	24	Understanding Linked List (YouTube)	https://www.youtube.com/watch?v=VOpjAHCee7c	685b5ccbe1277af85f2050d1
\.


--
-- Data for Name: _assignments_v_version_test_suites; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public._assignments_v_version_test_suites (_order, _parent_id, id, points, visibility, _uuid) FROM stdin;
\.


--
-- Data for Name: _submissions_v; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public._submissions_v (id, parent_id, version_tenant_id, version_student_id, version_assignment_id, version_status, version_is_locked, version_solution_code_html, version_solution_code_css, version_solution_code_js, version_solution_code_java, version_solution_code_c, version_score, version_passed_test_cases, version_failed_test_cases, version_feedback, version_updated_at, version_created_at, created_at, updated_at) FROM stdin;
3	3	1	16	1	review	f					#include <stdio.h>\n\n// define the structure of Node\n\nvoid main() {\n   int a,b,c;\n   printf("Enter a num: ");\n   scanf("%d",&a);\n   scanf("%d",&b);\n   scanf("%d",&c);\n   printf("%d",a*b*c*5);\n}	0	0	5	\N	2025-06-25 03:16:31.053+00	2025-06-25 03:16:30.886+00	2025-06-25 03:16:31.21+00	2025-06-25 03:16:31.21+00
4	4	1	8	1	review	f					#include <stdio.h>\n\n// define the structure of Node\nstruct Node{\n    int data;\n    struct Node* next;\n};\n\nvoid main() {\n    // initialize the node\n    struct Node* newNode = (struct Node*) malloc(sizeof(struct Node));\n    // take input from user\n    int n;\n    scanf("%d",&n);\n    // assign value to data member of node\n    newNode->data = n;\n    // assign next member of node to NULL\n    newNode->next = NULL;\n    // print the data member of node\n    printf("%d",newNode->data);\n\n    free(newNode);\n}	100	5	0	\N	2025-06-25 03:23:49.833+00	2025-06-25 03:23:49.66+00	2025-06-25 03:23:49.995+00	2025-06-25 03:23:49.995+00
5	5	1	1	5	review	f					#include<stdio.h>\r\n\r\nint add(int a,int b){\r\n  return a+b;\r\n}\r\n\r\nvoid main(){\r\n  int a,b;\r\n  scanf("%d %d",&a,&b);\r\n  printf("%d",add(a,b));\r\n}	0	0	1	\N	2025-06-25 03:25:35.478+00	2025-06-25 03:25:35.323+00	2025-06-25 03:25:35.622+00	2025-06-25 03:25:35.622+00
8	7	1	12	5	review	f					#include<stdio.h>\r\n// int sum(int a, int b);\r\nint sum(int a, int b){\r\n  int c = a+b;\r\n  return c;\r\n}\r\nvoid main(){\r\n  int a,b;\r\n  // printf("Enter two numbers : ");\r\n  scanf("%d %d",&a,&b);\r\n  printf("Sum using UDF : %d",sum(a,b));\r\n}	0	0	1	\N	2025-06-25 03:37:01.927+00	2025-06-25 03:37:01.766+00	2025-06-25 03:37:02.077+00	2025-06-25 03:37:02.078+00
11	8	1	14	2	review	f					// #include <stdio.h>\n// #include <stdlib.h>\n\n// struct node {\n//     int info;\n//     struct Node * link;\n// };\n\n// struct node *first = NULL;\n\n// void insertAtFirst(int x){\n//     struct node *newNode = (struct node *)malloc(sizeof(struct node));\n\n//     newNode -> info = x;\n//     newNode -> link = first;\n//     first = newNode;\n// }\n\n// void insertAtLast(int x){\n//     struct node *newNode = (struct node *)malloc(sizeof(struct node));\n\n//     if(first == NULL){\n//         first = newNode;\n//         return;\n//     }\n//     newNode -> info = x;\n//     newNode -> link = NULL;\n\n//     struct node *save = first;\n//     while(save -> link != NULL){\n//         save = save ->link;\n//     }\n//     save -> link =newNode;\n// }\n\n// void display(){\n//     if(first == NULL){\n//         printf("Empty");\n//         return;\n//     }\n//     struct node *save = first;\n//     while(save != NULL){\n//         printf(" %d -> ", save -> info);\n//         save = save->link;\n//     }\n//     printf("NULL \\n");\n// }\n\n// int main() {\n//    int choice, x;\n//     while(1){\n//         printf("Menu\\n");\n//         printf("1.insertAtFirst\\n 2.insertAtLast\\n 3.Display\\n 4.Exit\\n");\n\n//         printf("Enter your choice");\n//         scanf("%d", &choice);\n\n//         switch(choice){\n//             case 1:\n//                 printf("Enter the data: ");\n//                 scanf("%d", &x);\n//                 insertAtFirst(x);\n//                 break;\n            \n//             case 2:\n//                 printf("Enter data:");\n//                 scanf("%d", &x);\n//                 insertAtLast(x);\n//                 break;\n\n//             case 3:\n//                 printf("List is: ");\n//                 display();\n//                 break;\n\n//             case 4:\n//                 printf("Exiting..");\n//                 return 0;\n\n//             default:\n//                 printf("Invalid");\n//                 break;\n//         }\n//     }\n\n    \n//     return 0;\n// }	0	0	10	\N	2025-06-25 03:43:35.179+00	2025-06-25 03:43:35.016+00	2025-06-25 03:43:35.331+00	2025-06-25 03:43:35.331+00
12	8	1	14	2	review	f					#include <stdio.h>\n#include <stdlib.h>\n\nstruct node {\n    int info;\n    struct node *link;\n};\n\nstruct node *first = NULL;\n\nvoid insertAtFirst(int x){\n    struct node *newNode = (struct node *)malloc(sizeof(struct node));\n\n    newNode -> info = x;\n    newNode -> link = first;\n    first = newNode;\n}\n\nvoid insertAtLast(int x){\n    struct node *newNode = (struct node *)malloc(sizeof(struct node));\n\n    if(first == NULL){\n        first = newNode;\n        return;\n    }\n    newNode -> info = x;\n    newNode -> link = NULL;\n\n    struct node *save = first;\n    while(save -> link != NULL){\n        save = save ->link;\n    }\n    save -> link =newNode;\n}\n\nvoid display(){\n    if(first == NULL){\n        printf("Empty");\n        return;\n    }\n    struct node *save = first;\n    while(save != NULL){\n        printf(" %d -> ", save -> info);\n        save = save->link;\n    }\n    printf("NULL \\n");\n}\n\nint main() {\n   int choice, x;\n    {\n        printf("Menu\\n");\n        printf("1.insertAtFirst\\n 2.insertAtLast\\n 3.Display\\n 4.Exit\\n");\n\n        printf("Enter your choice");\n        scanf("%d", &choice);\n\n        switch(choice){\n            case 1:\n                printf("Enter the data: ");\n                scanf("%d", &x);\n                insertAtFirst(x);\n                break;\n            \n            case 2:\n                printf("Enter data:");\n                scanf("%d", &x);\n                insertAtLast(x);\n                break;\n\n            case 3:\n                printf("List is: ");\n                display();\n                break;\n\n            case 4:\n                printf("Exiting..");\n                return 0;\n\n            default:\n                printf("Invalid");\n                break;\n        }\n    }\n    return 0;\n}	0	0	10	\N	2025-06-25 03:55:25.636+00	2025-06-25 03:43:35.016+00	2025-06-25 03:55:25.787+00	2025-06-25 03:55:25.787+00
13	9	1	11	2	review	f					#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node * next;\n};\n\nstruct Node * head = NULL;\n\n \nvoid insertAtfirst(){\n    int n;\n    printf("enter value");\n    scanf("%d",&n);\n    struct Node *newNode = (struct Node*)malloc(sizeof(struct Node));\n    newNode-> data = n;\n    newNode->next = head;\n    head = newNode;\n        \n}\n\nvoid Display(){\n    struct Node *save = head;\n    while(save!= NULL){\n        printf("%d -> ",save-> data);\n        save = save->next;\n    }\n    printf("NULL\\n");\n}\n\nvoid insertAtEnd(){\n    struct Node *newNode = (struct Node *)malloc(sizeof(struct Node));\n    struct Node *save = head;\n    int n;\n    printf("enter number ");\n    scanf("%d",&n);\n    newNode->data = n;\n    while(save->next!= NULL){\n        save = save->next;\n    }\n    save->next = newNode;\n    newNode->next = NULL;\n}\n\nvoid deleteNode(){\n    \n}\n\nint main() {\n    int choice, data, pos;\n    insertAtfirst();\n    insertAtEnd();\n    Display();\n   \n    // TODO: Display menu\n    // TODO: Get user choice\n    // TODO: Implement switch case for all operations\n}	0	0	10	\N	2025-06-25 03:59:24.041+00	2025-06-25 03:59:23.885+00	2025-06-25 03:59:24.188+00	2025-06-25 03:59:24.188+00
14	10	1	7	2	review	f					#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node * next;\n};\nstruct Node* head=NULL;\nvoid insertAtfirst(){\n    int n;\n    printf("enter value");\n    scanf("%d",&n);\n    struct Node *newNode=(struct Node*)malloc(sizeof(struct Node));\n    newNode->data=n;\n    newNode->next=head;\n    head=newNode;\n}\nvoid Display(){\n    struct Node *save= head;\n    while(save!=NULL){\n        printf("%d",save->data);\n        save=save->next;\n    }\n    printf("NULL\\n");\n}\nvoid InsertATEnd(){\n    struct Node *newNode=(struct Node*)malloc(sizeof(struct Node));\n   struct Node *save=NULL;\nint n;\nprintf("enter value");\nscanf()\n    While(save->next!= NULL){\n        save=save->next;\n    }\n    save->next=newNode;\n    newNode->NULL;\n}\nint main() {\n    struct Node * head = NULL;\n    int choice, data, pos;\n     insertAtfirst();\n     Display();\n    // TODO: Display menu\n    // TODO: Get user choice\n    // TODO: Implement switch case for all operations\n}	0	0	10	\N	2025-06-25 04:01:05.398+00	2025-06-25 04:01:05.237+00	2025-06-25 04:01:05.55+00	2025-06-25 04:01:05.55+00
15	10	1	7	2	review	f					#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node * next;\n};\nstruct Node* head=NULL;\nvoid insertAtfirst(){\n    int n;\n    printf("enter value");\n    scanf("%d",&n);\n    struct Node *newNode=(struct Node*)malloc(sizeof(struct Node));\n    newNode->data=n;\n    newNode->next=head;\n    head=newNode;\n}\nvoid Display(){\n    struct Node *save= head;\n    while(save!=NULL){\n        printf("%d",save->data);\n        save=save->next;\n    }\n    printf("NULL\\n");\n}\nvoid InsertATEnd(){\n    struct Node *newNode=(struct Node*)malloc(sizeof(struct Node));\n   struct Node *save=NULL;\nint n;\nprintf("enter value");\nscanf()\n    While(save->next!= NULL){\n        save=save->next;\n    }\n    save->next=newNode;\n    newNode->NULL;\n}\nint main() {\n    struct Node * head = NULL;\n    int choice, data, pos;\n     insertAtfirst();\n     Display();\n    // TODO: Display menu\n    // TODO: Get user choice\n    // TODO: Implement switch case for all operations\n}	0	0	10	\N	2025-06-25 04:01:16.826+00	2025-06-25 04:01:05.237+00	2025-06-25 04:01:16.976+00	2025-06-25 04:01:16.977+00
16	11	1	5	2	review	f					#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node * next;\n};\n\nstruct Node* insertAtFirst(struct Node* head, int val){\n    struct Node* newNode = (struct Node*)malloc(sizeof(struct Node));\n    newNode->data = val;\n    newNode->next = head;\n    head = newNode;\n\n    return head;\n}\n\nstruct Node* deleteAtFirst(struct Node* head){\n    if(head == NULL) return head;\n    struct Node* save = head;\n    head = head->next;\n    free(save);\n\n    return head;\n}\n\nstruct Node* insertAtLast(struct Node* head, int val){\n    struct Node* newNode = (struct Node*)malloc(sizeof(struct Node));\n    newNode->data = val;\n    newNode->next = NULL;\n    if(head == NULL) return newNode;\n    struct Node* save = head;\n    while(save->next != NULL){\n        save = save->next;\n    }\n    save->next = newNode;\n    return head;\n}\n\nstruct Node* deleteAtLast(struct Node* head){\n    if(head == NULL || head->next == NULL) return NULL;\n    struct Node* save = head;\n    while(save->next->next != NULL){\n        save = save->next;\n    }\n    struct node* toDelete = save->next; \n    save->next = NULL;\n    free(toDelete);\n    return head;\n}\n\nvoid display(struct Node* head){\n    if(head == NULL){\n        printf("List is empty");\n        return;\n    }\n\n    struct Node* save = head;\n    while(save!=NULL){\n        printf("%d->",save->data);\n        save = save->next;\n    }\n    printf("NULL");\n}\n\n\nint main() {\n    struct Node * head = NULL;\n    int choice, data, pos;\n    while(1){\n     scanf("%d", &choice);\n     switch(choice){\n        case 1:\n            scanf("%d",&data);\n            head = insertAtFirst(head,data);\n            break;\n        case 2:\n            head = deleteAtFirst(head);\n            break;\n        case 3:\n        display(head);\n            break;\n        default:\n            return;\n     }   \n    }\n    // TODO: Display menu\n    // TODO: Get user choice\n    // TODO: Implement switch case for all operations\n}	10	1	9	\N	2025-06-25 04:02:15.894+00	2025-06-25 04:02:15.732+00	2025-06-25 04:02:16.046+00	2025-06-25 04:02:16.047+00
17	12	1	3	2	review	f					#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node * link;\n};\n\nstruct Node* first=NULL;\n\nvoid insertAtFirst(int value){\n    struct Node* newNode = (struct Node*)malloc(sizeof(struct Node));\n    newNode->data=value;\n    newNode->link=first;\n    first=newNode;\n}\n\nvoid insertAtLast(int value){\n    struct Node* newNode = (struct Node*)malloc(sizeof(struct Node));\n    newNode->data=value;\n    newNode->link=NULL;\n\n    if(first==NULL){\n        first=newNode;\n    }\n    else{\n        struct Node* save=first;\n        while(save->link!=NULL){\n            save=save->link;\n        }\n        save->link=newNode;\n    }\n}\n\nvoid deleteAtFirst(){\n    if(first==NULL){\n        printf("List is Empty");\n        return 0;\n    }\n\n    struct Node* temp=first;\n    first=first->link;\n\n    free(temp);\n}\n\nvoid display(){\n    if(first==NULL){\n        printf("List Empty");\n        return 0;\n    }\n\n    struct Node* save=first;\n    while(save!=NULL){\n        printf("%d -> ", save->data);\n        save=save->link;\n    }\n    printf("NULL");\n}\n\nint main() {\n    struct Node * head = NULL;\n    int choice, data, pos;\n\n    // TODO: Display menu\n    for(int i=1; i<=5; i++){\n        int x;\n        scanf("%d", &x);\n\n        insertAtLast(x);\n    }\n\n    deleteAtFirst();\n    deleteAtFirst();\n\n    display();\n    // TODO: Get user choice\n    // TODO: Implement switch case for all operations\n}	0	0	10	\N	2025-06-25 04:04:23.964+00	2025-06-25 04:04:23.802+00	2025-06-25 04:04:24.111+00	2025-06-25 04:04:24.111+00
18	13	1	6	2	review	f					#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int info;\n    struct Node * link;\n};\n\nstruct Node *first = NULL;\n\nvoid insertAtFirst(int x){\n    struct Node *newNode = (struct Node *)malloc(sizeof(struct Node));\n    newNode->link = NULL;\n    newNode->info = x;\n    first = newNode;\n}\n\nvoid insertAtLast(int x){\n    struct Node *newNode = (struct Node *)malloc(sizeof(struct Node));\n    if(first == NULL){\n        newNode->link = NULL;\n        first = newNode;\n        return ;\n    }\n    else{\n        struct Node *save = first;\n        while(save->link != NULL){\n            save = save->link;\n        }\n        save->info = x;\n        free(save);\n    }\n}\n\nvoid deleteNode(int x){\n    if(first == NULL){\n        printf("linkedlist is empty");\n        return;\n    }\n    else{\n        if(first->info == x){\n            first = first->link;\n            return ;\n        }\n        struct Node *save = first;\n        struct Node *pred = save;\n\n        while(save->info != x && save != NULL){\n            pred = save;\n            save = save->link;\n        }\n        if(save->info != x){\n            printf("node not found");\n        }\n        pred->link = save->link;\n        free(save);\n        free(pred);\n    }\n}\nvoid display(){\n    if(first == NULL){\n        printf("Empty \\n");\n        return;\n    }\n    struct Node *save = first;\n    while(save != NULL){\n        printf("%d", save->info);\n        save = save->link;\n    }\n    free(save);\n}\n\n\nint main() {\n\n    int choice=1, x,data, pos;\n    {\n        scanf("%d", &choice);\n        switch(choice){\n            case 1:\n            printf("data add:");\n            scanf("%d", &x);\n            insertAtFirst(x);\n            break;\n\n            case 2:\n            printf("data add:");\n            scanf("%d", &x);\n            insertAtLast(x);\n            break;\n\n            case 3:\n            printf("display: ");\n            display();\n            break;\n\n            case 4:\n            return 0;\n        }\n    }\n\n   \n}	0	0	10	\N	2025-06-25 04:09:02.596+00	2025-06-25 04:09:02.433+00	2025-06-25 04:09:02.756+00	2025-06-25 04:09:02.757+00
19	14	1	4	2	review	f					#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int info;\n    struct Node * link;\n};\n\nstruct Node * head = NULL;\n\nvoid insertAtFront(int data){\n    struct Node* newNode = (struct Node*)malloc(sizeof(struct Node));\n    newNode->info = data;\n    newNode->link = NULL;\n\n    if(head==NULL){\n        head = newNode;\n    }\n    newNode->link = head;\n    head = newNode;\n}\n\nvoid insertAtEnd(int data){\n    struct Node* newNode = (struct Node*)malloc((sizeof(struct Node)));\n    newNode->info = data;\n    newNode->link = NULL;\n\n    struct Node* save = head;\n\n    if(head==NULL){\n        head=save=newNode;\n    }\n\n    while(save!=NULL){\n        save = save->link;\n    }\n\n    save->link = newNode;\n}\n\nvoid display(){\n    struct Node* save =head;\n    while(save!=NULL){\n        printf("%d ->",save->info);\n        save = save->link;\n    }\n}\n\nvoid deleteAtFirst(){\n    struct Node* save = head;\n    if(head==NULL){\n        printf("List is empty");\n    }\n    head = head->link;\n    free(save);\n}\n\nvoid deleteAtLast(){\n    struct Node* pred = head;\n    struct Node* save = head->link;\n\n    if(head==NULL){\n        printf("List is empty");\n    }\n    while(save!=NULL){\n        pred = save;\n        save = save->link;\n    }\n    pred->link = NULL;\n\n    free(save);\n}\n\nvoid countNode(){\n    struct Node* save = head;\n    int count = 0;\n    while(save!=NULL){\n        count++;\n        save = save->link;\n    }\n    printf("Num of nodes = %d",count);\n}\n\nvoid main() {\n    \n    int choice, data, pos;\n\n    {\n        printf("1 for insertAtFront,2 for insertAtEnd,3 for deleteAtFirst,4 for deleteAtLast,5 for countNode,6 for display,0 for exit");\n        scanf("%d",&choice);\n\n        switch(choice){\n            case 1 :\n                printf("Enter data:");\n                scanf("%d",&data);\n                insertAtFront(data);\n                break;\n            case 2 :\n                printf("Enter data:");\n                scanf("%d",&data);\n                insertAtEnd(data);\n                break;\n            case 3:\n                deleteAtFirst();\n                break;\n            case 4:\n                deleteAtLast();\n                break;\n            case 5:\n                countNode();\n                break;\n            case 6:\n                display();\n                break;\n            default:\n                printf("invalid choice");\n                break;\n        }\n\n    }\n    // TODO: Display menu\n    // TODO: Get user choice\n    // TODO: Implement switch case for all operations\n}	0	0	10	\N	2025-06-25 04:10:12.374+00	2025-06-25 04:10:12.216+00	2025-06-25 04:10:12.52+00	2025-06-25 04:10:12.52+00
20	5	1	1	5	review	f					void main() {\r\n  int a=0, b=0, c=0;\r\n  scanf("%d %d %d", &a, &b, &c);\r\n  printf("%d", a+b+c);\r\n}\r\n	100	3	0	\N	2025-06-26 14:43:44.261+00	2025-06-25 03:25:35.323+00	2025-06-26 14:43:44.274+00	2025-06-26 14:43:44.274+00
21	5	1	1	5	review	f					void main() {\r\n  int a=0, b=0, c=0;\r\n  scanf("%d %d %d", &a, &b, &c);\r\n  printf("%d", a+b+c);\r\n}\r\n	100	3	0	\N	2025-06-26 14:47:23.499+00	2025-06-25 03:25:35.323+00	2025-06-26 14:47:23.505+00	2025-06-26 14:47:23.505+00
\.


--
-- Data for Name: assignments; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.assignments (id, tenant_id, title, subject_id, module_id, description, language, difficulty, points, due_date, instructions, requires_command_line_args, starter_code_html, starter_code_css, starter_code_js, starter_code_java, starter_code_c, solution_code_html, solution_code_css, solution_code_js, solution_code_java, solution_code_c, solution_notes, updated_at, created_at, _status) FROM stdin;
3	1	Compare Two Singly Linked Lists	1	\N	Write a C program that takes two singly linked lists as input and determines if they are identical in structure and content.	c	medium	15	2025-07-02 02:19:56.882+00	Write a C program to compare two singly linked lists and determine if they are identical. Two lists are considered identical if they have the same number of nodes, and the data in corresponding nodes is the same.\n\n**Input Format:**\nThe program will read integers from standard input to build two lists. A sentinel value of `-1` will be used to signify the end of input for each list.\nExample: `10 20 30 -1 10 20 30 -1` would create two lists: (10 -> 20 -> 30) and (10 -> 20 -> 30).\n\n**Steps:**\n1. Define the `struct Node` with an integer `data` and a pointer `next`.\n2. Create a helper function (e.g., `insertEnd`) to add nodes to the end of a list. This will simplify your main function.\n3. In `main`, create two head pointers, `head1` and `head2`, initialized to NULL.\n4. Read integers in a loop for the first list, calling your insert function for each one, until `-1` is read.\n5. Do the same for the second list.\n6. Implement a comparison function, e.g., `areSame(struct Node* a, struct Node* b)`, that returns 1 if the lists are identical and 0 otherwise.\n7. The comparison logic should traverse both lists simultaneously. If a data mismatch is found, or if one list is longer than the other, they are not the same.\n8. Based on the return value of your comparison function, print either `"Lists are same"` or `"Lists are not same"`.\n\n**Example Scenarios:**\n- Input: `10 20 -1 10 20 -1` → Output: `Lists are same`\n- Input: `10 20 -1 10 20 30 -1` → Output: `Lists are not same`\n- Input: `5 15 -1 5 25 -1` → Output: `Lists are not same`	f	\N	\N	\N	\N	#include <stdio.h>\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node* next;\n};\n\n\nint main() {\n    struct Node* head1 = NULL;\n    struct Node* head2 = NULL;\n    int data;\n\n    // Read first list\n\n    // Read second list\n\n    // show result\n\n    return 0;\n}	\N	\N	\N	\N	#include <stdio.h>\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node* next;\n};\n\nvoid insertEnd(struct Node** head_ref, int new_data) {\n    struct Node* new_node = (struct Node*)malloc(sizeof(struct Node));\n    struct Node* last = *head_ref;\n    new_node->data = new_data;\n    new_node->next = NULL;\n    if (*head_ref == NULL) {\n        *head_ref = new_node;\n        return;\n    }\n    while (last->next != NULL) {\n        last = last->next;\n    }\n    last->next = new_node;\n}\n\nint areSame(struct Node* a, struct Node* b) {\n    while (a != NULL && b != NULL) {\n        if (a->data != b->data) {\n            return 0; // Data is different\n        }\n        a = a->next;\n        b = b->next;\n    }\n    // If both are NULL, lists are same length and content\n    return (a == NULL && b == NULL);\n}\n\nint main() {\n    struct Node* head1 = NULL;\n    struct Node* head2 = NULL;\n    int data;\n\n    while (scanf("%d", &data) == 1 && data != -1) {\n        insertEnd(&head1, data);\n    }\n\n    while (scanf("%d", &data) == 1 && data != -1) {\n        insertEnd(&head2, data);\n    }\n\n    if (areSame(head1, head2)) {\n        printf("Lists are same");\n    } else {\n        printf("Lists are not same");\n    }\n\n    return 0;\n}	\N	2025-06-25 02:38:48.865+00	2025-06-25 02:19:56.959+00	published
2	1	Menu-Driven Singly Linked List 123	1	\N	Implement a complete menu-driven singly linked list program with insert, delete, display, and count operations.	c	medium	25	2025-07-02 02:19:55.435+00	Write a menu-driven program to implement a singly linked list with multiple operations. The program should display a menu and allow users to perform various operations until they choose to exit.\n\n**Required Operations:**\n1. Insert a node at the front of the linked list\n2. Display all nodes\n3. Delete the first node of the linked list\n4. Insert a node at the end of the linked list\n5. Delete the last node of the linked list\n6. Delete a node from specified position\n7. Count the number of nodes\n8. Exit\n\n**Menu Format:**\n```\n1. Insert at front\n2. Display all nodes\n3. Delete first node\n4. Insert at end\n5. Delete last node\n6. Delete from position\n7. Count nodes\n8. Exit\nEnter your choice:\n```\n\n**Implementation Details:**\n- Use `struct Node` with `int data` and `struct Node* next`\n- Handle empty list cases appropriately\n- For display: print nodes as `data1 -> data2 -> data3 -> NULL`\n- For empty list display: print `List is empty`\n- For position-based deletion, positions start from 1\n- Continue showing menu until user selects exit option\n\n**Example Interaction:**\n- Insert 10 at front → `Node inserted at front`\n- Display → `10 -> NULL`\n- Count → `Number of nodes: 1`	f	\N	\N	\N	\N	#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node * next;\n};\n\nint main() {\n    struct Node * head = NULL;\n    int choice, data, pos;\n\n    // TODO: Display menu\n    // TODO: Get user choice\n    // TODO: Implement switch case for all operations\n}	\N	\N	\N	\N	#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node * next;\n};\n\nvoid insertFront(struct Node ** head, int data) {\n    struct Node * newNode = (struct Node * ) malloc(sizeof(struct Node));\n    newNode -> data = data;\n    newNode -> next = * head;\n    * head = newNode;\n    printf("Node inserted at front\\n");\n}\n\nvoid display(struct Node * head) {\n    if (head == NULL) {\n        printf("List is empty\\n");\n        return;\n    }\n    struct Node * temp = head;\n    while (temp != NULL) {\n        printf("%d", temp -> data);\n        if (temp -> next != NULL) {\n            printf(" -> \\n\\n");\n        }\n        temp = temp -> next;\n    }\n    printf(" -> NULL\\n");\n}\n\nvoid deleteFirst(struct Node ** head) {\n    if ( * head == NULL) {\n        printf("List is empty\\n");\n        return;\n    }\n    struct Node * temp = * head;\n    * head = ( * head) -> next;\n    free(temp);\n    printf("First node deleted\\n");\n}\n\nvoid insertEnd(struct Node ** head, int data) {\n    struct Node * newNode = (struct Node * ) malloc(sizeof(struct Node));\n    newNode -> data = data;\n    newNode -> next = NULL;\n\n    if ( * head == NULL) {\n        * head = newNode;\n        printf("Node inserted at end\\n");\n        return;\n    }\n\n    struct Node * temp = * head;\n    while (temp -> next != NULL) {\n        temp = temp -> next;\n    }\n    temp -> next = newNode;\n    printf("Node inserted at end\\n");\n}\n\nvoid deleteLast(struct Node ** head) {\n    if ( * head == NULL) {\n        printf("List is empty\\n");\n        return;\n    }\n\n    if (( * head) -> next == NULL) {\n        free( * head);\n        * head = NULL;\n        printf("Last node deleted\\n");\n        return;\n    }\n\n    struct Node * temp = * head;\n    while (temp -> next -> next != NULL) {\n        temp = temp -> next;\n    }\n    free(temp -> next);\n    temp -> next = NULL;\n    printf("Last node deleted\\n");\n}\n\nvoid deletePosition(struct Node ** head, int pos) {\n    if ( * head == NULL) {\n        printf("List is empty\\n");\n        return;\n    }\n\n    if (pos == 1) {\n        deleteFirst(head);\n        return;\n    }\n\n    struct Node * temp = * head;\n    for (int i = 1; i < pos - 1 && temp != NULL; i++) {\n        temp = temp -> next;\n    }\n\n    if (temp == NULL || temp -> next == NULL) {\n        printf("Position not found\\n");\n        return;\n    }\n\n    struct Node * nodeToDelete = temp -> next;\n    temp -> next = nodeToDelete -> next;\n    free(nodeToDelete);\n    printf("Node deleted from position %d\\n", pos);\n}\n\nint countNodes(struct Node * head) {\n    int count = 0;\n    struct Node * temp = head;\n    while (temp != NULL) {\n        count++;\n        temp = temp -> next;\n    }\n    return count;\n}\n\nint main() {\n    struct Node * head = NULL;\n    int choice, data, pos;\n\n    while (1) {\n        scanf("%d", & choice);\n\n        switch (choice) {\n        case 1:\n            scanf("%d", & data);\n            insertFront( & head, data);\n            break;\n        case 2:\n            display(head);\n            break;\n        case 3:\n            deleteFirst( & head);\n            break;\n        case 4:\n            scanf("%d", & data);\n            insertEnd( & head, data);\n            break;\n        case 5:\n            deleteLast( & head);\n            break;\n        case 6:\n            printf("Enter position: \\n");\n            scanf("%d", & pos);\n            deletePosition( & head, pos);\n            break;\n        case 7:\n            printf("Number of nodes: %d", countNodes(head));\n            break;\n        case 8:\n            exit(0);\n        default:\n            printf("Invalid choice");\n        }\n    }\n\n    return 0;\n}	\N	2025-06-25 03:51:34.413+00	2025-06-25 02:19:55.512+00	published
1	1	Singly Linked List Node	1	\N	Write a C program to create a single node for a singly linked list, read its data using scanf, print the data, and free the memory.	c	easy	10	2025-07-02 02:19:53.441+00	Create a program to implement a single node for a singly linked list in C. The node should store an integer and a pointer to the next node. Read the integer using scanf, create the node dynamically, print its data, and free the memory.\n\n**Steps:**\n1. Define a `struct Node` with an integer `data` and a `next` pointer.\n2. Use `scanf` to read an integer from the user.\n3. Allocate memory for a node using `malloc`.\n4. Assign the scanned integer to the node's `data`. Set `next` to NULL.\n5. Print the node's data as `Node data: <value>`.\n6. Free the allocated memory using `free`.\n\n**Notes:**\n- Assume the input is a valid integer.\n- No need to check if `malloc` fails.\n- Use `int` for the node's data.\n\n**Example Inputs/Outputs:**\n- Input: `10` → Output: `Node data: 10`\n- Input: `-5` → Output: `Node data: -5`\n- Input: `0` → Output: `Node data: 0`	f	\N	\N	\N	\N	#include <stdio.h>\n\n// define the structure of Node\n\nvoid main() {\n    // initialize the node\n\n    // take input from user\n\n    // assign value to data member of node\n\n    // assign next member of node to NULL\n\n    // print the data member of node\n}	\N	\N	\N	\N	#include <stdio.h>\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node* next;\n};\n\nvoid main() {\n    struct Node* node = (struct Node*)malloc(sizeof(struct Node));\n\n    int val;\n    scanf("%d", &val);\n\n    node->data = val;\n\n    node->next = NULL;\n\n    printf("%d", node->data);\n    free(node);\n}	\N	2025-06-25 02:34:26.515+00	2025-06-25 02:19:53.518+00	published
4	1	Remove Duplicates from Sorted List	1	\N	Write a C program to remove duplicate nodes from a given sorted singly linked list.	c	hard	30	2025-07-02 02:19:58.323+00	Write a C program that removes duplicate elements from a sorted singly linked list. The list should be modified in-place, and the remaining elements should still be sorted.\n\n**Input Format:**\nThe program will read a sequence of sorted integers from standard input to build the linked list. A sentinel value of `-1` will signify the end of the input sequence.\n\n**Steps:**\n1.  Define the `struct Node` with an integer `data` and a pointer `next`.\n2.  Implement a helper function (e.g., `insertEnd`) to build the linked list from the input integers.\n3.  Implement the core function, `removeDuplicates(struct Node* head)`.\n4.  In this function, traverse the list with a pointer, let's call it `current`. The traversal should continue as long as `current` and `current->next` are not NULL.\n5.  Inside the loop, compare the data of the `current` node with the data of the `current->next` node.\n6.  If the data is the same, it's a duplicate. You need to bypass this duplicate node:\n    a. Create a temporary pointer to `current->next` (the node to be deleted).\n    b. Update `current->next` to point to the node *after* the duplicate (`temp->next`).\n    c. Free the memory of the temporary pointer.\n7.  **Important:** If you delete a node, do *not* advance the `current` pointer in that iteration. This allows you to check for multiple duplicates (e.g., 13 → 13 → 13).\n8.  If the data is different, it means there's no duplicate, so you can safely move to the next node by setting `current = current->next`.\n9.  Implement a `display` function to print the final list in the format `data1 -> data2 -> NULL`.\n\n**Example:**\n-   Input: `1 1 6 13 13 13 27 27 -1`\n-   Output: `1 -> 6 -> 13 -> 27 -> NULL`	f	\N	\N	\N	\N	#include <stdio.h>\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node* next;\n};\n\n\nint main() {\n    struct Node* head = NULL;\n    int data;\n\n    // remove duplicates\n\n    // display result\n\n    return 0;\n}	\N	\N	\N	\N	#include <stdio.h>\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node* next;\n};\n\nvoid insertEnd(struct Node** head_ref, int new_data) {\n    struct Node* new_node = (struct Node*)malloc(sizeof(struct Node));\n    new_node->data = new_data;\n    new_node->next = NULL;\n\n    if (*head_ref == NULL) {\n        *head_ref = new_node;\n        return;\n    }\n    struct Node* last = *head_ref;\n    while (last->next != NULL) {\n        last = last->next;\n    }\n    last->next = new_node;\n}\n\nvoid display(struct Node* node) {\n    if (node == NULL) {\n        printf("List is empty");\n        return;\n    }\n    while (node != NULL) {\n        printf("%d", node->data);\n        if (node->next != NULL) {\n            printf(" -> ");\n        }\n        node = node->next;\n    }\n    printf(" -> NULL");\n}\n\nvoid removeDuplicates(struct Node* head) {\n    struct Node* current = head;\n    if (current == NULL) return;\n\n    while (current->next != NULL) {\n        if (current->data == current->next->data) {\n            struct Node* temp = current->next;\n            current->next = temp->next;\n            free(temp);\n        } else {\n            current = current->next;\n        }\n    }\n}\n\nint main() {\n    struct Node* head = NULL;\n    int data;\n\n    while (scanf("%d", &data) == 1 && data != -1) {\n        insertEnd(&head, data);\n    }\n\n    removeDuplicates(head);\n\n    display(head);\n\n    return 0;\n}	\N	2025-06-25 02:37:45.568+00	2025-06-25 02:19:58.399+00	published
5	1	Create a UDF to add two numbers 	\N	\N	\N	c	easy	10	2025-06-25 06:00:00+00		\N	\N	\N	\N	\N	// include the standard input output lib\r\n\r\nvoid main() {\r\n  // declare the variables\r\n\r\n  // read the values from user\r\n\r\n  // calculate the sum\r\n\r\n  // print the sum\r\n}	\N	\N	\N	\N	void main(){\r\n\r\n}	\N	2025-06-26 12:57:22.951+00	2025-06-25 03:20:35.818+00	published
\.


--
-- Data for Name: assignments_blocks_action; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.assignments_blocks_action (_order, _parent_id, _path, id, action_type, action_selector, action_value, block_name) FROM stdin;
\.


--
-- Data for Name: assignments_blocks_assertion; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.assignments_blocks_assertion (_order, _parent_id, _path, id, assertion_type, assertion_selector, expected_value, expected_class, css_property, expected_css_value, expected_alert_text, block_name) FROM stdin;
\.


--
-- Data for Name: assignments_c_test_cases; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.assignments_c_test_cases (_order, _parent_id, id, title, input, expected_output, tolerance, is_hidden) FROM stdin;
1	1	685b5cc9e1277af85f2050c7	Positive Integer	10	10	0.001	f
2	1	685b5cc9e1277af85f2050c8	Negative Integer	-5	-5	0.001	f
3	1	685b5cc9e1277af85f2050c9	Zero	0	0	0.001	f
4	1	685b5cc9e1277af85f2050ca	Large Positive Integer	1000	1000	0.001	f
5	1	685b5cc9e1277af85f2050cb	Large Negative Integer	-1000	-1000	0.001	f
1	4	685b5ccee1277af85f2050ed	Provided Example Case	1 1 6 13 13 13 27 27 -1	1 -> 6 -> 13 -> 27 -> NULL	0.001	f
2	4	685b5ccee1277af85f2050ee	No Duplicates	1 2 3 4 5 -1	1 -> 2 -> 3 -> 4 -> 5 -> NULL	0.001	f
3	4	685b5ccee1277af85f2050ef	All Nodes are Duplicates	5 5 5 5 5 -1	5 -> NULL	0.001	f
4	4	685b5ccee1277af85f2050f0	Empty List	-1	List is empty	0.001	f
5	4	685b5ccee1277af85f2050f1	Duplicates at the End	10 20 30 30 -1	10 -> 20 -> 30 -> NULL	0.001	f
6	4	685b5ccee1277af85f2050f2	Duplicates at the Beginning	5 5 10 20 -1	5 -> 10 -> 20 -> NULL	0.001	f
1	3	685b5ccce1277af85f2050e1	Identical Lists	10 20 30 -1 10 20 30 -1	Lists are same	0.001	f
2	3	685b5ccce1277af85f2050e2	Different Data	10 20 30 -1 10 99 30 -1	Lists are not same	0.001	f
3	3	685b5ccce1277af85f2050e3	Different Length (First Shorter)	10 20 -1 10 20 30 -1	Lists are not same	0.001	f
4	3	685b5ccce1277af85f2050e4	Different Length (Second Shorter)	10 20 30 -1 10 20 -1	Lists are not same	0.001	f
5	3	685b5ccce1277af85f2050e5	Both Lists Empty	-1 -1	Lists are same	0.001	f
6	3	685b5ccce1277af85f2050e6	One List Empty	10 -1 -1	Lists are not same	0.001	f
7	3	685b5ccce1277af85f2050e7	Single Node Identical	5 -1 5 -1	Lists are same	0.001	f
1	2	685b5ccbe1277af85f2050d2	Insert Front and Delete First	1\n10\n3\n2\n8	Node inserted at front\nFirst node deleted\nList is empty	0.001	f
2	2	685b5ccbe1277af85f2050d3	Insert at End and Display	4\n10\n2\n8	Node inserted at end\n10 -> NULL	0.001	f
3	2	685b5ccbe1277af85f2050d4	Display Empty List	2\n8	List is empty	0.001	f
4	2	685b5ccbe1277af85f2050d5	Count Empty List	7\n8	Number of nodes: 0	0.001	f
5	2	685b5ccbe1277af85f2050d6	Multiple Insert Front and Count	1\n10\n1\n20\n2\n7\n8	Node inserted at front\nNode inserted at front\n20 -> 10 -> NULL\nNumber of nodes: 2	0.001	f
6	2	685b5ccbe1277af85f2050d7	Multiple Insert End	4\n10\n4\n20\n2\n8	Node inserted at end\nNode inserted at end\n10 -> 20 -> NULL	0.001	f
7	2	685b5ccbe1277af85f2050d8	Delete First from Empty List	3\n8	List is empty	0.001	f
8	2	685b5ccbe1277af85f2050d9	Delete Last from Empty List	5\n8	List is empty	0.001	f
9	2	685b5ccbe1277af85f2050da	Delete from Position	1\n10\n1\n20\n1\n30\n2\n6\n2\n2\n8	Node inserted at front\nNode inserted at front\nNode inserted at front\n30 -> 20 -> 10 -> NULL\nEnter position:\nNode deleted from position 2\n30 -> 10 -> NULL	0.001	f
10	2	685b5ccbe1277af85f2050db	Insert End and Delete Last Single Node	4\n10\n5\n2\n8	Node inserted at end\nLast node deleted\nList is empty	0.001	f
1	5	685b6d89c00a3949d984cf91	Sum of 5 and 10	5 10	15	\N	f
2	5	685d42ec21f772de1ebb3700	Sum of 11 and 22	11 22	33	\N	f
3	5	685d430321f772de1ebb3702	Sum of -89 and 2	-89 2	-87	\N	f
\.


--
-- Data for Name: assignments_hints; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.assignments_hints (_order, _parent_id, id, question, answer) FROM stdin;
1	1	685b5cc9e1277af85f2050c2	How do I read input from the user?	Use `scanf("%d", &variable)` to read an integer from the user.
2	1	685b5cc9e1277af85f2050c3	How do I create a node dynamically?	Use `malloc(sizeof(struct Node))` to allocate memory, set the `data` field, and make `next` NULL.
3	1	685b5cc9e1277af85f2050c4	How do I avoid memory leaks?	Use `free` to deallocate the node's memory after printing.
1	4	685b5ccee1277af85f2050e8	How do I check for a duplicate?	Since the list is sorted, you only need to compare a node with the one immediately following it (`current->data == current->next->data`).
2	4	685b5ccee1277af85f2050e9	What is the correct way to delete the node?	Store the node to be deleted (`current->next`) in a temporary variable, update `current->next` to `current->next->next`, and then `free` the temporary variable.
3	4	685b5ccee1277af85f2050ea	What if there are more than two duplicates in a row, like 5 -> 5 -> 5?	When you delete a duplicate, do not advance your main pointer (`current`). The loop will run again, comparing `current` with its new `next` node, handling consecutive duplicates correctly.
1	3	685b5ccce1277af85f2050dc	How should I approach the comparison logic?	Traverse both lists at the same time using two pointers. In each iteration, check if the data of the current nodes is equal. If not, they are different. The loop should continue as long as both pointers are not NULL.
2	3	685b5ccce1277af85f2050dd	What if the lists have different lengths?	After your traversal loop finishes, one pointer might be NULL while the other is not. If both pointers are not NULL at the same time, the lists have different lengths and are therefore not the same.
3	3	685b5ccce1277af85f2050de	How do I handle empty lists?	Your logic should naturally handle this. If both heads are NULL, the loop condition will be false, and the final check (are both pointers NULL?) will pass. If one is NULL and the other is not, it will correctly fail.
1	2	685b5ccbe1277af85f2050cc	How do I handle an empty list?	Check if head is NULL before performing operations. Initialize head to NULL at the start.
2	2	685b5ccbe1277af85f2050cd	How do I insert at the front?	Create new node, set its next to current head, then update head to point to new node.
3	2	685b5ccbe1277af85f2050ce	How do I insert at the end?	If list is empty, insert at front. Otherwise, traverse to last node and link the new node.
4	2	685b5ccbe1277af85f2050cf	How do I delete from a specific position?	Traverse to the position, keep track of previous node, then adjust the links and free the node.
\.


--
-- Data for Name: assignments_java_test_cases; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.assignments_java_test_cases (_order, _parent_id, id, title, input, expected_output, tolerance, is_hidden) FROM stdin;
\.


--
-- Data for Name: assignments_resources; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.assignments_resources (_order, _parent_id, id, title, url) FROM stdin;
1	1	685b5cc9e1277af85f2050c5	Singly Linked List in C - GeeksforGeeks	https://www.geeksforgeeks.org/singly-linked-list-tutorial
2	1	685b5cc9e1277af85f2050c6	Understanding Linked List (YouTube)	https://www.youtube.com/watch?v=VOpjAHCee7c
1	4	685b5ccee1277af85f2050eb	Remove duplicates from a sorted linked list - GeeksforGeeks	https://www.geeksforgeeks.org/remove-duplicates-from-a-sorted-linked-list/
2	4	685b5ccee1277af85f2050ec	Singly Linked List in C (Tutorial)	https://www.programiz.com/dsa/singly-linked-list
1	3	685b5ccce1277af85f2050df	Singly Linked List in C - GeeksforGeeks	https://www.geeksforgeeks.org/singly-linked-list-tutorial
2	3	685b5ccce1277af85f2050e0	Identical Linked Lists - GeeksforGeeks	https://www.geeksforgeeks.org/identical-linked-lists/
1	2	685b5ccbe1277af85f2050d0	Singly Linked List in C - GeeksforGeeks	https://www.geeksforgeeks.org/singly-linked-list-tutorial
2	2	685b5ccbe1277af85f2050d1	Understanding Linked List (YouTube)	https://www.youtube.com/watch?v=VOpjAHCee7c
\.


--
-- Data for Name: assignments_test_suites; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.assignments_test_suites (_order, _parent_id, id, points, visibility) FROM stdin;
\.


--
-- Data for Name: batches; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.batches (id, batch_id, updated_at, created_at) FROM stdin;
1	CSE-3A-A5	2025-06-25 02:19:51.804+00	2025-06-25 02:19:51.807+00
2	CSE-3A-A6	2025-06-25 02:19:52.477+00	2025-06-25 02:19:52.483+00
\.


--
-- Data for Name: batches_rels; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.batches_rels (id, "order", parent_id, path, users_id) FROM stdin;
1	1	1	students	26
2	2	1	students	27
3	3	1	students	28
4	4	1	students	29
5	5	1	students	30
6	6	1	students	31
7	7	1	students	32
8	8	1	students	33
9	9	1	students	34
10	10	1	students	35
11	11	1	students	36
12	12	1	students	37
13	13	1	students	38
14	14	1	students	39
15	15	1	students	40
16	16	1	students	41
17	17	1	students	42
18	18	1	students	43
19	19	1	students	44
20	20	1	students	45
21	1	2	students	46
22	2	2	students	47
23	3	2	students	48
24	4	2	students	49
25	5	2	students	50
26	6	2	students	51
27	7	2	students	52
28	8	2	students	53
29	9	2	students	54
30	10	2	students	55
31	11	2	students	56
32	12	2	students	57
33	13	2	students	58
34	14	2	students	59
35	15	2	students	60
36	16	2	students	61
37	17	2	students	62
38	18	2	students	63
39	19	2	students	64
40	20	2	students	65
\.


--
-- Data for Name: enrollments; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.enrollments (id, module_id, batch_id, access_start, access_end, updated_at, created_at) FROM stdin;
\.


--
-- Data for Name: modules; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.modules (id, title, updated_at, created_at) FROM stdin;
1	Module 1	2025-06-25 02:19:59.781+00	2025-06-25 02:19:59.785+00
2	Module 2	2025-06-25 02:20:00.501+00	2025-06-25 02:20:00.506+00
3	Module-3	2025-06-25 03:56:15.109+00	2025-06-25 03:56:15.14+00
\.


--
-- Data for Name: modules_rels; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.modules_rels (id, "order", parent_id, path, assignments_id) FROM stdin;
1	1	1	assignments	1
2	2	1	assignments	2
3	3	1	assignments	3
4	4	1	assignments	4
\.


--
-- Data for Name: payload_jobs; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.payload_jobs (id, input, completed_at, total_tried, has_error, error, task_slug, queue, wait_until, processing, updated_at, created_at) FROM stdin;
\.


--
-- Data for Name: payload_jobs_log; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.payload_jobs_log (_order, _parent_id, id, executed_at, completed_at, task_slug, task_i_d, input, output, state, error) FROM stdin;
\.


--
-- Data for Name: payload_locked_documents; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.payload_locked_documents (id, global_slug, updated_at, created_at) FROM stdin;
\.


--
-- Data for Name: payload_locked_documents_rels; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.payload_locked_documents_rels (id, "order", parent_id, path, users_id, tenants_id, batches_id, enrollments_id, subjects_id, modules_id, assignments_id, submissions_id, payload_jobs_id) FROM stdin;
\.


--
-- Data for Name: payload_migrations; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.payload_migrations (id, name, batch, updated_at, created_at) FROM stdin;
1	20250624_151436	1	2025-06-25 02:15:14.599+00	2025-06-25 02:15:14.105+00
2	20250624_172434	1	2025-06-25 02:15:14.912+00	2025-06-25 02:15:14.916+00
3	dev	-1	2025-06-26 23:43:26.596+00	2025-06-25 06:57:09.681+00
\.


--
-- Data for Name: payload_preferences; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.payload_preferences (id, key, value, updated_at, created_at) FROM stdin;
3	subjects-list	{"preset": null}	2025-06-25 02:20:44.438+00	2025-06-25 02:20:44.519+00
6	collection-assignments-4	{"fields": {"_index-1": {"tabIndex": 2}}}	2025-06-25 02:36:24.1+00	2025-06-25 02:36:20.328+00
7	collection-assignments-3	{"fields": {"_index-1": {"tabIndex": 2}}}	2025-06-25 02:38:46.167+00	2025-06-25 02:38:45.779+00
5	collection-assignments-2	{"fields": {"_index-1": {"tabIndex": 2}}}	2025-06-25 02:39:56.695+00	2025-06-25 02:31:31.398+00
9	enrollments-list	{"preset": null}	2025-06-25 03:08:48.862+00	2025-06-25 03:08:48.936+00
10	modules-list	{"preset": null}	2025-06-25 03:08:53.496+00	2025-06-25 03:08:53.57+00
11	batches-list	{"preset": null}	2025-06-25 03:08:58.194+00	2025-06-25 03:08:58.267+00
13	collection-submissions-5	{"fields": {"_index-1": {"tabIndex": 0}}}	2025-06-25 03:30:11.19+00	2025-06-25 03:30:09.757+00
14	submissions-list	{"limit": 10, "preset": null, "columns": [{"active": true, "accessor": "id"}, {"active": true, "accessor": "student"}, {"active": true, "accessor": "score"}, {"active": true, "accessor": "status"}, {"active": true, "accessor": "updatedAt"}]}	2025-06-25 03:49:23.262+00	2025-06-25 03:48:28.871+00
15	collection-assignments-2	{"fields": {"_index-1": {"tabIndex": 0}}}	2025-06-25 03:50:13.028+00	2025-06-25 03:49:23.18+00
8	assignments-list	{"limit": 10, "preset": null}	2025-06-25 03:52:22.421+00	2025-06-25 03:03:07.83+00
17	enrollments-list	{"preset": null}	2025-06-25 03:53:01.735+00	2025-06-25 03:53:01.84+00
18	batches-list	{"preset": null}	2025-06-25 03:53:06.306+00	2025-06-25 03:53:06.407+00
19	subjects-list	{"preset": null}	2025-06-25 03:53:26.072+00	2025-06-25 03:53:26.178+00
20	modules-list	{"preset": null}	2025-06-25 03:53:40.66+00	2025-06-25 03:53:40.766+00
22	tenants-list	{"preset": null}	2025-06-25 03:57:32.876+00	2025-06-25 03:57:32.983+00
16	collection-submissions-8	{"fields": {"_index-1": {"tabIndex": 1}}}	2025-06-25 04:06:43.832+00	2025-06-25 03:49:48.385+00
21	nav	{"open": true}	2025-06-25 04:06:59.224+00	2025-06-25 03:56:40.098+00
23	collection-submissions-12	{"fields": {"_index-1": {"tabIndex": 1}}}	2025-06-25 04:07:29.537+00	2025-06-25 04:07:29.659+00
4	users-list	{"limit": 10, "preset": null}	2025-06-25 04:36:49.849+00	2025-06-25 02:20:52.32+00
24	collection-submissions-8	{"fields": {"_index-1": {"tabIndex": 1}}}	2025-06-25 04:37:21.391+00	2025-06-25 04:37:21.472+00
2	submissions-list	{"limit": 10, "preset": null, "columns": [{"active": true, "accessor": "id"}, {"active": true, "accessor": "student"}, {"active": true, "accessor": "score"}, {"active": true, "accessor": "status"}, {"active": true, "accessor": "updatedAt"}]}	2025-06-25 07:07:50.234+00	2025-06-25 02:20:38.698+00
26	collection-assignments-6	{"fields": {"_index-1": {"tabIndex": 0}}}	2025-06-25 07:08:02.997+00	2025-06-25 07:07:57.304+00
1	assignments-list	{"limit": 10, "preset": null}	2025-06-26 04:53:32.836+00	2025-06-25 02:20:32.1+00
27	assignments-list	{"preset": null}	2025-06-26 04:55:39.423+00	2025-06-26 04:55:39.463+00
28	collection-submissions-7	{"fields": {"_index-1": {"tabIndex": 2}}}	2025-06-26 12:53:43.081+00	2025-06-26 12:53:41.1+00
12	collection-assignments-5	{"fields": {"_index-1": {"tabIndex": 0}, "cTestCases": {"collapsed": ["685b6d89c00a3949d984cf91", "685d42ec21f772de1ebb3700", "685d430321f772de1ebb3702"]}}}	2025-06-26 15:53:01.06+00	2025-06-25 03:20:40.838+00
\.


--
-- Data for Name: payload_preferences_rels; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.payload_preferences_rels (id, "order", parent_id, path, users_id) FROM stdin;
3	\N	3	user	1
8	\N	6	user	1
10	\N	7	user	1
11	\N	5	user	1
14	\N	9	user	1
15	\N	10	user	1
16	\N	11	user	1
29	\N	13	user	1
36	\N	14	user	2
41	\N	15	user	2
42	\N	8	user	2
43	\N	17	user	2
44	\N	18	user	2
45	\N	19	user	2
46	\N	20	user	2
49	\N	22	user	2
54	\N	16	user	2
56	\N	21	user	2
57	\N	23	user	2
58	\N	4	user	1
59	\N	24	user	1
66	\N	2	user	1
72	\N	26	user	1
73	\N	1	user	1
74	\N	27	user	66
86	\N	28	user	1
101	\N	12	user	1
\.


--
-- Data for Name: subjects; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.subjects (id, tenant_id, name, description, updated_at, created_at) FROM stdin;
1	1	Data Structures	Explore efficient methods to organize and store data, essential for optimizing algorithms and enhancing software performance.	2025-06-25 02:19:53.148+00	2025-06-25 02:19:53.153+00
\.


--
-- Data for Name: submissions; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.submissions (id, tenant_id, student_id, assignment_id, status, is_locked, solution_code_html, solution_code_css, solution_code_js, solution_code_java, solution_code_c, score, passed_test_cases, failed_test_cases, feedback, updated_at, created_at) FROM stdin;
3	1	16	1	review	f					#include <stdio.h>\n\n// define the structure of Node\n\nvoid main() {\n   int a,b,c;\n   printf("Enter a num: ");\n   scanf("%d",&a);\n   scanf("%d",&b);\n   scanf("%d",&c);\n   printf("%d",a*b*c*5);\n}	0	0	5	\N	2025-06-25 03:16:31.053+00	2025-06-25 03:16:30.886+00
4	1	8	1	review	f					#include <stdio.h>\n\n// define the structure of Node\nstruct Node{\n    int data;\n    struct Node* next;\n};\n\nvoid main() {\n    // initialize the node\n    struct Node* newNode = (struct Node*) malloc(sizeof(struct Node));\n    // take input from user\n    int n;\n    scanf("%d",&n);\n    // assign value to data member of node\n    newNode->data = n;\n    // assign next member of node to NULL\n    newNode->next = NULL;\n    // print the data member of node\n    printf("%d",newNode->data);\n\n    free(newNode);\n}	100	5	0	\N	2025-06-25 03:23:49.833+00	2025-06-25 03:23:49.66+00
7	1	12	5	review	f					#include<stdio.h>\r\n// int sum(int a, int b);\r\nint sum(int a, int b){\r\n  int c = a+b;\r\n  return c;\r\n}\r\nvoid main(){\r\n  int a,b;\r\n  // printf("Enter two numbers : ");\r\n  scanf("%d %d",&a,&b);\r\n  printf("Sum using UDF : %d",sum(a,b));\r\n}	0	0	1	\N	2025-06-25 03:37:01.927+00	2025-06-25 03:37:01.766+00
8	1	14	2	review	f					#include <stdio.h>\n#include <stdlib.h>\n\nstruct node {\n    int info;\n    struct node *link;\n};\n\nstruct node *first = NULL;\n\nvoid insertAtFirst(int x){\n    struct node *newNode = (struct node *)malloc(sizeof(struct node));\n\n    newNode -> info = x;\n    newNode -> link = first;\n    first = newNode;\n}\n\nvoid insertAtLast(int x){\n    struct node *newNode = (struct node *)malloc(sizeof(struct node));\n\n    if(first == NULL){\n        first = newNode;\n        return;\n    }\n    newNode -> info = x;\n    newNode -> link = NULL;\n\n    struct node *save = first;\n    while(save -> link != NULL){\n        save = save ->link;\n    }\n    save -> link =newNode;\n}\n\nvoid display(){\n    if(first == NULL){\n        printf("Empty");\n        return;\n    }\n    struct node *save = first;\n    while(save != NULL){\n        printf(" %d -> ", save -> info);\n        save = save->link;\n    }\n    printf("NULL \\n");\n}\n\nint main() {\n   int choice, x;\n    {\n        printf("Menu\\n");\n        printf("1.insertAtFirst\\n 2.insertAtLast\\n 3.Display\\n 4.Exit\\n");\n\n        printf("Enter your choice");\n        scanf("%d", &choice);\n\n        switch(choice){\n            case 1:\n                printf("Enter the data: ");\n                scanf("%d", &x);\n                insertAtFirst(x);\n                break;\n            \n            case 2:\n                printf("Enter data:");\n                scanf("%d", &x);\n                insertAtLast(x);\n                break;\n\n            case 3:\n                printf("List is: ");\n                display();\n                break;\n\n            case 4:\n                printf("Exiting..");\n                return 0;\n\n            default:\n                printf("Invalid");\n                break;\n        }\n    }\n    return 0;\n}	0	0	10	\N	2025-06-25 03:55:25.636+00	2025-06-25 03:43:35.016+00
9	1	11	2	review	f					#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node * next;\n};\n\nstruct Node * head = NULL;\n\n \nvoid insertAtfirst(){\n    int n;\n    printf("enter value");\n    scanf("%d",&n);\n    struct Node *newNode = (struct Node*)malloc(sizeof(struct Node));\n    newNode-> data = n;\n    newNode->next = head;\n    head = newNode;\n        \n}\n\nvoid Display(){\n    struct Node *save = head;\n    while(save!= NULL){\n        printf("%d -> ",save-> data);\n        save = save->next;\n    }\n    printf("NULL\\n");\n}\n\nvoid insertAtEnd(){\n    struct Node *newNode = (struct Node *)malloc(sizeof(struct Node));\n    struct Node *save = head;\n    int n;\n    printf("enter number ");\n    scanf("%d",&n);\n    newNode->data = n;\n    while(save->next!= NULL){\n        save = save->next;\n    }\n    save->next = newNode;\n    newNode->next = NULL;\n}\n\nvoid deleteNode(){\n    \n}\n\nint main() {\n    int choice, data, pos;\n    insertAtfirst();\n    insertAtEnd();\n    Display();\n   \n    // TODO: Display menu\n    // TODO: Get user choice\n    // TODO: Implement switch case for all operations\n}	0	0	10	\N	2025-06-25 03:59:24.041+00	2025-06-25 03:59:23.885+00
10	1	7	2	review	f					#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node * next;\n};\nstruct Node* head=NULL;\nvoid insertAtfirst(){\n    int n;\n    printf("enter value");\n    scanf("%d",&n);\n    struct Node *newNode=(struct Node*)malloc(sizeof(struct Node));\n    newNode->data=n;\n    newNode->next=head;\n    head=newNode;\n}\nvoid Display(){\n    struct Node *save= head;\n    while(save!=NULL){\n        printf("%d",save->data);\n        save=save->next;\n    }\n    printf("NULL\\n");\n}\nvoid InsertATEnd(){\n    struct Node *newNode=(struct Node*)malloc(sizeof(struct Node));\n   struct Node *save=NULL;\nint n;\nprintf("enter value");\nscanf()\n    While(save->next!= NULL){\n        save=save->next;\n    }\n    save->next=newNode;\n    newNode->NULL;\n}\nint main() {\n    struct Node * head = NULL;\n    int choice, data, pos;\n     insertAtfirst();\n     Display();\n    // TODO: Display menu\n    // TODO: Get user choice\n    // TODO: Implement switch case for all operations\n}	0	0	10	\N	2025-06-25 04:01:16.826+00	2025-06-25 04:01:05.237+00
11	1	5	2	review	f					#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node * next;\n};\n\nstruct Node* insertAtFirst(struct Node* head, int val){\n    struct Node* newNode = (struct Node*)malloc(sizeof(struct Node));\n    newNode->data = val;\n    newNode->next = head;\n    head = newNode;\n\n    return head;\n}\n\nstruct Node* deleteAtFirst(struct Node* head){\n    if(head == NULL) return head;\n    struct Node* save = head;\n    head = head->next;\n    free(save);\n\n    return head;\n}\n\nstruct Node* insertAtLast(struct Node* head, int val){\n    struct Node* newNode = (struct Node*)malloc(sizeof(struct Node));\n    newNode->data = val;\n    newNode->next = NULL;\n    if(head == NULL) return newNode;\n    struct Node* save = head;\n    while(save->next != NULL){\n        save = save->next;\n    }\n    save->next = newNode;\n    return head;\n}\n\nstruct Node* deleteAtLast(struct Node* head){\n    if(head == NULL || head->next == NULL) return NULL;\n    struct Node* save = head;\n    while(save->next->next != NULL){\n        save = save->next;\n    }\n    struct node* toDelete = save->next; \n    save->next = NULL;\n    free(toDelete);\n    return head;\n}\n\nvoid display(struct Node* head){\n    if(head == NULL){\n        printf("List is empty");\n        return;\n    }\n\n    struct Node* save = head;\n    while(save!=NULL){\n        printf("%d->",save->data);\n        save = save->next;\n    }\n    printf("NULL");\n}\n\n\nint main() {\n    struct Node * head = NULL;\n    int choice, data, pos;\n    while(1){\n     scanf("%d", &choice);\n     switch(choice){\n        case 1:\n            scanf("%d",&data);\n            head = insertAtFirst(head,data);\n            break;\n        case 2:\n            head = deleteAtFirst(head);\n            break;\n        case 3:\n        display(head);\n            break;\n        default:\n            return;\n     }   \n    }\n    // TODO: Display menu\n    // TODO: Get user choice\n    // TODO: Implement switch case for all operations\n}	10	1	9	\N	2025-06-25 04:02:15.894+00	2025-06-25 04:02:15.732+00
12	1	3	2	review	f					#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int data;\n    struct Node * link;\n};\n\nstruct Node* first=NULL;\n\nvoid insertAtFirst(int value){\n    struct Node* newNode = (struct Node*)malloc(sizeof(struct Node));\n    newNode->data=value;\n    newNode->link=first;\n    first=newNode;\n}\n\nvoid insertAtLast(int value){\n    struct Node* newNode = (struct Node*)malloc(sizeof(struct Node));\n    newNode->data=value;\n    newNode->link=NULL;\n\n    if(first==NULL){\n        first=newNode;\n    }\n    else{\n        struct Node* save=first;\n        while(save->link!=NULL){\n            save=save->link;\n        }\n        save->link=newNode;\n    }\n}\n\nvoid deleteAtFirst(){\n    if(first==NULL){\n        printf("List is Empty");\n        return 0;\n    }\n\n    struct Node* temp=first;\n    first=first->link;\n\n    free(temp);\n}\n\nvoid display(){\n    if(first==NULL){\n        printf("List Empty");\n        return 0;\n    }\n\n    struct Node* save=first;\n    while(save!=NULL){\n        printf("%d -> ", save->data);\n        save=save->link;\n    }\n    printf("NULL");\n}\n\nint main() {\n    struct Node * head = NULL;\n    int choice, data, pos;\n\n    // TODO: Display menu\n    for(int i=1; i<=5; i++){\n        int x;\n        scanf("%d", &x);\n\n        insertAtLast(x);\n    }\n\n    deleteAtFirst();\n    deleteAtFirst();\n\n    display();\n    // TODO: Get user choice\n    // TODO: Implement switch case for all operations\n}	0	0	10	\N	2025-06-25 04:04:23.964+00	2025-06-25 04:04:23.802+00
5	1	1	5	review	f					void main() {\r\n  int a=0, b=0, c=0;\r\n  scanf("%d %d %d", &a, &b, &c);\r\n  printf("%d", a+b+c);\r\n}\r\n	100	3	0	\N	2025-06-26 14:47:23.499+00	2025-06-25 03:25:35.323+00
13	1	6	2	review	f					#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int info;\n    struct Node * link;\n};\n\nstruct Node *first = NULL;\n\nvoid insertAtFirst(int x){\n    struct Node *newNode = (struct Node *)malloc(sizeof(struct Node));\n    newNode->link = NULL;\n    newNode->info = x;\n    first = newNode;\n}\n\nvoid insertAtLast(int x){\n    struct Node *newNode = (struct Node *)malloc(sizeof(struct Node));\n    if(first == NULL){\n        newNode->link = NULL;\n        first = newNode;\n        return ;\n    }\n    else{\n        struct Node *save = first;\n        while(save->link != NULL){\n            save = save->link;\n        }\n        save->info = x;\n        free(save);\n    }\n}\n\nvoid deleteNode(int x){\n    if(first == NULL){\n        printf("linkedlist is empty");\n        return;\n    }\n    else{\n        if(first->info == x){\n            first = first->link;\n            return ;\n        }\n        struct Node *save = first;\n        struct Node *pred = save;\n\n        while(save->info != x && save != NULL){\n            pred = save;\n            save = save->link;\n        }\n        if(save->info != x){\n            printf("node not found");\n        }\n        pred->link = save->link;\n        free(save);\n        free(pred);\n    }\n}\nvoid display(){\n    if(first == NULL){\n        printf("Empty \\n");\n        return;\n    }\n    struct Node *save = first;\n    while(save != NULL){\n        printf("%d", save->info);\n        save = save->link;\n    }\n    free(save);\n}\n\n\nint main() {\n\n    int choice=1, x,data, pos;\n    {\n        scanf("%d", &choice);\n        switch(choice){\n            case 1:\n            printf("data add:");\n            scanf("%d", &x);\n            insertAtFirst(x);\n            break;\n\n            case 2:\n            printf("data add:");\n            scanf("%d", &x);\n            insertAtLast(x);\n            break;\n\n            case 3:\n            printf("display: ");\n            display();\n            break;\n\n            case 4:\n            return 0;\n        }\n    }\n\n   \n}	0	0	10	\N	2025-06-25 04:09:02.596+00	2025-06-25 04:09:02.433+00
14	1	4	2	review	f					#include <stdio.h>\n\n#include <stdlib.h>\n\nstruct Node {\n    int info;\n    struct Node * link;\n};\n\nstruct Node * head = NULL;\n\nvoid insertAtFront(int data){\n    struct Node* newNode = (struct Node*)malloc(sizeof(struct Node));\n    newNode->info = data;\n    newNode->link = NULL;\n\n    if(head==NULL){\n        head = newNode;\n    }\n    newNode->link = head;\n    head = newNode;\n}\n\nvoid insertAtEnd(int data){\n    struct Node* newNode = (struct Node*)malloc((sizeof(struct Node)));\n    newNode->info = data;\n    newNode->link = NULL;\n\n    struct Node* save = head;\n\n    if(head==NULL){\n        head=save=newNode;\n    }\n\n    while(save!=NULL){\n        save = save->link;\n    }\n\n    save->link = newNode;\n}\n\nvoid display(){\n    struct Node* save =head;\n    while(save!=NULL){\n        printf("%d ->",save->info);\n        save = save->link;\n    }\n}\n\nvoid deleteAtFirst(){\n    struct Node* save = head;\n    if(head==NULL){\n        printf("List is empty");\n    }\n    head = head->link;\n    free(save);\n}\n\nvoid deleteAtLast(){\n    struct Node* pred = head;\n    struct Node* save = head->link;\n\n    if(head==NULL){\n        printf("List is empty");\n    }\n    while(save!=NULL){\n        pred = save;\n        save = save->link;\n    }\n    pred->link = NULL;\n\n    free(save);\n}\n\nvoid countNode(){\n    struct Node* save = head;\n    int count = 0;\n    while(save!=NULL){\n        count++;\n        save = save->link;\n    }\n    printf("Num of nodes = %d",count);\n}\n\nvoid main() {\n    \n    int choice, data, pos;\n\n    {\n        printf("1 for insertAtFront,2 for insertAtEnd,3 for deleteAtFirst,4 for deleteAtLast,5 for countNode,6 for display,0 for exit");\n        scanf("%d",&choice);\n\n        switch(choice){\n            case 1 :\n                printf("Enter data:");\n                scanf("%d",&data);\n                insertAtFront(data);\n                break;\n            case 2 :\n                printf("Enter data:");\n                scanf("%d",&data);\n                insertAtEnd(data);\n                break;\n            case 3:\n                deleteAtFirst();\n                break;\n            case 4:\n                deleteAtLast();\n                break;\n            case 5:\n                countNode();\n                break;\n            case 6:\n                display();\n                break;\n            default:\n                printf("invalid choice");\n                break;\n        }\n\n    }\n    // TODO: Display menu\n    // TODO: Get user choice\n    // TODO: Implement switch case for all operations\n}	0	0	10	\N	2025-06-25 04:10:12.374+00	2025-06-25 04:10:12.216+00
\.


--
-- Data for Name: tenants; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.tenants (id, name, domain, slug, updated_at, created_at) FROM stdin;
1	Darshan University	darshan.ac.in	darshan-university	2025-06-25 02:18:40.504+00	2025-06-25 02:18:40.499+00
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.users (id, username, full_name, division, updated_at, created_at, email, reset_password_token, reset_password_expiration, salt, hash, login_attempts, lock_until) FROM stdin;
2	1D1	CSE Dummy DS1	CSE-3A	2025-06-25 02:18:43.016+00	2025-06-25 02:18:42.479+00	<EMAIL>	\N	\N	441bc6e0c3e0be0112f3571deaab10f3ee9dcdda14866be53a117a20c4b69d84	64eada9902dcef47db2c8bbf9394a9a0c18304d45839f447fa3bf92791e66e5cc37189117f421f45f375b83ceb48b8b56a6a416df04d82840433a49ce9d0796a86d823a83a6fbfcb69b9b0b4f6e1ca1edd06a776e52bd723d59a8deed45d1aed28a25f67bcfe2712922d81ea057bc1a50031fe8c7f1b46dba6f88d4e550269173e55ba4268a52a819f5e7c55cbd2ef81adc2cc36a2916bc2af6c0b3633de514fbccbdeb8da8faf982923ddef4ecf9a568345fbd21ee5482f17ea929007ac8d33da18c334f8c3261e7bb5d256abf67c438413756fa62812a05783c057d47dcfd703c4466b90b9ad236a78718ead6391da1598360c78983953ee33b43829c487f68775920996306d12026475094263466873768e1e797c0eb227065fb0c84790a863aed88f0c53944828e07fe79f4049f80541623a31879b0998ffb6c102aa927b47b342d5653055ed670a5f0804004135a0cee8cf24b1f450c42ace01fde7cdae4db82760a0114c012e818c0e90793ed2505424657d50f50c9af990c32a0e6c31599e15796890660d7e871b5feb0e66ed63755f360385f5dac3e302b0b82e9a171415196ca370e7a92e52dcfa893e98f2feb7c0f438cfbc0bde48118031f86f43bbba5df2fe19dcdab4f7d69b8e1e3ebef26bfee2c43a57662083d41e92cd256585cbbae47e0b52ff47616490dc18862f578d5af8fecc88f61633221874206b5a	0	\N
3	1D2	CSE Dummy DS2	CSE-3A	2025-06-25 02:18:44.105+00	2025-06-25 02:18:43.606+00	<EMAIL>	\N	\N	f5f36f93809c873f4d5e4826e7a2700f84529d7da61f435bb3f78688d19559cc	f6d2c65971ffa2b07b4da6c9e6f064e0e555ddedb6dbc916b6b535d115995e70df8d62b87eb5e4a5fafe5eeb97abac2656ec348c5eb82cdc18c67e451560cf8e89dbc07a2e25babe46f10be028afabf6b7d773e3f419fc2dcbef5130c30c13dd847feeea4e56797752901cc5dbc42ad45d48141898b9b4443462bc67c3816d2d955a2b1c4e3e33e154e6b9a65d2755f20dae688f8d86a7ec69ece7d7e5ea5892c4645551e517db83ac5d92d0b2d9cdefa955ef09f7e6efe85fd53509f4c7e3fd75ec5fa1ccd81abdf94fd6848a8b97ecb35ea3068b01919a72956fb3465e2a3589b4b52999509f1ab5cafe474c96b293ea154028b5cbdee57e41cc86d1fe39ee46a5f96336b50f4715dc778cf8a88871c16370d8a38942ebe8fb381db8c59cfbcf55b8d55d8c79f455c5f0f5e926a103ca671b4c15a1297783d62c17b103c1b4cdce98dcf29474cc624b2084f185d9eddc645144d94afaec1d46a07c1933374cfe4a22c3d37fff982bae6d977e8f847ba7607767576fa2ff312422320967a6fac16ddba5579df25138e354eca5a90e24cb70c0c621d3590f3fd040c9ffeca192cfc6c707248b2f3a816c29f88de25adc1ef51bccbeb7c84ae9ddd2c47527f2ae626a139c2a1ab98da900547f870590dea0e3c92029d87128b8657bc94a121b6303f2a5da2f388654038080b8fedd24b7f1010f9e1df0ea11bc38749ce19f4f17	0	\N
4	1D3	CSE Dummy DS3	CSE-3A	2025-06-25 02:18:45.19+00	2025-06-25 02:18:44.695+00	<EMAIL>	\N	\N	a3d44499e5a5078e6831965231fbb32ca2aa71b1bc9c5026843c1f3c47b730cd	a7b5bfa2bf4b90cc06711f2b4262adb194132cb5d81900f46229e00bf3710998633c3deaa99b3f66cbb00c5ca46fc26f1800c2719c7c5f0a5c9be49f72e01decfa76400ed9d290cb212bf52f030f6511819a61b8d55c4e096f740fd575eab6cebee73499af2b3d3e5de075024478e9cb01cd44c3025b7b7db407b5af91fcebc0b8853d8b19aeba8299f27591683d60948a0c2c362410de1178d303d4e9fd9bdfcd64a49e37a0b44eeab2f0db221403a3a2afa0ed188b1a2b278c35301efd30d7d2ec85902df6d32377d8fa5b1c277553568b5a536be577fb5cb841e6df48eece968e1b4c875cf64e523ee077abad3e1aef9a831f2d447434eb8220a83e71ad2ce26fbf0f30ba0a565e965d742264336327da68aa5ea9259cda91eee4ee9b660ec3432ac3af2184229c29b04c4c612a212975f1a4144d080a4531e6f8cb41e19af8105ece87cde4960247bcf88f6c81c4ed9d75468d95f6db3fe21974074952bd121f8016d68f0778bb7ac1309085628d4716bc1b6c921d55fd2731efab2344467e453e60e5cac5634069d0d99feab7e1f531be274f5a18992143a752645b9737498567b3df6a6abc09fa2b9cd27f763058d3b094f5e7c301cc1e6aa821439362378a60f05fc21a5dd683320e78c7a08aa8bbb9a55206b3e85f4bfa451038d739301cb3abe6461b33a1cea4fbc5b1317d0093b948b4c29d0977dabea78bf1ac9c	0	\N
5	1D4	CSE Dummy DS4	CSE-3A	2025-06-25 02:18:46.282+00	2025-06-25 02:18:45.78+00	<EMAIL>	\N	\N	4f2f95b921df8df97b5fcaa8965b2290c4e98a67ce23e05ed1b3df70ae289626	98b5cb8b1492b46664d731be50beb2b161018e1d3c897277b9d5c54241c6545d13edf948d8bcd04ac6dcc41672d006d118b8851c7f3c0b6082680daf5f24d7222efb3ac448836f3af19019333317f8ca468a4db0d65ae7fa57087cdc1af4d8d05021c4fa41a2bf8b84fec4c53aa8f25808b83a673f6d1a39037b2f53917b2a99cf204aafcf834b2d1eee66cbe13c27c6d10a1b01977c9923812b0b0cc5615e1b62d8032f326b318eb6716c836362a36ea28cf07cf5cfcea5f9e4a0dc7f1e928ca6444f61a60032cbcac74e57a784ffa33a446420f0b75ba1030ccc539e14d9b28917e00e5ec4a37588ce25376640b2be07bc3568ba69c9d59af91c33443b71ae1c36bc9b975460ed80acf6722894b786f87408a80b66aba0cf8ab9aed5e4854248a2b0dd2eb3b5eeb41cdc8ed2e576c027384a6b4c65931442d7681ac1903dc6944650f16832c92f9c24445672d94b330393b1d7e2bab425d3d1f9ecc1e1beb7a066208f1b02fc48c1e4b34e9b54010f33bd2986dda6bcd8a9ab128b65554cb4c305029f96c6658a1c4ded92e2556566320b304d0c9b3b2c4931236c28ec62d25f57b6cf9b1194dd744b8b0b98075338e8c07677808078dbfef64cf2bbe9bb8abaabb3c568636f1eaf56462e6be1c83768733cfe0c11c7aee9f97aee091ddd0a6d0798ef02420160d2f601c2d796667bc49236c71ab7bdc4ad0734a8e8b44abe	0	\N
6	1D5	CSE Dummy DS5	CSE-3A	2025-06-25 02:18:47.364+00	2025-06-25 02:18:46.869+00	<EMAIL>	\N	\N	d4a4a4596a88cff2aafbbfceac0f5177e5b4dfd904d9e0c467240cbb8c74f00d	306ec1c5da546705eacad0c26123fdda08892f9f64cec1075eeaa5dcd3df4caaaaa9798f2f7f5ca8e1377c28c872e5fcffb6dc5fdce79198efb9e6d49a6115e57efc44b02081ea40818faab90cbdeccd4aa4b83df5d770738731cb547e508d15da1f5b311ebe2bd5f56280eab6d399447fd75ec6b43e744f29b1888d77a0389d0552e3d8ea02af8663f8a3ce33995f592b29ab8bb4b759b474d2188ccb0ba5620f08d91771f1048789496759bbe76753eb3dcbb96fea9806ef3d17ea50909c15ba48804be75b1405822eeea5d47e94cf4c8bdafb764f1efea4f85d28237e9059f7c1ed536a44423ca37d4525e950b434a9c5a6263c5a089efdce16407f98fbaf1091db1f92c31283fb3580fa4b822524c652e555d44b3a55cdaa5c042b7d781d84fbe7ced4b5bf9df5e49d3984396695e0762fe5fabbd5274c63117a64be67676828580061745cf20017ff033035b4382ed0ac88398e56251588a515675598d5b6092cb14f7b9630c7f9cc6a54ed7363752aebc387c259b8d87cb5b0a398bbae27d5efd32c8b85e31260e19225f5eedd81c38dce0e28003536a74d0b7ce7bc49b254e20ba7071fa19d5688b7715c23a931202a97e7fb9cbb1cc479396efaa4deb0c0c04d5d5e2ff5f4ff8f82fc151ee375862ff72adc14a8f6e2dcd9f61e3cd989a6894e8ef5ce62bde4d9611bba894b801005760ba4efbfb54ba6679be6319b	0	\N
7	1D6	CSE Dummy DS6	CSE-3A	2025-06-25 02:18:48.455+00	2025-06-25 02:18:47.95+00	<EMAIL>	\N	\N	99199d1bbe8355ccf1b9286835144d86a0fd049f42c846bf7f6b9943a4888c51	9c4bef3052d9b7aa3f16efba627be1c3df1d2dfce70d601a1a2e635e4ad1c6bd93f78763f29c2177298fb8bf4482439b94128bcc1bc60ee7630b358883f2fe8b9e86110b9d8c47e4a0c80e94ffd978205d922bdd959a09fe3cf57a47831e37fa1897f00f661240a020ac9c3231d4f7c2adbe5c3437a630a41f1f807b9d2e6c4eaa143288029a0e1544177578896790802114e6ad62755eabaccc99eb3ad407ad8949385bc6e732d326bd0334f3006e95e35faf7972e9b30968ab33f54a94404be9e56c540cda1dfd64951c246fa5b38c0704caf2e3d97abb3dddd16730e1ef1276c67ca4c89e8683872c0c90ad577f72ffb1695118f9b96c5b38586b4aaf5ca9d19ac28e636f82f06d776b65beca5367ebe774c50e362d0a26398e261841d7603306c55a3c79eddf99e0708fca818d3567827efbe111511aea43d7803d40529984d60d73ca428774bf1f608ffa3a5958fa744f92965d8b620c85c3a4c42b5ac22e064ead38fe1d7a08dd72784b1370b218c511f016fb408e41c322cfea8297251a6ed8c5223d5d029544bcae010f3807f8b62b0f0f605d7ddaf7479664191eaaed85dd3917ceafedd4466a084ffa2e298a462fa60258a4806b3d242401c1065fae9b81a8eea37fcec23f272cd43276dd3f9fcbd8bf04fa752043f411799c21abe1555b9fe546d0f299e7baf0b86140ccdf8257e41f3c47b626bff4bcbaacc609	0	\N
8	1D7	CSE Dummy DS7	CSE-3A	2025-06-25 02:18:49.538+00	2025-06-25 02:18:49.043+00	<EMAIL>	\N	\N	2ffc23ba0a6bc9fb2815ee5ce2722012af25ea7dc894313014e728838e235e5c	2b9f85ce67dcd3e04faa22a3e84c031f16b7a8d670413c21adf61a5e541742cfacf76e48ead4f97ecfef78598a410861d0f07025256e078e906bdda2e5d1ed1b541fde5b8af50eafcc168a97df194031596b48543b9d28610c8a254f41b58ffec231b7e043806bdc5eb7c9f1aba663882b736df5cd3a19edeb40d1418f151ee9634b0e192905a7907e58d0d61c04e4a2cdd0256715d1692f1eba79b3a370451c3f6b348cfec3d1b1dbbbd712f867ccd611eb038c19b16b9340ee4bc2d1f7ed563d22ba176e316fac325c00bb17dec0ba0418b1c30b3d2d1d2daeaa3404478348371848be9920fd7072c6fc309d7ca1ad127b7d4eba5e9fdd676b4a8769d6400e5f158559af2c3862742d7c534fdfbc3fdeedf36919731979fa324be15e1a623c10a90791c050b49ba4e9c4be3d75ecbea2dab74c8e22c1d3117d13afddf8de978d3b7d90b8107b035182019c9ea9bc9d3407de57e6c5b5af041e625cddf3b821f4d63207082e2c15717e15edcc35c5f269a3e308f05f33137c9c9ed3aabaec5a5c48be0c59d1fb0e00baa797b36adb65124c0e836ee0ea5ad850e3c46883551f6819f2f34063bc0e5761b35c26275db1e43c4b7ed834580b8b6f2c1dea11b8f8898e025c18295d3161bd638c0ef68fb7b7896da69f86bfbb8a4cf9ac1350ff05ffb2512db461322ea7259b5206355a9b8be823f56484a3ed57686045c0b8e0de	0	\N
9	1D8	CSE Dummy DS8	CSE-3A	2025-06-25 02:18:50.621+00	2025-06-25 02:18:50.124+00	<EMAIL>	\N	\N	0c35c862f394226669b197a0e858a0b8d38e2d08507ec1e91deee92bd0fff346	dc89062a02e7349d7cbb7f5402f16f9ff651ff47c6af93b6f9e5c00f04785a1b7d1ea3dee9d477ef16d11945bb96de8ada3fe584fc1aec63591f26f3d276ecfa892cd64089a6770bf71006a05ffd8bc8fa49d794feda365ac2cef7de44565b92ff8ce2342b17f8874e446591f8a1d8bbbc24267faac91c7fab022793dfcbcdb846a3c3c64660232ff328ef2507a89d8743e53f6213328541a0992837ad7fd19ac781fb1ef6bea1f22ca9957fd3790f30f863749c68c835c931c046d229a85291c71cc81262c012554e89215b437448ccac165c33362821690fc793da29695601dd6444e3956b9a15cf0a7fd10415d2efe9cab0c2568467700b9124dfdceff5b97cf13352bcd64df787d89894f08953e84a4169f81c42bc2613f87928a6c07593227e47d5457637f67ae9e3037e42df5430d266e350d02489930c3ad59c7007330b279518fe2eb3e8e3bbe5a9d2bf13151b0308142c80c7245f06bf443a2021daea9689190e22afc9a686da63438c984ff44258a978117424265008dda80f0218de796fdc89826d9538d1b6e9fc72c378304d227aca5e9042164155c78364ca56f82c79a076ad8393d8c0b1a7ff8c077dc42255c84eee418e64bef1f70b2e4b9892b4c45130f48ff34e0f0dd36db0045c8e339715788cf1fd87a70b8b3210d6b61b98dd18b30cd032fba0a530c9dae69a199300701d87f2650bbb8b202e079552	0	\N
10	1D9	CSE Dummy DS9	CSE-3A	2025-06-25 02:18:51.698+00	2025-06-25 02:18:51.206+00	<EMAIL>	\N	\N	705f8a997f50a4796bd1da37042b981ee69adecdddb4e6752a346c196b22a0e9	3ecbafb83eca86fd37a4ae9f4e00c0c319bc30b3083f3a5730504803c808e4d52af958b9d0828aad2d96142dd775eece87718008c0ef729a2c2c4f90810fc8ad9b6fa92822da7f6ece033a8ac6a4788dc9d1357de7862cd7409c5877cf3e8e3b66b01d8e7f1bb64c1626bdd92bc8da1ab21d698b23c225981b275cefcaa19f16cdcccf163a5bccd0c6b16716039230e283885bbf41f006432a30c8b64e7a2d573a7cb4beb45c6969fa5d434699c65611c28a30662a2daef6e621dd9e4f2d5c54cc0fa1c8ad38edadcef34a3c4035ad93c98e2bab8ae4faf5e9d4409af69c9b3e72eae080f55cf52a8c808fc98181c797e00b5734909385a8dafc5c6c625b600061fd7a8be34fe6fcf6c26e4514d69b54199dbe2f96ffe7ae586be5642b6552d8859449410c4976fe35b0f24818b25f72baf49a40c08e1c2833ccb976b371eb827f91ebde22a4acf6eeecb15416e8aa9984d39acc6e840c7f9082102ca4d485a6f4abd7408f1108653e87b4472b7bd0f2efea7c1eabb257fdebcbf2f772749f1b7339d0a862715484fafa5ccb8d4a01d388dec0ef0afa792cdce0c5d41afb9a80c7ee9b2e5d054e3b32a9ec98b44e981511300922b678e8500f377a33eff8e4829a016fa888faf482565cdf574db53fcb734b6dca8b66bd6e671ce81e36530460a16e04d76a2c64881feb0db9301593d6651a1e6af915ca514025aed2306d9f4f	0	\N
11	1D10	CSE Dummy DS10	CSE-3A	2025-06-25 02:18:52.859+00	2025-06-25 02:18:52.316+00	<EMAIL>	\N	\N	bef737b8661a4a8a843ba6d2cb9f0118370af3577ef5ceeb96dff247f4c5d6db	f813313709a7499ade8379d6b52bb19ea8e4aa23d50deddd8333597ab36ec8b11abd25bf03bf67e844d5e59b721ab24a37cfb46cd5809473254f3f120e16533cbd1281c13fcc9ff1076a7bfb720e361f1adcd2872165c15593a3bd8be1bae12869f1bb4fd2f6ba637214f16642700831e262b7cc6c993cab73c203888c87dfa085e0d8f8577eb42238d26e0d1aa2ed9e39b0420a8a2f7b1e99b009321794efc837e1afd7a84af61895b4eec631902a18e027f688e20aeceed5da9ca14bb659fa71aa6fa14ddc42c1b5672ca72ca0542bbaf6210c246aab3b991057ef5c2c37cca37530644c0838121eb29b18cf9bb3fb619f905a11770ca0f29bea38b3aec496a1c87a66cdbfd9b1114cf5cba2ec99806bb0f1e49fe6043090b91b978852d712b7cba945730e0015e832c7c8085d1226b65fa07af37850a608d0f1f7dfc98bc1fb3eb9a5b6d1385daac4e44e35bd349a3b70d88e0b13a773e37e7ce374316f86ca5bca3d5c04272386a92bd47c8c774b7d8240473d652aba30518d42095db1e9353421f5f96a93eac2dc6187bc0be7c5c298133669be33492e9f19ba4a3d15d516ddafe873115420f4fcad16d2f831d179f9371ec494886c6b104486b938fdcb7d30a941e5ba36b9126bac10d99f780ce0caa67b03a33c4345e2d8a867bcb41cfce1b013eebc1b9ea4afb55db620d572c5161d42ba75f5d7b8f9674e3d9832ac	0	\N
12	1D11	CSE Dummy DS11	CSE-3A	2025-06-25 02:18:53.937+00	2025-06-25 02:18:53.446+00	<EMAIL>	\N	\N	7c2185a6b2b301b302188cd1e9a4598ea57f39a9de1f565d6a4bcee6a7b8dcf5	84bf06c0e2ad1f7bc45179c1a65386d5d5f6bf6aefeed842ff8914f8c66316d283e7345fede2f181e106472e754173f7b091006b30b49349410f48c28873a9b676f3205a256c9819dff30c3e796ff9086545f62482cacd1c3977f884309789b3807933ebc9fc951ac48d9efd6077bb2368431ebf3d24d8ab2a4c15a0f6fc6a8f4d4baa0f6c2e613a9576cac6aea7463d31ef7d826e56937413d74108abc578872e7ce625f72c037c3dfc77fe222f032d475ed466501ba2e8ce99f9c9cc46026b99a201f17945e980f51de3417d7dcccfda92831db29e0470bf44c0bcef4ab03f4dcd41ea08186cdc2902f6b3f2ae79440ada4a470126647986c8cdfc52e7f95bdfb0016b2665611216a78a35e1663c58ba1df4a8d247f3b00ff2cb4acbbd162c7841eb715828b243c6ddf4a8a563bbf78b1ae58fb61d9e900a098dd9368bdc7e5de34458ca1dd78a929ea33d35dd4b306990e6dca5ccf45f930be5798fedb6682e110b73cd9430350cd75197825d207570719a4a831bfc845c54e67011d228fc7f6ffccfb6be73c8712549ec3700a24da49ea61e74266733907bad17c2c0acaaf42b56ff42a571800a106d1536464141866d639d96e0166b52687a236292118daf008aa808bfe1d982d44329179742a657aae85d79c40b536e709f76666df24b1bdefeae775c326bc2ce3909caef438e7e3dff6856fcb099a4f7c4c76bb4a8e6	0	\N
13	1D12	CSE Dummy DS12	CSE-3A	2025-06-25 02:18:55+00	2025-06-25 02:18:54.522+00	<EMAIL>	\N	\N	1a0ab256468c8478bb557c5d46b4dddca5f417e8f028e1b23cd4c17be902aefc	9a87045918509d96f969d311178f69bd6fe3a8b6797dfd47b1d48fdf22f3516bd1b7c33accdb5aa919dfa72845fa47e71cbac7f806747b9a7317fd1514690cf37977fc96eac9fddb3838fceaca0a92b6909946edd758dcdf7b98b8cf4d5d9df1d26f72c2390a21afb05fc203682ede0a5e206b682c7881d240953d6680d8b877e380231d0beb410fd68fcbe6e8fc7ca3255ad8d15dae3ddc0d46a8cbb882ebe994de4957ff502276bb3e8b5f4a1f1b1209091d2dff94359dfa1bfad55658f2a960b6c47b6a0ace6c61de45d7411b9aca1501fc5df21b263516c8f837549a61fcf7620684260bafa119d9376df39754c3ba79628e73a785f5b35dc026a1ec2648f216b16881ff8b01ebf52e036a4d309dc17ef3ddd142b260b2b9f463b632c2711ea9f4556dba07ec1017c6641efebd5a294362a80d7483b4784bc1a91dfff8d5bafae2dbee992476a048b2db7c8f6e7389f10a55ee4b714d6b79e198dd9966999d1e5add60c9c29a5e7023ecf28656c737a8527d53de8c32e4c85d7ee3f0923983bd9aa8129f4f01210def3fc3d97c7e1de35cbacaaef801285387c298b09013ab8c4f4ec54eca222b91c8c203728dfbcd4b03b512e9594b96693bf866bee0b6c0bae27bf894f37e4c89a1615187a84362a31fac6db8b8525820ddcebc2fcbbdb6730b2f4338d09f54bcfc490d9a5a2bd138ff430c45a49c7a5674ea78b9f889	0	\N
14	1D13	CSE Dummy DS13	CSE-3A	2025-06-25 02:18:56.062+00	2025-06-25 02:18:55.587+00	<EMAIL>	\N	\N	5a395d2236fc1b1cb6f609a80b552300a0d491a8513038922884bdf1f3c00c1d	09134db0f544220b54daa5705544d3e925ab69785c99dd018431314a5b705b43433cfd99c25c381df0eee2c40da3965dd93e6dc516e17be9fdc5a86bb777df11303f91dc33d8a4fe93d4a0a5a255176967ed123ac876f496ed89c24d38fbe4ac242a69153edb0c78968c471cb2aff421dfd5d40983230c28944b26735badb8691b5eee6be606eb387b4746f1f7d353d0b485566e43d3fc825cc5ed6270a7c76a3b616a2d0d6e6c784837c660524494607bd3f7cd11f49a68933b6e86c08fcde580be6ef1123bec159b6b1813e9e0e7ee66207481728ce18afe84d8cb7c0623e66d25f2cc72a811560a91feb67dfd904fc8a83b3a7cabb3321abf19bee9e26603907aeb91302b2c3411857889c2aab9e6db44e9fdd7e9cea9888dcc03071efddb4d0366c4aaffa739e535be2595a516b38290c0dd3211e1c01d135ecc1a18032f66512426abf86362ebff89ed7cfe82209f3fe63b356b22213236276dcb18f623149f52e1b493eb804c9c98822d3e6339f7c71d3ab8a67d67feaecf34128f15ce6bcf4f45b21aa5885d074e5649267fd98b174ab139b05af856c267d1aa9e7fb4935d88e76284949ce402defcebdfe3572ca5c62cabccef9b6e94026d63d34b6e6fb9b1c4568068f5acf185f76ec949debd9f4e38d05d9f6b46e16fb62777830a0671959bfc51f953250507f7796cfc82b63202f0eae924c2741f841b3e7a7384	0	\N
15	1D14	CSE Dummy DS14	CSE-3A	2025-06-25 02:18:57.122+00	2025-06-25 02:18:56.647+00	<EMAIL>	\N	\N	a2fa28f8c5a959222b5b90df4192b42862cdd80b19ef43cc2a316e1768e65f3b	588664367551773b54b47398860b3e98d53d73f81da9ddede08749ea707de04067d68e3545c9222b571f7bea10b7be5e8977d07b48714ec02449d78f479acc08b8a7b7d7cd81afedd4037f2e4faf57d7813cb1023bc6048b1ddc4f93dbe3cf789d9fa68c7fdf0adaeb442347bfdcaabfeb09a02cc5b0d745995ea96840019a5482d4d7ba27817af7df0f942ae06699312cb727a80a4e0e9da0342ef7a163400413888596db55f11a7ccfe9bd4acd9152772a791c0dce710f175051f2df7419bd6c501d6795db3ce8cbe3129ca6f0239d1aa86760a1020ce15edb5b6d5a625c2c5b0b0d944d4a3dddf1f0d62a6448958a328e82872730a44c5d5ab76a1f3b6bde4c10b40bc5929c346082ff259b3150d25f7363f5befa6cefe6e13cef007408bea6e22e361b3e73dee3465b901cb41a19e949694f6b48b525cf151180789188a8cc2f749183886b61dff018b1a508fe4551cb526125fffadc9c67e91e33ccbef50ea21d1475e26d75b590e1f0e3dbf3e67eaf849978a0962466b9659c787a7eed58674d398d78da5ecdd532a3aea18b2cb0b9bb2cad4ce9179dc54e785e293e94e2ef09dcc677e539dbc4cb3e976f87c92e4dfe4d0830be3523379ef84e4fbe69ac5297d2808f0af666de7ff4c111321572b7503cdbb45043755c6f978a7ea3357fecf2b37f93f44f1f5c54c597a482a9e37084aceba7b570425217163be73700	0	\N
16	1D15	CSE Dummy DS15	CSE-3A	2025-06-25 02:18:58.178+00	2025-06-25 02:18:57.703+00	<EMAIL>	\N	\N	555122de25f50d7c71375c2abd986feddbccddcc326877ef1a99dc1fadf4af3f	2505b994b98d3b314aef122bf59998633a864627272d834229a4668b6cfd160dbf365185f8a54b78a9861ff65b653bd87275a27e47636400e9efdb89970ef180628ffc0a2d42e01e42b4ecacec022faa03d0628ef74d4071c2ac2630c0a7145b59129bdff254d8608f0b159d5731888994cbbbbd9c4e219f44465213a2f6b7a794d52fd7ad606f29f7c1a577a21e3ee97ed1d281afdf6d12fc9fef992a506693ec3a608c64e357d9418df2b76b35d3d76a73daa6cda463d624d1d96245372b875276f2cd7dafd5e4f035485b6464732f7c1e5906be8d60f3edfc663043efcd0baf71f753a22112fdb16867ffcb2e56ef827979df656e0a06b8ebdc4fb521c6de17559c86b1417e9843f37ecd44b31871ce9d3798f49014d5acb189ea276d9c252771fa2dc297c28e63345f69feb6d13d070d17a0d76ed67fd85f1387f1de7f4ded3266f459719121baf842e041cf6ab73a321a8e8f59ceeb64800108c9111d0cc92ce506cc54e9eb54bb87a25fec69127c0f458a6e120f7acf07cc6f96605df42cf7454b40396986e2b7cf4b4acf53a4cd0f2af1314fb9724fdd7926aeb00300c96adb3d47582b009f691ed91b75b6cdd780cbe9ddf3e7b12611347ef3cd925e6066faa0e826c5f384bff096ec1ef1e50ee789bcfbb326f77d83fe0fe00263e1412a8b466e5818c6407b9a8a1b5a2909a4c8bc30c7f7a34d06224f1c3dbe91c4	0	\N
17	srm	Shruti R Maniar	\N	2025-06-25 02:18:59.237+00	2025-06-25 02:18:58.763+00	<EMAIL>	\N	\N	fd5d26a46162e63b89f5d68166e8e7600830aa0f94861a6e50b3eaee95d81c3a	09112470dfaf3e22b479f79c027547d6b7ca4252689a12c74f4ee5d76c5caa053ee264b99632809ad4effcfe590db689b16eea04fbe6a74f9128f67c3d639fe0ac52512bd194acec82632bf6627f8d913a5841031b634781f9e80937a0ecf66b00324f0cda44f4e3a94e04b978b568f4be40670e8661aa1f13f10d222673de6379d3d947b5c268702723ea759f51704410ff66610ad397f1d4e5e9bda0d928e3a70da0b5c51933e8ae578f1dc3aa54a2db6c14bec4aa0cb40d442bf6d4a129965eb289907b16c42066681cd52e4e4a9b805b791121254a0293e0e04e89dc5dedceb2e39eb84bdb528f26b583ddce40ad1a2699932f44dd12e911d50471493b584c8fa655ab262ddd3c246810a919630c18968c65a49dc5e426ca18f8c83d28f5717dbc783a8c0f32b9532a21021af66822cd1e9b1b53089a6f82c41e29af1284b15eae637b8c47a37206b443cbe69602639ff7113eff508b1d518507a034f44861d9653d1995db2ff22c7fa1112ddeb838f5d39c383f83cf347fc2a8a02b482eda3a2899e498ceb12047e79e33eb2b73a5e1e2bb9055c46496de7ad0e16a4540fd34ce14423746576ffe26af73ae89177c29de80a7d3a86eed8f412b59c896b4aa5c0f11cb421227d4cf050955d232c6b3d8dd9fa81ece62fcf263e3f3f84182b163e40a866e90dfe39e7f24f577915b12ec4ee86fd0c170cfd6f71549f31ef4	0	\N
18	dpv	Dharmik P Vasiyani	\N	2025-06-25 02:19:00.31+00	2025-06-25 02:18:59.825+00	<EMAIL>	\N	\N	2ce5bc12c0db89be064188b56ddcce3b0b4a79a95ecf152f48aa6c5efe6b7c88	38a17181ad0b8a819d0dbc2a5ad4d28de493844aca21f591db0c32dbb62cbd98f769909c5875af0f8a0162fa042421796928dcf15bbc8520ebb2c7734053518440812bb3c38b55733c81ffbf730898629bd2209d3c0f3bc985167194453d670bef90eb1ca1e9cef0a61b5fce7d101c9dde8e9e360ca4101b7a84d41c551c7f70ef616a7d128484ea42443319a0a362a401135131fec25662f3e78877825ed6a2540be10f4e4778652ec2ba81b931f87e58fb1e1236136e575c4d26a1d1b9214408607002f0810f006668c253fbf166f3c12e5adaa3396da3835d20d905b8dbe7b1dfdea1909c63e3399bc6fa915bed09c78e94f4bc54360f373b9dadb5b058f37e05dad0180a0f9915e465fc608738bfc09c1a03aa2a8bb81903fafac36cead67f9781b44dba5ed4bfaf3d3b14c751544427cc355c70dc14c4bdfc1c086f337c11aac158a73745e003b8482d7ef74ad8d274429d3f05e62e64aa0f773635137be2a546ef4f5daafad883083a21d70ce401c7fdca9aeb8dbedc667269c3a2b64343c8520f5d6ed2f70853503db275132556714b80794ab6e08ee751adfd7cc20ee910d46cb7a186aba0641bfeaf671a30d27479c851c684db6f14bc42299a757d1b36c355bf7c87309be325a7b65249ffea0f4892816c7f1273320f5c45a2476d42ff754735f71a6c50f22aace2fabb0058f5e55ed7a9958c6ae5695e80c4803f	0	\N
19	mrf	Madhuresh R Fichadiya	\N	2025-06-25 02:19:01.363+00	2025-06-25 02:19:00.892+00	<EMAIL>	\N	\N	0f1f13d08106e2b4cf28eb2447d773bb47e04d89e04058555cb1894d7994edb8	993958a7132d8ddc7a02c59e010125f8a6a753c36fd723512e06abb13da0939198eba4f283999a64bd134f748629dfd2c61936c901cc8b0da2a9b860b0b900fa171e1d642651a413bf2013fefe4d25e77f95aaee18ad50f385940d023d7db91fa8c8c0fc68e8aee773e744bc0011155ebf91ffba33d833f555558c28c3d63d5591b172dcd77c92625326fb3962c24a5df4a5e08c883a7a3ef0002db3b67fb28e5a9644a48cfb890fd9d4fcf61efaff11cec457850d30da63204a6cd98a52b799918210842d6e0e0cfb8a13fb863e5ba24ec06732c45592ba84631d54662e72a6a2d5e36c930d83992f5a169475e15fc36a7c7a0d03b7ff284b2d2d50970456a88c19945e691fc7e110d83c3fbd1e922606a5a3669c3aa1728223ceca04625cfd26a901cdd8c896718ccc3431799beebd92f33c107c974e5d236c6d86d3e4d683778cd6d3083098f128ef3b17dd9ca9eaacf0338c645ca5ccd31746aff72bf278060b55965385643b1f35e8e2ddf0092edfb126de19a37dcf2a87a47433d2e1fe4583b127c2d4b32ba32847ffad4fc165b5d14757a787dfabd0caca5d41742c1023dbd6d57cb72a378856fdc15278f678b946f9c0f1f822724c890266a1f8db2978de39f69eacea2046f753a20014b035e6ce2dd10978cea89259effe619ceb9d18ef54429ceb258fc4576a71a1493c25098044ecdf89cf80068de680cb06f136	0	\N
20	rgv	Rupesh G Vaishnav	\N	2025-06-25 02:19:02.443+00	2025-06-25 02:19:01.947+00	<EMAIL>	\N	\N	e862b85954f9ba9e95d01c78ee7d792341f019c6ef9a692219321fee90a4ea4d	8ed50768324767987dbcafd12f3255375b3e13421cd87f194c1c8581219c86fa887a4e44bd9007ee85113d99d6eff54fcd65f110f1970f59a69eae4af27bce041dd8c6e8aaf6a3d18c5bc84b82b4c591806e90fa2d5bb962859609e83424668cec1284dc245f7c5714efc6a6d47386edfcfc306dce471678c00edf61112cd0f6eacbe083edcdabab1483a3d43a78e0fd206f3f9831f8c7937aca4462175c0a7b72294435ff637f199879ebffb9b04de8011e410d8792ed889405c3e03a07f8f3d3ab3f813703c5593de020e74d56767f1c452e2052aeb893cd721b9850786a4c28ee6af5812eaf8bbf1b2227b373a6e9d02d494ed731dad9945a456cd533f72e6eaccbaea320cdceeb6562044945951079349f79b9b65a40d4b75a71709730e4aab21d8fb2d8c08e157c16674f9177068dd720795e693a2740957f2a95e487b1cbbd840ebabc76961491e8ba2facf9b0d201a298073689af5de62968113bf07387b6fb047dfe5e50d8f84161fb017306728d76e2052ee82104b0b3a93e8ced4581817dbafff7b4696501dd18a90cd01475846624f4445cd3d4c8565ac17746bcac631abb1ea52f54ad8da2b72f8fa4249399ee152fe9097ddd2dc40cfe1dc0c3835b4fb3f7bbe488632e82996a0eb3a3f478bbfa581499b077f911801fe9b6ac95203999c20b4017dc230e23bf6c63db28eea83d17b914c679c4c4f7b206a2e6	0	\N
21	puj	Dr. Pradyumansinh U Jadeja	\N	2025-06-25 02:19:03.607+00	2025-06-25 02:19:03.121+00	<EMAIL>	\N	\N	be8947fbd0d7c9af7cdfe59aec7b84f431317ce263eec8b38567ea707759cc3e	a38d9b75e3ac644b3156ec092c7699cd20dbfa6d3304e9329f99944e42d95ee29a1c4969d72d51c4784b7598c9cea6f0c618849604caf4dab881b027b3e147fe5a8945ec62fa538cb8aa8ff73d118fc37a2aaa3ff27f14010ae071ec20734b4e59fda2ac7c90a46aabe498e1fdd56996d3d2a2b6ac77ca866bfec7bbcc93e33ab0e2e2dfc8eaa44a5dc70cf64039ce81fd3638df4653510d455660be26f526b700e78ba1fc91c2ef6fb9dcbfa035b50b1b6b3b0ad98e39fd01cd0fb9cfa783fe674f81432c44c7eae62f4670ceed38b0d5d7926568b7557b1977e92ce2f7f084dd050a95739b0b7d930ed0fbd08e8ad804f5b754440a51157e5e50a899db5517253533ead45d4c3f358d84e1684087a795ecdccea8e848f2fdcdb0903b49019ec557253725343c99099758c963a09b4646763b52fb8ea846be11c157d68053e094b86f1277ef592fb3e04eafac70c92df3c193f78c73a16d44c7a8eb85159e2f424323c90a42bf33b23449dc0ca3d0900dc7dfec96ec7b614aa689622b698c78d26ca6f68459f2f945b4dd7033314c2991bdb607835539629376239c7a7a59a8f13bf35c8b2e9fccbd2f5f63f3ea1b1d6950201ecac3920475f374b4283d07f9201ea996bdc80caaaa9c201b2fa0fa562d1b626b7b063e17bfef274df6fbd41b3900cffa789e953e659d09403afdeb77ac72ab05dacee0828317c00bb06714be	0	\N
22	23010101058	Desai Jineesh Shaileshkumar	\N	2025-06-25 02:19:04.679+00	2025-06-25 02:19:04.19+00	<EMAIL>	\N	\N	9e067abd8253e125b399523203583b4e8cebec63ff85910aa9ecab2fec1ed788	3db6bff77fb6e853f3c58806353f6080ec336951edeb4cbea5f375bde55434509857c812857adc60607ecf2df18afee9e36d73d3638cb3f7a2c8eb811ee527b94a31f87334ec65ed7759c5b8eae8db6abbca612ce39782fdc0f06c372b3149d51482e78f93e5c15bc0b33a65c746861c6baba58fe2c9a63fdecb02832694c16437939eda1b0a16ffa65a65024381c693747cf247851f50bdf9d222ef5649cb1bcc4c923d36d932b5de65166efa22779b9b773d2fbacabc547bc060e67824392245239dd9d4660266778047cbc4b2646e2ef9420d1a8e85eeb4e2eced8f5074db2d666d45f48e37f345d62f339fec2c12ad07bf6bd186484288a38d7723b7cfe49e2b9ea42664ac2dc88bcd11a02198f5606cb51e63a425bada53e970a5d5399e81ce1f96450ea4fecb25f8ebdb7ac95293c7103324715827e9b126d7ffdc474709907f1386838fce5d08134813b36dd8ec7b9557511af028dd28584aac817ce44f107ed95a7fb0c3310210692f5349f89194abcad184432920e84f4b2d9c03d47256b6dae38042613fdc5c98ffaad7486d24aa239bc0e9012d2dd066fb6db2364db00bd0980addce39516230e356a855529284d7e3301a06c310ed84c92931c13032cdb154c34eb8f2e7eb5f26396fed975906cc4c7ee57252d7b2e2a56caebd14382e8ec7a14847dcfa2eee34a48d58ba927c4a30513f161f58ff1e1dfe86b2	0	\N
23	23010101247	Savaliya Ronit Ashokbhai	\N	2025-06-25 02:19:05.798+00	2025-06-25 02:19:05.26+00	<EMAIL>	\N	\N	0784356dfafda16fa9c8f0f292aace9031fe6588c35c7f6a5df489d0fc4297bc	5eac1693f392c648e157f7bd65f2d2a157937e31f64ab0fde665866d90ea1b7181d6d90568c8d0d9659a5664a378acab283e1edc5838a0a1b06c462ae0dcc9d30675d48fbec65f5561603c45c22b2a7c02bc01518086d0ae206bab703f1e53c06e2df21f5912461a5bdba1f535cd16c9c877f212f21d68e50e1bc1f18e504e49b5ef969031d51cfb8ee44edb7a8b534002f74eba8b3fe8def477599d990f78a4eb65c614fca9103d6a88c85686731c13386af5a5ed0d4ce80f02d261fdd6b5a6b09f0792faae842476014d28ec176de4b62e5c56444fc804059c3d4c07422dbb8f9e9e5ada81c977425faa4c46908549af1938416f28796f7512d1590f80b8c75f81c5d0ee73c899d15a319eab0d49b5e6d182a0ce5ea9f9cf8d97e43f3a9c5f8b2c36135e4047de45b1d447d44ebced7177efb26d37f32107f02a1fec764eaac21b1f720ffa7b8b5e2da1f6c493977188f4bce5d7b3a9524ce09acbe394c33876e84c8b0bbe899580d7463c4f39efffd1688b19542b6225f336aa25f0ef8e64f8c72d069370ed5177d26360561908c64f1bd60109c74094c8d29278e2b8b6b7ece528947de7e29ae11995e84a02cf755637aafcbd4a297bc5e1829d69cbb1904bc9eb9873d83873ce7d8f82924dc4a56320a587c7ef7cbcbbfdcad96cd46f6b54ef920008b039585ef23ad3b3bf9a17fa04382a95537da2965d269157d6f767	0	\N
24	23010101311	Zalariya Shrutiben Rajnikantbhai	\N	2025-06-25 02:19:06.898+00	2025-06-25 02:19:06.379+00	<EMAIL>	\N	\N	0b248d335934803f842586cf3542a61363175885f789d32e599f8f1508b3ab89	e29cfedf6c98cd01611e8ff3c3c47e7b0a0a906a9397a369f51f9068a30610fd57990758ced38a46d0c91f2a6846abcd862f2b7d07d80c61b903dde18c7147e53ceb1eb8e60655798789c1074b3ec9b24a82d520c7c7e83577bcdd3abb65bed0d1504c786228b72d6c291e854b438270e152680f2baee4b2b189cc570a5ecea0d90a41623a720b3ca30b20884103c8f0a6d93c80becf57d9e5fe2f4ddfa692ad194cd81facd61fdc05523992b45effdfb44466a742a529ea7ff241e6b96ac59db372fb0106e655d2ec9435512f27f6566e0c809cb3bd8efbe45cd64bd1c54da7ede66489c2e5545fdda1d7c4bb2578dd95a0d608c81282587199c38e27b0cc4372131a670f83074a8265668e996a105a7ba65d786d933f2fc8a27785dda0b0e52fc647100e21d316c941ab38279131f7fa5f19895f1757385b0f63a7780a57a9586e01d1dee66253e67b140c16c246cff89988f857aeada49319193c0590adc2d1e2bd74db759613a18c1bd3fa5bbb5d5768c20e08bcbbca6bd8487bcbff421979163000cd9339c0360ff18f82528f937f551ad857627052f5efab8528f3e1728751c0b970d359f87dda1dd88f1406f476f037dbb930cfa4cf3b58b28eb5235c6c1e1726e9d79feffe6147a7ac0a4f0cb708c6cf1aad5f2b0edf14fef6864d5822b905decd1a2b147c3067fed12e768e6509c14e84f6e1d0b5fd2b278c88e539	0	\N
25	23010101024	Bhatti Mahek Jigneshbhai	\N	2025-06-25 02:19:07.991+00	2025-06-25 02:19:07.483+00	<EMAIL>	\N	\N	7393f6cc55b97f0e7559098b3c02673c105b3e288bb4183433e3f7e07c78685b	0f50c6b42f97afa44d6b07201855eff91cc41a407362a6ff8a5340c997ee04073a5354c5c7733382eb76694693c9c4756827a0bd860f42959ed87e08ab37b5d6ba9b705de6671ff07a25ade5711eca7fe5007bc3b5da19a2d14b44d8d872c8f1d5c19bc70ee1403f757ab9fba00e846ecd3254c56ed946f9264145015ead4c4816bfd4052f37aa9ce0b28bd123a278ab674be8d779685ade93233eca6ca1b1082cb786d248121d57a718afc1fc1f9225f0f1eee5ee8a807f31d93ee70f9f0cb0bc305357744dea4bcbc54be2dbb09caa23351f794747410c9de75aac8afa64a18b6602d92107d6d9af9732fcfb87b6c7d9b016309d8a63cc2363344c35ac5dcb4fc049ea9efcbecdb314203b61935ded4697749da60f082a3f1011072fcab2a36c222cb65b84d02bbcc6a0f6a5e058c95114d70f71768fbf03f29d1a2080cccc79a3812b15c35cbe400e702902708790e3a36fc923b48715a24e2e33b3688bd23f5f5bb86f853af78c61c7635fba09c19994969749f1853bbe8dae7246fe091998197c135a8220a9074ae8e7d6cda130750774425afcd59291f0b71b88153bdee02659ef0a2a7910f5ea44ec362b2c6ec957495dab798fe2be1e5304bf8cc3caddb6347a9e8da864dd721c24adbc1fb02b2396b099419b855699d918e3608a9d4320bd697ab5755949c36af97e9b9e0b833cc05918a57cd7ec6742cb1680f1bc	0	\N
26	188	Ambasana Venisha Hitenbhai	CSE-3A-A5	2025-06-25 02:19:09.074+00	2025-06-25 02:19:08.583+00	<EMAIL>	\N	\N	71edba7d0c4f4abcbc06ace107423c235f1cbfccb900fba481a44b03f6932fa4	1903bd9df8118a3f89b1196f6c230f7538c0e477501812da5d3d39d5020273ca3e6d82be92242e4dee50864150f9f21621bff4c047748e14a2d8254b2af1b65b4e1bd43fd1246d9dc855c5ebac3c791110cfefe1b0c9f97abc83c661c9ac8390dca943e0a4912c236a8c60471450ae5df4bc5166ad140acb6e7ab21ba1affb08b66b8e5196097dffdf67387ea8b49e57f119aa56e2ca6e8fcd09b4b1118d60aeb50e93810d3c7e52f7cf6b62129e9e0f5163b4acbf4383d6a169dce8de686de8d5d0c756ef4c22632b1c6db9c33f5b3bcfbccf39ed6a47238b86c719abbfcb43d5a1b25c8ba3a4a76e0b59adc014eff5084e7ba91b7a76ecc276c7ce3436c3469d0a59bb05cac8ed205da1a49278358465e98ce30538cebc7775bc015bb3fd2b2dae706540e463f27b9dc600e3af3ca0e02dacb454cb76d6295f14bc656ea64c15b05760a01fa309fe8328f6603bc1b0b70efc7ad7c638a5d059fb54600d44c6449325e3e6870f21f1cb10d152a9bb8d012a76f464f82838957217cf2c5d77fcd26a28e09fdd5f906e56aa48e38dea8c7b53aa7da5483bd599d042930e73c195e97e9089359f1cfdf6b0ec3df51c30adffaae00e954f341d05fc00afb7a307d0ef93e49a994e06957a4ab67fa80597b65ff3c4de119e5aa25ddf66080112f34d39843e196af17917eb12b0da25aa018e7d0f977b3fc4a65e8407715048665650	0	\N
27	195	Buddhadev Misri Sanjaybhai	CSE-3A-A5	2025-06-25 02:19:10.167+00	2025-06-25 02:19:09.662+00	<EMAIL>	\N	\N	14e927c364bccd9d3b51acb6de38ab3b0aadb93bbca3a340f6c8ed4f2ed610ae	461538837e5001c4d33fa67e6964252cc4506acdd0ebcbb52b1ae85f3e84ba42eae8cde3a41da1a915dc91aaef792c2279ccbdc1e9cfd98c7fa270a5f0efdf6e244efe4aa4ffe8f06a073183ca51da159322da509e57e0f31934f098dd96d53d937ec54a6a71cdd9cb5272527bcbc917b097ef25b3d487db29b3ec111f7032c5f5bc038d14499e60e298cd5365f14b3bf3699a4fd384dc7b2474b08a0c91160980368f1df364dae7be84c61e46c27b22e7042dc47fc300d5317c401b6da583bdfa24a2172d6d10b5fe1473f19600e892312c7cf702baefdcdec22e7db1dabb5df3f5d5d3090fe0f89f296b909a5454ccf57f4a68e02bf3a3c4f53c8e73f05d2e400f24594575b52a5f6444eb8a798e4b8a576f05be8ebe1b3f18658889d53ba2e4483ecaf4018fb139168c194c2bee310bf9720cee3dbd8d911729f370743a479ce96ab6a2964a8ad6bc1c19c86df25dbcbbe0352e59063e0abfe8b7050dbd6bb537cf56c8a01c7c5bde70c1474677511d07b41ce19fa3e95d1b2edf658421f84b4d2c23d4514f55ee5ac408ed916944fe5dbdb1fa2cfa64dc2ab047494d7286c8903de7b2dee4f24a7e05fe32ffedf975a6fbdbca2ad97f93b58e6c78d246aef4a7bbb020345a05aeef0c3ab746c6340045a5288cb0f9576d5e1449b9807178c2307763754d12baf9fd994e26070f3f4acd4db0d064656a1175bb67fdb7c62e	0	\N
28	181	Charadva Abhishek Virenbhai	CSE-3A-A5	2025-06-25 02:19:11.26+00	2025-06-25 02:19:10.752+00	<EMAIL>	\N	\N	64b346cbbed48285263dd9d5b2aa649d3deee363a2e72f5111bda9a255774259	ffc7dbc757067a1848559e3a017d611183a56978139cda337d700e095fae5fb91c1e473186fb9a48a2cbe8a3bc158469a9de60565ecfee2d414409dd0ff32f552b53ef3b10e8d7d1354da1b308bc36370c28b71b2561745078554332163f1734a73e4d25135ff4c1d59239156b53ac656c5756ea1e7998d3dde6d22c25ce22014250e3644620da25129f7403b7a00c214ae79782e6f718b16c920e13a4af6baaeedde33384c08d88ae1847380ae7c8d14f6b11169864c53196e6c852c570683bc89d254554a39e69ba199a9255cd28f925522d998ae8635756ea472fb61276803b7fea14b75055e880b3c894ee7444d4ff226b9e11748633ca9748ea7f0999eba2f4fdfbbdc4e466636b25a5dc7b1822841b7270fdcbedca117f9f3e564b9d664701bf8bda24cfe6316e8489ac785cc36da19503f0129e34a1f4240c9342b0ac066c2d31040bdcfadf73c33c96cbc3cce8da9034dd2f4b48fcd7594b790d1c56de152a627cb598a3db24b7df5404dc2ced3d7e6e83c10f6c321e439cdeada2bd97ccbf7a9772b74bb20b4606e98df968f6d9a02cbd4529e93f9b12f29f8b665f805b2cde4f450814ce857f1babe97aeb7b6f9c07ec31803d3255cc4915dc29c5182814978afae8da50ddb95271bf698bdddfbfdf7d16c9cf9ca6e93b4de5dd46e26ad357f153b779aff1e7322bb98e3d16b1a5c28308c8b72cdbc1d0ce35940d	0	\N
29	191	Dave Dhruv Viralkumar	CSE-3A-A5	2025-06-25 02:19:12.342+00	2025-06-25 02:19:11.844+00	<EMAIL>	\N	\N	f2f3613631430ad16426a03bfc4413e820091bc87e5805feff009b78205b0131	234ecb15e693983ebbac0684d73a024eede1f3dc28cae075ef39c217041f63494e0f15a1aea836dd1fed0f67c42ad9ee0c052701078ad639dec258d78666cbb4012768b96abc1b92cebad982b91731e10bc5af8f82633cd5cf5b5bdb7353e38a75b76ba90a7481b7d91a2d7c68adb1bb82e809a9cbb5cf46b7ff3719efee86baebdb8dc1a7850afc68639930aa429af7131d5f24c93170c662a9106798af73702ab1c481ce4deebfa12206b91a013ccc3333cb4a0bdd86dcd49634b9dc2c7ee9af28afc46575c9a1c2338837445c23660c9e16119cdcd7b89988d23d4d7cbd281e433187cc50af0bc0ec05359797e2bc290924215f567a95e439be679865533c22a69a1bdbf7e19604dfb37ad21fde0bbe4ca0368db667fe687d7c5f866a395bcdc802822d4dfe7782eb03f4e008503334ee2d0fcd6c89a7410b54a31319ee1ac23dda20fdc4ce6a6ccfc53477819dbee051a49a7f1361554c93228ee5baf13eab577785e23f34cfdeb8305d1674d54f6c3e2b81c5c0410b7479a18ed87d0d552309aacad61b4935d59be5859bebeea5c41e86230ff31852c45c2e3f3f167e3e95f4941cd1ee83914f19404a365fd82272712afadf0ab54fb8ec4f2d2f06ba04ffcbaea5dc20203e075b51a6f649d8503c446a3ac639cb2bf3f1f34818ac597bbd570aec7ed1d8d6cd98528fc3eeb5f97c51e57efcf24a224620bc77baab0e3f	0	\N
30	182	Dave Krisha Kalpeshbhai	CSE-3A-A5	2025-06-25 02:19:13.425+00	2025-06-25 02:19:12.932+00	<EMAIL>	\N	\N	5debd9c40c2562d108a55028376cebc7397148d998650d65686d7b038f15a925	6454e4e421818d826cb199f9007b5cc0e4d56ee46f85139fabc8a6107c53c4d4ee2c6d7d49a1089a5c6296176d5b2a23254d5c96c711ef7e79636bc4ef865800d74e604f5ebbf45cb50e467781fb1d5a327c2c8513ca36dc1b4234619cfb0b0bc34ed82bef2e53ef45922942149bb08e56028d2e4771eb2edd8f4b792ebc371159bce33248f43fe705a6ec6511bc0385f3ca2aee9ec18086dff5802d7d19bb7811f5280c7bccefcf91ba2e69a798c32d25e0cf97913cdc02535aa460a340403e5b4efe42e4a761bdf267768b50e599a567829a370dd0436e1b872d5d9a0fe3fab87999735243d3127c6f2d4c9e0d94164d0ffcfa0e7a1bb66faf66520445e71f6006f720aeda389f718621c640b7a81c57717b3881aa7b8c01b62553bdcbaae6e3d77ed63af581b4458770c3e69d6d85e98d14cdfba6aa5265de8d3d15f874e8c65dc1cf0baf4a6fb31e9d84e1f0b377bf45271d4d0f9cf20e5cf565fdef1b4a729f6fc938c1b366a39396d50c08a122839fd0ed8831e70663b1be91e2f2aefd8eb5772922d80de26d64934651cd3ed0e03d3365858547d43261225e6661eebeff4e08f0c044d9fd6048630affba1b0107a36b2a6e4fa78d7e946e9e251218cfa891486d1cf47ca3d0df8ef34257027fdf1550575b4ce55a51f0fd21e0052e81ba14650d369a0032c493ac56e308181c172c90f7efc75da2cf745e9b5b4ee3c1	0	\N
31	190	Gambhava Mirali Sureshbhai	CSE-3A-A5	2025-06-25 02:19:14.495+00	2025-06-25 02:19:14.009+00	<EMAIL>	\N	\N	d46fbb6e572b4de1a1fb654af4c02dabda6049b45e9e0409fa7d7fb91368b495	71b1d6377d3009689d8926d63ee04bb97b98fa76d476fb305d218daedb44bec9dc80c24e63bcfa35fc271763c154226dcc60f0a35a1ffd462175bf387fa1c4f1814d2ac58330654a27ba7b44ef423cf7fa3c8f4f5b7683d040322bdfd7229dc9b0c33c045d5929c4ec34b6dd7cc3d3104cb1227049249afe906427191a93cb189c592a3051d3679349771b05bf626e90d2156ad67e3d7f45ea8f10d388bb36b3d937417ca5ded552d18884f2f6ee4cb24db7767db9cdc02873335f813618b6759d16889e9f047d2d2f18998468c53f935733128638a242bea31a12882767c820542f5947f360f0f0de2a5fbd55536d003861405afac92e3fabd277bae083a77b81d686a53156dd9e1941029aa9631497e67fcf1bd971fc32eb0ed3ee1a3019a0c5b3575e2fd5c126428b8a96d685ca101c870d6a98a4cf3f8bf9060d63644e5effa90912fe6420d3fe7b58eddb7fd13aac5b239500e84c18b101ee51dedb19c92bdffb64c7ee2de34c29d28b8274e885af34d3bb2fbe8fac49213738eb364783018418954ff6362c3f920894a8ed3a9772f898e7a540e04cf6ddbde9965c8532785929f21ec49c692895a6f5aad8f7953aaa0e01025805979bc93779900d05ca65adb4f46b38a892fb5db27a9f7a2ce0e8a3426b0ba12df70d82410b362da3391df86066e39140d7a762b1627c2733eb46e3710febf81444223d8f4cb019c677	0	\N
32	192	Ghetiya Ritu Pankajbhai	CSE-3A-A5	2025-06-25 02:19:15.576+00	2025-06-25 02:19:15.082+00	<EMAIL>	\N	\N	4a26678c49991b85aecf80f0b1ea2d0ce88f15c8fa8db41cce4cb16152e08c87	92cebd8d35e514566890dc5e5dba4f6f7458c290e8e566522705ba413fdf4d04711f514e23a508469afab0deaafa4c98891d6a7b31b5114769c3f78bd73aa89f31a9615950a15fa24bf10817d28510f8bd42396c7ff92df90fee511e7b00e41222039f1dce556f7b88feadb5a72866bcb06674822f5b41b8afdab58e7fa5cf2549c087fb8de54b62724ec8fbbaaadaed05f2d1bbbba24f8f07cd10677263c201c7f3e8b7583905308cfc4b5fb1718892bf13e5139e2d7ec46b04d49cb666a4c9872bb4b1e8ce5924d8a57deeac7d4186616e9477cfe85fd97d5c0ee2e781ff3162374bbb2067d998c28f00fde59d9dac58d5b6640ba2a8891c6acf0b5142b4eba1b60a62dcb03f3483075fe0c86cf00207eb951d937dddd97d68adc9b85f67a3bbb4dcbad5db49648882a1ce1424a8eecce7a984e14fe0cb1da9f6a4aa710390687e9a2028456b9ceca867b0b42838f3f7a8f92700489ec15a1d08736e702e053f8b721da5f22947b90d78757ca72ee2f10f97639df67d8a9b6cf9275c5c7da5c9adb84bc2eea9762b81a1c525d64a8eca65df1d8694d5d5c2802aeb93218cea7a64f74b45d9c8849aee56c804745c7c12c52f365784d264a16273027257f7057d8f0204adbb38206bd25e1c71d4cc1fb570e303d818ab131a8d01172d1be0f1d552f36ba4074d59803ebebfe3087a387cd99a2e7b662caa02affb10911cbe87	0	\N
33	196	Joshi Kairav Paren	CSE-3A-A5	2025-06-25 02:19:16.652+00	2025-06-25 02:19:16.162+00	<EMAIL>	\N	\N	d538e42a16555115ba3d786b8e69884fce6d950ae67ef694a48ca86175b42608	b12caf9c3fc0ee636890e027eb3e1d84e75582c9d0d2d3e47dbeb900681e7224156077c80c430f95770ae719356ccb10693bfa5182c0a27de060bbff641ba263fd667b9dcfbfab4891eb562c9fb083beb09daf8ed3f28ec5dc902ffdaa745253fae10971e23b86ae0b959cbc8fce37d39ff6c48eb4f185820da6743246e5e5d40637241c41707ef3441064cdedd0a049ef87d7f7a98b06e4b0f42a40167c4ab20c94df2ae273887fe0193dae6dac467f6c3dc87f13ff4f1468bb82b69933801bf13b2f62627de1889f0905cb0ac883b8b3be65014513c22e7a554f9c6d5ebfb1dc13f83b723f127dbfe2d10c1272e2cf31571567a44445a471b5ffc4f203ddd8914dfd01023801ad1adb01d8cf5b0148eb1e28ddbbbcd18d0b0749e425284f133871fe56a2d992e1513c6721deca19cf4002646f1ce59c0b7b17a7c9286e27f558fea1d794837040f2b0b583859d7542f275e0dffe170b8ab496688eacba13c7d76515458ed54956ee03997eec63be0d71cef9b6da6909ffec71b992f374cb235746f02d6231a903cda8b8ad093e6b3c19b61ff04f91b20e0d02b828958909fb761ebe8589cb937267b7bf98d4b8cf1135dcc44a85230a4938b3e36f06ef3173320b13053ee921947c05fd109376d5954a08ee982e473f096a55bac639ccb23afdfa138af439e40ac4681202f934db094d3ad207c63045f0fb47095e368ec23f	0	\N
34	183	Janvi Kalaria	CSE-3A-A5	2025-06-25 02:19:17.729+00	2025-06-25 02:19:17.238+00	<EMAIL>	\N	\N	3d1a9f6b00d26e886ff3e0769d9085f5d3ecc1c918df0758c002cc323182f3bf	1f5dbe5a58ae8ead23fe423a5dd6732fd1f0c02e4b6fb13461520ede13efa658e036249ab210c7f309db0ae6f3b129be306ae62e71eeae6bacbb1ad2af3aae5ffe3f70001122dd627af282de6ba985bc8f3a535f92714f79fc7f9d4e4146f12dcda3517509b3611861fbd87983e6a8c189ed9498d14d344c1d333ca15bf964093a28b987441bacbebb882bac001cab227aefc6c39eaa38a9b9e68c9d5383b6090b4b087b0a2922b68457f6534aaf957ff0aae77b32e050ee87a2f740e5c186a525069572330aa2833595bc59f6fd6011802504bf340caaff0748146fc34397d01629200da7c711311391b706c2e6e0101f1ba619f4ab4143ea2432530d27708b5be07a0b83603ed4b667c96816f3bdb525a4679effe10afbaeba9726f7bf5cd833f806199029c0123df214fb14baa1f8949ed02d685df051708776ac6d23926066d6f618c883570ed6b8e2b2388b39d44bd9583478f31132d7cc12a00812e2b8acdce6fc4828b2e2f75ef28032fa277fd9728e8e0111c7e1cf06dd76d39840a1ada6055e6c04d020b4324e676c07c58b805593e08a8751dd1f7ee8e96b2c25e72adb4bc5efc78835b74c75b80d1c54893ca904382be1aeba8de3424c1a36dd8d01824be9d18483ef6d72ff5752c5ed7a62a30ae245cd194a2488e53c5bc962922084b9a5c476b0882f09087f2d0a258b6a27fe4241a807d0dff363e55fa98f27	0	\N
35	187	Panchal Vedantkumar Niravbhai	CSE-3A-A5	2025-06-25 02:19:18.803+00	2025-06-25 02:19:18.312+00	<EMAIL>	\N	\N	815661b5d30b0bc312e6f25fc677628e5b25fafe68189b0018aa05f3a6fea9f5	1b31ec4aac585ffddc1ef7c5707968b183f874ac9e620ae2631d9b64ebe3d79eeb152b4fa4896df24ba89c8f9962ddd1f60e4e49092c0ae60980d05917314cce1eff0a28e4a1d743e78f13d3255db8a359322043b7d59bd1cdc73590bdd192274b8e4f3870fd5f7e0ff5b3200627b94bb19a81d588d149bf449ce7f6814f92699dce09d61a3f99b014c2c572fa45057e5c8b41ecc58b4bc26d8a430e45b5fbff36020ea45c4fb93a0618cf0f96f4087b21f1e7fecdb98288780d966ed1a67632f81c3e596fd392e58aa029abd931a6d1a952c58c3c1775e1f94afe738868f6205f4609ffd2107f84402ab70be52de47077efe997793350cd3ce5a70b18cd28388b1c0fa57dc017f2f893a834da871d3384ea9d28fcbc943a5dff6216965ec62938fb5365f27b8d5feeec3016c7972efcc823c46d0a41ec567226721fb706665883a8e67efc661829b883b3a54080eb596adc600b220df9177c4c5c506fb016ee37478c30fe45f9a115f85f10ad297d968306151d9ee1acbb5560ff826601302afec5a6182e550ee3841ba0ce80dde3e02c2c1bb99c14f8ca1f11429e782282667d90312ecab05e44a5f095e5dfabb70914ae713c58c6d3374070ff6cd29affe2981ac8b95a82b79cd8a20968ab68ce3bfab253a38d4fbceb5f82a3717ff71724ac12e5777550561e714634e53c45b682dc45e51da0b84d499654f39d0111c256	0	\N
36	193	Radhanpara Saumya Jatinbhai	CSE-3A-A5	2025-06-25 02:19:19.882+00	2025-06-25 02:19:19.392+00	<EMAIL>	\N	\N	aced17afd85e59531e877e75bddacd76c60a08ac2f39975580f62cc4209d1fb7	a04ed160a91388810b8c3f6bc35e2bd408844d39418fcd99b18270d9f77d5cba16bb76adae9d52ee29ab450871b591209205b03e878952393281ddf7d6afcdfcc1fb0acff1565a31d7a4aee12582b4309233f65b99bc395dce3325bc77919bc3213860194d8c17ae4a2bb48aa02b5c2396a8e97f955e6a55024ccefbce199e185edf581d396c403383cdf53b191f4ff4ec2e78c6bbe53e2cd6f89ca1686956375c4511499d30ad6414eb7915d180fa88b245aae8985cedb3b839de361dfe223be9796da78094f09a44fe7e6509170901c2bd782a4cb77abc9f021d5f2e1cc27eff61a109222a63ef2b0ef31d251ebe53c2d8e295198e71250ec0816401a9eee3770818ba6e1d59765b336860062580e0e600a80bf3d04527c768b00e7c628dc665440c658adffc4fff72b89576c39473a4234bc16806b89eee20c1371c8e3ebd10db82eea18fb8a4077b723539857054879a6b2d05a6730b07505c5dc8e56fe82da770505b53411fe8722751cabcfe3772a5175ee568c8b3a4a6f6f94c7c6106e9af26f70bc00c4f7b827f05839f67da95f7d3b63e7b42c7cea79b97e978426b3e5a4a227d882152cdb64802a6c2225883af19313934e3e79bb9cdf4d104f2a86804cbe7ca3eaf2beab16b3456594a772b2ae9649fed186dd102779cf60da23002bf071360867cb6880e123e51235ab1e6beaae3568928c214a25959b29a88d9	0	\N
37	198	Radhanpura Jeel Nimish	CSE-3A-A5	2025-06-25 02:19:20.959+00	2025-06-25 02:19:20.467+00	<EMAIL>	\N	\N	bf0eed6de751c26ca713298ffca274bf43649c6a4a4d5525679d0426a1e08abe	e4f5fb574cb5efefbc488b9db9a0f181d7a6eafd02866d02d321deda1ae334a9ff09741fd53322598a186024c9fa2264e92a078b49cf874c798eb984677951893c92e86050e3b16d89d7ed69cf8624504cade04e9285628d725fb5ecb7c7c20e4701d2cec1c6bd842c3afd5094fab36ab969fe7f434c14598fd65534d6d062830d80a2258f4c5763b50bf2640299f7608be800004e8ab88710795bdf640a00b9be233e2daacec16a6b2cae1fe924fb12cc0419c945634af824b2a2951573c2aa019386b2988b19aa745c6c1f618c30ada04baa9473aabd8f963f0ac51a7194562eac1b9dc905a95d9bcfd99f238b11aa0da0f9a4b88074452984578d3447924ac945fe05c76f1cb8ec76582359de485d80f67744eb88bfbe5b6c23c3a268b64fbd03287068b3de4fd2847c49ac551ebc43ebf7fcc3328eb36f196dc7f10a5f3de831821cc2ebe78e3ff52136f2525368c62b33f06be5e02981bcb45a5d8fc9008ebaead57fc6d75a231ea46935dba91296b33683b1a967952a87a9493726692bd3bdbad7d8e102b0a6d1b41f4a871d154c26fe31d03f8cdd011b9e2ca1898a092202ad58ddd3e96d6037183a24cd69ba10328749ef6f54d7f1584fbdaa530e765f1570365725366eb9686ad5d11fa1eaf15769eaeaa5b00e55a65e22f9667afd0a056511b78be33d143c9554b738095d5f4d4323bf67332d5d18f8c74caf5bc8	0	\N
38	189	Raichura Smeet Rohitkumar	CSE-3A-A5	2025-06-25 02:19:22.031+00	2025-06-25 02:19:21.545+00	<EMAIL>	\N	\N	cf78d2ec75afa6e941c3c4199e9f0e87b3df43a03f3c53ed916f647912b7447a	fd31a348d3fc7462aff3a3302d614a9f63f45349eba8de6bc0cc078809170e2d95eeca3dc9020d62fcbd70e0a1e948c73035d94d46b676041b7249ccfcd61863c1f5fa89081311400f50dac20dabaf7666c3beda2b6a94904d51941fa89435cb618076557f6b92636176493df0aa801c246de4922b5ea095c1e1dfdde7a38314df3e81c5fa440be796005eba71fe9b7beae0ae35496f3e925296b15b0752c0f0d0571d2fb97a732e3752f6a2f8b2a5449a6e6a7df2ffd0e6385a65ca364255b128a3ff8c9c7632104b671b82e6f9f662d6dbdddf80e3844871616675dfd382230a6f92c816bd575f6f2765702ec1ec8972f0a7035f6583b83635eb9c3d5966484bec99e466aba8e6528b0cdaaf15e6332aa2ade410228fd1a08d663cbbf8c05d24cf52ea57412f2b453b35d1d1b6e87244187141c96eb4f927b2ab4e4a8a6814ed1e5afdbcb09d4256310d19292a2cc2d5154800f919621dbd4f1d7f3841c50ddc764db05449f9a5e29c61f7382e0adb4bc8fab1fec2d54c8f5bf6eadb8d556aaab7428d6e2d7c16667775edb43f5885c9b4a0a306f1af62a27d2358ff5fa020be0aadd748511848ce60d85bf01820a0e86bdba678fad1b3bcb9ae7250cd9ff3e942096d51bcd282dc808201e902cab74bff35bfadbfaaa1eee706f3a3b2524e2e5808e5e1426ba5b67c123c865c9af92a75614d62324aacb4fcb0f9f7750ef6	0	\N
39	184	Rajwani Rohan Malik	CSE-3A-A5	2025-06-25 02:19:23.109+00	2025-06-25 02:19:22.616+00	<EMAIL>	\N	\N	25734724b1c769536a92282844065d81e9003c03b3d018530eb6aaf36d010944	017a9e777285735fb0f326fe83d7a5d80413ef53069add6879ba741d52d0e6d65ac38c05b1d545bb320b2be56b7d0f6326bebf3b5c42117c53a18b033c4a7fc4eaf62f566eebb759bf10e18a9ad2233be9d0627e3320633fdeca0a3ad4ec58cd7cfd802b50c38ab29be9a9ee76b0b32fa8bb16d7006a1959dd94e1954b6e07084ba3c562269664439d292443e0f96b9d52dfc2c7d7564af6f2d2a57dc523c99553a8f2b1ce4eef902c91cea43546ecd60a2aca5abe790f451c8e025af860c35e241d978ef2339f34a5af56826a14b59de89177efdae10549b7a656ff45512ea5459919c9099b1827fb58079a085d7f68f37d34e79127150f0c8824572cea2ec2b2334cad328725048511b0836cd9812125b265f8bc1f079d30aa08b873e5685e62efd9db1f999b83ea597cdb86834dc35e828c3a39066ba4dfcd1c28f49169317523582e5eadb15de07245abf3e72edb2ef9128a4f9f1b712bb0e770e4abba6244ddf7f66b0578d6856aeedcad090f79c88da3d514ac2c44d7c6b627c9ab0674c46c360eda1594b611cb399843bd840b59eab630ddd3c35ac5420ed7e6a22b998f64b4122cb35df49214b118668ec49b92a44bd13e1aa43f8f5b6ca6210126602fc8d286f1ba3013bee19829454c42b350c6b8c356bcfcf0a0d842eb96256b0840126ce287b93fa4b554e974662664441b846854f4db322793affa6b9531114d	0	\N
40	185	Rana Parthrajsinh Bhupatsinh	CSE-3A-A5	2025-06-25 02:19:24.182+00	2025-06-25 02:19:23.693+00	<EMAIL>	\N	\N	5a61c673ecc2d15c2510be69b39fe4403e4a97a5de101d75889c08ce3c94f542	94987b303fb48444776d5fc60438e4d370afaa8cf3845ae8857b9de3c88a9cd7ac04a7947111753f32ce9ceb4f1b0860116cb68435879a1acb434e4ac3e2ab0716b85ed970fe5612be8f6f22a9d7e7d3f1f56bfd90fde9936d07f01832908874e1286d4b5247eda6f614ce77f7796b681488c30166b04bd19afaf827b8020645219bb8c9cfdaa93a4243459868de10ee1b388495a7cab236f8b560974dc9004e56bc076a00ebfb0a3a022fc4c2a6f542ca233d487c929e3277a71c48f5c813adf658342643d398f1acf0aaf3b1014982da2a6633a51837252596a22e085a2307a73977717edc8aaef2ac7e181de7bb6f221b94a69375943a5d9019d8a43c1cccec2e61e08d5ecaf97265902a03885a8463fb0cefcc58be223dba128357ee4ca0ba624c9084124b72b2802680ea5a39e340c1266079f56aeb6a537c6b9a47384f479aac48ab6992183608ca89d9ad4d5fb9c0010d587c15de500262cd1a090037d8a00d20822eae6e85e8dda6536181bf37225c53b996fc4ed304e7c0f385a8319e8cb1ef24c8457839426d433102c49e976b4320199a1d4cca344930ae585cfc2df7b6d86eb6573c5b7fc2f690dd99e6c03e7787771295e9038f6a7223b3667328ec6a88c04ca4f97a99ee0745064f9c1536c13ffa6e7285850abe034a1f5d5eabdcc76e82781f7339fdfca409e23d9f19662adeba64d1adc798a4c8289c6f2d	0	\N
41	197	Savaliya Dhruvi Bharatbhai	CSE-3A-A5	2025-06-25 02:19:25.253+00	2025-06-25 02:19:24.766+00	<EMAIL>	\N	\N	c012b79a2caa923f9cfc857772b6ca219b06aa6b0f2557596b3364593998def8	07ce209d875f6f449b899b217eaa554d5b6a0400080328942421692a20f6f3ea8c3ced667252e3a7f935f6e7dc26ff4bd9f9b6912a68af90ec45330f66450eb4d5099795258a1a3be3cdd39629c03accfda8023b28a218ea158c5d973c466744f47c5f938dd2f670dfe561dfaa461f54fc41c2f5de497041bdffb8c72fea8e44f20e44165c3920a29e3df03f82a56fe2943b113e38ad918bba2c17faf02d951d877e620a00b84d472856257444b46b4b509eb81783eed02fa884a46dd2f65947ef993d62747137cbc5ca530d949ae80d1cf6adb75cd4d51c8dd574915aa00ec989e76c9aeb647ed1f8471f6f3c64a5f698a900b40246e81a1b5620cedfde2e823e25e1fe949373be461d33cef237de950cd5d7a91701b80a96338933feec19cb6ea02b627a105b9077fedcfdfc53610f45956278e406bf651700b9fc55d48e4af3607ea9389f4a157a4dd2faddb83848c2bb10269c219b7eacc8e590b0b16e55c3cf03f0cd2702424d5d35a16a486c7ebc23ff6bb13e4a167c9658a063d9d25fe6b2a6034683c73ca830d8d0c4d9bc6dd00f85a104261a4531251e65e422f495ebc1a508c4a6275d7cd11429b7198610013d77e79f97713522c0669273fd4d276f79452cf08bdbc37994cbaf2dfe67d3c416a4491d5d16af6f183d2bde58ff2910251bb30f55fab5ceb9d1df32fd93d285bb774ac054c8036e64441ca63be22a	0	\N
42	199	Savaliya Priyanshi Sureshbhai	CSE-3A-A5	2025-06-25 02:19:26.321+00	2025-06-25 02:19:25.836+00	<EMAIL>	\N	\N	da2cefdc81df7bf2aa0f997f5257b3028ae7be51e271b050315390c9ec2d8f1f	243978265c4ef663f4af2d740e2cfc4227f9386adb202652d17501efc8904ca732d4212f961d0c2bfe22153a36132c77f5ba146cc609d14cb976078290ea031e8418f14214757b64be9244679a7ff9d18fb51c83b4ca0746cdffce9513364450b775e6896a53ddbb04d632eae5b020545cc2d7ee419aac3e656ef84978e1b51ecad7f77381a7936ae1c3850157f63c78bb83984ac5ecbc283c0e309005a6b9da82199539e3680fac4afb1a721cfdb97e1793f7ebcb1e987bf28f8d5e415728c2c9d7329577921b32015bcf7a794aa0cdc147da8ba7e6114b4ed7f7775628f489cbadf4ed6a39d1168abb64833bc3a791ee954c2a3dfe4897f52d800e2d295339dbbe2d170dfbc5e6bfbc7e7eb83ce9cd6d940915ddb9d88e7492f67dc2a7197f382691e300926913be50f87cc5e503a658f850c95d35fb47b055c5f121edde1bcf1e52624ba103fa3219b377e1626a8d5fab6d8172e1c748bf94c6fb96860ff022846417af4b88180cbc7a90ac02015252dc67646ef0853fe5a464b6407a0edee9495be8fc31d5cf33e63537ec4caa43dc1655a573db4cc8f3c6bb3fb35addba0858eaac7df41f67b87df0b858711d53ac25977db6842e7404cdabebc396dde792b8d776fbc389f093da0aa827e01fa85f1a00c36ad29e1d4dba1e500624ddeb4b05ef816979a8117032c726427f8e944d0a20f0767b8b0734aee6d88aeb0ea8	0	\N
43	186	Solanki Ronak Arvindbhai	CSE-3A-A5	2025-06-25 02:19:27.397+00	2025-06-25 02:19:26.905+00	<EMAIL>	\N	\N	74ed2d0aac88fa6375186b867fa9815528c0c8481070a556392b016d81f7c789	e6218e13fd7160fea130b0191bf8ad53d947951314bb47868d2736abf758edf4a73ef340e469fb06ca54b5462cc35e3e92c575f134522dfd279d5dc5212af5a1579754025a2b71d255db198b768d1fdfbf68711d624d806ef4d6c2b015861b4916fee2cfbf095040e40f97d12ca92ff75b65e4b22a569f9e8390249af9bffb979d69d704c50c81ad4a93c7f9a68715919eee195dd565764de89536030f1e34a1eeaf85aa38157d11ee7c2fd9ca4f14572e27548288b673bff0864e33ec14a066dfee09b645f74bd652dd4e2f44541c6d2da75aa52b4d977fff39eb5887b17c366b1e2695755031288689b88995c4213be7ca4f5e6dbcf6efffa1ce8af6811c1aa9b5f98473adb75caa9d84ec0504669352371a7798cae778f3a99dfad8abafaa9bdd3829a8eb66952a4e5f0c19d7eb20d421e89efa2a320965e7855fec5adb92d7963ae43dd4c21feae85cb71dcaff414f2ebd0293a3a6567e5caeaf3c8b705e6c2338ed750e9074dd783a1cae741c70bcede75d6822101adb755706a766dd89506abe9a6fede1a174c0e41522efffd61bef5240e4bd6d397924b7ba1bb363d6a7b18a44e48211aaeec6c9d247e6a9365771f425afa3cd0095c14996c66cc7af3f8e123502b7d5c41b59473f9c12cb0da9202b16dfb95f81f5626c0420463c983e2cd90dd646f5feed03aae855d042149505c4fe2255213b42fea48a3308272f	0	\N
44	194	Yadav Gaurav Kaushikbhai	CSE-3A-A5	2025-06-25 02:19:28.466+00	2025-06-25 02:19:27.98+00	<EMAIL>	\N	\N	7ce4c00d1ac1a3009cf4fba10a5d28bd43eceee4872ffb2a57f70c5a7ab8dcae	dd2ff15ccc802677ed4c83832f80a11cca1fbc6a7d4d591ab78caddf2d1925129964037902ca2af73eb1281cd82aead6b60ec296e504dae9465d33c1bfa3ad59256b328fa2831336eddb7697aff52c6322e9083b37b10579c12ab7efb095a0d64df8f1e6f4cd3deeb0dd2f55b475eda7705e26269430f0938d23d1fb31a55f0d98ed508ce761004d35e5748a1b2a9846cdb7b400059caa2ea4aa131556eadf2fac0923357c6f35f900b185fc351d1bb6bf0e7d9c7ea35ed663d48c1b9b72cc02dbd392177543da14de44a40f3ffd942a380c8dda7bfdb00df0343869b4b7e34393606af397ae04c3947889715abbdf990a510ab95e427953de0fc3111c3c2620ab90426fc75e0905b94336eabbb5050ea832cc02a3a10fd06de7a5218305d58e3d4179645d6fb35165093b31b4029c0c6b677cf36dd241bc2572e4d4c9f305da90975b6a6e51e6ee99603b244df563c4ef6c2b397b1f2adf21ce913e9eee691f62deaa3471121806daa47a7248f29f34b5b30c2d25d9c71b7e8000d48b57b88a918ce0b6febf08fd41aa0fac3070496f5e50170072938fb0f400741b68c1a546b18b27fcadb25e312ac822c740003c93e6e1fb368d855df5440527be1bed6fc9fbcd3ae9e374750e069738007639f413af15eee48250a55b94814bcf9e625f641ac83a3fadc0a33f7c0a545f4d7803724dc0e06fc2355e18fe04aa71a16e572c	0	\N
45	200	Zala Bhavyadeepsinh Pruthvirajsinh	CSE-3A-A5	2025-06-25 02:19:29.536+00	2025-06-25 02:19:29.048+00	<EMAIL>	\N	\N	f1a6499103611e8ed57bc26fb54647ab56bdc51772400918a0b46918f26ea080	9239cfd83e55f2f519eedfef02ea300a2010ff8a0e85be86ab6325b97b29306429cd8f3367aad20b230992104f2b3475c6c7978f54ee02f3c045d30dc51ac19422b413adbbdb57b264cd210f2ca42cf4bf02bd205fedc35a242a985d16574a7d56605e711701ba746d7a0af551544edfa760b0f5f96ae3220c7c91d124368dff0c7dfc78be3fb0ca81ef5c1e3c6559237c545e294a0a805abe91a6f7ded0663d53be271df5d243aeb145c68c90e72dc8c70e8f83ae0f9b2602b307fa3197e8c14efe0b406a949ed7c2fa403ae5031c610c90de63c5ef8670790f1a9ddc55a3ab1a7002c251aeb538b761bdc842d93650d99b4059719dfc30d4bd9d14b5f05effda28722ac3472c067f262ba3cc7f93693281650ee8e0c9e98877ba76b42d6f78d651cec75efe5ab3b25eb2287dbfd862cc91f35218e642f17070ce270bf37abb69c5ac65e73f236c5dd80656283afc8c6fa360534d689f1679c05ad84a1d934bb4653125d7f14aece622cff295b26f155495e224447600762f5e91cd0cbf0e41598a0556986e96d407f2b290733d1d97272cd4ec04813a7190ad6951603d91bc939cae7e280dd41389e70885fee420c7d6fd20ccc4e7631b6e541e6053765c40f7818584501d6efb309e4a2bec31eb341f4b00cafbb3897dbe19debe8258397626ee9a25733d62bc13b1540e0d74f90f16230d94328c8585b30c338fbd611606	0	\N
46	215	Ambaliya Manan Jayeshbhai	CSE-3A-A6	2025-06-25 02:19:30.605+00	2025-06-25 02:19:30.119+00	<EMAIL>	\N	\N	7ce2b6186f5b58c42120b9646e99778c3f9073e1541da9ba0ecae0e7b325b0f1	8f593b4dc67b47a425ef1fcd9e1a62c063635c6161f9534ac4db61a296c4a00ed661f9d931d3bb81dc12c0374eb835c1da74f269a10e6a8ed3a7ed757a1bc5ebe245f3f81a1f9bdc4246dd1fd2529d2525b07def1884f6135d2bbb38f624ef0571d40a114e9115649472a24ea24f2fc66a3ac29f02bbaa2cce475576884acaa54834b7e6e97ee385a9d38478307a4bca9a0e2f52f9ef3f59f5981462391873de51d3ab9aa5900509b3a083742cb34daded3100bd9f1ea372070d3ffe538beb733ac692b1f97bd2771668f709abf9685994aa4defd4a3ddc64351f14a5571680ce7d56ea959ddec59c7a18c7083a1846ee233aa2d5a05183bb6ab7495b18f8bf98b137b8ec9e71fc6767a863335c445a2ad79e893c93085ec551d0debe1a8db230d7db2ef52bd27715d69e9e68e35a7d335e68cccceddf7ead1efe33ddb9d52cce2d1cde660e068aaa012a0c090ccb2b92ae384b689ac28725f681706ef354bd5022e8c526483c1289d4f7b587ebb45fc8ecfa9862ef56a9a40e29114f5e4bad3e93af1b2196bc551b92f0e2f0ce671ce767e21354f5ced3265b10e1a19022ce978fe96a3aae2ad564d2cb123b0bab841aff73ce2d5b148c1a893e7a82ca2b25d5d0ef436e7bbddec4182bcc903950fd6c791b8dfb94e5349b1d771311d8f0ec88a1f87cb5599865aa25de2ed79bf64382a9cc6af7a195fec7e477879940a6d20	0	\N
47	213	Badrakiya Meet Bharatbhai	CSE-3A-A6	2025-06-25 02:19:31.675+00	2025-06-25 02:19:31.187+00	<EMAIL>	\N	\N	e609dc2c3793ba4a1a9fca9fe456bd8221609a5a35c198560b82d9352a51e46f	2a805557e57530dc75d1709d281daaaafff13fb485888243442e3605902e3431e08bd8c90622c95e24855537fda2f4d1d721bc251f1de789e580ff28d85cba381b2ab6e633763c464497cc8644a0f6fd4a373d54e1fa99b542573cc320e417e8fb4b6bd76f1724f714da9aca48a71a607e0c4bdd1784b9b4515ec495d327602813f5a30c0d44c2e3ab1fd7f77a8e225f58eeb4f508a0ce720c618d12664898c2412842e765b580e2cb66cf78608173f38e3c017db7987c2aec84c719bb860dd42bab94a57455c45c62a47f10ac44d5c51bc1bcfd0d503e4dc45cb679aa84d7ad79e056f219f5f25ecdf4366bcc28ea08bb645df74d892e5bd557892214a65fa18e758f387e09abd4c060478d5f496cb11c17daa1ff5dec671150af08df0aeded5e85fd1685793d0a354d9e480edadc7d4348390cb7a1c3ee78dd450c1d0b825c82a257025fbe0724850668d7f61d708e732900f1c8dd260e2537f2c3006eedeba32020c34c9964a9aee8ff64724d6bb623b509c65f07a1fb4ffd142c02ef970208fdd140f7b5a85fac76b90ec73f7bc843f5b194e7fd33b67de6ff12814715ad93aeeda8c16dacab0a56b41a6dc55738f04378977b11bbdf5850b53eecbb4e47ea96565deaa07381f730cae5cb227a5903ac1a485fcfed8f3ca7815bfcae1729a6e51560f4132c72ef29f7aac7ed3f326ed9bddd2dd283189de1fec849f4fd89	0	\N
48	210	Bhanushali Hiral Bhaven	CSE-3A-A6	2025-06-25 02:19:32.794+00	2025-06-25 02:19:32.259+00	<EMAIL>	\N	\N	c7e382bdb9a430c3b73e42b8321dc94022c44f5fc5699d8716fd2cf299f58c2f	e6ee523b3dae46740e28f3474f45d30e61543c57535e0c4c9d328d30e94b1b3773980632757d56218daa2b321333ff18132c4e8ea0aec03ef92e642505b3f11a8166d528360720a3baac0a8c22c5a3325904c1b38cde9da33bbdf80cb4b750a582d0b3860b40abbebd5b774a817e7fd1a26b689875441c8bcab4156213c443262d4415165414b1956047b100b6b4b1503a27d159e5ce2edf3cd595d567f70afb3b6cb008315df6aba88f9ce58ea94e0f5d6089e67c17a3e922a7effd9c5963492618f7b578d01026426759491be1ef5a505ef47b55a10d9d9bd05c936a4eebe6e3fda69f92acfd1294f032a7598c31f5e2dd80defba379a501c48f5941056cf84758469466cfe4b8ddbe5140355ade9c91d2db5df6a3f3bfa7c0f1756a7a83ae36df90ce05479901e46f6eefb4b649d6f49970d50af2be7ecf84c083f66dc3862b04f1bdde9188bb0eac447d9703dce337c0bd9b62097a9f1714a51c8bee5107e9247878d71670837716efdf36f71cfbd892ef0d883e6d57723d3c901ac992b6dc2c776bf6914a7910d33f66032254461ed37365643007e7820ee31b7424b36a47517f6467f31ccdff261f619b0a211ba8dc86e46dc987127d823dfc07720f19cf752c8050976cff4772a35ede99c3c1aa21d17c271382994eed1c07297004855c1bc436b5e70f98e87a39cecf53f95efcf6cfddacefab44cef87bab39ee4251	0	\N
49	216	Hinsu Umang Bharatbhai	CSE-3A-A6	2025-06-25 02:19:33.905+00	2025-06-25 02:19:33.379+00	<EMAIL>	\N	\N	66e2011be9e6ff1c05711e2df360c6858ab12999f02ffb6f19df227461c4b100	5c5f685d91baa67d406a7570fc6eada0255ea82a17553b1a3c9d9d6dce079c9a5f4aa4d9c2bd0ba914cc280a202345fefebf057d036f6ac38b47f0f622c54f398988f5d6ac69c50770234bb1b40b8ad3607f6538dac9175a3cf13ae8bbb1e00ac62a4da3c98b703f8ddf491e959209a67c83e76b0a64a62ea16f7a9bc0fa5581bfb61278f524197b173e35325e2f5f746c38ea6df9a47f6e576fe4912abab7d5270746966670d299b9064ed173cc310ebf109eaa661c1ddd11899966e001682f52138a8deeda5fecf691309d15d01b5bb32ddd55fdf6cc798cbec84552c92b4989baa9946a6200ac332307573e80deb553b13fc4f51b7268cd7de45b134fbfd0d366a48f360e45ee3022c373e99d3f89c6d834353cbe1b857ec84513b4f5c8f62860e5c35cf117793909eaedd9f8b8f81cd0036662e81b8a89548692ac6675b8cd8b212c543a8b73e7ae354a2ba7b432704098aa2cdfba56fb8a795de071498691816073bcc26ea61b9925b2334925e83fd2589e097acd2931a281ef387a7fe8314f97820461852440f2fe6a688dc23e046bc729f1875878f5b9c28ff26229a4e98177f398180bcea55f7893af8017318275b9c86def2f4199ff4bf3df136707d0105f99fec2c523243d92d3609393f7a15beeeb60e977597d38ee831cbeaa130e1bb6c373062139a01eed42e9434ebe3b67f1f808a07cce667ea00b20bdfd3a	0	\N
50	217	Kakaniya Bhavy Niteshbhai	CSE-3A-A6	2025-06-25 02:19:35.001+00	2025-06-25 02:19:34.489+00	<EMAIL>	\N	\N	035adfdd7c5152b4a05f311e542ca755b3a85efaf090da37a9d23d90c60f98ac	946b2975b413d7c63004ae63ab10bb8701e5c2a784a4d3b2b6700081bbb1c8e7594e40daa1cc916b213166ae0129ae485f1608887e755284434898f8003620d0419b64f238edd1a1355206d1a0a2bdf459197db4cc0d6fdcd91e32d8c4a4e70212c546993e0fbbf9b5bac9396ce215babbaeecd8dbbd86ade780f1b3b7fe15c6bfddd78a7183cc2ecf72fa47ad6cdd274b524990647c6ea31e8b87a93abfcce8b3a5a6f737862b0d1317aa6f0abc68e5950203d11b6bb574179efce4f1c4bc905ca12b7fa4aafc8e15a8a758b886ac4407e980ad249264a0b76cd997a48c12073ec1a3bc6a00b09c06e716f9c197694e3ffb3e5d75039ddefe4afc013bfc8a008676616d28936c6cbc465bda6e6120a889d0627b07ad1dd9ddfaed9f5cb77af6d22ade7f7324610477227119cdebd8f57195b2db24b313763fece50c2d8f0c76fed5743a8abb0f58bef555dfeb7d770ad274e4b8ab4791ae4936e22253109b713a27ed79388a32f1d8293e0f56a05346e9cf7ef5329d10147a52a95ab8e32041c68c98479aaec75051421f0cbe76bf2403298c6f22eab74b2caf5176b9482c3388dc480344ad2da3848506710d3ce1ae7daaeb5c411d02ae370cf5fdd67277be3b2868e19194aaed721412521c74b2a38b705c9b4799880df94c3a94a74735893732a315f96e053f4090f073e328a8bfa36104900df64b723927a6a9619cbff5	0	\N
51	211	Kamaliya Akashkumar Rameshbhai	CSE-3A-A6	2025-06-25 02:19:36.083+00	2025-06-25 02:19:35.585+00	<EMAIL>	\N	\N	c89e984eb8c693e08cddafefd0f23ae539eda2b3c1ec44c43c24004f0dd02847	255e17e4c53bf791a61cc6ab872d47dbefce9bb501a256be0d38b29ed1a4dbd28b8c7d54b46e11c217075ae43f52c474e223ad2da4035f59d222f381069db16c43243ff94ff4d91d225905844c30cdfd6a7739471cc726e7c7405cea65cc2773438bd2bb19e4a532cf0924735b5e6eafbf45fcc22f4a73853b94342c7790669576a820d06a64969b5121dd68d550a5ecdbe51b9f301158faa63a262ee8fa4f370a5b5c5f2e59a06b787fea92016d57263d6993d510ae38e0997121145157fd9de9e285d2879843753aeab0976d0a9db21b82ed00cb9a7be4a0f4d303a8b79a44a27f39d00e2cea21839da5ca0e3ce955f39e0b46eb420c5d13ff20018a6d52832c4cc604e4c2080b398658b2694d39c31d1d021a28895d14331d586ab78e038600452e1e0116d8e47f21d18ba0ed699b66470efbbde3ee85a6e973e6fde33d2149e73e8566c26418cc451b0bd6ce0342daca621fdd2cfb0af2ca871834dced80b4e88adf0af515fda94cf01f84dd2d3960618c96e154844bde8911717ff7f0a9d6bddab6115cc830e0752f08beef00b13344de22415c9e6e3050667113c744b8d6525be5ff9c9bddf8c32892714da88bb16a5b9a9df1420832b32cb3b99dc2be476ae992f648f8a1f9b04d807c3eb11ebcb08924f4cb0f8eb31ebd256cf7daac91aff5c095831a7caab8b145a5c718c3f2a1f01744fa4cfacef0cbccd37b8297	0	\N
52	214	Kanani Kavit Nareshbhai	CSE-3A-A6	2025-06-25 02:19:37.18+00	2025-06-25 02:19:36.674+00	<EMAIL>	\N	\N	41ecc1acd8454d7adcf1cd33adf48e91671d0d6f7071240d47774094d6f593a4	31c2d19d47bb5a0909617cdc405d41f222dd7ca26580a3a3874f62efb212ee69ed0d2f44781b36d6e4f251243271ed696b3de143842a5ddebdec434a3004e3ce8b3b0377d5f2b174a4abca3b0cb5addd2ff2d852def0fe2f9c48028f2a4f5c422184bd959846edcc2d956963fe8bbf6e6ccc3b6fa6ca5470c583796eb91ffda474b0a58704e6fe740c14a2fb9408a8420eb9bd1366bbca7fab78ee1d3a2b9fdb4c9264e1386c196d56dccb064694bf0a26a9786561595eb7bba7d23a6e7ab8434a01503adb374dbc234f2cc3c9373815c973631912d51d55569df3969c52662e665c76a56e8540e838f7745a60f5a44f1ce29b230f55a0236b7481086537ef64ccd68250ff3f9d81a72a9d6c8f1acfcef1bcaa1bcc6771bf32d56837facc77bc8d7746286aacb4a43563fc1cb1500121cb3f93bfe8f702bd701386456e2859596c6d69d377d4cc73f5ee321c355d83e3e27694d90481be908a7724e52804b4581e8887ac8e3be9498ac7debf614f0643bb794116e220e79bd8fc51f31f6020c551c4635f4e6122fa1c92170a87376571f6d0e59145d557864334281cbc283753ac3774c5c1341504982a7c0eee36cd6ce958eef25c815369425b36c710cd7f088a5266f1cc70f95a350d4054974818c15ec103d21dbd204ad4d58f15196704df57855853006a9f56898e0b013643eca4d1f594a2573438f31de74a14af35c89a	0	\N
53	202	Lakhtaria Kevin Homin	CSE-3A-A6	2025-06-25 02:19:38.263+00	2025-06-25 02:19:37.765+00	<EMAIL>	\N	\N	8a1546956913b90c99878368bd58e3fc1d804b9cd7cdf20459ba7e8232fe0e95	0e97868dad9c18bd67e4bb88a2709b3eb78eea1d31b3cf74bb73561a1c1992c2ebe942f55c7d868f02d0a0c3ed14ce86ff9dc6bc9fa6152d527287c77c3513241d5561e041c52a7e73803b72cb3ba2224e325d74ae54ccd914111db45a50d8e116163467559714789717cfb1b0cc4382fb45d5c504efd0cee247a56deb281d54c88561748a1e12171ee096e7f950549551dbb0330d4c720fd1cd96625045c8bc9202c8e7fa3d533b931195669a34234cbc82575df15991e577fcdb06214535fa597a7677af25cd709f747b6910da762d7256096683f2aaea720645be1c36629be835e9ef960f51c062b95e1aaed7769140fa5ad6785220b5c532038f93368421b6eeecd2a9f44905cba827b84aed3e5ea0fe89cccced0d690632e3a7f067efdccef0469d3ca71b1c774ba538dfa52b6bdb3caa0a686f8ac7d23d77284ee2fb7aa532c49656e278ccfd29875c41a3f2467ade901acb78d6ebce46bfc1f295f63599393889eb50592c02988fa61e5d839698641865ba16fbfcc407acbe12dc81f4920c66f912ac4fb5ad4d6dfebacf2812b7c3fb6515cd3cdd7cf4072275b7b51f44edea15e673086b38ce7e739dabfeca78d4dc530d98309cc436ae2b48f6d1826ec5914f5390effad48944ffb54ebe2fc11901b244f90a68b230d4b5880743ab7ec1a9d9707f65cf395334be778acce741b32679e45d077446d29eaca877be91	0	\N
54	219	Mankada Hasan Shabbirhusain	CSE-3A-A6	2025-06-25 02:19:39.336+00	2025-06-25 02:19:38.847+00	<EMAIL>	\N	\N	fbd92b4dc9cdd766f25fca7c38b005a7827ca5fa4df2f303e10ebf8f1deacae1	f35cb315f5551de21100616fb7c1005be71d48c267829997d3c3e7f1a9a06d032986147387f8559242961c47937a3b71813675416013639735a6dc77fb38afb0617ebb37c77527de84fe88233ec119121525452fc8e6a662aa895e209b762017a6adf95d66f81cff888bc1d11ecf18cad5c0a6bf4a7d2ef6c676e2d56b53f6633a99ae800c484f09d95c3443051673cfc66b41ce8e990f41a1b1e84c79ccd72cf110fa7751a6d3ef9cbd73173593ae590d95997a106a34af91876260c7fbfc392f5665ca3a59b3f53fb9e3233bd8af556ed8abf10e5cf55f63e086f54a4f84e006616356a439024604c2e085f4309174dd9f18fd1f1d09294a3c69efaebb5a01b14d850c2893beecb6a4622ea487ce5d20407a7f4a19b4820e0aa96d6fc5713c7b8ee1674c889dd3e08edb381ca3a4a0aea3af41f4ee868a182d76111f88aecb3b8782b818cf88c011dd2faf98c61430007464d5c471e23e19ee56cb566599e039f86a9435ad9bc468bf9add991fd48ac5cbcd78a5331fee2adaed5ddcae8335e0077da6d6ceac1d591d0ce5e08ff573134e4e522edc8de360a6dff49f4b8d4fc23716f1663aa01b9f75caa6ddbdd5f937c7354a89c67557809c11cd26ffd0957e66e48364416fda1ac68c9885eef68ade97351d0f89e353ef255b96e7a5e35351ed5e2b6935c1729e51ff75787d53d0b5c4475d18728b6428fe9440b2260600	0	\N
55	220	Mehta Dhairy Kalpeshbhai	CSE-3A-A6	2025-06-25 02:19:40.406+00	2025-06-25 02:19:39.918+00	<EMAIL>	\N	\N	8b25021427f57d568e0e5ecd74758617d9c7c7ed15ec175e185d67c6dd377ecd	06aa7267053081496249901a03113d4dd40fabea1ae9cfe42b23502449bbb402ed957d9e16974dc10bd67bacac70c21253fbd41b81057c49bbcbd837dd65a594d7123640760cd38aa3bc98863eefb2110bfa63adfc666bcd6ad1ca96220c56dab5ae44711e214c05a31d982150bf28899a5c1c2f064bedc84faf0c30142313e762bafe8924dedbdd68c416e9316a79bde54851c6d2c76e4ee8e6d4367b11e5be1b76491b0c9d64c94ba363fde12bcf59bde3a59fb5e71d5928430aa7acdf5aa70e5ab6b7842e43b8df32c45504c74273d63e66267402df073676ea5687b7e47cd7dc8fe183ea25051244acd0746d44f205667677510db291a9c34382db5a528626e82244b1b0f01b45e15deca3498fe9569fbdf5c2e91cb81051a2948831bb0e9b1e7ae39b3c94a076798cb92562caa7a073bf23e91fe551f9efeed2db89e25e90d5200b89d50e9da45656cd827a34931662312b2a7a65c65d8149d46ec3ae97f394332dbe82bfd9f0556a244a8ae57f1837af8a946a29533690362d9f99c084d3d32a4ddc010b4be0bc98b744fe3a7d11d2af61f1098ebea8ae8f729389e3e18a7ed07ef99322e18fac9c54883d2149767bebc5153d452c36b96ffa829c01ca1c84566a3064b4319358ebe11903a3f8fdb12dfd7f18bffb45ddbf650753cb91615dc97ced96bcb021cfa905ae1862ec329b21385b3aec0f57fa17015e42297e	0	\N
56	206	Mendapara Piyu Amishbhai	CSE-3A-A6	2025-06-25 02:19:41.475+00	2025-06-25 02:19:40.989+00	<EMAIL>	\N	\N	0d897aa56f595be1a04e3d084c0ac1b2eac8a906f38c4145d135bd667050fe6f	a12eb860cdc7884a38f4e76e4d5c447a839962d8b9ddbf1b82e3dbad90cfca6fae17b8225c4383dc521188403292f1322bdb64b15e4ebb72199ad07a36cda08aa5c29426615c2e751fb6d709c4e6cb415a8082d7ad2c590a20322ccddc0690022f5b7653fffa04a74feb25254ea6d3e267696af029ceeee82607d2499b69656f2cc0e1d404c2404c8b09118bbae08480776fe273a3fa93dba527a5d5a94a911708a0572cd3d95027c653e2f0db354357afb26a0630e2a2238306624ad09de6bb29d4e4bd32a7cbf3207591a4c7e3b92b70fad7be82797728b4f02a9c5e2cf6ad47455bfa6d871cbf423237aaf3e7fbc3a812aecee8efeb8862c7351ff8e22e13a324a4b3ab602fa3594e657fbeeb64ddc73ba6ad4e827822ed44e08f287cc67c6b17f9fab9fc3320a7401e94fa59f507bf30cb8c11ff1cbe2c21f8d41c9f0156228217780ca4ce68071cd30c5f091244706912458bd1a088d85198d7274e1d04171baa1a493b518e8492016044ba98745aea7143f9fed17a042a82eb2f56df4fa3a5b7c94f01f7c0fa00ba3cbb6b1f3026fae668481dec20ea43658a4c0d08e31497d9d80acfa7ddbce4d54fed0bde0a1bb198e2587591e3220cbc384cf0b82b91d6a655de1d775390cdf77b62b0cb5bc3e4360d961a64265c7147b215cdb742d4a17cb6d7349ad300d461112aa2cfbe494eff39e2d812baaaa65a9bea8597aa	0	\N
57	203	Moliya Kartavya Bhaveshbhai	CSE-3A-A6	2025-06-25 02:19:42.553+00	2025-06-25 02:19:42.06+00	<EMAIL>	\N	\N	08ab9d7d68431eb1c8d8e8e539ca86da4c3383dc79aece216d3c579e2a10cab6	47be30cc6d93d55aa4d6670107b9cae15232fac37d4ea6a79e7739ec3f2ed9fe3914fdc43ee00fa2f2017fc2af59d7e86ce0af6ddc19fbb016333c93f4a4a3d9a0ec98a83a2d46144f89e42d27dc6399230330fcfcaf51ad9b4afb94e75dd8e215e82cdc21c1b5bb7156780af0361d2a34541be27a399e7f962bd1eac6ae99e8b9eee6c6d187fc39d027594873ed7d8acadee43b939cfdffcd00e2c9ad8993c01df459027e7f4d2406ec6277c6037af2adc5c026a72357a356bbbe15bd5f6f9c73501d15b98fd7b61d77ba2e7697474f94d716c8b4c03b0ffd8bd7825b1174f264d826945518aa11e0665494540b94d723dcbe43589a85c605d663e063537f2d8d3a69e84f81518855068fd38617a4d9d518b24cf368aafc46b619e54f4d4cc34df12583d0fc1a81da963bd767fedf2d12302a2b749e66796f792573f8bbda0d2b510b3fee8dba48067f325749b0412bdc4163a55ab1e885238e9deed64b8661f6962a5ce7fbddedd800d904fa05ee45c5fb117a9a7fec9b5e2dbc729b8be2c646d8cbff511f0650e0f882b017ca398bea7151217a44d10b7fc754761f971f7f636a315623121762b890b4adbd6c2f4f056053238b037f6794f753dfe4e0233acfeb6328512c2c9ff504b96fef1273363fbe80ab129c31a59bf3ec68f51247babf27cc88243c6ee4eb1ad3e690e1c513c1263f287e9f6bfd4f47d8ed553f092b	0	\N
58	204	Monpara Nij Hareshbhai	CSE-3A-A6	2025-06-25 02:19:43.625+00	2025-06-25 02:19:43.136+00	<EMAIL>	\N	\N	72bbb26a0c41bc43ae00d3380ba1e61d4229b6a5f4c9358ccd6fa4e1e4dae835	bb1a3b385809b2e69cfabf8db23c8fdde88940c915b9e870a23653f2950a36a5bfc7a67afbf48515d041b78e21454a4fdb4455bbfaa266ff836d4187a4d3761c02426d544d69f2ffbae5e7223124b33dcd2119caec9a4d76e3072709b3c3ca84b04fc830e835f9fffac075a0f1658a7a7d399d9a9bcbfd15137fcddf24103c967af8c80ca521808a703d100a20c1a97203793efd711e59c91497e624d7508cea4011d36e9fa3c62aa2b481cc70d449f27fb829692f4c003da41747c40cfa02351baafdcdd9056b60043e50af2ac606187c503aea1645cf0be8b2f9b7a879c618094bf50f698c17bf3bc235f34641019ee3b68dc0a2c956ea26da368238607e7b5e1a1493df665eab26edc2928e7694f062e1539074a4e0941d21e10b99b579988440fb0f119b2b44383b620808e0eae210f769de41412700657cc00d0f9d9dc1634bafd090c4f7453386050e3cf1cef5abdf253e5b47639e12cef7ee156c3c4520cc811956dae9f2d2feb659744d05ef8eebd5cf4ed0dff7c461ad17eb10f0243df106845a5c7b543fdc28859be12e90842a25c6abb816b07d05f527f0359c6050a3252281a6150d1d45a6ac35285cacdb959a6a6a70aab2f7f0f4881b778329c98a39cf68b96435d879e3152ceb56ca3eecf38997d640a257e06a919ab9f982abc82cfd4243f92104503fb0f4d958caaec989fbf55031dbc3346ffd5d010c2a	0	\N
59	218	Padia Kartik Balkrishna	CSE-3A-A6	2025-06-25 02:19:44.699+00	2025-06-25 02:19:44.209+00	<EMAIL>	\N	\N	6fbf4af45f6c1030f8829196cbc06984c71b7b69fac9a1b78f2ec5ef53926ef7	79dc823cd5576c4e9593c085c23a5826c87620ed8ecdc25e3a68158dfd4961577635a13bbcb8df19d72009f45fc789a88f04363198c2abafdaff2a053817e61986fc029479e150c0534ed0bf9a188c1a5013b4b5fafc78716d829064f086ffad64c209f29d0603d612714b5e581a21ecf471488fefd1f0a36778dc8bddaeea73bf54fbb90a1a94ccba9a2f545c99153ec2dfabde43d05aeaaa63f10271f30f5e347b0693851699b6c88b496e69adfd6959e6a568a9da1289e6a549c318bb2599121a5aec6d2d82ba196a1dd31b49ec8fb09c9d39aeafad0d66c6de5ec95575b2c1fa3125b7481304dc8bef3f6572672d1be727047fc4be85ce05803ef92ea1139b494258e631f577b9ca8f24969471d043faadf43a3e1d5c28695a4797f3123974be868e4522ef1c1cb8205dc753f28a680b6b172cd02c1325b364c3c52eaeddb2ccf57158d7fe419cdf08963e62fe0e1f3b598ff5c13390db38e785b9f7a7bd6f23a753a65a3bd4918ddd417a1abf26a7dc9f90f278135c90827ffba1f7000a129a763064a5c130202239ef93f98d414a57f0253c5ea8c3e8ba1b716454fbde3df724ccbd615b227417f2d0a965b7f309ff06a10b95003b4c23de67d2326e589012e5fd3c9e77693ca9562c226bb6425da4ce19d4eba14973fba244a0f00e12ba9578c8498b2a68aaba54589bfe084404e713fa951956d4a892d5a2f2347843	0	\N
60	207	Pandya Karan Tusharbhai	CSE-3A-A6	2025-06-25 02:19:45.767+00	2025-06-25 02:19:45.282+00	<EMAIL>	\N	\N	41a79b23a077cf038e11598083453b802d6c040ad53cbe64bd9fdede70051ee4	7e573ee9ea3cf47716cf71a266774f4534e53fd24d7d6c5cba2dfb8c62083086b456df2319e72bc16d7f19b707bb42be012fdfdc013756e3bb5bc8fccb8dc55717b37c5cc4cf6364e3642b282de0bac7dce78ac73a4bcde3ca4b7594b86a7e3c527100f4e46d54f0436d1b1b43d96c1cb2584bd1eb40d65937cd5d404637313dea9adc913277c4e54e73b1a26861a62f40a8dddf17b50c623ea3c777eed789b80f586b798f7fb305c89f737791cef199abeb5efb733fc4be4fa3ce5412bf6e0a522721fe125a9d1542b38f0d8167e4b76fcf7803ebf1c4b981855f708ea4c9790683e495510f2739a556c692f02cd18b98612625a7acaaa760aa06e2479188137c7c989bcd48638bf57998d4cc7328e329213204bae7605ec911f0f740adfaf4b4772e17332b47289dc7d02d5b9a7f7e0b69c09262bb3fcee491ada938f62343cddfc238f662a40bfee2af2d31eb5c2b6c6ce66944af1fc981afd38784778237a5ce9bcf3e0569623e0b2d2beea7c496c939489f5b1124af054eee4075e94bb28b6673856b3b488a47daa2b6d229fb047ea44be4c872048d704a598209b5f9bdc356760e4922af6388270522662b2357b1c656fe585c1180afbbb9a9fabadbcbfc592d86331316e7d8ebcc57b15e4302206d4b5e09947de844a7148f4e7fc8edc03500f44cd4cff6142bd23fed17bcfbe238a2bffaf64ce69f85f3dcf97024f4	0	\N
61	212	Popat Honey Nileshbhai	CSE-3A-A6	2025-06-25 02:19:46.834+00	2025-06-25 02:19:46.349+00	<EMAIL>	\N	\N	9fbb3c10ac1e870a0f5905c80d7bb23484c5deb5fb854a2ad5c2517809826e87	a55433fbafe5cdb4b9620faeb09c8e2c79a1795c63da9c8c434df67d93f180b7d71479a073defd420db86f73b518409a1775c98788302b8fa289941d7ab83e9c8302533d6aa98f47cf15fe33534a3061146d82c55360c0fff70d7a44be42634f0c5c8547a7400879548404ac0503f31b0c9c72a1ee91f06fe34cf160a1413f72d06652bdecf0e85bc6d81a39c0f73e1a8dd6e828000ba0c379be6659623066d178cef3f3a1c0bd14a9c35b0661dd24ae1e656cecc5734e8bd6f4e2dc8b1d7f36cc9675f97b5b5c45a2bfda656e50e60ed25d5cdaad26a3f1cfd7b579518542539d806aaa596200b6d18d358ac8b89d75751ba12132d35372b00dec5556870dd83faa70405e02546f5bfd3eca8d82b54a68852fcf880ce985f4c4b8e4e64e656bf68026b860f7ed2c55718e8f20d1cb8cbc904cf3732f5c454521827c87989db1d8661238bf746a1c9da0c67711224c7ba9b83c9c728a1f372f75319a405a7bd45cbd6cc5bf197ebf7db7276fa27faca41402c21655a5b98651ef4691ac523e2d043cff9c0224d1e18576fa9bfd8943fcef930c43b438328460fa81d4d46a623cf28ce8efb76090c5b94daa5709ec90799d62d534206a878c4d67450dd3f52eb19204e574d5bfb43421267440f2c18ca80db24816dba59d7af8b7c02d9e9c96daf87fddf8a05327a49a6142f528c74d916075b0a1fcac63b7e199f28dc9a08e6e	0	\N
63	201	Sinha Milankumar Sujitkumar	CSE-3A-A6	2025-06-25 02:19:48.98+00	2025-06-25 02:19:48.488+00	<EMAIL>	\N	\N	1cd3d93b6c790c14c08b93d1c1a7ce5a62b9a038660c701deb8e00b3ca23ca5d	6c53f3f8144356ee56186b7e53e6fbd531ecffb5bb322d3e12280b3ea56fac44e81909bc6a55e1aadbf0e73ab1e80416698053efe2561c549e8b6ad94bf6a0c776108534cf01c6d53279d2f023f3509be300697fa0c2aab9a78467d556a7e6d3acad1563d6e5a409e7c92237c515ddcfb1cc9e235dec7c05bfc8351a568a382066543d439d695946f3c91a491b2d020e1d121c63d92209a22f7fa543123281d9ca5af25a5e70f99c8dcc7c8879ac65e72322081cc63951f12939a0805e053dfdca2e2638fde59c616178d1dd9c67b0230057dc185479b0bbc19777dd18f8ee3364a6b9e95b067b42bf94a3ae31ea5402e2194554256c70654f4c7923ca00152244dcb3f15dfd534b8dfff955b222722d99cbba93f16d0651d390261655aed2f910e9b35b1f000ca86e9f791a16067d572dbfe03ec2e324085cb66afbd8fb3783250d50683e3a8761ed2f80b1d93fbd6b5f24d5ebba63629712bcb9e59ea061af2239579617ba7d28f2bd9d6c0d3ce209399545f1f8c62478f96a799223d854bc272063f2ef8a0c85cea41b9c2c08addee52f26d9cc26f48b7d3d5390fbd4a9ce44f3b3a94029c01af52d32585a7cbda95668bba1233e94bbaff01d7399d35722010177e7862766e06b9873f50a520e91aa738154cfe07f22479140fa3de44dd06a70d4c5ac48741d26a0d1cbe76521971dba68168d263c741859fa431200a4f1	0	\N
64	205	Divya Thakar	CSE-3A-A6	2025-06-25 02:19:50.054+00	2025-06-25 02:19:49.564+00	<EMAIL>	\N	\N	35b4c9fe4e7c90e7675c3403382d790f29df160e54266494d23524b11dd50044	306ffa371b3b30a0613b4c471708dc0b2e97ad0e764814511159a65e852ee8f7b79155fb3da676842b2eced78376e33f81332f29d5d662c7ea92b5219e91c40a5b8267fd861aa0d67a8831b1027cfd2c8dc25f8b2500c2bed9c7f6cdfabe8cf2d64d3e738086377c5d35daac446337690e704b5860e243b4b8c44285ab22465c8bc5feffd49dd858f7da4e07c2134ece30a59e927595e59113e249db6ab8d418926d54e774bb0fcdda213a3b0f5db12754897329640de278cdd9055019957734a034f1417683e3ea6f00fc1967ea940c0625acb373bb103234fa7af928b910f87b37005ac1a420b7b29b8c3970c5e46adc7f5cc3b5a8ca67bf26f385d29b280758c8bc0bc3d3eda015159fb9d2c4baa876770c749e0f80bf6c6c7122abf4669370c37b100d9938bac32a6977cabe0754ce0243d0fe0202bd7703ace8d2ccfe6bf69d58326426fce80e02de14b3a002c6571e59e228c339321f539a43adcce9b5f1fc741d9ca7312792f09d610562d3b61cd02bf2d6e0239b9c045aa11461376c4dd2b2df4c77d104843579254ed5d9b38b25895254c3a3d9e8ff851736bb8165e479792ba2cecafde51a7f9d90a1a6146347f88095aa70e991c5a6f23a92c2d204a64b2bc574202771ae096fb45221e3447e639dc7b81fd597dc7a7aa0704078fa56a39ef50e32ce4e12711cb588e5c5eed1d738168d3bbc948f847a5f6d8e33	0	\N
65	209	Virpariya Heer Manishbhai	CSE-3A-A6	2025-06-25 02:19:51.129+00	2025-06-25 02:19:50.641+00	<EMAIL>	\N	\N	59e8378fa4364967d77a944fa4725401d7bfcb564a4cb87adea3f107753d4ee0	cc2debf8f0a1b50af2bf7b33a48933894ee780c537e70112687bf9675c3a5bf6bde54e30615a5fe563d5031c6f3b93b7acd63ebb08454d99a7d6d3ac47f4bd961f7284c74984f950192730f92b4c54552258dd0c2dd653c5e871858fc59de548fbb78e9eee83542fca4ae3c753e80e4d218f2689d860c1975504cb1b6248f48aa7606674fe2a18aae8cae0f8aed3ef9f07a1f238f30ae5911ab6dc3818c327322dffc05857e0221c12232d63caf5a817fdbe9f1c81459acf7e12c931b81ba1804fc6231272ab48bec404167f50b65e522e1c5bc0d2a073dd2e4204d8a19b9fc5c001f95478849ae15c3d168ad9c31a2051d3887bdca5cb23c67f8e7c68ba24ecf5b7e160dd327984d13309bd8a89e3bb301020540d9debc8ca4e7e0d8eb47e59b039dd46d53563d7c479f6b839c7385d247c83e23da25fee1db5a9d9ccf4fb5bf6fc49e536b4c59805eb1b38e6a9e76934a3c49a8bb784c246abb0058792de43bd04486c9a573e31f9f24ae464ac0c40211ef4369f09de4bc7077ea557e077ab02490448fe2963ef91d85f2ed38a06663c3891ba68a053856230927929c805645380e380c598a44d73dc3f82e4e0a331c764c812457ea4fd07fe9eebcff313f487ae2cfbb730abd496ced248127c74d17ff8e71ba09c8d6593cbfd7965808e0b8cf8d2d200cf67cf85b720dd230766cd244acfe2a09a646d08a4ee466bffeedf	0	\N
62	208	Raval Meet Mohitkumar	CSE-3A-A6	2025-06-25 02:40:43.261+00	2025-06-25 02:19:47.418+00	<EMAIL>	\N	\N	e8e11b77630c02b0f032a150858cdf9ee841326cb91effd328b79d857b2714b4	fb28de49d10d1665a3e8e8c9f284d76a4b68157637645b8778584809833f7abfdeb81b3d63928bae942f1d4af854ecfcb2e21458e50459d368cdd8bfe1454aaa84a475ae7e8d7d1b2c5fe816cacf8616e91748082c40d6bee6f92f962e6274327afbd6deac8a251f0d6dbf31f3ed4d2eb5239a6f75d28ce1c5c484bbe0341ef19126858beea7538b396ba89787af95c5f49d1162a8e9f4ac1163f49b8cd6653d939277c4e0a7206560c82508db897d8533fb31488f778d794351676bbe1ad83834c11f4be41aa36523ffe29dac57424937f34f244078f76b0266183e0ce27c3a404da63948585fa92b5cdc0422e34639e424bc6d50074bf0f28863a828e9fdf9192a16013c81e084f0313c5ce33a35c2aa7a939379008825c568a58323ca1e050497cc800ce61565e3827dfb11764462241703fc9a48399ef9db8cc21910c0b2f438d769affcc834b04f65866861cc47282e5c92d6509929f4abfaf37515f4b606b9b47a3e2ea5915f66ce694ad1e8f62186b8947cffb9bf87e3d5cd276f221ae6d39529f4864ebda08006cb26c6687acdd1ba51d5853a6439338c8896d60a451c15d16f69cf171e89ce2efe3ed8a1ba012d03d631c20849863c7090968a55b0b2c6a3f061014ec4c2a16544b263add60cbbc43d32bc73d178b34000ce2d9137c2e169ef3d465f7e044e3607ebe75f3790ede142bdd8dea58392037efc2c668e	1	\N
1	soham	Soham	\N	2025-06-25 03:02:13.347+00	2025-06-25 02:18:40.814+00	<EMAIL>	\N	\N	13330c7923c855888b038eba1f7aca3106924a832f468c8d22517d867988ba73	82ebc39b66b655ef49bf24b9c385aac375613dcb489157592413d8b5d28d0795a67122f8d550fb673d3c2057a85ee43cdee3946c9fabdab1ec91be412537117c71394d8f900afaa49f767fa4169429184a7ea50cd71ac6d4fc0cac916c3afc73798e16422dbe94a58ae0db33d5e95ccadeaee30fda479b480fdb7a7aa1f71e639e141cbc99c9dcb0ee48eb9065d8ed57c8a74159dac4d41c57805f9aacade93e128f74caec63f708f6521e89ab2297ec77e82c370657ff802601cbc8d389a94fd61cccd285639be6e57dc2e2f9c41634e325ef609ac3a46a520476aa22b658d1e059b52b4f2161717def6c81bca9e5edd092c98ed0f1a80931f598896dbc1aafa3876408228e28dd07ac5dc76c2761f247249368a335123d759f74b8069a97c180650bf51ffec7062c5713aec8583549872f2f4ffc5a290d7390dfde6cdb994c941c7f2c10a9442bc1afeca2aff36a58245729a6756c1bde37f59b5af9b5824df02bc7c9587c8968a141406a8628f60f3ed5c094efa906553c667003da4238e9dfe9521afc939c5bef1a8f362a30315e8362d6b20635225d31c7809f0e9efad09b712edd3f5104146f59136d7d5aab9c8e94e3c931246d117ea48b9a9731705f944f24bd1752ae2ef70b83068e2350e34790b42d87f8d016771b570064d303c9e1bb4094e8368d4f5f82004c2f0185ad3f1ef9723435d1f1ca27191380a3519d	1	\N
66	dhruv123	Dhruv Rupeshbhai Pithwa	0	2025-06-26 07:05:08.012+00	2025-06-26 04:55:00.58+00	<EMAIL>	\N	\N	45d3e73f7e376e86a849f97ab8e83fda3a5b9c455cf97c6c9734a9baab8a581d	f67604276f41804a4a5eeba3a985802cd81a042b8e4d9f4a21660e8fe6bfbb7f60eb6d2e19fc2c14586aece88372aa0f5fed3ff6f5696b0fa38cde755d51016dbd6efccde00988a99eac995e1e40f1013a4c0f4a198f59848cc168e92f864f0d2b44b954500a19fb93c87bcd310100e5e3666477472daf52fb181fc1e09f3a7bc443e4e248bb67e7ff1eddf1aef40a014a29c8067b4163506529cfe47aa6b39129fc6b74afeff1132802506ca22be9e568346cbb0bd0ec793ce4e1669735d69691ed2838e045637c6cfa7d0977441cb3003aefbb2e83351f9765be9f53bcb8bd82631c49e8bd5bcef7947481467778cf09f98505bd7690e7ad6021a72cb68a0993fade7636fe27d6ead59aee8288f221f58dfe8390a54a9d6c408061e936bf12b07d5ecfecf72414bc09e382f1554280cece3b955e640e0d0a74f5bd62fcb73a1db23414eece6c6bf41aef16d2c2863734b4dc1c4b19c989baa1b53b5522d5ac0560ab717382edb27c499710c2ee51bf2888aa5c9c5d56e73d58e2b6684c3f10221028e7ed401f82016f69e5cf40c8e80dc7ab1e19a2bcaf9916cecf1363bcc04403f85cc26eb64a529e8b88b3585cede0b8fd916e20534b40abd401c63c50ba9a13b19ec291452bbcdeed33386b1a2ef1791926451e9a551fd6d66024610f0c8eb20e4826d7a3c5a89ce6fc3938ea7ecacaac8cd8e0ca3c7023327da5cc5d19	0	\N
67	student	Student	CSE-5A-A5	2025-06-26 14:13:08.061+00	2025-06-26 14:13:05.194+00	<EMAIL>	\N	\N	d8929784ec63d5c3f9c8ea4166b71746fc8c22b68be934eb3ee820d36894a237	bf5d3b07670f633338ebef763049a7c208194918f12ddf20417e9ca849103b0e74a3d9faf51fa1bf1942d6ffca1a4c7db5a7fd73e87993996a828ebb67f24e78ce051d03584ac1a6d566eae73c6f4053cd13460819d9bed748efce965ebd4579978f8678e068162b3190de3e2331b9a510981202657a11c92526a0c5077c0819d2fcab5fbb478587dc1325b0836361cbe9f775d9aa83dc1221c366e6b405043267dcc42c631cac5b5391481010ae26a4726f6b2115f9d3e35a13c939b3dfcf284dbff04670aedf0a4d160e4f7537f59a0699445549219bef769eefaace90467311aaeb0fd4fa7fdff7c2acaeeb06318d0277bb6b515cea9804e19cd7622b7ed3e462883ace93258431dfdc8a0a178ea3d9610ffefe9bf29bf3c97e0e932518183d1b6acd8c44ecdcf1ffdb1865330fe479e9a23d12bfc35f5294eed82ed3f9c05f1b35b53a774cd4b95367785064cd90529953fa419447d37de540488f00b9a106346fc8b72cbff0d21b76950fd8e99aceaebf8a0504384fc33472e5534395573ad82bb7973e33bb19f548022ee015ec52667d425feabec336e3c2f612b14fed759181ee9aba70ea44e2eda5a2aed95bb9d194483828e70983d415b9c4f175c24515b8e59be6bf32bcbaf86130e3506b4929a605675acf81717929471d09bb730d6a9fa93ec93a0bda6b2231d251c72afdb07fcc892ab11badd7fac8a33967aa	0	\N
\.


--
-- Data for Name: users_roles; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.users_roles ("order", parent_id, value, id) FROM stdin;
1	2	user	2
1	3	user	3
1	4	user	4
1	5	user	5
1	6	user	6
1	7	user	7
1	8	user	8
1	9	user	9
1	10	user	10
1	11	user	11
1	12	user	12
1	13	user	13
1	14	user	14
1	15	user	15
1	16	user	16
1	17	user	17
1	18	user	18
1	19	user	19
1	20	user	20
1	21	user	21
1	22	user	22
1	23	user	23
1	24	user	24
1	25	user	25
1	26	user	26
1	27	user	27
1	28	user	28
1	29	user	29
1	30	user	30
1	31	user	31
1	32	user	32
1	33	user	33
1	34	user	34
1	35	user	35
1	36	user	36
1	37	user	37
1	38	user	38
1	39	user	39
1	40	user	40
1	41	user	41
1	42	user	42
1	43	user	43
1	44	user	44
1	45	user	45
1	46	user	46
1	47	user	47
1	48	user	48
1	49	user	49
1	50	user	50
1	51	user	51
1	52	user	52
1	53	user	53
1	54	user	54
1	55	user	55
1	56	user	56
1	57	user	57
1	58	user	58
1	59	user	59
1	60	user	60
1	61	user	61
1	63	user	63
1	64	user	64
1	65	user	65
1	62	user	66
1	1	super-admin	67
1	66	user	70
1	67	user	71
\.


--
-- Data for Name: users_tenants; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.users_tenants (_order, _parent_id, id, tenant_id) FROM stdin;
1	2	685b5c82e1277af85f205082	1
1	3	685b5c83e1277af85f205083	1
1	4	685b5c84e1277af85f205084	1
1	5	685b5c85e1277af85f205085	1
1	6	685b5c86e1277af85f205086	1
1	7	685b5c87e1277af85f205087	1
1	8	685b5c89e1277af85f205088	1
1	9	685b5c8ae1277af85f205089	1
1	10	685b5c8be1277af85f20508a	1
1	11	685b5c8ce1277af85f20508b	1
1	12	685b5c8de1277af85f20508c	1
1	13	685b5c8ee1277af85f20508d	1
1	14	685b5c8fe1277af85f20508e	1
1	15	685b5c90e1277af85f20508f	1
1	16	685b5c91e1277af85f205090	1
1	17	685b5c92e1277af85f205091	1
1	18	685b5c93e1277af85f205092	1
1	19	685b5c94e1277af85f205093	1
1	20	685b5c95e1277af85f205094	1
1	21	685b5c97e1277af85f205095	1
1	22	685b5c98e1277af85f205096	1
1	23	685b5c99e1277af85f205097	1
1	24	685b5c9ae1277af85f205098	1
1	25	685b5c9be1277af85f205099	1
1	26	685b5c9ce1277af85f20509a	1
1	27	685b5c9de1277af85f20509b	1
1	28	685b5c9ee1277af85f20509c	1
1	29	685b5c9fe1277af85f20509d	1
1	30	685b5ca0e1277af85f20509e	1
1	31	685b5ca2e1277af85f20509f	1
1	32	685b5ca3e1277af85f2050a0	1
1	33	685b5ca4e1277af85f2050a1	1
1	34	685b5ca5e1277af85f2050a2	1
1	35	685b5ca6e1277af85f2050a3	1
1	36	685b5ca7e1277af85f2050a4	1
1	37	685b5ca8e1277af85f2050a5	1
1	38	685b5ca9e1277af85f2050a6	1
1	39	685b5caae1277af85f2050a7	1
1	40	685b5cabe1277af85f2050a8	1
1	41	685b5cace1277af85f2050a9	1
1	42	685b5cade1277af85f2050aa	1
1	43	685b5caee1277af85f2050ab	1
1	44	685b5cafe1277af85f2050ac	1
1	45	685b5cb1e1277af85f2050ad	1
1	46	685b5cb2e1277af85f2050ae	1
1	47	685b5cb3e1277af85f2050af	1
1	48	685b5cb4e1277af85f2050b0	1
1	49	685b5cb5e1277af85f2050b1	1
1	50	685b5cb6e1277af85f2050b2	1
1	51	685b5cb7e1277af85f2050b3	1
1	52	685b5cb8e1277af85f2050b4	1
1	53	685b5cb9e1277af85f2050b5	1
1	54	685b5cbae1277af85f2050b6	1
1	55	685b5cbbe1277af85f2050b7	1
1	56	685b5cbce1277af85f2050b8	1
1	57	685b5cbee1277af85f2050b9	1
1	58	685b5cbfe1277af85f2050ba	1
1	59	685b5cc0e1277af85f2050bb	1
1	60	685b5cc1e1277af85f2050bc	1
1	61	685b5cc2e1277af85f2050bd	1
1	63	685b5cc4e1277af85f2050bf	1
1	64	685b5cc5e1277af85f2050c0	1
1	65	685b5cc6e1277af85f2050c1	1
1	62	685b5cc3e1277af85f2050be	1
1	1	685b5c80e1277af85f205081	1
1	66	685cd2747ce274d1dd03a015	1
1	67	685d5542027dc615d4de9100	1
\.


--
-- Data for Name: users_tenants_roles; Type: TABLE DATA; Schema: public; Owner: sandip
--

COPY public.users_tenants_roles ("order", parent_id, value, id) FROM stdin;
1	685b5c82e1277af85f205082	faculty	2
1	685b5c83e1277af85f205083	student	3
1	685b5c84e1277af85f205084	student	4
1	685b5c85e1277af85f205085	student	5
1	685b5c86e1277af85f205086	student	6
1	685b5c87e1277af85f205087	student	7
1	685b5c89e1277af85f205088	student	8
1	685b5c8ae1277af85f205089	student	9
1	685b5c8be1277af85f20508a	student	10
1	685b5c8ce1277af85f20508b	student	11
1	685b5c8de1277af85f20508c	student	12
1	685b5c8ee1277af85f20508d	student	13
1	685b5c8fe1277af85f20508e	student	14
1	685b5c90e1277af85f20508f	student	15
1	685b5c91e1277af85f205090	student	16
1	685b5c92e1277af85f205091	faculty	17
1	685b5c93e1277af85f205092	faculty	18
1	685b5c94e1277af85f205093	faculty	19
1	685b5c95e1277af85f205094	faculty	20
1	685b5c97e1277af85f205095	faculty	21
1	685b5c98e1277af85f205096	faculty	22
1	685b5c99e1277af85f205097	faculty	23
1	685b5c9ae1277af85f205098	faculty	24
1	685b5c9be1277af85f205099	faculty	25
1	685b5c9ce1277af85f20509a	student	26
1	685b5c9de1277af85f20509b	student	27
1	685b5c9ee1277af85f20509c	student	28
1	685b5c9fe1277af85f20509d	student	29
1	685b5ca0e1277af85f20509e	student	30
1	685b5ca2e1277af85f20509f	student	31
1	685b5ca3e1277af85f2050a0	student	32
1	685b5ca4e1277af85f2050a1	student	33
1	685b5ca5e1277af85f2050a2	student	34
1	685b5ca6e1277af85f2050a3	student	35
1	685b5ca7e1277af85f2050a4	student	36
1	685b5ca8e1277af85f2050a5	student	37
1	685b5ca9e1277af85f2050a6	student	38
1	685b5caae1277af85f2050a7	student	39
1	685b5cabe1277af85f2050a8	student	40
1	685b5cace1277af85f2050a9	student	41
1	685b5cade1277af85f2050aa	student	42
1	685b5caee1277af85f2050ab	student	43
1	685b5cafe1277af85f2050ac	student	44
1	685b5cb1e1277af85f2050ad	student	45
1	685b5cb2e1277af85f2050ae	student	46
1	685b5cb3e1277af85f2050af	student	47
1	685b5cb4e1277af85f2050b0	student	48
1	685b5cb5e1277af85f2050b1	student	49
1	685b5cb6e1277af85f2050b2	student	50
1	685b5cb7e1277af85f2050b3	student	51
1	685b5cb8e1277af85f2050b4	student	52
1	685b5cb9e1277af85f2050b5	student	53
1	685b5cbae1277af85f2050b6	student	54
1	685b5cbbe1277af85f2050b7	student	55
1	685b5cbce1277af85f2050b8	student	56
1	685b5cbee1277af85f2050b9	student	57
1	685b5cbfe1277af85f2050ba	student	58
1	685b5cc0e1277af85f2050bb	student	59
1	685b5cc1e1277af85f2050bc	student	60
1	685b5cc2e1277af85f2050bd	student	61
1	685b5cc4e1277af85f2050bf	student	63
1	685b5cc5e1277af85f2050c0	student	64
1	685b5cc6e1277af85f2050c1	student	65
1	685b5cc3e1277af85f2050be	student	66
1	685b5c80e1277af85f205081	faculty	67
1	685cd2747ce274d1dd03a015	faculty	70
1	685d5542027dc615d4de9100	student	71
\.


--
-- Name: _assignments_v_blocks_action_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public._assignments_v_blocks_action_id_seq', 1, false);


--
-- Name: _assignments_v_blocks_assertion_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public._assignments_v_blocks_assertion_id_seq', 1, false);


--
-- Name: _assignments_v_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public._assignments_v_id_seq', 21, true);


--
-- Name: _assignments_v_version_c_test_cases_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public._assignments_v_version_c_test_cases_id_seq', 110, true);


--
-- Name: _assignments_v_version_hints_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public._assignments_v_version_hints_id_seq', 48, true);


--
-- Name: _assignments_v_version_java_test_cases_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public._assignments_v_version_java_test_cases_id_seq', 1, false);


--
-- Name: _assignments_v_version_resources_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public._assignments_v_version_resources_id_seq', 28, true);


--
-- Name: _assignments_v_version_test_suites_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public._assignments_v_version_test_suites_id_seq', 1, false);


--
-- Name: _submissions_v_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public._submissions_v_id_seq', 19, true);


--
-- Name: assignments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public.assignments_id_seq', 7, true);


--
-- Name: batches_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public.batches_id_seq', 2, true);


--
-- Name: batches_rels_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public.batches_rels_id_seq', 40, true);


--
-- Name: enrollments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public.enrollments_id_seq', 1, false);


--
-- Name: modules_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public.modules_id_seq', 3, true);


--
-- Name: modules_rels_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public.modules_rels_id_seq', 4, true);


--
-- Name: payload_jobs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public.payload_jobs_id_seq', 1, false);


--
-- Name: payload_locked_documents_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public.payload_locked_documents_id_seq', 15, true);


--
-- Name: payload_locked_documents_rels_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public.payload_locked_documents_rels_id_seq', 30, true);


--
-- Name: payload_migrations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public.payload_migrations_id_seq', 3, true);


--
-- Name: payload_preferences_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public.payload_preferences_id_seq', 27, true);


--
-- Name: payload_preferences_rels_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public.payload_preferences_rels_id_seq', 74, true);


--
-- Name: subjects_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public.subjects_id_seq', 1, true);


--
-- Name: submissions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public.submissions_id_seq', 14, true);


--
-- Name: tenants_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public.tenants_id_seq', 1, true);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public.users_id_seq', 66, true);


--
-- Name: users_roles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public.users_roles_id_seq', 70, true);


--
-- Name: users_tenants_roles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: sandip
--

SELECT pg_catalog.setval('public.users_tenants_roles_id_seq', 70, true);


--
-- Name: _assignments_v_blocks_action _assignments_v_blocks_action_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_blocks_action
    ADD CONSTRAINT _assignments_v_blocks_action_pkey PRIMARY KEY (id);


--
-- Name: _assignments_v_blocks_assertion _assignments_v_blocks_assertion_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_blocks_assertion
    ADD CONSTRAINT _assignments_v_blocks_assertion_pkey PRIMARY KEY (id);


--
-- Name: _assignments_v _assignments_v_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v
    ADD CONSTRAINT _assignments_v_pkey PRIMARY KEY (id);


--
-- Name: _assignments_v_version_c_test_cases _assignments_v_version_c_test_cases_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_version_c_test_cases
    ADD CONSTRAINT _assignments_v_version_c_test_cases_pkey PRIMARY KEY (id);


--
-- Name: _assignments_v_version_hints _assignments_v_version_hints_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_version_hints
    ADD CONSTRAINT _assignments_v_version_hints_pkey PRIMARY KEY (id);


--
-- Name: _assignments_v_version_java_test_cases _assignments_v_version_java_test_cases_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_version_java_test_cases
    ADD CONSTRAINT _assignments_v_version_java_test_cases_pkey PRIMARY KEY (id);


--
-- Name: _assignments_v_version_resources _assignments_v_version_resources_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_version_resources
    ADD CONSTRAINT _assignments_v_version_resources_pkey PRIMARY KEY (id);


--
-- Name: _assignments_v_version_test_suites _assignments_v_version_test_suites_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_version_test_suites
    ADD CONSTRAINT _assignments_v_version_test_suites_pkey PRIMARY KEY (id);


--
-- Name: _submissions_v _submissions_v_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._submissions_v
    ADD CONSTRAINT _submissions_v_pkey PRIMARY KEY (id);


--
-- Name: assignments_blocks_action assignments_blocks_action_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.assignments_blocks_action
    ADD CONSTRAINT assignments_blocks_action_pkey PRIMARY KEY (id);


--
-- Name: assignments_blocks_assertion assignments_blocks_assertion_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.assignments_blocks_assertion
    ADD CONSTRAINT assignments_blocks_assertion_pkey PRIMARY KEY (id);


--
-- Name: assignments_c_test_cases assignments_c_test_cases_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.assignments_c_test_cases
    ADD CONSTRAINT assignments_c_test_cases_pkey PRIMARY KEY (id);


--
-- Name: assignments_hints assignments_hints_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.assignments_hints
    ADD CONSTRAINT assignments_hints_pkey PRIMARY KEY (id);


--
-- Name: assignments_java_test_cases assignments_java_test_cases_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.assignments_java_test_cases
    ADD CONSTRAINT assignments_java_test_cases_pkey PRIMARY KEY (id);


--
-- Name: assignments assignments_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_pkey PRIMARY KEY (id);


--
-- Name: assignments_resources assignments_resources_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.assignments_resources
    ADD CONSTRAINT assignments_resources_pkey PRIMARY KEY (id);


--
-- Name: assignments_test_suites assignments_test_suites_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.assignments_test_suites
    ADD CONSTRAINT assignments_test_suites_pkey PRIMARY KEY (id);


--
-- Name: batches batches_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.batches
    ADD CONSTRAINT batches_pkey PRIMARY KEY (id);


--
-- Name: batches_rels batches_rels_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.batches_rels
    ADD CONSTRAINT batches_rels_pkey PRIMARY KEY (id);


--
-- Name: enrollments enrollments_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.enrollments
    ADD CONSTRAINT enrollments_pkey PRIMARY KEY (id);


--
-- Name: modules modules_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.modules
    ADD CONSTRAINT modules_pkey PRIMARY KEY (id);


--
-- Name: modules_rels modules_rels_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.modules_rels
    ADD CONSTRAINT modules_rels_pkey PRIMARY KEY (id);


--
-- Name: payload_jobs_log payload_jobs_log_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_jobs_log
    ADD CONSTRAINT payload_jobs_log_pkey PRIMARY KEY (id);


--
-- Name: payload_jobs payload_jobs_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_jobs
    ADD CONSTRAINT payload_jobs_pkey PRIMARY KEY (id);


--
-- Name: payload_locked_documents payload_locked_documents_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_locked_documents
    ADD CONSTRAINT payload_locked_documents_pkey PRIMARY KEY (id);


--
-- Name: payload_locked_documents_rels payload_locked_documents_rels_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_locked_documents_rels
    ADD CONSTRAINT payload_locked_documents_rels_pkey PRIMARY KEY (id);


--
-- Name: payload_migrations payload_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_migrations
    ADD CONSTRAINT payload_migrations_pkey PRIMARY KEY (id);


--
-- Name: payload_preferences payload_preferences_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_preferences
    ADD CONSTRAINT payload_preferences_pkey PRIMARY KEY (id);


--
-- Name: payload_preferences_rels payload_preferences_rels_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_preferences_rels
    ADD CONSTRAINT payload_preferences_rels_pkey PRIMARY KEY (id);


--
-- Name: subjects subjects_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.subjects
    ADD CONSTRAINT subjects_pkey PRIMARY KEY (id);


--
-- Name: submissions submissions_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.submissions
    ADD CONSTRAINT submissions_pkey PRIMARY KEY (id);


--
-- Name: tenants tenants_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.tenants
    ADD CONSTRAINT tenants_pkey PRIMARY KEY (id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: users_roles users_roles_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.users_roles
    ADD CONSTRAINT users_roles_pkey PRIMARY KEY (id);


--
-- Name: users_tenants users_tenants_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.users_tenants
    ADD CONSTRAINT users_tenants_pkey PRIMARY KEY (id);


--
-- Name: users_tenants_roles users_tenants_roles_pkey; Type: CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.users_tenants_roles
    ADD CONSTRAINT users_tenants_roles_pkey PRIMARY KEY (id);


--
-- Name: _assignments_v_blocks_action_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_blocks_action_order_idx ON public._assignments_v_blocks_action USING btree (_order);


--
-- Name: _assignments_v_blocks_action_parent_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_blocks_action_parent_id_idx ON public._assignments_v_blocks_action USING btree (_parent_id);


--
-- Name: _assignments_v_blocks_action_path_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_blocks_action_path_idx ON public._assignments_v_blocks_action USING btree (_path);


--
-- Name: _assignments_v_blocks_assertion_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_blocks_assertion_order_idx ON public._assignments_v_blocks_assertion USING btree (_order);


--
-- Name: _assignments_v_blocks_assertion_parent_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_blocks_assertion_parent_id_idx ON public._assignments_v_blocks_assertion USING btree (_parent_id);


--
-- Name: _assignments_v_blocks_assertion_path_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_blocks_assertion_path_idx ON public._assignments_v_blocks_assertion USING btree (_path);


--
-- Name: _assignments_v_created_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_created_at_idx ON public._assignments_v USING btree (created_at);


--
-- Name: _assignments_v_latest_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_latest_idx ON public._assignments_v USING btree (latest);


--
-- Name: _assignments_v_parent_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_parent_idx ON public._assignments_v USING btree (parent_id);


--
-- Name: _assignments_v_updated_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_updated_at_idx ON public._assignments_v USING btree (updated_at);


--
-- Name: _assignments_v_version_c_test_cases_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_version_c_test_cases_order_idx ON public._assignments_v_version_c_test_cases USING btree (_order);


--
-- Name: _assignments_v_version_c_test_cases_parent_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_version_c_test_cases_parent_id_idx ON public._assignments_v_version_c_test_cases USING btree (_parent_id);


--
-- Name: _assignments_v_version_hints_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_version_hints_order_idx ON public._assignments_v_version_hints USING btree (_order);


--
-- Name: _assignments_v_version_hints_parent_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_version_hints_parent_id_idx ON public._assignments_v_version_hints USING btree (_parent_id);


--
-- Name: _assignments_v_version_java_test_cases_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_version_java_test_cases_order_idx ON public._assignments_v_version_java_test_cases USING btree (_order);


--
-- Name: _assignments_v_version_java_test_cases_parent_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_version_java_test_cases_parent_id_idx ON public._assignments_v_version_java_test_cases USING btree (_parent_id);


--
-- Name: _assignments_v_version_resources_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_version_resources_order_idx ON public._assignments_v_version_resources USING btree (_order);


--
-- Name: _assignments_v_version_resources_parent_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_version_resources_parent_id_idx ON public._assignments_v_version_resources USING btree (_parent_id);


--
-- Name: _assignments_v_version_test_suites_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_version_test_suites_order_idx ON public._assignments_v_version_test_suites USING btree (_order);


--
-- Name: _assignments_v_version_test_suites_parent_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_version_test_suites_parent_id_idx ON public._assignments_v_version_test_suites USING btree (_parent_id);


--
-- Name: _assignments_v_version_version__status_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_version_version__status_idx ON public._assignments_v USING btree (version__status);


--
-- Name: _assignments_v_version_version_created_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_version_version_created_at_idx ON public._assignments_v USING btree (version_created_at);


--
-- Name: _assignments_v_version_version_difficulty_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_version_version_difficulty_idx ON public._assignments_v USING btree (version_difficulty);


--
-- Name: _assignments_v_version_version_due_date_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_version_version_due_date_idx ON public._assignments_v USING btree (version_due_date);


--
-- Name: _assignments_v_version_version_language_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_version_version_language_idx ON public._assignments_v USING btree (version_language);


--
-- Name: _assignments_v_version_version_module_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_version_version_module_idx ON public._assignments_v USING btree (version_module_id);


--
-- Name: _assignments_v_version_version_subject_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_version_version_subject_idx ON public._assignments_v USING btree (version_subject_id);


--
-- Name: _assignments_v_version_version_tenant_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_version_version_tenant_idx ON public._assignments_v USING btree (version_tenant_id);


--
-- Name: _assignments_v_version_version_updated_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _assignments_v_version_version_updated_at_idx ON public._assignments_v USING btree (version_updated_at);


--
-- Name: _submissions_v_created_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _submissions_v_created_at_idx ON public._submissions_v USING btree (created_at);


--
-- Name: _submissions_v_parent_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _submissions_v_parent_idx ON public._submissions_v USING btree (parent_id);


--
-- Name: _submissions_v_updated_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _submissions_v_updated_at_idx ON public._submissions_v USING btree (updated_at);


--
-- Name: _submissions_v_version_version_assignment_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _submissions_v_version_version_assignment_idx ON public._submissions_v USING btree (version_assignment_id);


--
-- Name: _submissions_v_version_version_created_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _submissions_v_version_version_created_at_idx ON public._submissions_v USING btree (version_created_at);


--
-- Name: _submissions_v_version_version_student_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _submissions_v_version_version_student_idx ON public._submissions_v USING btree (version_student_id);


--
-- Name: _submissions_v_version_version_tenant_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _submissions_v_version_version_tenant_idx ON public._submissions_v USING btree (version_tenant_id);


--
-- Name: _submissions_v_version_version_updated_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX _submissions_v_version_version_updated_at_idx ON public._submissions_v USING btree (version_updated_at);


--
-- Name: assignments__status_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments__status_idx ON public.assignments USING btree (_status);


--
-- Name: assignments_blocks_action_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_blocks_action_order_idx ON public.assignments_blocks_action USING btree (_order);


--
-- Name: assignments_blocks_action_parent_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_blocks_action_parent_id_idx ON public.assignments_blocks_action USING btree (_parent_id);


--
-- Name: assignments_blocks_action_path_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_blocks_action_path_idx ON public.assignments_blocks_action USING btree (_path);


--
-- Name: assignments_blocks_assertion_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_blocks_assertion_order_idx ON public.assignments_blocks_assertion USING btree (_order);


--
-- Name: assignments_blocks_assertion_parent_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_blocks_assertion_parent_id_idx ON public.assignments_blocks_assertion USING btree (_parent_id);


--
-- Name: assignments_blocks_assertion_path_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_blocks_assertion_path_idx ON public.assignments_blocks_assertion USING btree (_path);


--
-- Name: assignments_c_test_cases_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_c_test_cases_order_idx ON public.assignments_c_test_cases USING btree (_order);


--
-- Name: assignments_c_test_cases_parent_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_c_test_cases_parent_id_idx ON public.assignments_c_test_cases USING btree (_parent_id);


--
-- Name: assignments_created_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_created_at_idx ON public.assignments USING btree (created_at);


--
-- Name: assignments_difficulty_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_difficulty_idx ON public.assignments USING btree (difficulty);


--
-- Name: assignments_due_date_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_due_date_idx ON public.assignments USING btree (due_date);


--
-- Name: assignments_hints_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_hints_order_idx ON public.assignments_hints USING btree (_order);


--
-- Name: assignments_hints_parent_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_hints_parent_id_idx ON public.assignments_hints USING btree (_parent_id);


--
-- Name: assignments_java_test_cases_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_java_test_cases_order_idx ON public.assignments_java_test_cases USING btree (_order);


--
-- Name: assignments_java_test_cases_parent_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_java_test_cases_parent_id_idx ON public.assignments_java_test_cases USING btree (_parent_id);


--
-- Name: assignments_language_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_language_idx ON public.assignments USING btree (language);


--
-- Name: assignments_module_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_module_idx ON public.assignments USING btree (module_id);


--
-- Name: assignments_resources_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_resources_order_idx ON public.assignments_resources USING btree (_order);


--
-- Name: assignments_resources_parent_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_resources_parent_id_idx ON public.assignments_resources USING btree (_parent_id);


--
-- Name: assignments_subject_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_subject_idx ON public.assignments USING btree (subject_id);


--
-- Name: assignments_tenant_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_tenant_idx ON public.assignments USING btree (tenant_id);


--
-- Name: assignments_test_suites_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_test_suites_order_idx ON public.assignments_test_suites USING btree (_order);


--
-- Name: assignments_test_suites_parent_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_test_suites_parent_id_idx ON public.assignments_test_suites USING btree (_parent_id);


--
-- Name: assignments_updated_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX assignments_updated_at_idx ON public.assignments USING btree (updated_at);


--
-- Name: batch_accessStart_accessEnd_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX "batch_accessStart_accessEnd_idx" ON public.enrollments USING btree (batch_id, access_start, access_end);


--
-- Name: batches_batch_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE UNIQUE INDEX batches_batch_id_idx ON public.batches USING btree (batch_id);


--
-- Name: batches_created_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX batches_created_at_idx ON public.batches USING btree (created_at);


--
-- Name: batches_rels_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX batches_rels_order_idx ON public.batches_rels USING btree ("order");


--
-- Name: batches_rels_parent_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX batches_rels_parent_idx ON public.batches_rels USING btree (parent_id);


--
-- Name: batches_rels_path_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX batches_rels_path_idx ON public.batches_rels USING btree (path);


--
-- Name: batches_rels_users_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX batches_rels_users_id_idx ON public.batches_rels USING btree (users_id);


--
-- Name: batches_updated_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX batches_updated_at_idx ON public.batches USING btree (updated_at);


--
-- Name: enrollments_batch_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX enrollments_batch_idx ON public.enrollments USING btree (batch_id);


--
-- Name: enrollments_created_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX enrollments_created_at_idx ON public.enrollments USING btree (created_at);


--
-- Name: enrollments_module_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX enrollments_module_idx ON public.enrollments USING btree (module_id);


--
-- Name: enrollments_updated_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX enrollments_updated_at_idx ON public.enrollments USING btree (updated_at);


--
-- Name: modules_created_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX modules_created_at_idx ON public.modules USING btree (created_at);


--
-- Name: modules_rels_assignments_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX modules_rels_assignments_id_idx ON public.modules_rels USING btree (assignments_id);


--
-- Name: modules_rels_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX modules_rels_order_idx ON public.modules_rels USING btree ("order");


--
-- Name: modules_rels_parent_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX modules_rels_parent_idx ON public.modules_rels USING btree (parent_id);


--
-- Name: modules_rels_path_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX modules_rels_path_idx ON public.modules_rels USING btree (path);


--
-- Name: modules_updated_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX modules_updated_at_idx ON public.modules USING btree (updated_at);


--
-- Name: payload_jobs_completed_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_jobs_completed_at_idx ON public.payload_jobs USING btree (completed_at);


--
-- Name: payload_jobs_created_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_jobs_created_at_idx ON public.payload_jobs USING btree (created_at);


--
-- Name: payload_jobs_has_error_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_jobs_has_error_idx ON public.payload_jobs USING btree (has_error);


--
-- Name: payload_jobs_log_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_jobs_log_order_idx ON public.payload_jobs_log USING btree (_order);


--
-- Name: payload_jobs_log_parent_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_jobs_log_parent_id_idx ON public.payload_jobs_log USING btree (_parent_id);


--
-- Name: payload_jobs_processing_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_jobs_processing_idx ON public.payload_jobs USING btree (processing);


--
-- Name: payload_jobs_queue_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_jobs_queue_idx ON public.payload_jobs USING btree (queue);


--
-- Name: payload_jobs_task_slug_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_jobs_task_slug_idx ON public.payload_jobs USING btree (task_slug);


--
-- Name: payload_jobs_total_tried_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_jobs_total_tried_idx ON public.payload_jobs USING btree (total_tried);


--
-- Name: payload_jobs_updated_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_jobs_updated_at_idx ON public.payload_jobs USING btree (updated_at);


--
-- Name: payload_jobs_wait_until_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_jobs_wait_until_idx ON public.payload_jobs USING btree (wait_until);


--
-- Name: payload_locked_documents_created_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_locked_documents_created_at_idx ON public.payload_locked_documents USING btree (created_at);


--
-- Name: payload_locked_documents_global_slug_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_locked_documents_global_slug_idx ON public.payload_locked_documents USING btree (global_slug);


--
-- Name: payload_locked_documents_rels_assignments_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_locked_documents_rels_assignments_id_idx ON public.payload_locked_documents_rels USING btree (assignments_id);


--
-- Name: payload_locked_documents_rels_batches_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_locked_documents_rels_batches_id_idx ON public.payload_locked_documents_rels USING btree (batches_id);


--
-- Name: payload_locked_documents_rels_enrollments_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_locked_documents_rels_enrollments_id_idx ON public.payload_locked_documents_rels USING btree (enrollments_id);


--
-- Name: payload_locked_documents_rels_modules_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_locked_documents_rels_modules_id_idx ON public.payload_locked_documents_rels USING btree (modules_id);


--
-- Name: payload_locked_documents_rels_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_locked_documents_rels_order_idx ON public.payload_locked_documents_rels USING btree ("order");


--
-- Name: payload_locked_documents_rels_parent_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_locked_documents_rels_parent_idx ON public.payload_locked_documents_rels USING btree (parent_id);


--
-- Name: payload_locked_documents_rels_path_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_locked_documents_rels_path_idx ON public.payload_locked_documents_rels USING btree (path);


--
-- Name: payload_locked_documents_rels_payload_jobs_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_locked_documents_rels_payload_jobs_id_idx ON public.payload_locked_documents_rels USING btree (payload_jobs_id);


--
-- Name: payload_locked_documents_rels_subjects_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_locked_documents_rels_subjects_id_idx ON public.payload_locked_documents_rels USING btree (subjects_id);


--
-- Name: payload_locked_documents_rels_submissions_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_locked_documents_rels_submissions_id_idx ON public.payload_locked_documents_rels USING btree (submissions_id);


--
-- Name: payload_locked_documents_rels_tenants_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_locked_documents_rels_tenants_id_idx ON public.payload_locked_documents_rels USING btree (tenants_id);


--
-- Name: payload_locked_documents_rels_users_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_locked_documents_rels_users_id_idx ON public.payload_locked_documents_rels USING btree (users_id);


--
-- Name: payload_locked_documents_updated_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_locked_documents_updated_at_idx ON public.payload_locked_documents USING btree (updated_at);


--
-- Name: payload_migrations_created_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_migrations_created_at_idx ON public.payload_migrations USING btree (created_at);


--
-- Name: payload_migrations_updated_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_migrations_updated_at_idx ON public.payload_migrations USING btree (updated_at);


--
-- Name: payload_preferences_created_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_preferences_created_at_idx ON public.payload_preferences USING btree (created_at);


--
-- Name: payload_preferences_key_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_preferences_key_idx ON public.payload_preferences USING btree (key);


--
-- Name: payload_preferences_rels_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_preferences_rels_order_idx ON public.payload_preferences_rels USING btree ("order");


--
-- Name: payload_preferences_rels_parent_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_preferences_rels_parent_idx ON public.payload_preferences_rels USING btree (parent_id);


--
-- Name: payload_preferences_rels_path_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_preferences_rels_path_idx ON public.payload_preferences_rels USING btree (path);


--
-- Name: payload_preferences_rels_users_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_preferences_rels_users_id_idx ON public.payload_preferences_rels USING btree (users_id);


--
-- Name: payload_preferences_updated_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX payload_preferences_updated_at_idx ON public.payload_preferences USING btree (updated_at);


--
-- Name: student_assignment_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE UNIQUE INDEX student_assignment_idx ON public.submissions USING btree (student_id, assignment_id);


--
-- Name: subjects_created_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX subjects_created_at_idx ON public.subjects USING btree (created_at);


--
-- Name: subjects_tenant_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX subjects_tenant_idx ON public.subjects USING btree (tenant_id);


--
-- Name: subjects_updated_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX subjects_updated_at_idx ON public.subjects USING btree (updated_at);


--
-- Name: submissions_assignment_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX submissions_assignment_idx ON public.submissions USING btree (assignment_id);


--
-- Name: submissions_created_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX submissions_created_at_idx ON public.submissions USING btree (created_at);


--
-- Name: submissions_student_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX submissions_student_idx ON public.submissions USING btree (student_id);


--
-- Name: submissions_tenant_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX submissions_tenant_idx ON public.submissions USING btree (tenant_id);


--
-- Name: submissions_updated_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX submissions_updated_at_idx ON public.submissions USING btree (updated_at);


--
-- Name: tenants_created_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX tenants_created_at_idx ON public.tenants USING btree (created_at);


--
-- Name: tenants_slug_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE UNIQUE INDEX tenants_slug_idx ON public.tenants USING btree (slug);


--
-- Name: tenants_updated_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX tenants_updated_at_idx ON public.tenants USING btree (updated_at);


--
-- Name: users_created_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX users_created_at_idx ON public.users USING btree (created_at);


--
-- Name: users_email_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE UNIQUE INDEX users_email_idx ON public.users USING btree (email);


--
-- Name: users_roles_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX users_roles_order_idx ON public.users_roles USING btree ("order");


--
-- Name: users_roles_parent_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX users_roles_parent_idx ON public.users_roles USING btree (parent_id);


--
-- Name: users_tenants_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX users_tenants_order_idx ON public.users_tenants USING btree (_order);


--
-- Name: users_tenants_parent_id_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX users_tenants_parent_id_idx ON public.users_tenants USING btree (_parent_id);


--
-- Name: users_tenants_roles_order_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX users_tenants_roles_order_idx ON public.users_tenants_roles USING btree ("order");


--
-- Name: users_tenants_roles_parent_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX users_tenants_roles_parent_idx ON public.users_tenants_roles USING btree (parent_id);


--
-- Name: users_tenants_tenant_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX users_tenants_tenant_idx ON public.users_tenants USING btree (tenant_id);


--
-- Name: users_updated_at_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX users_updated_at_idx ON public.users USING btree (updated_at);


--
-- Name: users_username_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX users_username_idx ON public.users USING btree (username);


--
-- Name: version_student_version_assignment_idx; Type: INDEX; Schema: public; Owner: sandip
--

CREATE INDEX version_student_version_assignment_idx ON public._submissions_v USING btree (version_student_id, version_assignment_id);


--
-- Name: _assignments_v_blocks_action _assignments_v_blocks_action_parent_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_blocks_action
    ADD CONSTRAINT _assignments_v_blocks_action_parent_id_fk FOREIGN KEY (_parent_id) REFERENCES public._assignments_v(id) ON DELETE CASCADE;


--
-- Name: _assignments_v_blocks_assertion _assignments_v_blocks_assertion_parent_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_blocks_assertion
    ADD CONSTRAINT _assignments_v_blocks_assertion_parent_id_fk FOREIGN KEY (_parent_id) REFERENCES public._assignments_v(id) ON DELETE CASCADE;


--
-- Name: _assignments_v _assignments_v_parent_id_assignments_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v
    ADD CONSTRAINT _assignments_v_parent_id_assignments_id_fk FOREIGN KEY (parent_id) REFERENCES public.assignments(id) ON DELETE SET NULL;


--
-- Name: _assignments_v_version_c_test_cases _assignments_v_version_c_test_cases_parent_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_version_c_test_cases
    ADD CONSTRAINT _assignments_v_version_c_test_cases_parent_id_fk FOREIGN KEY (_parent_id) REFERENCES public._assignments_v(id) ON DELETE CASCADE;


--
-- Name: _assignments_v_version_hints _assignments_v_version_hints_parent_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_version_hints
    ADD CONSTRAINT _assignments_v_version_hints_parent_id_fk FOREIGN KEY (_parent_id) REFERENCES public._assignments_v(id) ON DELETE CASCADE;


--
-- Name: _assignments_v_version_java_test_cases _assignments_v_version_java_test_cases_parent_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_version_java_test_cases
    ADD CONSTRAINT _assignments_v_version_java_test_cases_parent_id_fk FOREIGN KEY (_parent_id) REFERENCES public._assignments_v(id) ON DELETE CASCADE;


--
-- Name: _assignments_v _assignments_v_version_module_id_modules_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v
    ADD CONSTRAINT _assignments_v_version_module_id_modules_id_fk FOREIGN KEY (version_module_id) REFERENCES public.modules(id) ON DELETE SET NULL;


--
-- Name: _assignments_v_version_resources _assignments_v_version_resources_parent_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_version_resources
    ADD CONSTRAINT _assignments_v_version_resources_parent_id_fk FOREIGN KEY (_parent_id) REFERENCES public._assignments_v(id) ON DELETE CASCADE;


--
-- Name: _assignments_v _assignments_v_version_subject_id_subjects_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v
    ADD CONSTRAINT _assignments_v_version_subject_id_subjects_id_fk FOREIGN KEY (version_subject_id) REFERENCES public.subjects(id) ON DELETE SET NULL;


--
-- Name: _assignments_v _assignments_v_version_tenant_id_tenants_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v
    ADD CONSTRAINT _assignments_v_version_tenant_id_tenants_id_fk FOREIGN KEY (version_tenant_id) REFERENCES public.tenants(id) ON DELETE SET NULL;


--
-- Name: _assignments_v_version_test_suites _assignments_v_version_test_suites_parent_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._assignments_v_version_test_suites
    ADD CONSTRAINT _assignments_v_version_test_suites_parent_id_fk FOREIGN KEY (_parent_id) REFERENCES public._assignments_v(id) ON DELETE CASCADE;


--
-- Name: _submissions_v _submissions_v_parent_id_submissions_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._submissions_v
    ADD CONSTRAINT _submissions_v_parent_id_submissions_id_fk FOREIGN KEY (parent_id) REFERENCES public.submissions(id) ON DELETE SET NULL;


--
-- Name: _submissions_v _submissions_v_version_assignment_id_assignments_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._submissions_v
    ADD CONSTRAINT _submissions_v_version_assignment_id_assignments_id_fk FOREIGN KEY (version_assignment_id) REFERENCES public.assignments(id) ON DELETE SET NULL;


--
-- Name: _submissions_v _submissions_v_version_student_id_users_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._submissions_v
    ADD CONSTRAINT _submissions_v_version_student_id_users_id_fk FOREIGN KEY (version_student_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: _submissions_v _submissions_v_version_tenant_id_tenants_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public._submissions_v
    ADD CONSTRAINT _submissions_v_version_tenant_id_tenants_id_fk FOREIGN KEY (version_tenant_id) REFERENCES public.tenants(id) ON DELETE SET NULL;


--
-- Name: assignments_blocks_action assignments_blocks_action_parent_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.assignments_blocks_action
    ADD CONSTRAINT assignments_blocks_action_parent_id_fk FOREIGN KEY (_parent_id) REFERENCES public.assignments(id) ON DELETE CASCADE;


--
-- Name: assignments_blocks_assertion assignments_blocks_assertion_parent_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.assignments_blocks_assertion
    ADD CONSTRAINT assignments_blocks_assertion_parent_id_fk FOREIGN KEY (_parent_id) REFERENCES public.assignments(id) ON DELETE CASCADE;


--
-- Name: assignments_c_test_cases assignments_c_test_cases_parent_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.assignments_c_test_cases
    ADD CONSTRAINT assignments_c_test_cases_parent_id_fk FOREIGN KEY (_parent_id) REFERENCES public.assignments(id) ON DELETE CASCADE;


--
-- Name: assignments_hints assignments_hints_parent_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.assignments_hints
    ADD CONSTRAINT assignments_hints_parent_id_fk FOREIGN KEY (_parent_id) REFERENCES public.assignments(id) ON DELETE CASCADE;


--
-- Name: assignments_java_test_cases assignments_java_test_cases_parent_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.assignments_java_test_cases
    ADD CONSTRAINT assignments_java_test_cases_parent_id_fk FOREIGN KEY (_parent_id) REFERENCES public.assignments(id) ON DELETE CASCADE;


--
-- Name: assignments assignments_module_id_modules_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_module_id_modules_id_fk FOREIGN KEY (module_id) REFERENCES public.modules(id) ON DELETE SET NULL;


--
-- Name: assignments_resources assignments_resources_parent_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.assignments_resources
    ADD CONSTRAINT assignments_resources_parent_id_fk FOREIGN KEY (_parent_id) REFERENCES public.assignments(id) ON DELETE CASCADE;


--
-- Name: assignments assignments_subject_id_subjects_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_subject_id_subjects_id_fk FOREIGN KEY (subject_id) REFERENCES public.subjects(id) ON DELETE SET NULL;


--
-- Name: assignments assignments_tenant_id_tenants_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_tenant_id_tenants_id_fk FOREIGN KEY (tenant_id) REFERENCES public.tenants(id) ON DELETE SET NULL;


--
-- Name: assignments_test_suites assignments_test_suites_parent_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.assignments_test_suites
    ADD CONSTRAINT assignments_test_suites_parent_id_fk FOREIGN KEY (_parent_id) REFERENCES public.assignments(id) ON DELETE CASCADE;


--
-- Name: batches_rels batches_rels_parent_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.batches_rels
    ADD CONSTRAINT batches_rels_parent_fk FOREIGN KEY (parent_id) REFERENCES public.batches(id) ON DELETE CASCADE;


--
-- Name: batches_rels batches_rels_users_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.batches_rels
    ADD CONSTRAINT batches_rels_users_fk FOREIGN KEY (users_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: enrollments enrollments_batch_id_batches_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.enrollments
    ADD CONSTRAINT enrollments_batch_id_batches_id_fk FOREIGN KEY (batch_id) REFERENCES public.batches(id) ON DELETE SET NULL;


--
-- Name: enrollments enrollments_module_id_modules_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.enrollments
    ADD CONSTRAINT enrollments_module_id_modules_id_fk FOREIGN KEY (module_id) REFERENCES public.modules(id) ON DELETE SET NULL;


--
-- Name: modules_rels modules_rels_assignments_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.modules_rels
    ADD CONSTRAINT modules_rels_assignments_fk FOREIGN KEY (assignments_id) REFERENCES public.assignments(id) ON DELETE CASCADE;


--
-- Name: modules_rels modules_rels_parent_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.modules_rels
    ADD CONSTRAINT modules_rels_parent_fk FOREIGN KEY (parent_id) REFERENCES public.modules(id) ON DELETE CASCADE;


--
-- Name: payload_jobs_log payload_jobs_log_parent_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_jobs_log
    ADD CONSTRAINT payload_jobs_log_parent_id_fk FOREIGN KEY (_parent_id) REFERENCES public.payload_jobs(id) ON DELETE CASCADE;


--
-- Name: payload_locked_documents_rels payload_locked_documents_rels_assignments_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_locked_documents_rels
    ADD CONSTRAINT payload_locked_documents_rels_assignments_fk FOREIGN KEY (assignments_id) REFERENCES public.assignments(id) ON DELETE CASCADE;


--
-- Name: payload_locked_documents_rels payload_locked_documents_rels_batches_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_locked_documents_rels
    ADD CONSTRAINT payload_locked_documents_rels_batches_fk FOREIGN KEY (batches_id) REFERENCES public.batches(id) ON DELETE CASCADE;


--
-- Name: payload_locked_documents_rels payload_locked_documents_rels_enrollments_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_locked_documents_rels
    ADD CONSTRAINT payload_locked_documents_rels_enrollments_fk FOREIGN KEY (enrollments_id) REFERENCES public.enrollments(id) ON DELETE CASCADE;


--
-- Name: payload_locked_documents_rels payload_locked_documents_rels_modules_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_locked_documents_rels
    ADD CONSTRAINT payload_locked_documents_rels_modules_fk FOREIGN KEY (modules_id) REFERENCES public.modules(id) ON DELETE CASCADE;


--
-- Name: payload_locked_documents_rels payload_locked_documents_rels_parent_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_locked_documents_rels
    ADD CONSTRAINT payload_locked_documents_rels_parent_fk FOREIGN KEY (parent_id) REFERENCES public.payload_locked_documents(id) ON DELETE CASCADE;


--
-- Name: payload_locked_documents_rels payload_locked_documents_rels_payload_jobs_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_locked_documents_rels
    ADD CONSTRAINT payload_locked_documents_rels_payload_jobs_fk FOREIGN KEY (payload_jobs_id) REFERENCES public.payload_jobs(id) ON DELETE CASCADE;


--
-- Name: payload_locked_documents_rels payload_locked_documents_rels_subjects_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_locked_documents_rels
    ADD CONSTRAINT payload_locked_documents_rels_subjects_fk FOREIGN KEY (subjects_id) REFERENCES public.subjects(id) ON DELETE CASCADE;


--
-- Name: payload_locked_documents_rels payload_locked_documents_rels_submissions_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_locked_documents_rels
    ADD CONSTRAINT payload_locked_documents_rels_submissions_fk FOREIGN KEY (submissions_id) REFERENCES public.submissions(id) ON DELETE CASCADE;


--
-- Name: payload_locked_documents_rels payload_locked_documents_rels_tenants_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_locked_documents_rels
    ADD CONSTRAINT payload_locked_documents_rels_tenants_fk FOREIGN KEY (tenants_id) REFERENCES public.tenants(id) ON DELETE CASCADE;


--
-- Name: payload_locked_documents_rels payload_locked_documents_rels_users_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_locked_documents_rels
    ADD CONSTRAINT payload_locked_documents_rels_users_fk FOREIGN KEY (users_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: payload_preferences_rels payload_preferences_rels_parent_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_preferences_rels
    ADD CONSTRAINT payload_preferences_rels_parent_fk FOREIGN KEY (parent_id) REFERENCES public.payload_preferences(id) ON DELETE CASCADE;


--
-- Name: payload_preferences_rels payload_preferences_rels_users_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.payload_preferences_rels
    ADD CONSTRAINT payload_preferences_rels_users_fk FOREIGN KEY (users_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: subjects subjects_tenant_id_tenants_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.subjects
    ADD CONSTRAINT subjects_tenant_id_tenants_id_fk FOREIGN KEY (tenant_id) REFERENCES public.tenants(id) ON DELETE SET NULL;


--
-- Name: submissions submissions_assignment_id_assignments_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.submissions
    ADD CONSTRAINT submissions_assignment_id_assignments_id_fk FOREIGN KEY (assignment_id) REFERENCES public.assignments(id) ON DELETE SET NULL;


--
-- Name: submissions submissions_student_id_users_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.submissions
    ADD CONSTRAINT submissions_student_id_users_id_fk FOREIGN KEY (student_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: submissions submissions_tenant_id_tenants_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.submissions
    ADD CONSTRAINT submissions_tenant_id_tenants_id_fk FOREIGN KEY (tenant_id) REFERENCES public.tenants(id) ON DELETE SET NULL;


--
-- Name: users_roles users_roles_parent_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.users_roles
    ADD CONSTRAINT users_roles_parent_fk FOREIGN KEY (parent_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: users_tenants users_tenants_parent_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.users_tenants
    ADD CONSTRAINT users_tenants_parent_id_fk FOREIGN KEY (_parent_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: users_tenants_roles users_tenants_roles_parent_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.users_tenants_roles
    ADD CONSTRAINT users_tenants_roles_parent_fk FOREIGN KEY (parent_id) REFERENCES public.users_tenants(id) ON DELETE CASCADE;


--
-- Name: users_tenants users_tenants_tenant_id_tenants_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: sandip
--

ALTER TABLE ONLY public.users_tenants
    ADD CONSTRAINT users_tenants_tenant_id_tenants_id_fk FOREIGN KEY (tenant_id) REFERENCES public.tenants(id) ON DELETE SET NULL;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: sandip
--

REVOKE USAGE ON SCHEMA public FROM PUBLIC;


--
-- PostgreSQL database dump complete
--

