import json
from locust import HttpUser, between, task

class WebsiteUser(HttpUser):
    # Set the host to match your fetch request's domain and port
    host = "http://*************:3001"
    wait_time = between(5, 15)

    @task(1) # Higher weight for browsing assignments
    def view_assignment(self):
        """Simulates viewing the assignment page."""
        self.client.get("/assignments/6")

    @task(2) # Task for submitting an assignment
    def submit_assignment(self):
        """Simulates submitting an assignment."""
        headers = {
            'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0',
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Referer': 'http://*************:3001/assignments/6',
            'Content-Type': 'application/json',
            'sentry-trace': '41851318bf914d4838bb178da81b465e-928c6d6cab7814dc-1',
            'baggage': 'sentry-environment=production,sentry-release=7450ccb566ee869314d3382665fad8f228944f8b,sentry-public_key=8cb8ea32027d0ecfe88dc7f60b2e5245,sentry-trace_id=41851318bf914d4838bb178da81b465e,sentry-org_id=4508189259202560,sentry-transaction=GET%20%2Fassignments%2F%5Baid%5D,sentry-sampled=true,sentry-sample_rand=0.9814864899429627,sentry-sample_rate=1',
            'Origin': 'http://*************:3001',
            'Connection': 'keep-alive',
            'Cookie': 'super-labs-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6tXm1XR6YivomExaB5jDp6u-UHU3Bt-9jQBBYfU0zH4; payload-tenant=1; ph_phc_TOPkjOFAAXyZr10arOuchZhUUJgjwD7mm6lZyHb06ms_posthog=%7B%22distinct_id%22%3A%2201979b79-f501-78e3-92c7-7ac8632b945b%22%2C%22%24sesid%22%3A%5B1750752782616%2C%220197a0db-b102-7378-8f2b-89ee33939ff6%22%2C1750750441730%5D%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2F*************%3A3000%2Flogin%3Ferror%3Dno-token%26redirect%3D%252F%22%7D%7D',
            'Priority': 'u=4',
        }
        data = {
            "c": "",
            "passedTestCases": 0,
            "failedTestCases": 1
        }
        self.client.post("/api/assignments/6/submit", headers=headers, json=data)