import { summaryWorkflow } from './src/mastra/workflows/summary-workflow';

// Test the summary workflow
async function testSummaryWorkflow() {
  try {
    const testInput = {
      code: `
public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
}`,
      testsResult: [
        {
          input: "",
          expected: "Hello, World!",
          actual: "Hello, World!",
          status: "PASS"
        }
      ]
    };

    console.log('Testing summary workflow...');
    const result = await summaryWorkflow.execute(testInput);
    console.log('Summary workflow result:', result);
  } catch (error) {
    console.error('Error testing summary workflow:', error);
  }
}

testSummaryWorkflow();
