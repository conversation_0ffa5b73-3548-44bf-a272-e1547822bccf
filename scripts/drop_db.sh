#!/bin/bash

DB_TO_DROP="mydb"
ADMIN_DB_URL="postgres://sandip:mypassword@localhost:5433/postgres"

echo "Dropping database '$DB_TO_DROP'..."

# Terminate active connections
psql "$ADMIN_DB_URL" -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '$DB_TO_DROP';"

# Drop database
psql "$ADMIN_DB_URL" -c "DROP DATABASE IF EXISTS \"$DB_TO_DROP\";"

if [[ $? -eq 0 ]]; then
  echo "✅ Database '$DB_TO_DROP' dropped successfully."
else
  echo "❌ Failed to drop database '$DB_TO_DROP'."
  exit 1
fi
