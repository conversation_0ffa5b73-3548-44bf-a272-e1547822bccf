#!/bin/bash

# Configuration
SQL_FILE="$1"
DB_URL="postgres://sandip:mypassword@localhost:5433/mydb"
ADMIN_DB_URL="${DB_URL%/*}/postgres"

# Check SQL file exists
if [[ ! -f "$SQL_FILE" ]]; then
  echo "❌ Error: SQL file '$SQL_FILE' not found."
  exit 1
fi

# Function to extract components from DB URL
parse_pg_url() {
  local url="$1"
  # Example: postgres://user:pass@host:port/db
  proto="$(echo "$url" | grep '://' | sed -e's,^\(.*://\).*,\1,g')"
  url_no_proto="${url/${proto}/}"
  userpass_hostport_db="${url_no_proto}"
  IFS='@' read -r userpass hostport_db <<< "$userpass_hostport_db"
  IFS=':' read -r DB_USER DB_PASSWORD <<< "$userpass"
  IFS='/' read -r hostport DB_NAME <<< "$hostport_db"
  IFS=':' read -r DB_HOST DB_PORT <<< "$hostport"
}

# Parse DB URL for credentials and components
parse_pg_url "$DB_URL"
export PGPASSWORD="$DB_PASSWORD"

# Check if database exists
echo "🔍 Checking if database '$DB_NAME' exists..."
DB_EXISTS=$(psql "$ADMIN_DB_URL" -tAc "SELECT 1 FROM pg_database WHERE datname = '$DB_NAME'")

# Create the database if it doesn't exist
if [[ "$DB_EXISTS" != "1" ]]; then
  echo "🆕 Database '$DB_NAME' does not exist. Creating..."
  createdb -U "$DB_USER" -h "$DB_HOST" -p "$DB_PORT" "$DB_NAME" || {
    echo "❌ Failed to create database."
    exit 2
  }
else
  echo "✅ Database '$DB_NAME' already exists."
fi

# Restore the backup
echo "📦 Restoring '$SQL_FILE' to database '$DB_NAME'..."
psql -U "$DB_USER" -h "$DB_HOST" -p "$DB_PORT" -d "$DB_NAME" < "$SQL_FILE"

if [[ $? -eq 0 ]]; then
  echo "✅ Restore completed successfully."
else
  echo "❌ Restore failed."
  exit 3
fi
