#!/bin/bash

# Settings
BACKUP_DIR="/home/<USER>/db_backups"
DATE=$(date +\%Y-\%m-\%d_\%H-\%M)
CONTAINER_NAME="local_postgres"
DB_USER="sandip"
DB_NAME="mydb"
DB_PASSWORD="mypassword"

# Ensure backup directory exists
mkdir -p "$BACKUP_DIR"

# Run backup using docker exec
docker exec -e PGPASSWORD=$DB_PASSWORD $CONTAINER_NAME pg_dump -U $DB_USER $DB_NAME > "$BACKUP_DIR/${DB_NAME}_backup_$DATE.sql"

# Optional: remove backups older than 7 days
find "$BACKUP_DIR" -type f -name "*.sql" -mtime +7 -exec rm {} \;
