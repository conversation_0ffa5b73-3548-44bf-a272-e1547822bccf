oha -m POST -z 1m \
-H 'Accept: */*' \
-H 'Accept-Language: en-US,en;q=0.9' \
-H 'Connection: keep-alive' \
-H 'Content-Type: application/json' \
-H 'Cookie: super-labs-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************.jq9ofV5W_BoXWK3p3gZ1c605u4cHIrHbgXEuJ2TlxqQ; payload-tenant=1; ph_phc_TOPkjOFAAXyZr10arOuchZhUUJgjwD7mm6lZyHb06ms_posthog=%7B%22distinct_id%22%3A%220197a2b7-32d3-7ee5-a286-a75683b0e1e8%22%2C%22%24sesid%22%3A%5B1750781868230%2C%220197a2b7-32d2-7566-b98d-2eab38b4006b%22%2C1750781604562%5D%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22http%3A%2F%2Flocalhost%3A3000%2Flogin%3Ferror%3Dno-token%26redirect%3D%252Fassignments%22%7D%7D' \
-H 'Origin: http://localhost:3000' \
-H 'Referer: http://localhost:3000/assignments/4' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
-H 'baggage: sentry-environment=production,sentry-release=74a66098cca91626b210e0483ead03f4255dbcdf,sentry-public_key=8cb8ea32027d0ecfe88dc7f60b2e5245,sentry-trace_id=518b2004661ad6ffaf1ca931569c7f3f,sentry-org_id=4508189259202560,sentry-transaction=GET%20%2Fassignments%2F%5Baid%5D,sentry-sampled=true,sentry-sample_rand=0.23895189383501725,sentry-sample_rate=1' \
-H 'sec-ch-prefers-color-scheme: dark' \
-H 'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"' \
-H 'sec-ch-ua-mobile: ?0' \
-H 'sec-ch-ua-platform: "Linux"' \
-H 'sentry-trace: 518b2004661ad6ffaf1ca931569c7f3f-b4444171f86b371a-1' \
-d '{"java":"import java.util.Scanner;\n\nclass Node {\n    int data;\n    Node next;\n\n    Node(int d) {\n        data = d;\n        next = null;\n    }\n}\n\nclass LinkedList {\n    Node head;\n\n    // Insert node at the end of the list\n    public void insert(int data) {\n\n    }\n\n    // Remove duplicate nodes from a sorted linked list\n    public void removeDuplicates() {\n\n    }\n\n    // Display the linked list\n    public void display() {\n\n    }\n}\n\npublic class Main {\n    public static void main(String[] args) {\n        Scanner scanner = new Scanner(System.in);\n        LinkedList list = new LinkedList();\n\n        // Read input and create linked list\n        int n = scanner.nextInt();\n        for (int i = 0; i < n; i++) {\n            int data = scanner.nextInt();\n            list.insert(data);\n        }\n\n        // Remove duplicates\n        list.removeDuplicates();\n\n        // Display the updated list\n        list.display();\n\n        scanner.close();\n    }\n}","passedTestCases":0,"failedTestCases":1}' \
'http://localhost:3000/api/assignments/4/submit'
