services:
  app:
    image: ${REGISTRY:-docker.io}/${IMAGE_NAME:-your-org/your-app}:${TAG:-latest}
    environment:
      - NODE_ENV=staging
      - DATABASE_URL=${STAGING_DATABASE_URL}
      - REDIS_URL=${STAGING_REDIS_URL}
      # Add other environment variables as needed
    ports:
      - '3000:3000'
    depends_on:
      - db
      - redis

  db:
    image: postgres:14-alpine
    environment:
      - POSTGRES_USER=${STAGING_DB_USER}
      - POSTGRES_PASSWORD=${STAGING_DB_PASSWORD}
      - POSTGRES_DB=${STAGING_DB_NAME}
    volumes:
      - staging_postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:alpine
    volumes:
      - staging_redis_data:/data

volumes:
  staging_postgres_data:
  staging_redis_data:
