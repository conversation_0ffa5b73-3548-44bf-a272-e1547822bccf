# name: E2E Tests
# on:
#   push:
#     branches: [main, master]
#   pull_request:
#     branches: [main, master]

# jobs:
#   test:
#     timeout-minutes: 60
#     runs-on: ubuntu-latest

#     steps:
#       - uses: actions/checkout@v4

#       - uses: actions/setup-node@v4
#         with:
#           node-version: lts/*

#       - name: Install dependencies
#         run: npm install -g pnpm && pnpm install

#       - name: Create .env file
#         run: cp .env.example .env

#       - name: Setup database
#         run: |
#           bash ./scripts/cleandb.sh
#           export SEED_DB=true

#       - name: Install Playwright Browsers
#         run: pnpm exec playwright install --with-deps

#       - name: Run Playwright tests
#         run: SEED_DB=true pnpm exec playwright test

#       - uses: actions/upload-artifact@v4
#         if: ${{ !cancelled() }}
#         with:
#           name: playwright-report
#           path: playwright-report/
#           retention-days: 30
