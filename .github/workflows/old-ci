# name: ci

# on:
#   push:
#     branches:
#       - main
#   pull_request:
#     branches:
#       - main

# jobs:
#   build:
#     runs-on: ubuntu-latest
#     steps:
#       - uses: actions/checkout@v3
#       - uses: pnpm/action-setup@v2
#         with:
#           version: 10
#       - uses: actions/setup-node@v3
#         with:
#           node-version: 18
#           cache: 'pnpm'
#       - run: pnpm install --frozen-lockfile
#       - run: pnpm run typecheck
#       - run: pnpm run lint
#       - run: pnpm run build
#       - run: pnpm run test
#       # - run: pnpm run test:e2e
#       - run: pnpm run cloc
